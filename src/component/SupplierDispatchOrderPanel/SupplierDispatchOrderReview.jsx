import React from 'react';
import PropTypes from 'prop-types';
import SupplierDispatchOrderBasicInfo from './SupplierDispatchOrderBasicInfo';
import { formModes } from '../../constants/formConstant';
import SupplierDispatchOrderProductInfo from './SupplierDispatchOrderProductInfo';
import SupplierDispatchOrderFinalInfo from './SupplierDispatchOrderFinalInfo';

const SupplierDispatchOrderReview = props => {
  const { basicInfoForm, productsForm, finalInfoForm, initialValue, setInitialValue } = props;

  return (
    <>
      <SupplierDispatchOrderBasicInfo
        form={basicInfoForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }} //TODO: improve mode handling
        setInitialValue={setInitialValue}
      />
      <SupplierDispatchOrderProductInfo
        form={productsForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }}
      />
      <SupplierDispatchOrderFinalInfo
        form={finalInfoForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }}
      />
    </>
  );
};

export default SupplierDispatchOrderReview;

SupplierDispatchOrderReview.propTypes = {
  basicInfoForm: PropTypes.object.isRequired,
  productsForm: PropTypes.object.isRequired,
  finalInfoForm: PropTypes.object.isRequired,
  initialValue: PropTypes.object.isRequired,
  formView: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func.isRequired,
};
