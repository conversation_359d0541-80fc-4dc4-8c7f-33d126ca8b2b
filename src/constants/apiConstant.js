const apiEndpoints = {
  customerSeacrh: '/customer/search',
  customerCrud: '/customer',
  customerResetPassword: '/customer/reset-password',
  task: '/activities',
  employeTask: 'activities/my-activities',
  taskSummary: 'activities/activity-summary',
  criticalPathTasks: '/critical-path',
  orderBookValidatePO: '/order-book/validatePO',
  orderBookHardAssignPO:'/order-book/hard-assign',
  orderBookSearch: '/order-book/search',
  Search: (type) => `/${type}/search`,
  orderBookCrud: '/order-book',
  supplierOrderBookSearch: '/supplier-order-book/search',
  supplierOrderBookCrud: '/supplier-order-book',
  supplierOrderBookValidatePO: '/supplier-order-book/validatePO',
  dispatchOrderSearch: '/customer-order/search',
  dispatchOrderSearchByPoNumber: '/customer-order/findByPoNumber',
  dispatchOrderCrud: '/customer-order',
  dispatchOrderValidateProductQuantity:'/customer-order/validateOrderQuantity',
  supplierDispatchOrderSearch: '/supplier-order/search',
  supplierDispatchOrderCrud: '/supplier-order',
  supplierDispatchOrderValidateProductQuantity:'/supplier-order/validateOrderQuantity',
  productSearch: '/product/search',
  productCRUD: '/product',
  supplierSearch: '/supplier/search',
  supplierCrud: '/supplier',
  employeeSearch: '/employee/search',
  employeeCrud: '/employee',
  getFormConfig: '/config',
  login: '/auth/login',
  logout: '/auth/logout',
  refreshToken: '/auth/refresh-tokens',
  storageFileUpload: '/storage/file/upload',
  storageFileDownload: '/storage/file/getSignedUrl',
  poValidator: '/',
  productSuppliers: '/supplier/product-suppliers',
  uploadMstackDocument: '/supplier/upload-mstack-documents',
  deleteMstackDocument: '/supplier/delete-mstack-document',
  enquirySearch: '/enquiry/search',
  enquiryCrud: '/enquiry',
  enquiryAssign: '/enquiry/assign',
  approveEnquiry: '/enquiry/approveEnquiry',
  approveQuotation: '/enquiry/approve-quotation',
  rejectionQuotation: '/enquiry/reject-quotation',
  negotiateQuotation: '/enquiry/negotiate',
  closeEnquiry: '/enquiry/close-enquiry',
  createQuotation: '/enquiry/create-quotation',
  fetchDocComments: '/document-review/comments',
  sendForReview:'/document-review/send-for-review',
  sendToCustomer: '/document-review/send-to-customer',
  approveAndSend: '/document-review/approve-and-send',
  sendForRevision: '/document-review/send-for-revision',
  addDocComment: '/document-review/addComment',
  updateDocComment: '/document-review/updateComment',
  deleteDocComment: '/document-review/deleteComment',
  resetDocComment: '/document-review/deleteAllComment',
  updateDocument: '/updateDraftDocument',
  fetchFileForOrder: '/docEngine/docMeta/files',
  validFileType: '/docEngine/template/valid-fileTypes',
  aggregrateDocData: '/docEngine/template/aggregate',
  generateDocument: '/docEngine/convert_html_to_pdf',
  generateSupplierPo:'/supplier-order-book/generate-po',
  createDocMeta: '/docEngine/docMeta',
  fetchDraftFileData: '/docEngine/docMeta/draft-files',
  getdocumentsForDispatchOrder: '/docEngine/docMeta/customer-order-files',
  approveDocument: '/document-review/approve',
  downloadDocumentZip: (id) => `/docEngine/docMeta/${id}/files-zip`,
  fetchUploadDocuments: '/docEngine/template/templates-by-category/Other Documents',
  dispatchOrderV2: '/customer-order/orderBook',
  generateInvoiceNumber: '/customer-order/generate-invoice-number',
  supplierOrderBookV2: '/supplier-order-book/customer-order-book',
  invoiceCrud: '/invoice',
  invoiceSearch: '/invoice/search',
  invoiceGetReport: '/invoice/getReport',
  inventorySearch: '/inventory/search',
  inventoryProductSearch: '/inventory-product/search',
  inventoryStock: '/inventory-product/getStockDetail',
  inventoryBatch: '/inventory-product/getBatches',
  inventoryTransactions: '/inventory-product/getTransactions',
  batch: '/product-batch',
  fetchinventoryOutOrder: '/inventory-out-order/getByOrderBook',
  inventoryOutOrder: '/inventory-out-order',
  virtualBatch: '/product-batch/virtual-batches',
  packaging:'/packaging',
  packagingSearch:'/packaging/search',
  unallocatedBatch: '/product-batch/unallocated-batches',
  allocateBatch: '/product-batch/allocate-batches',
  resetAllocatedBatch: '/product-batch/reset-batches',
  allocatedBatch: '/product-batch/allocated-batches',
  fetchOBAllocatedInventory: '/inventory-product/getAllocatedInventory',
  checkInventory: '/inventory-product/checkInventory',
  inventoryUnits: '/inventory-product/product-units',
  productInventoryUnits: '/inventory-product/getAIForProduct',
  unallocateInventory: '/inventory-product/unAllocateInventory',
  employeeDetails: '/employee/user-details',
  permissionsGroup: '/permissionsGroup',
  permissionConfig: '/permission-config',
  employeePasswordReset: '/employee/user-details/reset-password',
  report:'/report',
  mailCrud:'/mail',
  googleLogin: '/auth/google-login',
  tasksEndPoint:'/activities/search',
  updateTasks: (id) => `/activities/${id}`,
  putEndpoint:  (entity,id) => `/${entity}/${id}`,
  getOrdersByCustomerIdEndPoint: (customerId) => `/order-book?customerId=${customerId}`,
  createInvoice: '/invoice/v2/create',
  getInvoice : (id) => '/invoice/v2?id=' + id,
  getSupplierByNameEndPoint : (name,customerName) => `/supplier-order-book/get-supplierOrderBook-by-name?name=${name}&customerName=${customerName}`,
  getCalculatedMarginData : (poId) => '/invoice/v2/calculate?poId=' + poId,
  getOrderTreeDataFromId: `/order-book-tree`,
};

export default apiEndpoints;
