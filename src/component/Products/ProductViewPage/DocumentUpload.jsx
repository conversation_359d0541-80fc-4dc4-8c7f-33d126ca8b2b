import React from 'react';
import PropTypes from 'prop-types';
import { Form, Row, Col, Select, Input } from 'antd';
import FileUpload from '../../FileUpload';

const DocumentUpload = (props) => {
  const { field, productDocumentsType } = props;

  return (
    <>
      <Row gutter={6} style={{ width: '100%' }}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'documentType']}
            rules={[{ required: true, message: 'Please select a document type' }]}
            style={{ marginBottom: '6px' }}
          >
            <Select options={productDocumentsType} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={6} style={{ width: '100%' }}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'remark']}
            style={{ marginBottom: '6px' }}
          >
            <Input.TextArea placeholder='Remark'/>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={6} style={{ width: '100%' }}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'files']}
            rules={[{ required: true, message: 'Please upload the file' }]}
          >
            <FileUpload
              category="Orders"
              meta={{ documentType: 'label', poId: 123 }}
              maxFileLimit={1}
              buttonText='Upload File'
              btnStyle={{ display: 'block'}}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
}
export default DocumentUpload;

DocumentUpload.propTypes = {
  field: PropTypes.object.isRequired,
  productDocumentsType: PropTypes.array.isRequired,
};
