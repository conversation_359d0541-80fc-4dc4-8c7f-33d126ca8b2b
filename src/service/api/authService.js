import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig';

export const login = ({ username = '', password = '' }) => {
  return axiosInstance.post(apiEndpoints.login, { username, password });
};

export const logout = (refreshToken = '') => {
  return axiosInstance.post(apiEndpoints.logout, { refreshToken });
};

export const refreshToken = (refreshToken = '') => {
  return axiosInstance.post(apiEndpoints.refreshToken, { refreshToken });
};

export const googleLogin = ({ googleToken= '' }) => {
  return axiosInstance.post(apiEndpoints.googleLogin, { googleToken });
};
