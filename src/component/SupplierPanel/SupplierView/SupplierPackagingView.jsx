import { Descriptions } from 'antd';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

const SupplierPackagingView = props => {
  const { packagingData } = props;

  const items = [
    {
      key: '1',
      label: 'Packaging Type',
      span: { xs: 4, md: 2, xl: 1 },
      children: packagingData.type,
    },
    {
      key: '2',
      label: 'Dimensions',
      span: { xs: 4, md: 2, xl: 1 },
      children: packagingData.dimension,
    },
    {
      key: '3',
      label: 'Pack Size',
      span: { xs: 4, md: 2, xl: 1 },
      children: packagingData.packSize,
    },
    {
      key: '4',
      label: 'Tare Weight',
      span: { xs: 4, md: 2, xl: 1 },
      children: packagingData.tareWeight,
    },
  ];

  useEffect(() => {
    // update address
    console.log('current packaging data ', packagingData);
  }, []);

  return <Descriptions colon={false} layout="vertical" items={items} labelStyle={{ fontWeight: '700', color: '#23568A' }}/>;
};

export default SupplierPackagingView;

SupplierPackagingView.propTypes = {
  packagingData: PropTypes.object,
};
