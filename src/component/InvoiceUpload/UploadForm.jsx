import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Input, Col, Form, Row, DatePicker } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';
import FileUpload from '../../component/FileUpload';
import { getCustomerList } from '../../service/api/customerApi';
import { getSupplierList } from '../../service/api/supplierService';
import { createInvoiceV2, getAllocationPercentage } from '../../service/api/invoiceApi';
import { getTasks } from '../../service/api/taskService';
import { getDispatchOrderList } from '../../service/api/dispatchOrderApi';
import { getSupplierByName } from '../../service/api/supplierOrderBookApi';
import { calculateMarginData } from '../../service/api/invoiceApi';
import PropTypes from 'prop-types';
import currencyRates from '../../util/currency.json';
import { message } from 'antd';
import useClickTracker from '../PostHog/useClickTracker';
import dayjs from 'dayjs';

const { TextArea } = Input;

const costDescriptions = [
  'Supplier Invoice',
  'First mile logistics',
  'Port charges',
  'Sea Freight',
  'Warehouse',
  'Last mile logistics',
  'Destination Charges',
  'Duty Applicable',
  'Packaging',
  'DD+RODTEP',
  'Others',
];

const deviationReasons = [
  'No Deviation Observed',
  'Cost Price changed from Supplier',
  'Did not estimate this cost earlier',
  'Estimated the cost but ended up being more',
  'Extra cost due to penalty',
  'Market price increased',
  'Scope of work got changed',
  'Others',
];

const fieldsToClear = [
  'invoiceNumber',
  'invoiceDate',
  'invoiceAmount',
  'currency',
  'dueDate',
  'taxAmount',
  'allocatedAmount',
  'invoiceQty',
  'reasonForDeviation',
  'remarks',
  'invoiceFile',
  'conversionRate',
];

const currencies = ['USD', 'EUR', 'JPY', 'INR', 'CNY', 'AED'];

const UploadForm = ({
  setShowMarginAnalysis,
  setRfqData,
  supplierOrder,
  customerOrder,
  invoice,
  setInvoice,
  setMarginData,
  masterCurrency,
  marginData,
  setPOData,
  POData,
  setLoading,
}) => {
  const [form] = Form.useForm();
  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: '',
    invoiceDate: '',
    currency: '',
    invoiceAmount: '',
    taxAmount: '',
    allocatedAmount: '',
    invoiceQty: '',
    dueDate: '',
    costDescription: '',
    reasonForDeviation: '',
    remarks: '',
    file: null,
    supplierName: '',
    customerName: '',
    supplierPO: '',
    chemicalName: [], // Contains objects with id and name
    customerPO: '',
    dispatchOrder: '',
    supplierCustomerName: '',
    customCostDescription: '',
    conversionRate: 1.00000,

  });
  const [customerNames, setcustomerNames] = useState([]);
  const [supplierCustomerNames, setSupplierCustomerNames] = useState([]);
  const [supplierNames, setsupplierNames] = useState([]);
  const [activityData, setActivityData] = useState();
  const [customerDispatchOrders, setCustomerDispatchOrders] = useState();
  const [supplierPos, setSupplierPos] = useState();
  const [supplierPOData, setSupplierPOData] = useState();
  const [chemicalNames, setChemicalNames] = useState();
  const [ids, setIds] = useState();
  const [calculatedValues, setCalculatedValues] = useState({
    variance: 0,
    deviation: 0,
  });
  const [allocationPercentage, setAllocationPercentage] = useState(null);
  const [originalAllocationData, setOriginalAllocationData] = useState(null);
  const [hasExistingInvoiceData, setHasExistingInvoiceData] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);
  const prevCostDescriptionRef = useRef();
  const { trackClick } = useClickTracker();

  useEffect(() => {
    prevCostDescriptionRef.current = invoiceData.costDescription;
  }, []);

  // clearing stale values in case of deviation and variance calculation.
  useEffect(() => {
    const currentCostDescription = invoiceData.costDescription;

    if (
      (currentCostDescription === 'Supplier Invoice' ||
        (prevCostDescriptionRef.current &&
          prevCostDescriptionRef.current === 'Supplier Invoice')) &&
      !customerOrder
    ) {
      setShowMarginAnalysis(false);
      clearFormFields();
      setMarginData({});
      clearSpecificFields(['customerPO', 'dispatchOrder', 'supplierPO', 'chemicalName']);
    }

    // Update the previous value reference
    prevCostDescriptionRef.current = currentCostDescription;
  }, [invoiceData.costDescription]);

  // Handle supplier name or customer name selection for Supplier Invoice
  useEffect(() => {
    if (invoiceData.costDescription === 'Supplier Invoice') {
      const selectedName = invoiceData.supplierName || '';
      const customerName = invoiceData.supplierCustomerName || '';
      clearSpecificFields(['supplierPO', 'chemicalName']);

      if (selectedName || customerName) {
        setLoading(true);
        getSupplierByName(selectedName, customerName) // Replace 'Lakeland' with actual parameter
          .then(response => {
            let newResponse = response?.data?.map(supplier => supplier.orderBookId);
            setSupplierPOData(response?.data);
            setSupplierPos(newResponse);
          })
          .catch(error => {
            console.error('Error fetching supplier by name:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
     if(!customerOrder){ setShowMarginAnalysis(false);
      setMarginData({});}
      clearFormFields();
    }
  }, [invoiceData.supplierName, invoiceData.supplierCustomerName]);

  // Add the method
  const clearSpecificFields = fieldsToReset => {
    const formValues = {};
    const stateValues = {};

    fieldsToReset.forEach(field => {
      formValues[field] = undefined;
      stateValues[field] = '';
    });

    form.setFieldsValue(formValues);

    setInvoiceData(prev => ({
      ...prev,
      ...stateValues,
    }));
  };

  //resetting the values
  useEffect(() => {
    clearSpecificFields(['customerPO', 'dispatchOrder']);
  }, [invoiceData.customerName]);

  // Handle customer name selection for other categories
  useEffect(() => {
    if (invoiceData.costDescription && invoiceData.costDescription !== 'Supplier Invoice') {
      if (invoiceData.customerName) {
        setShowMarginAnalysis(false);
        clearFormFields();
        setMarginData({});
        setLoading(true);
        const payload = {
          size: 100,
          number: 0,
          filters: {
            status: ['TODO', 'IN_PROGRESS'],
            name: ['Invoice Upload'],
          },
          search: {
            customerName: invoiceData.customerName,
          },
        };

        getTasks(payload)
          .then(response => {
            const newData = response?.data?.content?.map(task => task.secondaryId);
            setActivityData(newData);
            setIds(
              response?.data?.content?.map(task => {
                return {
                  customerOrderBookId: task.orderId,
                  customerPurchcaseOrderId: task.secondaryId,
                };
              })
            );
          })
          .catch(error => {
            console.error('Error fetching tasks:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [invoiceData.customerName]);

  // Handle supplier PO selection
  useEffect(() => {
    if (invoiceData.supplierPO) {
      // Find the matching supplier PO from supplierPOData
      const matchingSupplierPO = supplierPOData?.find(
        po => po.orderBookId === invoiceData.supplierPO
      );
      
      if (!matchingSupplierPO) {
        return; // Return empty array if no match found
      }
      clearSpecificFields(['chemicalName']);
      // Extract chemical names and IDs from products
      const chemicalData =
        matchingSupplierPO.products?.map(product => ({
          productId: product.product?.productId,
          name: product.product?.tradeName
        })).filter(chemical => chemical.productId && chemical.name) || [];

      setPOData({
        customerPurchcaseOrderId: matchingSupplierPO?.matchedActivities[0]?.secondaryId,
        customerOrderBookId: matchingSupplierPO?.matchedActivities[0]?.orderId,
      });
      // Filter out any undefined/null values
      setMarginData(null);
      setChemicalNames(chemicalData);
      getDispatchOrderListFromConfig(
        100,
        0,
        {
          purchaseOrderNumber: [matchingSupplierPO?.matchedActivities[0]?.secondaryId], //matchingSupplierPO?.matchedActivities[0]?.secondaryId
        },
        {},
        matchingSupplierPO?.matchedActivities[0]?.secondaryId
      );
      // supplierPOData?.filter
    }
  }, [invoiceData.supplierPO]);

  // Handle customer PO selection
  useEffect(() => {
    if (invoiceData.customerPO || customerOrder) {
      if (customerOrder) {
        setPOData({
          customerPurchcaseOrderId: customerOrder?.purchaseOrderNumber,
          customerOrderBookId: customerOrder?.orderBookId,
        });
      } else {
        setPOData({
          customerPurchcaseOrderId: invoiceData.customerPO,
          customerOrderBookId: ids?.find(
            id => id.customerPurchcaseOrderId === invoiceData.customerPO
          )?.customerOrderBookId,
        });
      }
      setMarginData(null);
      clearSpecificFields(['dispatchOrder']);
      getDispatchOrderListFromConfig(
        100,
        0,
        {
          purchaseOrderNumber: [customerOrder?.purchaseOrderNumber || invoiceData?.customerPO], //customerOrder.purchaseOrderNumber || invoiceData.customerPO
        },
        {},
        customerOrder?.purchaseOrderNumber || invoiceData?.customerPO
      );
    }
  }, [invoiceData.customerPO, customerOrder]);

  // Handle dispatch orderList
  const getDispatchOrderListFromConfig = (
    pageSize,
    pageNumber,
    filters,
    searchkey,
    purchaseOrderNumber
  ) => {
    setLoading(true);
    getDispatchOrderList(pageSize, pageNumber, filters, '')
      .then(response => {
        let newData = response?.data?.content?.map(order => order.orderId);
        setCustomerDispatchOrders(newData);
        setShowMarginAnalysis(true);
        let rfqData = response?.data?.content;
        setRfqData(rfqData);
        calculateMarginData(purchaseOrderNumber).then(res => {
          if (res.data) {
            let invoiceData = res.data;
            setMarginData(invoiceData);
          }
        });
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    try {
      let initialFormValues = {};
      let initialInvoiceData = {};
      if (supplierOrder && supplierOrder.length > 0) {
        let prefillData = {
          supplierPO: supplierOrder.map(supplier => supplier.orderBookId),
          chemicalNames: supplierOrder.map(supplier =>
            supplier.products.map(product => product.product.tradeName)
          ),
          supplierNames: supplierOrder.map(supplier => supplier.supplier.name),
        };
        // Get unique supplier names for the dropdown
        setsupplierNames([...new Set(prefillData?.supplierNames)]);
        const selectedSupplierName = prefillData.supplierNames[0];
        // Filter ALL POs for the selected supplier (not just first one)
        const selectedSupplierPOs = supplierOrder
          .filter(supplier => supplier.supplier.name === selectedSupplierName)
          .map(supplier => supplier.orderBookId);

        setSupplierPos(selectedSupplierPOs);

        setSupplierPos(selectedSupplierPOs);

        // Also get all chemicals for this supplier from all their POs
        const selectedSupplierChemicals = supplierOrder
          .filter(supplier => supplier.supplier.name === selectedSupplierName)
          .flatMap(supplier => supplier.products.map(product => ({
            productId: product.product?.productId,
            name: product.product?.tradeName
          })))
          .filter(chemical => chemical.productId && chemical.name);

        setChemicalNames(selectedSupplierChemicals);

        // setSupplierPos(prefillData?.supplierPO);
        // setChemicalNames(prefillData?.chemicalNames?.flat());
        setsupplierNames(prefillData?.supplierNames);

        initialFormValues.supplierPO = prefillData.supplierPO[0];
        // Set the first chemical's productId as the initial value
        initialFormValues.chemicalName = selectedSupplierChemicals.length > 0 ? [selectedSupplierChemicals[0].productId] : [];
        initialFormValues.supplierName = prefillData.supplierNames[0];

        initialInvoiceData = { ...initialInvoiceData, ...initialFormValues };
      }

      if (customerOrder) {
        let prefillData = {
          customerName: customerOrder.customer.name,
          customerPO: customerOrder.purchaseOrderNumber,
        };
        setcustomerNames([prefillData?.customerName]);
        setSupplierCustomerNames([prefillData?.customerName]);
        setActivityData([prefillData?.customerPO]);
        initialFormValues.customerName = prefillData.customerName;
        initialFormValues.customerPO = prefillData.customerPO;
        initialFormValues.supplierCustomerName = prefillData.customerName;
        initialInvoiceData = { ...initialInvoiceData, ...initialFormValues };
      }

      if (!customerOrder) {
        getSupplierList().then(response => {
          if (response.data && response.data.content) {
            let supplierNames = response.data.content.map(supplier => supplier.name);
            setsupplierNames(supplierNames);
          }
        });
        getCustomerList()
          .then(response => {
            if (response.data && response.data.content) {
              let customerNames = response.data.content.map(customer => customer.name);
              setcustomerNames(customerNames);
              setSupplierCustomerNames(customerNames);
            }
          })
          .catch(error => {
            console.log(error);
          });
      }

      // Set form values and state in one go
      if (Object.keys(initialFormValues).length > 0) {
        form.setFieldsValue(initialFormValues);
        setInvoiceData(prevData => ({ ...prevData, ...initialInvoiceData }));
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  }, [form, customerOrder]);

  const calculateTotal = () => {
    const amount = parseFloat(form.getFieldValue('invoiceAmount')) || 0;
    const tax = parseFloat(form.getFieldValue('taxAmount')) || 0;
    return (amount + tax).toFixed(2);
  };

  const calculateTotalInvoiceAmount = () => {
    return totalAmount;
  };

  const handleSubmit = values => {
    if (
      !(values.supplierName || values.supplierCustomerName) &&
      values.costDescription === 'Supplier Invoice'
    ) {
      message.error('Either Select Supplier or Customer Name to proceed!!');
      return;
    }
    setLoading(true);
    const costType =
      values.costDescription === 'Others' ? values.customCostDescription : values.costDescription;
    let payload = {};
    payload.customerPurchcaseOrderId = POData?.customerPurchcaseOrderId;
    payload.customerName =
      values.costDescription === 'Supplier Invoice'
        ? values.supplierCustomerName
        : values.customerName;
    payload.customerOrderBookId = POData?.customerOrderBookId;

    // Convert selected productId strings to objects with productId and name
    let chemicalNameData = [];
    if (values.costDescription === 'Supplier Invoice' && values.chemicalName && values.chemicalName.length > 0) {
      chemicalNameData = values.chemicalName.map(productId => {
        const chemical = chemicalNames?.find(chem => chem.productId === productId);
        return {
          productId: productId,
          name: chemical?.name || ''
        };
      }).filter(chem => chem.name); // Filter out any with empty names
    }

    // Ensure correct values for DD+RODTEP
    const isNegativeValidation = requiresNegativeValidation(values.costDescription);
    let finalInvoiceAmount = values.invoiceAmount;
    let finalTaxAmount = values.taxAmount;
    let finalAllocatedAmount = values.allocatedAmount;
    
    if (isNegativeValidation) {
      // For DD+RODTEP, ensure values are negative in the database
      finalInvoiceAmount = values.invoiceAmount ? (parseFloat(values.invoiceAmount) >= 0 ? (parseFloat(values.invoiceAmount) * -1).toString() : values.invoiceAmount) : values.invoiceAmount;
      finalTaxAmount = values.taxAmount ? (parseFloat(values.taxAmount) >= 0 ? (parseFloat(values.taxAmount) * -1).toString() : values.taxAmount) : values.taxAmount;
      finalAllocatedAmount = values.allocatedAmount ? (parseFloat(values.allocatedAmount) >= 0 ? (parseFloat(values.allocatedAmount) * -1).toString() : values.allocatedAmount) : values.allocatedAmount;
    }

    const output = {
      ...(values.costDescription === 'Supplier Invoice'
        ? {
            // For Supplier Invoice - send first 2 fields
            supplierName: values.supplierName,
            supplierOrderId: values.supplierPO,
            chemicalName: chemicalNameData, // Contains objects with productId and name
          }
        : {
            // For other cost descriptions - send next 2 fields
            customerDispatchOrderId: values.dispatchOrder,
          }),
      invoiceType: costType,
      invoiceNumber: values.invoiceNumber,
      invoiceDate: values.invoiceDate,
      invoiceAmountWithoutTax: finalInvoiceAmount,
      currencyType: values.currency,
      dueDate: values.dueDate || '',
      taxAmount: finalTaxAmount || '',
      allocatedAmount: finalAllocatedAmount || '',
      invoiceQuantity: values.invoiceQty || '',
      reasonforDeviation: values.reasonForDeviation || '',
      remarks: values.remarks || '',
      invoiceFile: values?.invoiceFile === '' ? null : values?.invoiceFile,
      conversionRate: values.conversionRate,
      // not in values, use static or provide dynamically
    };
    if (!payload.invoices) {
      payload.invoices = [];
    }
    payload.invoices.push(output);

    // Print the payload being sent to the backend
    console.log("Invoice payload being sent to backend:", JSON.stringify(payload, null, 2));

    createInvoiceV2(payload)
      .then(response => {
        console.log('Invoice uploaded successfully:', response.data);
        calculateMarginData(POData?.customerPurchcaseOrderId).then(res => {
          if (res.data) {
            let invoiceData = res.data;
            setMarginData(invoiceData);
          }
        });
        delete payload.invoices;
        payload.invoice_type = costType;
        payload.action_name = 'Uploading Invoice';
        trackClick('button_clicked', payload);
        clearFormFields();

        message.success('Invoice uploaded successfully!'); 
      })
      .catch(error => {
        console.error('Error uploading invoice:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.currency) {
      let parentCurrency = masterCurrency;

      if (parentCurrency) {
        const directRate =
          currencyRates[changedValues.currency][`${changedValues.currency}${parentCurrency}`] ||
          1.0;
        const rateString = directRate.toFixed(5);
        const rate = parseFloat(rateString);
        // Set the conversion rate in the form
        form.setFieldsValue({ conversionRate: rate });
        
        // Update state with both currency and conversion rate
        setInvoiceData(prevData => ({
          ...prevData,
          currency: changedValues.currency,
          conversionRate: rate,
        }));
      } else {
        // Handle other field changes normally
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
        }));
      }
    }
    // Handle cost description change
    else if (changedValues.costDescription) {
      // If changing away from "Others", clear the custom description
      if (changedValues.costDescription !== 'Others') {
        form.setFieldsValue({ customCostDescription: undefined });
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
          customCostDescription: '',
        }));
      } else {
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
        }));
      }
    } else {
      // Handle other field changes
      setInvoiceData(prevData => ({
        ...prevData,
        ...changedValues,
      }));
    }
  };

  const isSupplierInvoice = invoiceData.costDescription === 'Supplier Invoice';
  const isOtherInvoiceType =
    invoiceData.costDescription && invoiceData.costDescription !== 'Supplier Invoice';
  const watchedSupplierName = Form.useWatch('supplierName', form);
  const watchedSupplierCustomerName = Form.useWatch('supplierCustomerName', form);
  const watchedSupplierPO = Form.useWatch('supplierPO', form);
  const watchedCustomerName = Form.useWatch('customerName', form);
  const watchedCustomerPO = Form.useWatch('customerPO', form);

  // Add useEffect to recalculate when values change
  useEffect(() => {
    let allocatedAmount = form.getFieldValue('allocatedAmount');
    const conversionRate = form.getFieldValue('conversionRate');
    const costDescription = form.getFieldValue('costDescription');
    const selectedChemicals = form.getFieldValue('chemicalName');

    if (allocatedAmount && conversionRate && costDescription && marginData) {
              const calculated = calculateVarianceAndDeviation(
          costDescription,
          allocatedAmount,
          conversionRate,
          marginData
        );
      setCalculatedValues(calculated);
    } else {
      // Clear calculated values when required fields are missing
      setCalculatedValues({ variance: 0, deviation: 0 });
    }
      }, [
      form.getFieldValue('allocatedAmount'),
      form.getFieldValue('conversionRate'),
      form.getFieldValue('costDescription'),
      marginData,
      invoiceData.allocatedAmount, // Add dependency on invoiceData.allocatedAmount
    ]);

  // Add useEffect to handle form clearing when cost description changes
  useEffect(() => {
    const currentCostDescription = form.getFieldValue('costDescription');
    const previousCostDescription = prevCostDescriptionRef.current;
    
    if (previousCostDescription && currentCostDescription && previousCostDescription !== currentCostDescription) {
      // Clear form fields when cost description changes
      clearFormFields();
    }
    
    prevCostDescriptionRef.current = currentCostDescription;
  }, [form.getFieldValue('costDescription')]);

  // Update total amount when invoice amount or tax amount changes
  useEffect(() => {
    const invoiceAmount = form.getFieldValue('invoiceAmount') || 0;
    const taxAmount = form.getFieldValue('taxAmount') || 0;
    const costDescription = form.getFieldValue('costDescription');
    const isNegativeValidation = requiresNegativeValidation(costDescription);
    
    if (isNegativeValidation) {
      // For DD+RODTEP, values are already negative (with prefilled minus sign)
      const invoiceNum = parseFloat(invoiceAmount);
      const taxNum = parseFloat(taxAmount);
      
      // Calculate total (both values should be negative)
      const total = invoiceNum + taxNum;
      setTotalAmount(total);
    } else {
      // For other cost types, calculate normally
      const total = parseFloat(invoiceAmount) + parseFloat(taxAmount);
      setTotalAmount(total);
    }
  }, [form.getFieldValue('invoiceAmount'), form.getFieldValue('taxAmount'), form.getFieldValue('costDescription')]);

  // Reset form data when cost description changes
  useEffect(() => {
    const currentCostDescription = form.getFieldValue('costDescription');
    
    // Use a simpler approach - reset form whenever cost description changes
    if (currentCostDescription) {
      console.log('Cost description changed to:', currentCostDescription);
      
      // Clear form fields when cost description changes
      form.setFieldsValue({
        invoiceAmount: undefined,
        taxAmount: undefined,
        allocatedAmount: undefined,
        invoiceQty: undefined,
        reasonForDeviation: undefined,
        remarks: undefined,
        invoiceFile: undefined,
        // Clear all supplier and customer fields
        supplierName: undefined,
        supplierCustomerName: undefined,
        supplierPO: undefined,
        chemicalName: undefined,
        customerName: undefined,
        customerPO: undefined,
        dispatchOrder: undefined,
      });
      
      // Clear state data
      setInvoiceData(prevData => ({
        ...prevData,
        invoiceAmount: '',
        taxAmount: '',
        allocatedAmount: '',
        invoiceQty: '',
        reasonForDeviation: '',
        remarks: '',
        file: null,
        supplierName: '',
        supplierCustomerName: '',
        supplierPO: '',
        chemicalName: [],
        customerName: '',
        customerPO: '',
        dispatchOrder: '',
      }));
      
      // Reset calculated values
      setCalculatedValues({
        variance: 0,
        deviation: 0,
      });
      
      // Reset total amount
      setTotalAmount(0);
      
      // Clear allocation percentage
      setAllocationPercentage(null);
      setOriginalAllocationData(null);
    }
  }, [form.getFieldValue('costDescription')]);

  // Note: Removed auto re-validation for allocated amount to prevent duplicate error messages
  // Validation will happen when user interacts with the field or on form submission

  // Check allocation percentage when invoice number changes with debounce
  useEffect(() => {
    const invoiceNumber = form.getFieldValue('invoiceNumber');
    
    if (invoiceNumber) {
               // Add debounce to prevent API calls on every keystroke
         const timeoutId = setTimeout(() => {
           const costDescription = form.getFieldValue('costDescription');
           
           // Always pass cost description as invoice type
           const invoiceType = costDescription;

           getAllocationPercentage(invoiceNumber, invoiceType)
          .then(response => {
            setAllocationPercentage(response.data);
            setOriginalAllocationData(response.data);
            
            // If existing invoice data found, prefill all fields
            if (response.data.currencyType && response.data.conversionRate) {
              const costDescription = form.getFieldValue('costDescription');
              const isNegativeValidation = requiresNegativeValidation(costDescription);
              
              // For DD+RODTEP, ensure values are negative (only if they were positive in DB)
              const invoiceAmount = isNegativeValidation && response.data.invoiceAmountWithoutTax 
                ? (parseFloat(response.data.invoiceAmountWithoutTax) >= 0 
                    ? (parseFloat(response.data.invoiceAmountWithoutTax) * -1).toString()
                    : response.data.invoiceAmountWithoutTax)
                : response.data.invoiceAmountWithoutTax;
              const taxAmount = isNegativeValidation && response.data.taxAmount 
                ? (parseFloat(response.data.taxAmount) >= 0 
                    ? (parseFloat(response.data.taxAmount) * -1).toString()
                    : response.data.taxAmount)
                : response.data.taxAmount;
              
              form.setFieldsValue({
                currency: response.data.currencyType,
                conversionRate: response.data.conversionRate,
                invoiceDate: response.data.invoiceDate ? dayjs(response.data.invoiceDate) : undefined,
                dueDate: response.data.dueDate ? dayjs(response.data.dueDate) : undefined,
                invoiceAmount: invoiceAmount || undefined,
                taxAmount: taxAmount || undefined,
                allocatedAmount: undefined, // Reset allocated amount when existing data is found
                invoiceQty: response.data.invoiceQuantity || undefined
              });
              setHasExistingInvoiceData(true);
            } else {
              // When no existing data found, clear all form data
              form.setFieldsValue({
                currency: undefined,
                conversionRate: undefined,
                invoiceDate: undefined,
                dueDate: undefined,
                invoiceAmount: undefined,
                taxAmount: undefined,
                allocatedAmount: undefined,
                invoiceQty: undefined
              });
              setAllocationPercentage(null);
              setOriginalAllocationData(null);
              setHasExistingInvoiceData(false);
              // Reset calculated values
              setTotalAmount(0);
              setCalculatedValues({
                variance: 0,
                deviation: 0,
              });
            }
          })
          .catch(error => {
            console.error('Error fetching allocation percentage:', error);
            // On error, clear all form data
            form.setFieldsValue({
              currency: undefined,
              conversionRate: undefined,
              invoiceDate: undefined,
              dueDate: undefined,
              invoiceAmount: undefined,
              taxAmount: undefined,
              allocatedAmount: undefined,
              invoiceQty: undefined
            });
            setAllocationPercentage(null);
            setOriginalAllocationData(null);
            setHasExistingInvoiceData(false);
            // Reset calculated values
            setTotalAmount(0);
            setCalculatedValues({
              variance: 0,
              deviation: 0,
            });
          });
      }, 500); // Wait 500ms after user stops typing
      
      // Cleanup timeout if user types again
      return () => clearTimeout(timeoutId);
    } else {
      // Clear allocation data when invoice number is empty
      setAllocationPercentage(null);
      setOriginalAllocationData(null);
      setHasExistingInvoiceData(false);
      // Clear form fields when invoice number is empty
      form.setFieldsValue({
        currency: undefined,
        conversionRate: undefined,
        invoiceDate: undefined,
        dueDate: undefined,
        invoiceAmount: undefined,
        taxAmount: undefined,
        allocatedAmount: undefined,
        invoiceQty: undefined
      });
      // Reset calculated values
      setTotalAmount(0);
      setCalculatedValues({
        variance: 0,
        deviation: 0,
      });
    }
  }, [invoiceData.invoiceNumber]);

  // Recalculate allocation percentage when allocated amount changes
  useEffect(() => {
    const invoiceNumber = form.getFieldValue('invoiceNumber');
    const allocatedAmount = form.getFieldValue('allocatedAmount');
    
    if (invoiceNumber) {
      // Get existing allocations only if we have valid allocation data for current invoice
      const existingTotalAllocated = (originalAllocationData && originalAllocationData.invoiceNumber === invoiceNumber) 
        ? originalAllocationData.totalAllocatedAmount || 0 
        : 0;
      const newAllocatedAmount = parseFloat(allocatedAmount) || 0;
      
      // Calculate new total allocated amount (existing + new)
      const newTotalAllocated = existingTotalAllocated + newAllocatedAmount;
      
      // Use the current total amount from state
      const totalInvoiceAmount = totalAmount;
      
      // Calculate new percentage
      let newPercentage = 0;
      
      // For DD+RODTEP, use absolute values for percentage calculation
      const costDescription = form.getFieldValue('costDescription');
      const isNegativeValidation = requiresNegativeValidation(costDescription);
      
      if (isNegativeValidation) {
        // For DD+RODTEP: use absolute values for percentage calculation
        const absTotalAmount = Math.abs(totalInvoiceAmount);
        const absTotalAllocated = Math.abs(newTotalAllocated);
        
        if (absTotalAmount > 0) {
          newPercentage = (absTotalAllocated / absTotalAmount) * 100;
        }
      } else {
        // For other types: use normal calculation
        if (totalInvoiceAmount > 0) {
          newPercentage = (newTotalAllocated / totalInvoiceAmount) * 100;
        }
      }
      
      // Cap the percentage at 100% if allocated amount exceeds total invoice amount
      if (newPercentage > 100) {
        newPercentage = 100;
      }
      
      // Update the allocation percentage with the new calculation
      console.log('Allocation calculation:', {
        invoiceNumber,
        existingTotalAllocated,
        newAllocatedAmount,
        newTotalAllocated,
        totalInvoiceAmount,
        newPercentage,
        isNegativeValidation: requiresNegativeValidation(form.getFieldValue('costDescription'))
      });
      
      setAllocationPercentage({
        invoiceNumber: invoiceNumber,
        totalAllocatedAmount: newTotalAllocated,
        allocationPercentage: newPercentage
      });
    } else {
      // Clear allocation percentage when no invoice number
      setAllocationPercentage(null);
    }
  }, [invoiceData.allocatedAmount, totalAmount, originalAllocationData, form.getFieldValue('costDescription')]);



  const calculateVarianceAndDeviation = (
    costDescription,
    invoiceAmount,
    conversionRate,
    marginData
  ) => {
    if (!marginData || !costDescription || !invoiceAmount || !conversionRate) {
      return { variance: 0, deviation: 0 };
    }

    const COST_TYPE_MAPPING = {
      'Supplier Invoice': 'Supplier Invoice',
      'First mile logistics': 'First mile logistics',
      'Sea Freight': 'Sea Freight',
      'Last mile logistics': 'Last mile logistics',
      'Destination Charges': 'Destination Charges',
      'Duty Applicable': 'Duty Applicable',
      'Packaging': 'Packaging',
      'DD+RODTEP': 'DD+RODTEP',
    };

    const mappedCostType = COST_TYPE_MAPPING[costDescription];
    
    // Original logic for all cost types (including Supplier Invoice with multi-select)
    const costData = marginData.find(item => item.costType === mappedCostType);
    const rfqValue = costData?.rfqValue || 0;
    const convertedInvoiceAmount = parseFloat(invoiceAmount) * parseFloat(conversionRate);
    const currentInvoiceValue = convertedInvoiceAmount;
    const variance = rfqValue - currentInvoiceValue;
    const deviation = rfqValue !== 0 ? ((rfqValue - currentInvoiceValue) / rfqValue) * 100 : 100;

    return {
      variance: parseFloat(variance.toFixed(2)),
      deviation: parseFloat(deviation.toFixed(2)),
    };
  };

  // Function to check if cost description requires negative validation
  const requiresNegativeValidation = (costDescription) => {
    return costDescription === 'DD+RODTEP';
  };

  const clearFormFields = () => {
    const clearedValues = {};
    fieldsToClear.forEach(field => {
      clearedValues[field] = field.includes('Date') ? undefined : '';
    });

    form.setFieldsValue(clearedValues);

    setInvoiceData(prevData => ({
      ...prevData,
      ...clearedValues,
      file: null,
    }));

    setCalculatedValues({
      variance: 0,
      deviation: 0,
    });
  };

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <FileTextOutlined className="h-6 w-6" />
          Upload Invoice
        </div>
      }
      className="w-full max-w-4xl mx-auto"
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit} onValuesChange={onValuesChange}>
        {/* File Upload section */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg text-center mb-4">
          <Row justify="center" style={{ marginBottom: 0 }}>
            <Col span={4}>
              <Form.Item
                name="invoiceFile"
                rules={[{ required: true, message: 'Please upload Invoice File' }]}
                style={{
                  marginTop: 16,
                  marginBottom: 0,
                  '.ant-form-item-explain-error': {
                    whiteSpace: 'nowrap',
                  },
                }}
              >
                <FileUpload
                  category="Orders"
                  meta={{ poId: 123, documentType: 'PurchaseOrder' }}
                  isSingle={true}
                />
              </Form.Item>
            </Col>
          </Row>
          <p className="text-sm text-gray-500 ">
            {invoiceData.file ? invoiceData.file.name : 'Upload PDF, JPEG, or PNG files'}
          </p>
        </div>

        {/* Cost Description */}
        <Form.Item
          label="Cost Description"
          name="costDescription"
          rules={[{ required: true, message: 'Please select cost description' }]}
        >
          <Select placeholder="Select cost type">
            {costDescriptions.map((desc, idx) => (
              <Select.Option key={idx} value={desc}>
                {desc}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Custom Cost Description - Show only when "Others" is selected */}
        {invoiceData.costDescription === 'Others' && (
          <Form.Item
            label="Cost Description Name"
            name="customCostDescription"
            rules={[
              {
                required: true,
                message: 'Please enter cost description name',
              },
            ]}
          >
            <Input
              placeholder="Enter custom cost description name"
              onChange={e =>
                setInvoiceData(prev => ({
                  ...prev,
                  customCostDescription: e.target.value,
                }))
              }
            />
          </Form.Item>
        )}

        {/* Supplier Invoice Mapping Fields */}
        {isSupplierInvoice && (
          <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-lg font-medium text-blue-900">Supplier Invoice Mapping</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item label="Supplier Name" name="supplierName">
                <Select
                  placeholder="Search and select supplier"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierNames.map((supplier, idx) => (
                    <Select.Option key={idx} value={supplier}>
                      {supplier}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label="Customer Name" name="supplierCustomerName">
                <Select
                  placeholder="Search and select customer"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierCustomerNames?.map((customer, idx) => (
                    <Select.Option key={idx} value={customer}>
                      {customer}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            {(watchedSupplierName ||
              watchedSupplierCustomerName ||
              (supplierOrder && supplierOrder.length > 0)) && (
              <Form.Item
                label="Supplier PO"
                name="supplierPO"
                rules={[{ required: true, message: 'Please select Supplier PO' }]}
              >
                <Select
                  placeholder="Search and select Supplier PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierPos?.map((po, idx) => (
                    <Select.Option key={idx} value={po}>
                      {po}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {(watchedSupplierPO || (supplierOrder && supplierOrder.length > 0)) && (
              <Form.Item 
                label="Chemical Name" 
                name="chemicalName"
                rules={[{ required: true, message: 'Please select at least one chemical' }]}
              >
                <Select
                  placeholder="Select chemicals from Supplier PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                  mode="multiple"
                >
                  {chemicalNames?.map((chemical, idx) => (
                    <Select.Option key={idx} value={chemical.productId}>
                      {chemical.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            <p className="text-sm text-blue-700">
              Note: Select either Supplier Name or Customer Name to proceed with Supplier PO
              selection.
            </p>
          </div>
        )}

        {/* Customer Order Mapping Fields for Other Invoice Types */}
        {isOtherInvoiceType && (
          <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="text-lg font-medium text-green-900">Customer Order Mapping</h3>

            <Form.Item label="Customer Name" name="customerName">
              <Select
                placeholder="Search and select customer"
                showSearch
                optionFilterProp="children"
                allowClear={customerOrder ? false : true}
              >
                {customerNames?.map((customer, idx) => (
                  <Select.Option key={idx} value={customer}>
                    {customer}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            {(watchedCustomerName || customerOrder) && (
              <Form.Item
                label="Customer PO"
                name="customerPO"
                rules={[{ required: true, message: 'Please select Customer PO' }]}
                initialValue={customerOrder ? activityData[0] : undefined}
              >
                <Select
                  placeholder="Search and select Customer PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {activityData?.map((po, idx) => (
                    <Select.Option key={idx} value={po}>
                      {po}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {(watchedCustomerPO || customerOrder) && (
              <Form.Item
                label="Dispatch Order"
                name="dispatchOrder"
                initialValue={
                  customerOrder && customerDispatchOrders?.length === 1
                    ? customerDispatchOrders[0]
                    : undefined
                }
              >
                <Select
                  placeholder="Select dispatch order (auto-filled if single)"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {customerDispatchOrders?.map((order, idx) => (
                    <Select.Option key={idx} value={order}>
                      {order}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            <p className="text-sm text-green-700">
              Note: Customer PO will be auto-filled when accessed through customer order task.
              Dispatch order will auto-fill if only one exists.
            </p>
          </div>
        )}

        {/* Invoice Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            label="Invoice Number"
            name="invoiceNumber"
            rules={[{ required: true, message: 'Please enter invoice number' }]}
          >
            <Input placeholder="Enter invoice number" />
          </Form.Item>

          <Form.Item
            label="Invoice Date"
            name="invoiceDate"
            rules={[{ required: true, message: 'Please select invoice date' }]}
          >
            <DatePicker style={{ width: '100%' }} disabled={hasExistingInvoiceData} />
          </Form.Item>

          <Form.Item
            label="Currency"
            name="currency"
            rules={[{ required: true, message: 'Please Select Currency' }]}
          >
            <Select 
              placeholder="Select currency"
              disabled={hasExistingInvoiceData}
            >
              {currencies?.map(curr => (
                <Select.Option key={curr} value={curr}>
                  {curr}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Add the Conversion Rate field here */}
          <Form.Item
            label="Conversion Rate"
            name="conversionRate"
            rules={[{ required: true, message: 'Please enter conversion rate' }]}
          >
            <Input 
              type="number" 
              step="0.00001" 
              placeholder="0.00000" 
              style={{ width: '100%' }}
              disabled={hasExistingInvoiceData}
            />
          </Form.Item>

          <Form.Item
            label="Due Date"
            name="dueDate"
            rules={[{ required: true, message: 'Please select due date' }]}
          >
            <DatePicker style={{ width: '100%' }} disabled={hasExistingInvoiceData} />
          </Form.Item>

          <Form.Item
            label="Invoice Amount (without tax)"
            name="invoiceAmount"
            rules={[
              { required: true, message: 'Please enter invoice amount' },
              {
                validator: (_, value) => {
                  // Skip validation if value is empty (required rule handles this)
                  if (!value || value.toString().trim() === '') {
                    return Promise.resolve();
                  }
                  
                  const costDescription = form.getFieldValue('costDescription');
                  const isNegativeValidation = requiresNegativeValidation(costDescription);
                  
                  // For DD+RODTEP, remove the minus sign for validation
                  let valueToValidate = value;
                  if (isNegativeValidation && value.startsWith('-')) {
                    valueToValidate = value.substring(1);
                  }
                  
                  // Check for valid number
                  const numValue = parseFloat(valueToValidate);
                  if (isNaN(numValue)) {
                    return Promise.reject(new Error('Please enter a valid number'));
                  }
                  
                  // Validate based on cost description type
                  if (isNegativeValidation) {
                    // For DD+RODTEP: value must be positive (since minus is prefilled)
                    if (numValue <= 0) {
                      return Promise.reject(new Error('Invoice amount must be greater than 0'));
                    }
                  } else {
                    // For other types: value must be positive (at least 0.1)
                    if (numValue < 0.1) {
                      return Promise.reject(new Error('Invoice amount must be at least 0.1'));
                    }
                  }
                  return Promise.resolve();
                },
              },
            ]}
            help={(() => {
              const costDescription = form.getFieldValue('costDescription');
              const isNegativeValidation = requiresNegativeValidation(costDescription);
              const fieldErrors = form.getFieldError('invoiceAmount');
              
              // If there are errors, show the error message
              if (fieldErrors.length > 0) {
                return fieldErrors[0];
              }
              
              // If no errors and it's DD+RODTEP, show info message
              if (isNegativeValidation) {
                return "ℹ️ Always negative: drawback amount";
              }
              
              return null;
            })()}
            validateStatus={(() => {
              const costDescription = form.getFieldValue('costDescription');
              const isNegativeValidation = requiresNegativeValidation(costDescription);
              const fieldErrors = form.getFieldError('invoiceAmount');
              
              // If there are errors, show error status
              if (fieldErrors.length > 0) {
                return "error";
              }
              
              // If no errors and it's DD+RODTEP, show info status
              if (isNegativeValidation) {
                return "info";
              }
              
              return "";
            })()}
          >
            <Input 
              type="number" 
              step="0.01" 
              placeholder={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-0.00" : "0.00"}
              min={requiresNegativeValidation(form.getFieldValue('costDescription')) ? undefined : "0.1"}
              disabled={hasExistingInvoiceData} 
              addonBefore={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-" : null}
              addonBeforeStyle={{ 
                backgroundColor: '#f5f5f5', 
                border: '1px solid #d9d9d9',
                borderRight: 'none',
                padding: '4px 11px',
                color: '#666',
                fontWeight: 'bold',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
              onChange={(e) => {
                const value = e.target.value;
                const costDescription = form.getFieldValue('costDescription');
                const isNegativeValidation = requiresNegativeValidation(costDescription);
                
                if (isNegativeValidation && value) {
                  // For DD+RODTEP, ensure the value is negative
                  const numValue = parseFloat(value);
                  if (!isNaN(numValue) && numValue > 0) {
                    // Only convert if the value is positive (to avoid infinite loop)
                    const negativeValue = (numValue * -1).toString();
                    form.setFieldValue('invoiceAmount', negativeValue);
                  }
                }
              }}
            />
          </Form.Item>
        </div>

        {/* Add spacing after invoice amount row */}
        <div style={{ marginBottom: '16px' }}></div>

        {/* Custom Row Layouts */}
        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-start', marginBottom: '16px' }}>
          <Form.Item
            label="Tax Amount"
            name="taxAmount"
            style={{ flex: 1, marginBottom: 0 }}
            rules={[
              {
                validator: (_, value) => {
                  // Tax amount is optional, so return success if empty
                  if (!value || value.toString().trim() === '') {
                    return Promise.resolve();
                  }
                  
                  const costDescription = form.getFieldValue('costDescription');
                  const isNegativeValidation = requiresNegativeValidation(costDescription);
                  
                  // For DD+RODTEP, remove the minus sign for validation
                  let valueToValidate = value;
                  if (isNegativeValidation && value.startsWith('-')) {
                    valueToValidate = value.substring(1);
                  }
                  
                  // Check for valid number
                  const numValue = parseFloat(valueToValidate);
                  if (isNaN(numValue)) {
                    return Promise.reject(new Error('Please enter a valid number'));
                  }
                  
                  // Validate based on cost description type
                  if (isNegativeValidation) {
                    // For DD+RODTEP: value must be positive (since minus is prefilled)
                    if (numValue < 0) {
                      return Promise.reject(new Error('Tax amount must be greater than or equal to 0'));
                    }
                  } else {
                    // For other types: value must be non-negative
                    if (numValue < 0) {
                      return Promise.reject(new Error('Tax Amount cannot be negative.'));
                    }
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input 
              type="number" 
              step="0.01" 
              placeholder={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-0.00" : "0.00"}
              disabled={hasExistingInvoiceData} 
              addonBefore={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-" : null}
              addonBeforeStyle={{ 
                backgroundColor: '#f5f5f5', 
                border: '1px solid #d9d9d9',
                borderRight: 'none',
                padding: '4px 11px',
                color: '#666',
                fontWeight: 'bold',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
              onChange={(e) => {
                const value = e.target.value;
                const costDescription = form.getFieldValue('costDescription');
                const isNegativeValidation = requiresNegativeValidation(costDescription);
                
                if (isNegativeValidation && value) {
                  // For DD+RODTEP, ensure the value is negative
                  const numValue = parseFloat(value);
                  if (!isNaN(numValue) && numValue > 0) {
                    // Only convert if the value is positive (to avoid infinite loop)
                    const negativeValue = (numValue * -1).toString();
                    form.setFieldValue('taxAmount', negativeValue);
                  }
                }
              }}
            />
          </Form.Item>

          <Form.Item 
            label="Total Amount"
            style={{ flex: 1, marginBottom: 0 }}
          >
            <Input
              value={totalAmount.toFixed(2)}
              readOnly
              style={{ 
                backgroundColor: '#f5f5f5', 
                fontWeight: 'bold',
                color: '#000'
              }}
            />
          </Form.Item>

          <Form.Item 
            label="Invoice Quantity" 
            name="invoiceQty"
            style={{ flex: 1, marginBottom: 0 }}
          >
            <Input type="number" placeholder="Enter quantity" />
          </Form.Item>
        </div>

        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-start', marginBottom: '16px' }}>
          <Form.Item
            label="Allocated Amount"
            name="allocatedAmount"
            style={{ flex: 1, marginBottom: 0 }}
            rules={[
              { required: true, message: 'Please enter allocated amount' },
              {
                validator: (_, value) => {
                  // Skip validation if value is empty (required rule handles this)
                  if (!value || value.toString().trim() === '') {
                    return Promise.resolve();
                  }
                  
                  const costDescription = form.getFieldValue('costDescription');
                  const isNegativeValidation = requiresNegativeValidation(costDescription);
                  
                  // For DD+RODTEP, remove the minus sign for validation
                  let valueToValidate = value;
                  if (isNegativeValidation && value.startsWith('-')) {
                    valueToValidate = value.substring(1);
                  }
                  
                  // Check for valid number
                  const numValue = parseFloat(valueToValidate);
                  if (isNaN(numValue)) {
                    return Promise.reject(new Error('Please enter a valid number'));
                  }
                  
                  // Validate based on cost description type
                  if (isNegativeValidation) {
                    // For DD+RODTEP: value must be positive (since minus is prefilled)
                    if (numValue <= 0) {
                      return Promise.reject(new Error('Allocated amount must be greater than zero'));
                    }
                  } else {
                    // For other types: value must be positive (greater than 0)
                    if (numValue <= 0) {
                      return Promise.reject(new Error('Allocated amount must be greater than zero'));
                    }
                  }
                  
                  const totalInvoiceAmount = calculateTotalInvoiceAmount();
                  // Use existing allocated amount only if we have valid allocation data for current invoice
                  const existingAllocatedAmount = (originalAllocationData && originalAllocationData.invoiceNumber === form.getFieldValue('invoiceNumber')) 
                    ? originalAllocationData.totalAllocatedAmount || 0 
                    : 0;
                  const remainingAmount = totalInvoiceAmount - existingAllocatedAmount;
                  
                  if (isNegativeValidation) {
                    // For DD+RODTEP: check if absolute value doesn't exceed remaining amount
                    if (Math.abs(numValue) > Math.abs(remainingAmount)) {
                      return Promise.reject(new Error(`Allocated amount cannot exceed remaining amount (${remainingAmount.toFixed(2)})`));
                    }
                  } else {
                    // For other types: check if value doesn't exceed remaining amount
                    if (numValue > remainingAmount) {
                      return Promise.reject(new Error(`Allocated amount cannot exceed remaining amount (${remainingAmount.toFixed(2)})`));
                    }
                  }
                  
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input 
              type="number" 
              step="0.01" 
              placeholder={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-0.00" : "0.00"}
              min={requiresNegativeValidation(form.getFieldValue('costDescription')) ? undefined : "0.01"}
              addonBefore={requiresNegativeValidation(form.getFieldValue('costDescription')) ? "-" : null}
              addonBeforeStyle={{ 
                backgroundColor: '#f5f5f5', 
                border: '1px solid #d9d9d9',
                borderRight: 'none',
                padding: '4px 11px',
                color: '#666',
                fontWeight: 'bold',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
              onChange={(e) => {
                const value = e.target.value;
                const costDescription = form.getFieldValue('costDescription');
                const isNegativeValidation = requiresNegativeValidation(costDescription);
                
                if (isNegativeValidation && value) {
                  // For DD+RODTEP, ensure the value is negative
                  const numValue = parseFloat(value);
                  if (!isNaN(numValue) && numValue > 0) {
                    // Only convert if the value is positive (to avoid infinite loop)
                    const negativeValue = (numValue * -1).toString();
                    form.setFieldValue('allocatedAmount', negativeValue);
                  }
                }
                
                setInvoiceData(prev => ({
                  ...prev,
                  allocatedAmount: value
                }));
              }}
            />
          </Form.Item>

          <Form.Item 
            label="Total Invoice Allocated %"
            style={{ flex: 1, marginBottom: 0 }}
          >
            <Input
              value={allocationPercentage ? `${allocationPercentage.allocationPercentage?.toFixed(2)}%` : '0.00%'}
              readOnly
              style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
            />
          </Form.Item>
        </div>

        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-start', marginBottom: '16px' }}>
          <Form.Item label="Variance" style={{ flex: 1, marginBottom: 0 }}>
            <Input
              value={calculatedValues?.variance}
              readOnly
              style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
            />
          </Form.Item>
          <Form.Item label="Deviation (%)" style={{ flex: 1, marginBottom: 0 }}>
            <Input
              value={calculatedValues?.deviation}
              readOnly
              style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
            />
          </Form.Item>
        </div>

        {/* Reason for Deviation */}
        <Form.Item
          label="Reason for Deviation"
          name="reasonForDeviation"
          rules={[{ required: true, message: 'Please select reason for deviation' }]}
        >
          <Select placeholder="Select reason for deviation">
            {deviationReasons.map(reason => (
              <Select.Option key={reason} value={reason}>
                {reason}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Remarks */}
        <Form.Item
          label="Remarks"
          name="remarks"
          rules={[{ required: true, message: 'Please add remarks' }]}
        >
          <TextArea
            rows={4}
            placeholder="Enter any additional remarks (Mandatory, fill NA if not applicable)"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
            Upload Invoice
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

UploadForm.propTypes = {
  setShowMarginAnalysis: PropTypes.func.isRequired,
  setRfqData: PropTypes.func.isRequired,
  supplierOrder: PropTypes.array,
  customerOrder: PropTypes.object,
  invoice: PropTypes.object,
  setInvoice: PropTypes.func.isRequired,
  setMarginData: PropTypes.func.isRequired,
  masterCurrency: PropTypes.string,
  marginData: PropTypes.arrayOf(PropTypes.object).isRequired,
  POData: PropTypes.object.isRequired,
  setPOData: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
};

export default UploadForm;
