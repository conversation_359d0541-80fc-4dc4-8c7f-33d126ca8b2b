import { UserOutlined } from '@ant-design/icons';
import { Layout, Menu } from 'antd';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import logo from '../../../assets/logo/Mstack-logo-1.svg';
import { REFRESH_TOKEN_KEY_NAME } from '../../../constants/localStorageConstants';
import RouteFactory from '../../../service/RouteFactory';
import { logout } from '../../../service/api/authService';
import { setSideDrawerStatus } from '../../../store/actions/collapseSideDrawer';
import { setFormView } from '../../../store/actions/formView';
import { deleteAuthLocalStorage } from '../../../util/auth';
import { getUserTabsFromPermissions, hasPermission } from '../../../util/userUtils';
import css from './AsidePanel.module.css';
import { setSideDrawerData } from '../../../store/actions/sideDrawerData';
import settingIconFilled from '../../../assets/icons/settingsFilled.svg';
import settingIcon from '../../../assets/icons/settings_Regular.svg';
import { userPermissionsList } from '../../../constants/UserTabsConstants';
import { usePostHog } from "posthog-js/react";

const AsidePanel = () => {
  const collapseAsideBar = useSelector(state => state.collapseAsideBar);
  const user = useSelector(state => state.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const posthog = usePostHog();

  const handleLogoClick = () => {
    dispatch(setSideDrawerData(null));
    dispatch(setSideDrawerStatus(true));
    dispatch(setFormView({ show: false, mode: null }));
  };

  const userTabs = getUserTabsFromPermissions(user.permissions);
  const onTabClick = () => {
    dispatch(setSideDrawerData(null));
    dispatch(setSideDrawerStatus(true));
    dispatch(setFormView({ show: false, mode: null }));
  };

  const getMenuItemfromUserTabs = () => {
    const tabs = userTabs.map(tab => ({
      key: tab.key,
      label: (
        <Link className='flex flex-col justify-center items-center' onClick={() => onTabClick()} to={tab.path}>
          <img src={getCurrentTabKeyFromPath() === tab.key? tab.activeIcon : tab.icon} alt={tab.key}/>
          <div className=' text-[10px] leading-3'>{tab.label}</div>
        </Link>
      ),
      className: `w-full !m-0 !mb-6 !p-0 !h-auto !rounded-none !bg-transparent !border-transparent ${getCurrentTabKeyFromPath() === tab.key ? '!border-l-[3px] !border-l-color-text-heading !border-solid' : ''}`,
    }));
    if (hasPermission(userPermissionsList.mstackAdmin, user.permissions)) {
      tabs.push({
        key: 'settings',
        label: (
          <Link
            className='flex flex-col items-center'
            onClick={() => onTabClick()}
            to={new RouteFactory().dashboard().settings().build()}
          >
            <img
              src={getCurrentTabKeyFromPath() === 'settings' ? settingIconFilled : settingIcon}
              alt="settings"
            />
            <div className='mt-2 text-[10px] leading-3'>Settings</div>
          </Link>
        ),
        style: {
          position: 'absolute',
          bottom: '45px',
          zIndex: 1,
          transition: 'all 0.2s',
          overflowX: 'hidden',
          padding: 0,
        },
        className: `w-full !m-0 !mb-6 !p-0 !h-auto !rounded-none !bg-transparent !border-transparent ${getCurrentTabKeyFromPath() === 'settings' ? '!border-l-[3px] !border-l-color-text-heading !border-solid' : ''}`,
      });
    }

    tabs.push({
      key: 'userProfile',
      icon: <UserOutlined />,
      // label: `${user.name}`,
      className: css.activeItem,
      style: {
        position: 'absolute',
        bottom: 0,
        zIndex: 1,
        transition: 'all 0.2s',
        overflowX: 'hidden',
        padding: 0,
      },
      children: [
        {
          key: 'logout',
          label: 'Logout',
          onClick: async () => {
            try {
              await logout(localStorage.getItem(REFRESH_TOKEN_KEY_NAME));
              deleteAuthLocalStorage();
              if (posthog && user) {
                posthog.capture('user_logout', {
                  timestamp: new Date().toISOString(),
                  email: user.username,
                });
              }
            } catch (error) {
              console.log('error while logging out user', error);
            }
            navigate(0);
          },
        },
      ],
    });
    return tabs;
  };

  // TODO: implement proper check for path
  const getCurrentTabKeyFromPath = () => {
    
    if (pathname.includes('customer')) return 'customer';
    if (pathname.includes('enquiry')) return 'enquiry';
    if (pathname.includes('supplier-order-book')) return 'supplierOrderBook';
    if (pathname.includes('order-book')) return 'orderBook';
    if (pathname.includes('supplier-dispatch-order')) return 'supplierDispatchOrder';
    if (pathname.includes('dispatch-order')) return 'dispatchOrder';
    if (pathname.includes('invoice')) return 'invoice';
    if (pathname.includes('supplier')) return 'supplier';
    if (pathname.includes('products')) return 'products';
    if (pathname.includes('inventory')) return 'inventory';
    if (pathname.includes('settings')) return 'settings';
    if(pathname.includes('reports')) return 'reports';
  };

  return (
    <Layout.Sider
      style={{
        padding: collapseAsideBar ? '0' : '12px 0 0',
        overflowX: 'hidden',
        zIndex: '100',
        background: '#f0f0f0',
      }}
      width="5%"
      trigger={null}
      collapsible
      collapsed={collapseAsideBar}
      collapsedWidth={0}
      className={css.asideBar}
      theme="light"
    >
      {/* <Link 
        to={new RouteFactory().dashboard().build()} 
        className={css.logo}
        onClick={null}
      > */}
        <img src={logo} alt="Mstack" loading="lazy" />
      {/* </Link> */}
      <Menu
        style={{
          marginTop: '15px',
          width: '100%',
          border: 'none',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '5px',
          background: 'unset',
        }}
        mode="vertical"
        selectedKeys={[getCurrentTabKeyFromPath()]}
        items={getMenuItemfromUserTabs()}
      />
    </Layout.Sider>
  );
};

export default AsidePanel;
