import React from 'react';
import PropTypes from 'prop-types';
import { Descriptions, Table } from 'antd';
import FileUpload from '../FileUpload';
import {
  paymentTermsDate,
  productIncoTermsList,
  productUOMList,
  incotermsDataList,
} from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const OrderReviewData = props => {
  const { orderBookEntry, fetchAssignedQuantity } = props;

  const getLabelForShipmentMethodOrDefault = key => {
    const label = getEnumValueFromList(key, incotermsDataList.FOB[0].child);
    return label ? label : key;
  };

  const testingRequired = orderBookEntry.products.filter(product => product.testingRequired === true);
  const testingRequiredValue = testingRequired.length > 0;
  
  const dispatchWithResults = orderBookEntry.products.filter(product => product.dispatchWithResults === true);
  const dispatchWithResultsValue = dispatchWithResults.length > 0;

  const orderFieldItems = [
    ...(orderBookEntry?.inventoryId ? 
      (orderBookEntry?.inventoryData?.name || orderBookEntry?.inventory?.name ? [{
        key: '1',
        label: 'Inventory Name',
        children: orderBookEntry?.inventoryData?.name || orderBookEntry?.inventory?.name,
      }] : []) :
      (orderBookEntry?.customer?.name || orderBookEntry?.customerData?.name ? [{
        key: '1',
        label: 'Customer Name',
        children: orderBookEntry?.customer?.name || orderBookEntry?.customerData?.name,
      }] : [])
    ),
    ...(orderBookEntry?.purchaseOrderNumber ? [{
      key: '2',
      label: 'PO number',
      children: orderBookEntry?.purchaseOrderNumber,
    }] : []),
    ...(orderBookEntry?.purchaseOrderDate ? [{
      key: '3',
      label: 'PO date',
      children: getDateFromTimeStamp(orderBookEntry?.purchaseOrderDate),
    }] : []),
    ...(orderBookEntry?.inventoryId ? [] : [{
      key: '4',
      label: 'PO file',
      children: (
        <FileUpload
          value={orderBookEntry?.purchaseOrderFile}
          disabled
          deleteDisabled
        />
      ),
    }]),
    ...(orderBookEntry?.category ? [{
      key: '5',
      label: 'Category',
      children: orderBookEntry?.category,
    }] : []),
    ...(orderBookEntry?.deliveryAddress ? [{
      key: '6',
      label: 'Delivery Address',
      children: orderBookEntry?.deliveryAddress,
    }] : []),
    ...(orderBookEntry?.deliveryDate ? [{
      key: '7',
      label: 'Delivery Date',
      children: getDateFromTimeStamp(orderBookEntry?.deliveryDate),
    }] : []),
    ...(orderBookEntry?.billTo ? [{
      key: '8',
      label: 'Billing address',
      children: orderBookEntry?.billTo,
    }] : []),
  ];
  const paymentFieldItems= [
    ...(orderBookEntry?.paymentTerms?.poPaymentTerms ? [{
      key: '1',
      label: 'PO Payment Terms',
      children: orderBookEntry?.paymentTerms?.poPaymentTerms,
    }] : []),
    ...(orderBookEntry?.paymentTerms?.advanceAmount ? [{
      key: '2',
      label: 'PO Advance (in %)',
      children: orderBookEntry?.paymentTerms?.advanceAmount,
    }] : []),
    ...(orderBookEntry?.paymentTerms?.creditAmount ? [{
      key: '3',
      label: 'Credit amount(%)',
      children: orderBookEntry?.paymentTerms?.creditAmount,
    }] : []),
    ...(orderBookEntry?.paymentTerms?.creditorDays ? [{
      key: '4',
      label: 'Credit days',
      children: orderBookEntry?.paymentTerms?.creditorDays,
    }] : []),
    ...(orderBookEntry?.buyerCurrency ? [{
      key: '5',
      label: 'Currency',
      children: orderBookEntry?.buyerCurrency,
    }] : []),
    ...(orderBookEntry?.poValue ? [{
      key: '6',
      label: 'PO Value',
      children: orderBookEntry?.poValue,
    }] : []),
    ...(orderBookEntry?.taxPercent ? [{
      key: '7',
      label: 'Tax Percentage',
      children: orderBookEntry?.taxPercent,
    }] : []),
    ...(orderBookEntry?.paymentTerms?.remarks ? [{
      key: '8',
      label: 'Remarks',
      children: (
        <div>{orderBookEntry?.paymentTerms?.remarks}</div>
      ),
    }] : []),
  ];
  const productFieldColumns = [
    // Always show Product column as it's essential
    {
      title: 'Product',
      dataIndex: 'product',
      key: 'product',
      render: (_, {product}) => product?.tradeName,
      fixed: 'left',
      width: 200,
    },
    // Always show Quantity column as it's essential
    {
      title: 'Assigned / Total Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 200,
      render:(_, record) => `${fetchAssignedQuantity(record.id)}/${record?.quantity} ${getEnumValueFromList(record?.uom, productUOMList)}`
    },
    // Conditionally show Price column
    ...(orderBookEntry?.products?.some(product => product?.price) ? [{
      title: 'Price/unit($)',
      dataIndex: 'price',
      key: 'price',
      width: 150,
    }] : []),
    // Conditionally show Customer Product Code column
    ...(orderBookEntry?.products?.some(product => product?.customerProductCode) ? [{
      title: 'Customer Product Code',
      dataIndex: 'customerProductCode',
      key: 'customerProductCode',
      width: 150,
    }] : []),
    // Conditionally show HS Code column
    ...(orderBookEntry?.products?.some(product => product?.hsCode) ? [{
      title: 'HS Code',
      dataIndex: 'hsCode',
      key: 'hsCode',
      width: 150,
    }] : []),
    // Conditionally show Incoterms column
    ...(orderBookEntry?.products?.some(product => product?.incoterms) ? [{
      key: 'incoterms',
      title: 'Incoterms',
      dataIndex: 'incoterms',
      width: 250,
      render: (_, record) =>(
        <>
          <div>
            <span style={{ fontWeight: '600' }}>Type:</span>
            <span>
              {getEnumValueFromList(record?.incoterms?.type, productIncoTermsList)}
            </span>
          </div>
          <div>
            <span style={{ fontWeight: '600' }}>Destination Country: </span>
            <span>{record?.incoterms?.country}</span>
          </div>
          {record?.incoterms?.data && Object.keys(record.incoterms.data).length > 0
            ? Object.keys(record.incoterms.data).map(key => (
                <div key={key}>
                  <span style={{ fontWeight: '600' }}>{camelCaseToTitle(key)}:</span>
                  <span>
                    {getLabelForShipmentMethodOrDefault(record.incoterms?.data?.[key])}
                  </span>
                </div>
              ))
            : ''}
        </>
      ),
    }] : []),
    // Conditionally show Expected Delivery Date column
    ...(orderBookEntry?.products?.some(product => product?.expectedDeliveryDate) ? [{
      key: 'expectedDeliveryDate',
      title: 'Expected Delivery Date',
      dataIndex: 'expectedDeliveryDate',
      width: 150,
      render:(_, record) => getDateFromTimeStamp(record?.expectedDeliveryDate),
    }] : []),
    // Conditionally show Packaging column
    ...(orderBookEntry?.products?.some(product => product?.packaging) ? [{
      key: 'packaging',
      title: 'Packaging',
      dataIndex: 'packaging',
      width: 300,
      render: (_, record) => (
        <>
          <div>
            <span style={{ fontWeight: '600' }}>Type:</span>
            <span>{record?.packaging?.type}</span>
          </div>
          {record?.packaging?.type !== 'Others' && (
            <>
              <div>
                <span style={{ fontWeight: '600' }}>Pack Size:</span>
                <span>{record?.packaging?.packSize}</span>
              </div>
              <div>
                <span style={{ fontWeight: '600' }}>Tare Weight:</span>
                <span>{record?.packaging?.tareWeight}</span>
              </div>
              <div>
                <span style={{ fontWeight: '600' }}>Dimension:</span>
                <span>{record?.packaging?.dimension}</span>
              </div>
            </>
          )}
          {record?.packaging?.type === 'Others' && record?.packaging?.otherPackagingDetails && (
            <div>
              <span style={{ fontWeight: '600' }}>Other Packaging Details:</span>
              <span>{record.packaging.otherPackagingDetails}</span>
            </div>
          )}
        </>
      ),
    }] : []),
    // Conditionally show Other Packaging Details column
    ...(orderBookEntry?.products?.some(product => product?.packaging?.type === 'Others' && product?.packaging?.otherPackagingDetails) ? [{
      key: 'otherPackagingDetails',
      title: 'Other Packaging Details',
      dataIndex: 'packaging',
      width: 200,
      render: (_, record) => (
        record?.packaging?.type === 'Others' ? record?.packaging?.otherPackagingDetails : '-'
      ),
    }] : []),
    // Conditionally show Units column
    ...(orderBookEntry?.products?.some(product => product?.units) ? [{
      key: 'units',
      title: 'Total number of packaging units',
      dataIndex: 'units',
      width: 150,
    }] : []),
    // Conditionally show Mode of Export column (CUSTOMER_ORDER only)
    ...(orderBookEntry?.products?.some(product => product?.modeOfExport) ? [{
      key: 'modeOfExport',
      title: 'Mode of Export',
      dataIndex: 'modeOfExport',
      width: 150,
    }] : []),
    // Conditionally show Testing Flag column
    ...(orderBookEntry?.products?.some(product => product?.testingRequired !== undefined) ? [{
      key: 'testingFlag',
      title: 'Testing Flag',
      dataIndex: 'testingRequired',
      width: 200,
      render: (_, record) => (
        <>
          <div>
            <span style={{ fontWeight: '600' }}>Testing Required:</span>
            <span>{record?.testingRequired ? 'Yes' : 'No'}</span>
          </div>
          {record?.testingRequired && (
            <div>
              <span style={{ fontWeight: '600' }}>Dispatch With Results:</span>
              <span>{record?.dispatchWithResults ? 'Yes' : 'No'}</span>
            </div>
          )}
        </>
      ),
    }] : []),

  ];

  return (
    <div className='flex flex-col gap-5'>
      <Descriptions
        title={<div className='text-lg font-semibold'>Order Details</div>}
        items={orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
      />
      <div className='text-lg font-semibold'>Product Detail</div>
      <Table
        columns={productFieldColumns}
        dataSource={orderBookEntry.products.map((product) => ({...product, key: product.id}))}
        pagination={false}
        scroll={{
          x: '100%',
        }}
      />
    </div>
  );
};

export default OrderReviewData;

OrderReviewData.propTypes = {
  orderBookEntry: PropTypes.object.isRequired,
  fetchAssignedQuantity: PropTypes.func,
};
