/* eslint-disable react/prop-types */
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { isUserLoggedIn, isUserPermitted } from '../../util/auth';
import { isDateExpired } from '../../util/dateUtils';
import { REFRESH_DATE_KEY_NAME, REFRESH_TOKEN_KEY_NAME } from '../../constants/localStorageConstants';
import PageLoader from '../Loaders/PageLoader';

const ProtectedRoute = props => {
  const { route } = props;
  const user = useSelector(state => state.user);

  //TODO:convert it to custom hook utility
  const isUserAuthenticated = useMemo(() => {
    if (!isUserLoggedIn(user)) {
      return false;
    }
    if (isDateExpired(localStorage.getItem(REFRESH_DATE_KEY_NAME))) {
      // refresh token expired
      return false;
    }
    return true;
  }, [user]);

  const getRouteElementAfterValidation = () => {
    let refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY_NAME);
    if (route.authReq && !isUserAuthenticated && refreshToken) {
      return <PageLoader />;
    }
    else if (route.authReq && !isUserAuthenticated) return <Navigate to="/login" />;

    if (route.allowedView && !isUserPermitted(user.permissions, route.allowedView))
      return <Navigate to="/not-authorized" replace />;
    return <route.component />;
  };
  
  return getRouteElementAfterValidation();
};

export default ProtectedRoute;
