import React from 'react';
import { useDispatch } from 'react-redux';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import InventoryListView from './InventoryListView';

const InventoryDashboard = () => {
  const dispatch = useDispatch();

  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };

  return (
    <>
      <InventoryListView
        collapseSideDrawerHandler={collapseSideDrawerHandler}
      />
    </>
  );
};

export default InventoryDashboard;
