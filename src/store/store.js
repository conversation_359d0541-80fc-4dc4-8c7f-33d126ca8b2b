import { configureStore } from '@reduxjs/toolkit';
import storage from 'redux-persist/lib/storage'; // Use localStorage for persistence
import { persistReducer, persistStore } from 'redux-persist';
import rootReducer from './reducer';
import EnvKeys from '../constants/EnviornmentKeys';

// Configuration for redux-persist
const persistConfig = {
  key: 'root', // Key for localStorage
  storage, // Storage engine (localStorage)
  whitelist: ['supplierReducer'], // Specify what to persist
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const initialStore = {
  user: {
    name: '',
    email: '',
    username: '',
    is_logged_in: false,
    permissions: [],
    userId: '',
  },
  collapseAsideBar: false,
  sideDrawerData: null,
  collapseSideDrawer: true,
  formView: {
    show: false,
    mode: null,
  },
  accountOwnerList: [],
  packagingList: [],
};

const store = configureStore({
  reducer: persistedReducer,
  devTools: EnvKeys.isDev,
  preloadedState: initialStore,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // ✅ Ignores non-serializable warnings
    }),
});

export const persistor = persistStore(store); // Persistor instance

export default store;
