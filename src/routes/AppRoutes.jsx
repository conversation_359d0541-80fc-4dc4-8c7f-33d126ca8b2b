import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import routes from '.';
import NotFoundPage from '../route-pages/ErrorNotFoundPage';
import UnauthorizedPage from '../route-pages/ErrorUnauthorizedRoute';
import ProtectedRoute from '../component/ProtectedRoute/ProtectedRoute';
import LoginPage from '../route-pages/LoginPage';
import LayoutSkeleton from '../component/LayoutComponents/LayoutSkeleton';
import PostHogTrackerOnPageView from '../component/PostHog/PostHogTrackerOnPageView';

const AppRoute = () => {
  return (
    <BrowserRouter>
    <PostHogTrackerOnPageView/>
      <Routes>
        <Route element={<LayoutSkeleton />}>
          {routes.map((route, index) => (
            <Route
              key={index}
              path={route.path}
              element={<ProtectedRoute route={route} />}
              exact={route.exact}
            />
          ))}
        </Route>
        <Route path="/login" element={<LoginPage />} exact />
        <Route path="/not-authorized" element={<UnauthorizedPage />} exact />
        <Route path="/not-found" element={<NotFoundPage />} exact />
        <Route path="/" element={<Navigate replace to="/login" />} exact />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default AppRoute;
