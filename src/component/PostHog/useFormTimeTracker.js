import { useEffect, useRef } from "react";
import { usePostHog } from "posthog-js/react";
import { useSelector } from "react-redux";

const useFormTimeTracker = (formName = "Unnamed Form") => {
  const posthog = usePostHog();
  const user = useSelector((state) => state.user);
  const formStartTimeRef = useRef(null);
  const hasEnded = useRef(false);
  const hasStarted = useRef(false);

  const startTracking = () => {
    if (!hasStarted.current) {
      formStartTimeRef.current = Date.now();
      hasStarted.current = true;
      console.log(`[Form Tracker] Started tracking for "${formName}" at ${new Date().toLocaleTimeString()}`);
    }
  };

  const endTracking = (submitted = true,selectedOption = "",additionalInfo = {}) => {
    if (hasStarted.current && user?.id) {
      const durationMs = Date.now() - formStartTimeRef.current;
      const durationMinutes = Math.round((durationMs / (1000 * 60)) * 100) / 100;
      
      posthog.capture("form_filled", {
        form_name: formName,
        time_taken_minutes: durationMinutes,
        submitted,
        email: user.username,
        formStartTimeRef:formStartTimeRef.current,
        order_type : selectedOption,
        ...additionalInfo
      });

      console.log(`[Form Tracker] "${formName}" completed. Time: ${durationMinutes} minutes`);
      hasEnded.current = true;
      hasStarted.current = false;
    }
  };

  // Cleanup on unmount (abandoned form)
  useEffect(() => {
    return () => {
      if (hasStarted.current && user?.id) {
        const durationMs = Date.now() - formStartTimeRef.current;
        const durationMinutes = Math.round(durationMs / (1000 * 60));

        posthog.capture("form_filled", {
          form_name: formName,
          time_taken_minutes: durationMinutes,
          submitted: false,
          email: user.username,
          formStartTimeRef : formStartTimeRef.current,
          order_type: ""
        });

        console.log(`[Form Tracker] "${formName}" abandoned after ${durationMinutes} minutes`);
      }
    };
  }, [formName, posthog, user]);

  return { startTracking, endTracking };
};

export default useFormTimeTracker;
