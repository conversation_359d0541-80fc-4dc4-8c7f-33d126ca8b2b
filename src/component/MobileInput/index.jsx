import React from 'react';
import { Form, Input, Select, Space } from 'antd';
import { countryList } from '../../constants/formConstant';
import PropTypes from 'prop-types';

export default function MobileInput({
  countryFormName = 'countryCode',
  mobileFormName = 'mobile',
  isRequired = false,
}) {
  const prefixSelector = (
    <Form.Item name={countryFormName} noStyle>
      <Select
        style={{
          width: 100,
        }}
        popupMatchSelectWidth={false}
        optionLabelProp="label"
        showSearch
        optionFilterProp="country"
      >
        {countryList.map((country, index) => (
          <Select.Option
            key={index}
            value={country.dial_code}
            label={`+${country.dial_code}`}
            country={country.name}
          >
            <Space>
              <span role="img" aria-label={country.dial_code}>
                {country.flag}
              </span>
              {`+${country.dial_code} ${country.name}`}
            </Space>
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );
  return (
    <Form.Item
      label="Phone Number"
      name={mobileFormName}
      rules={[{ required: isRequired, message: 'please enter a valid mobile number ' }]}
    >
      <Input addonBefore={prefixSelector} type="tel" />
    </Form.Item>
  );
}

MobileInput.propTypes = {
  countryFormName: PropTypes.string,
  mobileFormName: PropTypes.string,
  isRequired: PropTypes.bool,
};
