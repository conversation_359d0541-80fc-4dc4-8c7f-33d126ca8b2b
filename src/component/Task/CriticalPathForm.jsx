import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, Input, DatePicker, Button, Card } from 'antd';
import PropTypes from 'prop-types';
import { generateCriticalPath, updateCriticalPath } from '../../service/api/activityApi';
import { DateFormat } from '../../constants/formConstant';

const CriticalPathProductForm = props => {
  // eslint-disable-next-line react/prop-types
  const { formFields, index } = props;
  return (
    <Card size="small" title={`Product ${index + 1}`}>
      {/* eslint-disable-next-line react/prop-types */}
      {formFields.map(field => (
        <Form.Item
          key={field.name}
          label={field.label}
          name={['products', index, field.name]}
          rules={[{ required: true }]}
          initialValue={field.value}
          hidden={field.hidden}
        >
          {field.type === 'number' ? <InputNumber /> : <Input disabled={field.disabled} />}
        </Form.Item>
      ))}
    </Card>
  );
};

const CriticalPathFormModal = ({ visible, onCancel, products, entityId, onSumbit, updateMode }) => {
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const handleFormSubmit = () => {
    // console.log(form.getFieldValue());
    form.validateFields().then(values => {
      setLoading(true);
      if (updateMode) {
        updateCriticalPath('CUSTOMER_ORDER', entityId, 'DefaultCriticalPath', values)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        generateCriticalPath('CUSTOMER_ORDER', entityId, 'DefaultCriticalPath', values)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    });
  };
  const cancelModal = () => {
    if (loading) return;
    form.resetFields();
    onCancel();
  };

  const formFields = products.map(prod => {
    return [
      { name: 'id', label: 'id', value: prod.id, hidden: true },
      { name: 'productName', label: 'Product Name', value: prod.product.tradeName, disabled: true },
      { name: 'productId', label: 'Product Id', value: prod.product.id, hidden: true },
      { name: 'testingTime', label: 'Testing Time', type: 'number' },
      { name: 'intransitRoadDays', label: 'Intransit Road Days', type: 'number' },
    ];
  });

  const initialValue = {
    intransitDaysSea: '',
    deliveryDate: '',
    // TODO SHOULD SHIPMENT DATE BE ALSO PRESENT
    documentDispatch: '',
    additionalTimeAtCFS: '',
    stuffingToSailDays: '',
    products: [
      ...products.map(prod => {
        return {
          productName: prod.product.tradeName,
          productId: prod.product.id,
          testingTime: '',
          intransitRoadDays: '',
        };
      }),
    ],
  };

  useEffect(() => {
    form.setFieldValue(initialValue);
  }, []);

  return (
    <Modal
      open={visible}
      title={updateMode ? 'Update Critical Path' : 'Generate Critical Path'}
      onCancel={cancelModal}
      footer={[
        <Button key="cancel" onClick={cancelModal}>
          Cancel
        </Button>,
        <Button loading={loading} key="submit" type="primary" onClick={handleFormSubmit}>
          Submit
        </Button>,
      ]}
      styles={{
        body: {
          maxHeight: '50vh',
          overflow: 'scroll',
        },
      }}
    >
      <Form preserve={false} form={form}>
        <Form.Item
          label="Intransit Days (Sea)"
          name="intransitDaysSea"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>

        <Form.Item label="Delivery Date" name="deliveryDate" rules={[{ required: true }]}>
          <DatePicker format={DateFormat} />
        </Form.Item>

        <Form.Item label="Document Dispatch" name="documentDispatch" rules={[{ required: true }]}>
          <InputNumber />
        </Form.Item>

        <Form.Item
          label="Additional Time at CFS"
          name="additionalTimeAtCFS"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>

        <Form.Item
          label="Stuffing to Sail Days"
          name="stuffingToSailDays"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>
        {formFields.map((fields, index) => (
          <CriticalPathProductForm key={index} formFields={fields} index={index} />
        ))}
      </Form>
    </Modal>
  );
};

CriticalPathFormModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  products: PropTypes.array.isRequired,
  onCancel: PropTypes.func.isRequired,
  entityId: PropTypes.string.isRequired,
  onSumbit: PropTypes.func.isRequired,
  updateMode: PropTypes.bool.isRequired,
};

export default CriticalPathFormModal;
