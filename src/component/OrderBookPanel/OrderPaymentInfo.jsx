import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Col, Form, Input, InputNumber, Row, Select,DatePicker } from 'antd';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import { formModes, paymentTermsDate,DateFormat } from '../../constants/formConstant';
import { CalendarOutlined } from '@ant-design/icons';

const OrderPaymentInfo = props => {
  const { form, initialValue, formView } = props;

  const getFormPrefilldValues = () =>
    initialValue && initialValue.paymentTerms
      ? {
          paymentTerms: { ...initialValue.paymentTerms },
          remarks: initialValue.remarks,
        }
      : {};

  useEffect(() => {
    if (initialValue && initialValue.paymentTerms) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);

  return (
    <Form
      name="order_payment_info"
      layout="vertical"
      scrollToFirstError
      initialValues={formView === formModes.CREATE ? {} : initialValue}
      size="middle"
      form={form}
      preserve={false}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="billTo" label="Billing Addres">
            <Input.TextArea style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'poPaymentTerms']}
            label="PO Payment terms"
            rules={
              initialValue?.inventoryId
                ? []
                : [
                    {
                      required: true,
                      message: 'PO payment terms cannot be empty',
                    },
                  ]
            }
          >
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditAmount']}
            label="Credit amount(in %)"
            rules={
              initialValue?.inventoryId
                ? []
                : [
                    {
                      required: true,
                      message: 'Credit amount can only be a valid number!',
                      type: 'number',
                    },
                    () => ({
                      validator(_, value) {
                        if (value !== undefined && value > 100) {
                          return Promise.reject(new Error(`credit can't be greater then 100!`));
                        } else if (value !== undefined && value <= 100) return Promise.resolve();

                        return Promise.reject(new Error('Unable to validate!'));
                      },
                    }),
                  ]
            }
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditorDays']}
            label="Credit days"
            rules={
              initialValue?.inventoryId
                ? []
                : [
                    {
                      required: true,
                      message: 'Credit days can only be a valid number!',
                      type: 'number',
                    },
                  ]
            }
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'startDate']}
            label="Payment terms should be based on?"
            rules={
              initialValue?.inventoryId
                ? []
                : [
                    {
                      required: true,
                      message: 'Please input start date!',
                    },
                  ]
            }
          >
            <Select>
              {paymentTermsDate.map(date => (
                <Select.Option key={date.key} value={date.value}>
                  {date.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="remarks" label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              savetimeStamp
              showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default OrderPaymentInfo;

OrderPaymentInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
};
