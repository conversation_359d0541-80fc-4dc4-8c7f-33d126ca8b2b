import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import DocumentList from './DocumentList';
import DocumentPreview from './DocumentPreview';
import DocumentComments from './DocumentComments';
import { <PERSON><PERSON>, Col, Dropdown, Row, Modal } from 'antd';
import PageLoader from '../Loaders/PageLoader';
import {
  addCommentToFile,
  approveAndSend,
  approveDocument,
  getDraftFileData,
  removeCommentFromFile,
  resetFileComments,
  sendForReview,
  sendForRevision,
  updateCommentFromFile,
} from '../../service/api/documentApi';
import {
  DownOutlined,
  SyncOutlined,
  DownloadOutlined,
  SendOutlined,
  CheckCircleFilled,
} from '@ant-design/icons';
import upload from '../../assets/icons/arrow_upload.svg';
import generateDoc from '../../assets/icons/generation_logo.svg';
// import docApprove from '../../assets/icons/doc_approval.svg';
// import { canBeGenerated } from '../../util/userUtils';
import { disabledInvoiceTypesForUpload, docStatus } from '../../constants/formConstant';
import { useNotificationContext } from '../../provider/NotificationProvider';

const DocumentViewer = props => {
  const {
    orderId,
    document,
    hide,
    previewOnly,
    onFileUpload,
    handleDocGeneration,
    onDocumentApprove,
    onDownload,
    height,
    sendDocToCustomer,
    canBeGeneratedByUser,
    canApprove,
    canSendForReview,
    canSendToCustomer,
    canRevert,
    fetchData,
    canGenerateInvoiceNumber,
    generateInvoiceNumber
  } = props;

  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [draftFile, setDraftFile] = useState(null);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const { api } = useNotificationContext();
  const { confirm } = Modal;

  const getReviewConfirmationModal = (title, content, okFun, cancelFunc) => {
    return confirm({
      title,
      content,
      onOk() {
        okFun();
      },
      onCancel() {
        cancelFunc();
      },
    });
  };

  const fetchDraftFileVersions = () => {
    // console.log('document ',document)
    if (draftFile && selectedDocument) return;
    setLoading(true);
    getDraftFileData(orderId)
      .then(res => {
        if (res.data) {
          setDraftFile(res.data?.[document.label] || []);
          setSelectedDocument(res.data?.[document.label]?.[0]);
          setLoading(false);
          // console.log('selected doucment ',res.data?.[document.label]?.[0])
        }
      })
      .catch(err => {
        setLoading(false);
        console.log(err);
      });
  };

  const onApproveClick = () => {
    setLoading(true)
    approveDocument(orderId, selectedDocument.docType, selectedDocument.fileId)
      .then(() => {
        hide();
        setLoading(false)
        onDocumentApprove(selectedDocument);
      })
      .catch(err => {
        setLoading(false)
        console.log(err);
        onDocumentApprove(false, err);
      });
  };

  useEffect(() => {
    if (document?.fileId && !previewOnly) {
      fetchDraftFileVersions();
    }
    if (previewOnly) {
      setDraftFile([document]);
      setSelectedDocument(document);
    }
  }, [document?.fileId]);

  useEffect(() => {}, [comments]);

  const handleReview = (doc, meta) => {
    setLoading(true)
    if (meta?.case == 'SEND_FOR_REVIEW') {
      sendForReview(doc?.id)
        .then(() => {
          return fetchData(); // Return the fetchData promise
        })
        .then(() => {
          hide(); // Hide after fetchData completes
        })
        .then(() => {
          api.open({
            key: 'Sent For Review',
            message: 'Document sent for review',
            duration: 3,
            icon: (
              <CheckCircleFilled
                style={{
                  color: '#107C10',
                }}
              />
            ),
          });
          setLoading(false)
        })
        .catch(err => {
          setLoading(false)
          console.error(err); // Log any error that occurs in the chain
        });
    } else if (meta?.case == 'APPROVE_AND_SEND') {
      approveAndSend(doc?.id)
        .then(() => {
          return fetchData(); // Return the fetchData promise
        })
        .then(() => {
          hide(); // Hide after fetchData completes
        })
        .then(() => {
          api.open({
            key: 'Sent to customer',
            message: 'Document apporved and sent to customer ',
            duration: 3,
            icon: (
              <CheckCircleFilled
                style={{
                  color: '#107C10',
                }}
              />
            ),
          });
          setLoading(false)
        })
        .catch(err => {
          setLoading(false)
          console.error(err); // Log any error that occurs in the chain
        });
    } else if (meta?.case == 'SEND_FOR_REVISION') {
      sendForRevision(doc?.id)
        .then(() => {
          return fetchData(); // Return the fetchData promise
        })
        .then(() => {
          hide(); // Hide after fetchData completes
        })
        .then(() => {
          api.open({
            key: 'Sent For Revision',
            message: 'Document sent for revision',
            duration: 3,
            icon: (
              <CheckCircleFilled
                style={{
                  color: '#107C10',
                }}
              />
            ),
          });
          setLoading(false)
        })
        .catch(err => {
          setLoading(false)
          console.error(err); // Log any error that occurs in the chain
        });
    }
  };

  const handleInvoiceGeneration=()=>{
    setLoading(true)
    generateInvoiceNumber() .then(() => {
      return fetchData(); // Return the fetchData promise
    })
    .then(() => {
      hide(); // Hide after fetchData completes
    })
    .then(() => {
      api.open({
        key: 'Invoice Number generated',
        message: 'Invoice number generated sucessfully',
        duration: 3,
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ),
      });
      setLoading(false)
    })
    .catch(err => {
      setLoading(false)
      console.error(' failed to generate invoice ',err); // Log any error that occurs in the chain
    });
  }

  const getUpdateDocOptions = doc => {
    let options = [];
    // upload option
    if (!disabledInvoiceTypesForUpload.includes(doc?.docType)) {
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            icon={<img src={upload} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Upload
            <input
              type="file"
              accept="application/pdf, image/*"
              className="absolute z-[2] opacity-0"
              onChange={e => {
                hide();
                setTimeout(() => {
                  onFileUpload(e, selectedDocument);
                }, 500);
              }}
            />
          </Button>
        ),
        key: options.length,
      });
    }
    // generate option
    if (canBeGeneratedByUser(selectedDocument?.docType)) {
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            onClick={() => {
              hide();
              handleDocGeneration(selectedDocument);
            }}
            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Re-Generate
          </Button>
        ),
        key: options.length + 1,
      });
    }
    // Send For Review
    if (selectedDocument?.status == docStatus.PENDING_FOR_REVIEW && canSendForReview()) {
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            onClick={() => {
              getReviewConfirmationModal(
                'Send For Review ',
                'Are you sure to send for review ? ',
                () => {
                  handleReview(selectedDocument, { case: 'SEND_FOR_REVIEW' });
                },
                () => {
                  console.log('cancelled');
                }
              );
            }}
            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Send For Review
          </Button>
        ),
        key: options.length + 1,
      });
    }
    // Approve and Send
    if (selectedDocument?.status == docStatus.PENDING_FOR_APPROVAL && canApprove()) {
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            onClick={() => {
              getReviewConfirmationModal(
                'Approve document',
                'Are you sure you want to approve document ? ',
                () => {
                  onApproveClick();
                },
                () => {
                  console.log('cancelled');
                }
              );
            }}
            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Approve
          </Button>
        ),
        key: options.length + 1,
      });
      if (canSendToCustomer()) {
        options.push({
          label: (
            <Button
              type="link"
              className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
              onClick={() => {
                getReviewConfirmationModal(
                  'Approve & Send document',
                  'Are you sure you want to approve & send document to customer ? ',
                  () => {
                    handleReview(selectedDocument, { case: 'APPROVE_AND_SEND' });
                  },
                  () => {
                    console.log('cancelled');
                  }
                );
              }}
              icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Approve & Send
            </Button>
          ),
          key: options.length + 1,
        });
      }
    }
    // Send For Revision
    if (selectedDocument?.status == docStatus.PENDING_FOR_APPROVAL && canRevert()) {
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            onClick={() => {
              getReviewConfirmationModal(
                'Send for revision',
                'Are you sure you want to send to document for revision ',
                () => {
                  handleReview(selectedDocument, { case: 'SEND_FOR_REVISION' });
                },
                () => {
                  console.log('cancelled');
                }
              );
            }}
            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Revert to L1
          </Button>
        ),
        key: options.length + 1,
      });
    }
    // console.log('can generate invoice ',canGenerateInvoiceNumber())
    if(canGenerateInvoiceNumber()){
      options.push({
        label: (
          <Button
            type="link"
            className="p-0 relative flex items-center rounded bg-base-white text-text-black hover:bg-none"
            onClick={() => {
              getReviewConfirmationModal(
                'Generate Invoice Number',
                'Are you sure generate invoice number? ',
                () => {
                  handleInvoiceGeneration()
                },
                () => {
                  console.log('cancelled invoice number generation');
                }
              );
            }}
            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Generate invoice number
          </Button>
        ),
        key: options.length + 1,
      });
    }
    // console.log(options)
    return options;
  };

  const handleAddComment = comment => {
    setLoading(true);
    addCommentToFile(selectedDocument.fileId, comment)
      .then(res => {
        if (res && res.id && res.comment) {
          setComments(prevComments => {
            let newComments = [...prevComments];
            newComments.unshift({
              ...res,
            });
            return newComments;
          });
        }
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleRemoveComments = index => {
    setLoading(true);
    removeCommentFromFile(comments[index].id)
      .then(() => {
        setComments(prevComments => {
          let newComments = [...prevComments];
          newComments.splice(index, 1);
          return newComments;
        });
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleUpdateComment = (highlightId, position, content, comment) => {
    setLoading(true);
    const updatedData = {
      position,
      content,
      comment,
    };
    updateCommentFromFile(highlightId, updatedData)
      .then(res => {
        setComments(prevComments => {
          const newCommentList = prevComments.map(commentobj => {
            return commentobj.id === highlightId
              ? {
                  ...res,
                }
              : commentobj;
          });
          return newCommentList;
        });
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const resetComments = () => {
    setLoading(true);
    resetFileComments(selectedDocument.fileId)
      .then(() => {
        setComments([]);
      })
      .catch(err => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // const setNewComments = (comments) => {
  //   setComments(comments);
  // }

  return (
    <>
      {loading || !draftFile ? <PageLoader /> : null}
      <div className={`relative ${height || 'h-[80vh]'}`}>
        <div className="mb-2.5 flex items-center justify-end gap-2">
          {/* {selectedDocument?.id === draftFile?.[0]?.id ? ( */}
          <Button
            disabled
            loading={loading}
            className="rounded-md flex"
            type="primary"
            onClick={() => {
              getReviewConfirmationModal(
                'Send to customer',
                'Are you sure you want to send document to customer ? ',
                () => {
                  sendDocToCustomer(selectedDocument?.id);
                },
                () => {
                  console.log('cancelled');
                }
              );
            }}
            icon={<SendOutlined style={{ marginTop: '4px' }} />}
          >
            Send to customer
          </Button>
          {/* ): null} */}
          {/* {console.log(updateDocOptions)} */}
          {!previewOnly &&
          !selectedDocument?.approved &&
          selectedDocument?.id === draftFile?.[0]?.id ? (
            <Dropdown
              trigger={['click']}
              menu={{ items: [...getUpdateDocOptions(selectedDocument)] }}
            >
              <Button
                loading={loading}
                className="rounded-md"
                type="primary"
                icon={<SyncOutlined style={{ color: '#fff' }} />}
              >
                Update Document
                <DownOutlined style={{ color: '#fff', fontSize: '10px' }} />
              </Button>
            </Dropdown>
          ) : null}
          {previewOnly && onDownload ? (
            <Button
              loading={loading}
              className="rounded-md"
              type="primary"
              icon={<DownloadOutlined style={{ color: '#fff' }} />}
              onClick={() => onDownload(document)}
            >
              Download
            </Button>
          ) : null}
          <Button
            className="bg-base-white text-black border-solid border-black rounded-md"
            type="default"
            onClick={() => hide()}
          >
            Cancel
          </Button>
        </div>
        <Row gutter={0} className="h-[calc(80vh-50px)]">
          <Col span={6}>
            <DocumentList
              document={draftFile}
              selectedDocument={selectedDocument}
              setSelectedDocument={setSelectedDocument}
            />
          </Col>
          <Col
            span={previewOnly ? 18 : 12}
            className=" relative border border-solid border-border-color"
          >
            {selectedDocument && selectedDocument?.fileId ? (
              <DocumentPreview
                preivewDocument={selectedDocument}
                comments={comments}
                handleAddComment={handleAddComment}
                handleUpdateComment={handleUpdateComment}
                // mode={mode}
                setLoading={setLoading}
              />
            ) : null}
          </Col>
          {previewOnly ? null : (
            <Col span={6}>
              {selectedDocument && selectedDocument?.fileId ? (
                <DocumentComments
                  orderId={orderId}
                  preivewDocument={selectedDocument}
                  comments={comments}
                  handleRemoveComments={handleRemoveComments}
                  resetComments={resetComments}
                  handleUpdateComment={handleUpdateComment}
                  setComments={setComments}
                  latestVersion={draftFile && draftFile.length ? draftFile[0] : null}
                />
              ) : null}
            </Col>
          )}
        </Row>
      </div>
    </>
  );
};

export default DocumentViewer;

DocumentViewer.propTypes = {
  orderId: PropTypes.string,
  document: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
  previewOnly: PropTypes.bool,
  onFileUpload: PropTypes.func,
  handleDocGeneration: PropTypes.func,
  onDocumentApprove: PropTypes.func,
  onDownload: PropTypes.func,
  height: PropTypes.string,
  sendDocToCustomer: PropTypes.func,
  canBeGeneratedByUser: PropTypes.func,
  canApprove: PropTypes.func,
  canSendForReview: PropTypes.func,
  canSendToCustomer: PropTypes.func,
  canRevert: PropTypes.func,
  fetchData: PropTypes.func,
  canGenerateInvoiceNumber: PropTypes.func,
  generateInvoiceNumber: PropTypes.func
};
