import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import { getOrderByPOId } from '../../service/api/orderBookApi';
import RouteFactory from '../../service/RouteFactory';

const OrderLink = ({ orderId, setLoading, poNumber }) => {
  const navigate = useNavigate();

  const handleOrderView = async (event) => {
    setLoading(true);
    try {
      const orderResponse = await getOrderByPOId(orderId, 'order-book', 'orderBookId');
      
      const id = orderResponse?.data?.content[0]?.id;

      if (id) {
        const path = new RouteFactory().dashboard().orderBook().setId(id).view().build();
        const url = `${window.location.origin}${path}`; // Construct the full URL for the orderBook view

        // Open the URL in a new tab
        window.open(url, '_blank');
      } else {
        console.error('Order not found for orderId:', orderId);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching order:', error);
      setLoading(false);
    }
  };

  return <Typography.Link onClick={handleOrderView} style={{ cursor: 'pointer' }}>{poNumber}</Typography.Link>;
};

OrderLink.propTypes = {
  orderId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  setLoading: PropTypes.func.isRequired,
  poNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default OrderLink;
