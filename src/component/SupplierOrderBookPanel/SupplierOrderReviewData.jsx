import React from 'react';
import PropTypes from 'prop-types';
import SupplierOrderProductsInfo from './SupplierOrderProductsInfo';
import SupplierOrderPaymentInfo from './SupplierOrderPaymentInfo';
import { formModes } from '../../constants/formConstant';
import SupplierOrderBookBasicInfo from './SupplierOrderBookBasicInfo';

const SupplierOrderReviewData = props => {
  const { basicInfoForm, productsForm, paymentInfoForm, initialValue } = props;

  return (
    <>
      <SupplierOrderBookBasicInfo
        form={basicInfoForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }} //TODO: improve mode handling
      />
      <SupplierOrderProductsInfo
        form={productsForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }}
      />
      <SupplierOrderPaymentInfo
        form={paymentInfoForm}
        initialValue={initialValue}
        formView={{ mode: formModes.READ }}
      />
    </>
  );
};

export default SupplierOrderReviewData;

SupplierOrderReviewData.propTypes = {
  basicInfoForm: PropTypes.object.isRequired,
  productsForm: PropTypes.object.isRequired,
  paymentInfoForm: PropTypes.object.isRequired,
  initialValue: PropTypes.object.isRequired,
  formView: PropTypes.object.isRequired,
};
