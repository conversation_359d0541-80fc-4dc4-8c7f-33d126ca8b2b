/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Button, Space, message } from 'antd';
import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import { useDispatch, useSelector } from 'react-redux';
import { paymentTermsDate } from '../../constants/formConstant';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { getEnumValueFromList } from '../../util/formUtil';
import { Link } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { deleteSupplier } from '../../service/api/supplierService';

const SupplierSideBar = props => {
  const { supplier, hide } = props;
  const productDetail = supplier?.products;

  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const [messageApi, contextHolder] = message.useMessage();

  const openFormView = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };
  const hideSideDrawer = () => {
    hide();
  };

  const handleDelete = id => {
    deleteSupplier(id)
      .then(() => {
        console.log('sucessfully deleted');
        window.location.replace(new RouteFactory().dashboard().supplier().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        messageApi.error(errMsg);
      });
  };

  return supplier && productDetail ? (
    <div className={css.customerSider}>
      {contextHolder}
      <div className={css.titleBar}>
        <div className={css.title}>{supplier?.supplierId}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().supplier().setId(supplier.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => openFormView()}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        {hasPermission(userPermissionsList.updateSupplier, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().supplier().setId(supplier.id).edit().build()}>
            <Button
              onClick={() => openFormView()}
              style={{}}
              type="primary"
              icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Edit
            </Button>
          </Link>
        ) : null}
        {hasPermission(userPermissionsList.deleteSupplier, user.permissions) ? (
          <Button onClick={() => handleDelete(supplier.id)}>Delete</Button>
        ) : null}
      </div>
      <Space direction="vertical" size="large">
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Supplier Name
          </div>
          <div className={css.value}>{supplier?.name}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Mobile Number
          </div>
          <div className={css.value}>{supplier?.mobile}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Email
          </div>
          <div className={css.value}>{supplier?.email}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Revenue (in Million $)
          </div>
          <div className={css.value}>{supplier?.revenue}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Address
          </div>
          <div className={css.value}>
            {supplier?.address &&
              `${supplier.address?.street} ${supplier.address?.city}, ${supplier.address?.state} ${supplier.address?.country} - ${supplier.address?.postalCode}`}
          </div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Created
          </div>
          <div className={css.value}>{getDateFromTimeStamp(supplier?.createdAt)}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ fontSize: '18px', color: 'rgba(35, 86, 138, 0.70)' }}>
            Payment Terms
          </div>
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Credit Amount(in %)
          </div>
          <div className={css.value}>{supplier?.paymentTerms?.creditAmount}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Creditor Days
          </div>
          <div className={css.value}>{supplier?.paymentTerms?.creditorDays}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Payment terms based on
          </div>
          <div className={css.value}>
            {getEnumValueFromList(supplier?.paymentTerms?.startDate, paymentTermsDate)}
          </div>
        </Space.Compact>
        {supplier.products?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Products
            </div>
            <div className={css.value}>
              {supplier.products.map((product, i) => (
                <div key={i} style={{ marginBottom: '12px' }}>
                  {product?.product?.tradeName}
                </div>
              ))}
            </div>
          </Space.Compact>
        )}
      </Space>
    </div>
  ) : null;
};

export default SupplierSideBar;

SupplierSideBar.proptypes = {
  order: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
  productName: PropTypes.string.isRequired,
};
