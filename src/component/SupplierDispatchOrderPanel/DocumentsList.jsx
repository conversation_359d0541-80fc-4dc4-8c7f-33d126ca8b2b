import { Button, Modal, List, Tag, message } from 'antd';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { 
  DownloadOutlined, 
  EyeOutlined 
} from '@ant-design/icons';
import { downloadFile } from '../../service/api/storageService';
// import { getEmployeeList } from '../../service/api/employee';
// import {  getEmployeesNameFromId } from '../../util/userUtils';


const styles = {
  documentItem: {
    marginBottom: '8px',
    padding: '12px',
    border: '1px solid #f0f0f0',
    borderRadius: '4px',
    backgroundColor: '#fafafa',
  },
  documentType: {
    fontSize: '16px',
    fontWeight: '500',
    color: '#1890ff',
    marginBottom: '8px',
  },
  fileInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '4px',
  },
  label: {
    fontWeight: '500',
    marginRight: '8px',
  },
};

const DocumentsList = ({ documents }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [api, contextHolder] = message.useMessage();

  const getFileUrl = async (file) => {
    try {
      api.loading({
        key: 'fileAction',
        content: 'Processing...'
      });
      
      const apiRes = await downloadFile(file.fileId);
      api.success({
        key: 'fileAction',
        content: 'Success'
      });
      return apiRes.data.url;
    } catch (error) {
      console.error('Error while getting file URL:', error);
      api.error({
        key: 'fileAction',
        content: 'Failed to process file'
      });
      throw error;
    }
  };

  const onDownload = async file => {
    try {
      const url = await getFileUrl(file);
      window.open(url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    }
  };

  const handleDocPreview = async (file) => {
    try {
      const url = await getFileUrl(file);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error while previewing file:', error);
    }
  };

  if (!documents || Object.keys(documents).length === 0) {
    return <span>No documents available</span>;
  }

  const totalDocs = Object.values(documents).reduce((sum, arr) => sum + arr.length, 0);

  return (
    <>
      {contextHolder}
      <Button type="link" onClick={() => setIsModalVisible(true)}>
        View Documents ({totalDocs})
      </Button>
      
      <Modal
        title="Documents"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <List
          dataSource={Object.entries(documents)}
          renderItem={([docType, files]) => (
            <List.Item>
              <div className="w-full">
                <h3 className="text-base font-medium mb-2">{docType}</h3>
                <div className="pl-4">
                  {files.map((file, index) => (
                    <div 
                      key={file.uid || index} 
                      className="mb-3 p-3 bg-gray-50 rounded-md flex justify-between items-center"
                    >
                      <div className="flex-grow">
                        <div className="font-medium">{file.name}</div>
                        {file.remark && (
                          <div className="text-gray-500 text-sm mt-1">
                            Remark: {file.remark}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-3">
                        {file.isApproved !== null && (
                          <Tag color={file.isApproved ? 'green' : 'red'}>
                            {file.isApproved ? 'Approved' : 'Not Approved'}
                          </Tag>
                        )}
                        {/* <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleDocPreview(file)}
                          title="Preview"
                        /> */}
                        <Button
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={() => onDownload(file)}
                          title="Download"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </List.Item>
          )}
        />
      </Modal>
    </>
  );
};

DocumentsList.propTypes = {
  documents: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.shape({
    uid: PropTypes.string,
    name: PropTypes.string.isRequired,
    isApproved: PropTypes.bool,
    remark: PropTypes.string
  }))).isRequired
};


export default DocumentsList;