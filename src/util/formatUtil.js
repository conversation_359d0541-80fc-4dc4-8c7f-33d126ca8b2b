export function getSimilarKeyCount(array, key) {
  const checkMap = {};
  const result = [];

  array.forEach((obj) => {
    const keyValue = obj[key];

    // Count occurrences of each key
    if (!checkMap[keyValue]) {
      checkMap[keyValue] = 1;
      obj.count = array.filter((item) => item[key] === keyValue).length;
    }

    result.push(obj);
  });
  return result;
}

export const enumKeyToTitle = (input) => {
  // Check if the input string is not empty
  if (input.length === 0) {
    return '';
  }

  // Split the input string into words using underscores as delimiters
  const words = input.split('_');

  // Capitalize the first letter of each word and convert the rest to lowercase
  const formattedWords = words.map((word) =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  );

  // Join the formatted words with spaces to create the final string
  const formattedString = formattedWords.join(' ');

  return formattedString;
}


export function saveByteArray(reportName, byte) {
  const data = new Blob([byte], {type: "application/zip"});
  const blobUrl = window.URL.createObjectURL(data);
  const link = document.createElement('a');
  link.href = blobUrl;
  link.download =  reportName; // Set the desired file name
  document.body.appendChild(link);
  link.click();

  // Clean up resources
  window.URL.revokeObjectURL(blobUrl);
  document.body.removeChild(link);
}

export function roundToTwoDecimalPlaces(number) {
  return Math.ceil(number * 100) / 100;
}