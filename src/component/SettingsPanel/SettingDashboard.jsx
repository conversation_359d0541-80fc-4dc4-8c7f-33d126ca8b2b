import { Tabs } from 'antd'
import React, { useState } from 'react'
import EmployeePanel from './EmployeePanel'
import PackagingPanel from './PackagingPanel'
import CustomerPanel from './CustomerPanel'
import { RightOutlined } from '@ant-design/icons'
import MiscPanel from './MiscPanel'

const SettingDashboard = () => {

  const [activeTab, setActiveTab] = useState('1');

  const tabPanels = [
    {
      key: '1',
      label: 'Employee',
      // children: <UserPanel />
    },
    {
      key: '2',
      label: 'Packaging',
      // children: <PackagingPanel />
    },
    {
      key: '3',
      label: 'Customer',
      // children: <CustomerPanel />
    },
    {
      key: '4',
      label: 'Misc',
      // children: <CustomerPanel />
    }

  ];

  const getActiveTabLabel = () => {
    const tab = tabPanels.find((tab) => tab.key === activeTab);
    if (tab?.label) return tab?.label;
    return '';
  }
  const getActiveTabContent = () => {
    switch(activeTab) {
      case '1': return <EmployeePanel />;
      case '2': return <PackagingPanel />
      case '3': return <CustomerPanel />
      case '4': return <MiscPanel />
      default: return;
    }
  }

  return (
    <div className='h-full'>
      <div className='sticky top-0 z-10 bg-base-white px-5 flex items-center gap-5' style={{ borderBottom: '1px solid rgba(30, 30, 30, 0.1)' }}>
        <div className='text-xl font-semibold'>Settings</div>
        <div>
          <Tabs
            items={tabPanels}
            defaultActiveKey={'1'}
            activeKey={activeTab}
            onChange={(activeKey) => setActiveTab(activeKey)}
            type='line'
            destroyInactiveTabPane={true}
            tabBarStyle={{margin: '0'}}
          />
        </div>
      </div>
      <div className='p-4 flex items-center gap-2.5 text-[17px]'>
        <span>Settings</span>
        <span><RightOutlined /></span>
        <span className='font-medium'>{getActiveTabLabel()}</span>
      </div>
      <div className='h-[90%]'>
        {getActiveTabContent()}
      </div>
    </div>
  )
}

export default SettingDashboard