import React, { useState, useEffect } from 'react';
import { CalendarOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { downloadFile } from '../../service/api/storageService';
import { getOrderByPOId } from '../../service/api/orderBookApi';

const QCApprovalTable = ({ formData = {}, onDownload, record }) => {
  const [orderBookData, setOrderBookData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [mappedProducts, setMappedProducts] = useState([]);

  useEffect(() => {
    let isMounted = true;
    
    const fetchOrderBookData = async () => {
      if (record?.orderId) {
        setLoading(true);
        try {
          const response = await getOrderByPOId(record.orderId,"order-book","orderBookId");
          if (isMounted) {
            setOrderBookData(response.data.content[0]);
          }
        } catch (error) {
          if (isMounted) {
            console.error('Error fetching order book data:', error);
          }
        } finally {
          if (isMounted) {
            setLoading(false);
          }
        }
      }
    };

    fetchOrderBookData();

    return () => {
      isMounted = false;
    };
  }, [record?.orderId]);

  // Map products from formData with orderBookData
  useEffect(() => {
    console.log("formData", formData);
    console.log("orderBookData", orderBookData);
    if (formData?.products && orderBookData?.products) {
        console.log("formData", formData);
        console.log("orderBookData", orderBookData);
      const mapped = formData.products.map(formProduct => {
        // Find matching product in orderBookData
        const matchingOrderBookProduct = orderBookData.products.find(orderBookProduct => {
          // Match by product ID or product name
          return (
            orderBookProduct.product?.id === formProduct.product?.id
          );
        });

        return {
          formProduct,
          orderBookProduct: matchingOrderBookProduct || null,
          isMatched: !!matchingOrderBookProduct
        };
      });

      setMappedProducts(mapped);
      console.log('Mapped products:', mapped);
    }
  }, [formData?.products, orderBookData?.products]);

  const DocumentsData = [
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: [
        'SDS',
        'TDS',
        'COA',
        'invoice',
        'DGD',
        'COO',
        'Others',
      ],
    },
    {
      patch: true,
      path: 'batchNumber',
      inputType: 'text',
      label: 'Batch Number',
      mandatory: true,
      key: 'batchNumber',
    },
  ];

  const TestingSentData = [
    {
      patch: true,
      path: 'sentForTestingOn',
      inputType: 'date',
      label: 'Sent For Testing On',
      mandatory: true,
      key: 'sentForTestingOn',
    },
    {
      patch: true,
      path: 'sentForTestingBy',
      inputType: 'text',
      label: 'Sent For Testing By',
      mandatory: true,
      key: 'sentForTestingBy',
    },
  ];

  const TestingResultsData = [
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'labMoa',
    },
    {
        path: 'documents', // Specific groups only
        inputType: 'document',
        label: 'Documents',
        key: 'documents',
        group: [
          'labCOA'
        ],
      },
    {
      patch: true,
      path: 'labCoaReceivedBy',
      inputType: 'text',
      label: 'Lab Coa Received By',
      mandatory: true,
      key: 'labCoaReceivedBy',
    },
    {
      patch: true,
      path: 'testingDate',
      inputType: 'text',
      label: 'Testing Happened On',
      mandatory: true,
      key: 'testingDate',
    },
  ];

  const PackagingData = [
    {
        path: 'documents', // Specific groups only
        inputType: 'document',
        label: 'Documents',
        key: 'documents',
        group: [
          'productPackagingPhotos',
          'supplierInvoice'
        ],
      },
    {
      patch: true,
      path: 'packagingDoneBy',
      inputType: 'text',
      label: 'Packaging Done By',
      mandatory: true,
      key: 'packagingDoneBy',
    }
  ]; 

  const renderValue = (inputType, value, group = []) => {
    if (!value || (typeof value === 'object' && Object.keys(value).length === 0)) return 'N/A';

    switch (inputType) {
      case 'text':
      case 'number':
        return <span className="font-semibold text-gray-800">{value}</span>;

      case 'date': {
        // Handle both string dates and Date objects
        let dateValue = value;
        if (typeof value === 'string') {
          // If it's an ISO string, parse it
          dateValue = dayjs(value);
        }
        return (
          <span className="flex items-center gap-2 font-semibold text-gray-800">
            <CalendarOutlined />
            {dayjs(dateValue).format('DD-MM-YYYY')}
          </span>
        );
      }

      case 'file': {
        const files = Array.isArray(value)
          ? value
          : value && typeof value === 'object'
          ? [value]
          : [];
        return (
          <div className="flex flex-col gap-2">
            {files.length > 0
              ? files.map((file, index) => (
                  <div key={file.fileId || index} className="flex gap-2 flex-wrap items-center">
                    <div>
                      <div>{file.name || 'Unnamed File'}</div>
                    </div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))
              : 'No Files'}
          </div>
        );
      }

      case 'document': {
        const groupsToShow = group.length > 0 ? group : Object.keys(value);

        return groupsToShow
          .filter(docType => Array.isArray(value[docType]) && value[docType].length > 0)
          .map((docType, idx) => {
            const files = value[docType];
            return (
              <div key={idx} className="flex flex-col gap-2 mb-3">
                <h4 className="font-medium text-gray-600">{docType}</h4>
                {files.map((file, index) => (
                  <div
                    key={file.fileId || index}
                    className="flex gap-2 flex-wrap items-center bg-gray-100 p-2 rounded-md"
                  >
                    <div>{file.name || `File ${index + 1}`}</div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))}
              </div>
            );
          });
      }

      default:
        return <span>{value}</span>;
    }
  };

  const renderSection = (title, data) => (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
        {title}
      </h3>
      <div className="space-y-4">
        {data.map(({ key, label, inputType, group, path }) => {
          const fieldPath = path?.split('.') || [];
          let value = formData;
          fieldPath.forEach(p => {
            value = value?.[p];
          });
          return (
            <div
              key={key}
              className="flex flex-col p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors"
            >
              <h4 className="text-sm font-semibold text-gray-700 mb-2">{label}</h4>
              {renderValue(inputType, value, group)}
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderProductSection = () => (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
        Product Information
      </h3>
      <div className="space-y-3">
        {mappedProducts.map((mappedProduct, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex-1">
              <span className="font-semibold text-gray-800">
                {mappedProduct.formProduct?.product?.tradeName || 
                 mappedProduct.formProduct?.product?.name || 
                 mappedProduct.formProduct?.product || 
                 `Product ${index + 1}`}
              </span>
            </div>
            
            {mappedProduct.orderBookProduct && (
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Testing Required:</span>
                  <span className="font-semibold text-gray-800">
                    {mappedProduct.orderBookProduct.testingRequired ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Dispatch With Results:</span>
                  <span className="font-semibold text-gray-800">
                    {mappedProduct.orderBookProduct.dispatchWithResults ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            )}
            
            {!mappedProduct.orderBookProduct && (
              <div className="text-sm text-gray-500 italic">
                No matching order book product found
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-bold text-gray-900 mb-6 border-b border-gray-300 pb-3">
        QC Approval Data
      </h2>
      
      {mappedProducts.length > 0 && renderProductSection()}
      {renderSection('Testing Sent', TestingSentData)}
      {renderSection('Testing Results', TestingResultsData)}
      {renderSection('Packaging Information', PackagingData)}
      {renderSection('Documents', DocumentsData)}
    </div>
  );
};

QCApprovalTable.propTypes = {
  formData: PropTypes.object.isRequired,
  onDownload: PropTypes.func.isRequired,
  record: PropTypes.object.isRequired,
};

export default QCApprovalTable; 