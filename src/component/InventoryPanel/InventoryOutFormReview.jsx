import React from 'react';
import PropTypes from 'prop-types';
import { Collapse, Descriptions, Table } from 'antd';
import { productUOMList } from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';

const InventoryOutFormReview = (props) => {
  const { inventoryOutEntry } = props;

  const InventoryFieldItems = [
    {
      key: '1',
      label: 'Inventory Name',
      children: inventoryOutEntry?.inventoryData?.name || inventoryOutEntry?.inventoryDetail?.name || '-',
    },
    {
      key: '2',
      label: 'Inventory Id',
      children: inventoryOutEntry?.inventoryData?.inventoryId || inventoryOutEntry?.inventoryDetail?.inventoryId || '-',
    },
    {
      key: '3',
      label: 'Inventory Country',
      children: inventoryOutEntry?.inventoryData?.country || inventoryOutEntry?.inventoryDetail?.country || '-',
    },
    {
      key: '4',
      label: 'Customer Dispatch Order',
      children: inventoryOutEntry?.dispatchData?.dispatchOrderId || inventoryOutEntry?.customerDispatchOrderNumber ||  '-',
    },
  ];
  const mappedSupplierDataHeader = [
    {
      title: 'SDO#',
      dataIndex: 'orderId',
      key: 'orderId',
    },
    {
      title: 'Supplier Name',
      dataIndex: 'supplierName',
      key: 'supplierName',
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
    },
  ];
  const orderProductsList = inventoryOutEntry?.products?.length
  ? inventoryOutEntry.products.map((product, index) => ({
      key: index,
      label: product?.productData?.tradeName || product?.product?.tradeName,
      children: (
        <Descriptions
          layout="vertical"
          column={2}
          items={getProductFields(product)}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      )
    }))
  : [];

  function getProductFields (productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Units',
        children: productData?.units,
      },
      {
        key: '4',
        label: 'Packaging',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>{productData?.packagingData?.type || productData?.packaging?.type}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Pack Size:</span>
              <span>{productData?.packagingData?.packSize || productData?.packaging?.packSize}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Tare Weight:</span>
              <span>{productData?.packagingData?.tareWeight || productData?.packaging?.tareWeight}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Dimension:</span>
              <span>{productData?.packagingData?.dimension || productData?.packaging?.dimension}</span>
            </div>
          </div>
        ),
      },
      {
        key: '5',
        label: 'Mapped supplier Data',
        children: (
          <Table            
            columns={mappedSupplierDataHeader}
            dataSource={productData?.productBatches?.map((item) => ({...item, key: item.orderId}))}
            pagination={false}
          />
        ),
      },
    ]
  }

  return (
    <div className='flex flex-col gap-5'>
      <Descriptions
        title={<div className='text-lg font-semibold'>Inventory Order Details</div>}
        items={InventoryFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <div className='text-lg font-semibold'>Product Detail</div>
      <Collapse
        style={{ marginTop: '20px' }}
        items={orderProductsList} defaultActiveKey={['0']}
      />
    </div>
  )
}

export default InventoryOutFormReview;

InventoryOutFormReview.propTypes = {
  inventoryOutEntry: PropTypes.object.isRequired,
};
