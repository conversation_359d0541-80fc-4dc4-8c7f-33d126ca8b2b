import { But<PERSON>, Collapse, Drawer, Form } from 'antd';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import uploadIcon from '../../../assets/icons/upload_file.svg';
import emptyDoc from '../../../assets/icons/empty_doc.svg';
import css from './ProductViewPage.module.css';
import FileUpload from '../../FileUpload';
import DocumentUpload from './DocumentUpload';
import { CloseOutlined } from '@ant-design/icons';
import { deleteMstackDocument, uploadSupplierProductMstackDocument } from '../../../service/api/supplierService';
import { getFormConfigFromName } from '../../../service/api/formConfig';
import { formConfigName } from '../../../constants/formConstant';
import { getEnumValueFromList } from '../../../util/formUtil';

const ProductSupplierCard = (props) => {

  const { productId, supplier, supplierIndex, setMstackDocuments, handleDeleteMstackDocument } = props;

  const [open, setOpen] = useState(false);
  const [loader, setLoader] = useState(false);
  const [productCertificatesType, setProductCertificatesType] = useState([]);
  const [productDocumentsType, setProductDocumentsType] = useState([]);

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  const uploadMstackDocument = (formValue) => {
    setLoader(true);
    uploadSupplierProductMstackDocument(supplier.id, productId, formValue.documents).then((res) => {
      if (res.status === 200 && res.data) {
        setMstackDocuments(res.data);
        setLoader(false);
        setOpen(false);
      } 
    }).catch((err) => {
      setLoader(false);
      console.log(err);
    })
  }
  const handleDocumentDelete = (documentType, docIndex, fileindex) => {
    const files = supplier.product.mstackDocuments[docIndex].files;
    const fileId = files?.[fileindex].fileId;
    if (fileId) {
      deleteMstackDocument(supplier.id, productId, fileId, documentType).then((res) => {
        if (res.status === 200) {
          handleDeleteMstackDocument(docIndex, fileindex);
        }
      }).catch((err) => {
        console.log(err);
      })
    }
  }

  const getUploadButton = (
    <Button onClick={showDrawer} type="default" size="middle" className={css.btnFlexStyle}>
      <span>Upload Doc</span>
      <img src={uploadIcon} alt="upload"/>
    </Button>
  );
  const emptyDocElement = (
    <div className={css.emptyDoc}>
      <img src={emptyDoc} alt="Empty"/>
      <div>No documents added</div>
    </div>
  );
  const item = [{
    key: '1',
    label: <div className={css.collapseTitle}>{supplier.name}</div>,
    extra: getUploadButton,
    children: (
      <>
        <div className={css.productAttributes}>
          <div className={css.attributeContainer}>
            <div className={css.label}>HS code</div>
            <div className={css.value}>{supplier?.product?.hsCode}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Typical order size</div>
            <div className={css.value}>{supplier?.product?.typicalOrderSize}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Last traded Price</div>
            <div className={css.value}>{supplier?.product?.traded_price}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Lead time</div>
            <div className={css.value}>{supplier?.product?.leadTime}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Capacity available</div>
            <div className={css.value}>{supplier?.product?.capacityAvailable}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Total capacity</div>
            <div className={css.value}>{supplier?.product?.totalCapacity}</div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Hazardous</div>
            <div className={css.value}>
              {supplier?.product?.hazardous && supplier?.product?.hazardousLevel?.length
                ? supplier?.product?.hazardousLevel.join('/')
                : 'False'
              }
            </div>
          </div>
          <div className={css.attributeContainer}>
            <div className={css.label}>Supplier type</div>
            <div className={css.value}>{supplier?.product?.supplierType}</div>
          </div>
        </div>
        <div className={css.collapseSection}>
          <div className={css.title}>Available Packaging</div>
          <div>
            {supplier?.product?.packaging?.length ? supplier.product.packaging.map((item, index) =>(
              <div className={css.packagingGrid} key={index}>
                <div className={css.attributeContainer}>
                  <div className={css.label}>Packing type</div>
                  <div className={css.value}>{item?.type}</div>
                </div>
                <div className={css.attributeContainer}>
                  <div className={css.label}>Pack size</div>
                  <div className={css.value}>{item?.packSize}</div>
                </div>
                <div className={css.attributeContainer}>
                  <div className={css.label}>Tare weight</div>
                  <div className={css.value}>{item?.tareWeight}</div>
                </div>
                <div className={css.attributeContainer}>
                  <div className={css.label}>Dimension</div>
                  <div className={css.value}>{item?.dimension}</div>
                </div>
              </div>
            )) : 'None'}
          </div>
        </div>
        <div className={css.remarksSection}>
          <div>
            <div className={css.label}>Export approved remarks</div>
            <div>{supplier?.product?.exportApprovedRemarks?.length ? supplier.product.exportApprovedRemarks.map((remark, index) => (
              <div key={index}>{remark}</div>
            )) : 'None'}</div>
          </div>
          <div>
            <div className={css.label}>Duty remarks</div>
            <div>{supplier?.product?.dutyRemarks?.length ? supplier.product.dutyRemarks.map((remark, index) => (
              <div key={index}>{remark}</div>
            )) : 'None'}</div>
          </div>
        </div>
        <div className={css.collapseSection}>
          <div className={css.title}>Certificate Documents</div>
          {supplier?.product?.certificateDocuments?.length ? (
            <div className={css.docGrid}>
              {supplier.product.certificateDocuments.map((certificate, index) => (
                <div key={index}>
                  <div className={css.label}>{getEnumValueFromList(certificate.documentType, productCertificatesType)}</div>
                  <FileUpload
                    value={certificate?.files}
                    disabled
                    deleteDisabled
                  />
                </div>
              ))}
            </div>
          ) : emptyDocElement}
        </div>
        <div className={css.collapseSection}>
          <div className={css.title}>Other Documents</div>
          {supplier?.product?.documents?.length ? (
            <div className={css.docGrid}>
              {supplier.product.documents.map((document, index) => (
                <div key={index}>
                  <div className={css.label}>{getEnumValueFromList(document.documentType, productDocumentsType)}</div>
                  <FileUpload
                    value={document?.files}
                    disabled
                    deleteDisabled
                  />
                </div>
              ))}
            </div>
          ) : emptyDocElement}
        </div>
        <div className={css.collapseSection} style={{ borderBottom: 'none', marginBottom: '0' }}>
          <div className={css.title}>Mstack Documents</div>
          {supplier?.product?.mstackDocuments?.length ? (
            <div className={css.docGrid}>{supplier.product.mstackDocuments.map((document, index) => (
              <div key={index}>
                <div className={css.label}>{getEnumValueFromList(document.documentType, productDocumentsType)}</div>
                  <FileUpload
                    value={document?.files}
                    disabled
                    onDelete={(fileindex) => handleDocumentDelete(document.documentType, index, fileindex)}
                  />
                </div>
              ))} 
            </div>
          ) : emptyDocElement}
        </div>
      </>
    ),    
  }]

  useEffect(() => {
    const fetchListApi = [
      getFormConfigFromName(formConfigName.productCertificates),
      getFormConfigFromName(formConfigName.productDocuments),
    ];
    Promise.all(fetchListApi).then((res) => {
      if (res?.[0]?.data?.value) {
        setProductCertificatesType(res?.[0].data.value)
      }
      if (res?.[1]?.data?.value) {
        setProductDocumentsType(res?.[1].data.value)
      }
    }).catch((error) => {
      console.log(error);
    })
  }, []);

  return (
    <>
      <Collapse
        defaultActiveKey={supplierIndex === 0 ? ['1'] : null}
        expandIconPosition='end'
        items={item}
        style={{ marginBottom: '16px' }}
      />
      <Drawer
        title={
          <div className={css.drawerTitleBox}>
            <div className={css.drawerTitle}>Upload Documents</div>
            <div className={css.drawerSubTitle}>{supplier.name}</div>
          </div>
        }
        classNames={{
          header: css.drawerHeader
        }}
        placement="right"
        onClose={onClose}
        open={open}
      >
        <Form
          name="upload_mstack_doc"
          layout="vertical"
          scrollToFirstError
          size="large"
          onFinish={uploadMstackDocument}
        >
          <div className={css.formList}>
            <Form.List
              name="documents"
              rules={[
                {
                  required: true,
                  message: 'At least 1 document is required!',
                },
              ]}
              style={{}}
            >
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map((field, index) => {
                    return (
                      <div key={index}>
                        <div className={css.docHeader}>
                          <span className={css.docTitle}>{`Document ${index+1}`}</span>
                          <span>
                            <CloseOutlined onClick={() => remove(field.name)} width={16} height={16}/>
                          </span>
                        </div>
                        <DocumentUpload
                          remove={remove}
                          field={field}
                          docType
                          productDocumentsType={productDocumentsType}
                        />
                      </div>
                    );
                  })}
                  <Button type="default" onClick={() => add()} block>
                    + Add Document
                  </Button>
                  <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                </>
              )}
            </Form.List>
          </div>
          <Button loading={loader} type="primary" htmlType="submit" block className={css.docSubmitBtn}>
            Submit
          </Button>
        </Form>
      </Drawer>
    </>
  )
}

export default ProductSupplierCard;

ProductSupplierCard.propTypes = {
  productId: PropTypes.string.isRequired,
  supplier: PropTypes.object.isRequired,
  supplierIndex: PropTypes.number.isRequired,
  setMstackDocuments: PropTypes.func.isRequired,
  handleDeleteMstackDocument: PropTypes.func.isRequired,
};