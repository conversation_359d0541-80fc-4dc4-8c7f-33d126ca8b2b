import React, { useState } from 'react'
import PropTypes from 'prop-types';
import { templateObj } from './Template';

const DocumentPreview = (props) => {
  const {docType, formData} = props;

  const [zoomValue, setZoomValue] = useState(0.8);

  const handleZoomIn = () => {
    setZoomValue(prevZoom => Math.min(prevZoom + 0.1, 1.5)); // Adjust the zoom increment as needed
  };

  const handleZoomOut = () => {
    setZoomValue(prevZoom => Math.max(prevZoom - 0.1, 0.5)); // Adjust the zoom decrement as needed
  };
  
  return (
    <div className='w-[70%] flex overflow-scroll bg-neutral-300'>
      <div style={{ zoom: zoomValue }}>
        <div className='previewContainer'>
          {/* template starts */}
          {templateObj[docType] ? templateObj[docType](formData) : null}
          {/* template ends */}
        </div>
      </div>
      <div className="absolute bottom-[2%] left-[60%] text-base-white font-semibold rounded-full" style={{backgroundColor: '#171F36'}}>
        <span className='cursor-pointer px-5 py-2.5 border-r-solid borlder-r-base-white' onClick={() => handleZoomOut()}>-</span>
        {/* <span>|</span> */}
        <span>{(zoomValue * 100).toFixed(0)}%</span>
        {/* <span>|</span> */}
        <span className='px-5 py-2.5 border-r-solid borlder-r-base-white cursor-pointer' onClick={() => handleZoomIn()}>+</span>
      </div>
    </div>
  )
}

export default DocumentPreview;

DocumentPreview.propTypes = {
  docType: PropTypes.string.isRequired,
  formData: PropTypes.object.isRequired,
};