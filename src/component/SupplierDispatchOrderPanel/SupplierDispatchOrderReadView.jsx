import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Descriptions, Table, Modal, List, Tag } from 'antd';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import {
  formModes,
  orderStatusList,
  paymentTermsDate,
  productIncoTermsList,
} from '../../constants/formConstant';
import { camelCaseToTitle } from '../../util/stringUtils';
import { Typography, message } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { userEntity, userPermissionsList } from '../../constants/UserTabsConstants';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { productColumnHeaders } from '../../constants/TableConstants';
import { hasPermission } from '../../util/userUtils';
import { useSelector } from 'react-redux';
import BatchModal from '../BatchModal/BatchModal';
import { deleteSupplierDispatchOrderFromId } from '../../service/api/supplierDispatchOrderApi';
import RouteFactory from '../../service/RouteFactory';
import { getBatchDetails } from '../../service/api/batchApi';
import ObjectUtil from '../../util/objectUtil';
import DocumentsList from './DocumentsList';

const SupplierDispatchOrderReadView = props => {
  const { orderbookData, dispatchOrder, setDispatchOrder, customerorderbookId } = props;

  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchModalMode, setBatchModalMode] = useState(null);
  const [batchDetails, setBatchDetails] = useState({});

  const user = useSelector(state => state.user);
  const [messageApi, contextHolder] = message.useMessage();

  const { Title } = Typography;
  const fieldItems = [
    ...(dispatchOrder?.purchaseOrderNumber != null
      ? [
          {
            key: '1',
            label: 'PO number',
            children: dispatchOrder?.purchaseOrderNumber,
          },
        ]
      : []),
    ...(dispatchOrder?.purchaseOrderDate != null
      ? [
          {
            key: '2',
            label: 'PO Date',
            children: getDateFromTimeStamp(dispatchOrder?.purchaseOrderDate),
          },
        ]
      : []),
    ...(dispatchOrder?.status != null
      ? [
          {
            key: '3',
            label: 'Status',
            children: (
              <div
                style={{
                  color: dispatchOrder?.status
                    ? orderStatusList[dispatchOrder.status].color
                    : '#000',
                  fontWeight: '600',
                }}
              >
                {dispatchOrder?.status ? orderStatusList[dispatchOrder.status].label : '-'}
              </div>
            ),
          },
        ]
      : []),
    // {
    //   key: '4',
    //   label: 'Type of bill lading',
    //   children: getEnumValueFromList(dispatchOrder?.typeOfBL, billLadingTypes),
    // },
    ...(dispatchOrder?.shipmentDate != null
      ? [
          {
            key: '5',
            label: 'Shipment date ( Sailing Date )',
            children: getDateFromTimeStamp(dispatchOrder?.shipmentDate),
          },
        ]
      : []),
    // {
    //   key: '6',
    //   label: 'Delivery date',
    //   children: getDateFromTimeStamp(dispatchOrder?.deliveryDate),
    // },
    ...(dispatchOrder?.incoterms != null
      ? [
          {
            key: '8',
            label: 'Incoterms',
            children: (
              <div className="flex flex-col">
                <div>
                  <span style={{ fontWeight: '500' }}>Type:</span>
                  <span>
                    {getEnumValueFromList(dispatchOrder?.incoterms?.type, productIncoTermsList)}
                  </span>
                </div>
                <div>
                  <span style={{ fontWeight: '500' }}>Country:</span>
                  <span>{dispatchOrder?.incoterms?.country}</span>
                </div>
                {dispatchOrder?.incoterms?.data &&
                Object.keys(dispatchOrder.incoterms.data).length > 0
                  ? Object.keys(dispatchOrder.incoterms.data).map(key => (
                      <div key={key}>
                        <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                        <span>{dispatchOrder.incoterms.data[key]}</span>
                      </div>
                    ))
                  : ''}
              </div>
            ),
          },
        ]
      : []),
    ...(dispatchOrder?.paymentTerms?.creditAmount != null
      ? [
          {
            key: '10',
            label: 'Credit amount(in %)',
            children: dispatchOrder?.paymentTerms?.creditAmount,
          },
        ]
      : []),
    ...(dispatchOrder?.paymentTerms?.creditorDays != null
      ? [
          {
            key: '11',
            label: 'Credit days',
            children: dispatchOrder?.paymentTerms?.creditorDays,
          },
        ]
      : []),
    ...(dispatchOrder?.paymentTerms?.startDate != null
      ? [
          {
            key: '12',
            label: 'Payment terms based on',
            children: getEnumValueFromList(
              dispatchOrder?.paymentTerms?.startDate,
              paymentTermsDate
            ),
          },
        ]
      : []),
    {
      key: '16',
      label: 'Supplier ARN number',
      children: dispatchOrder?.arnNumber,
    },
    ...(dispatchOrder?.destinationBookingMadeOn != null
      ? [
          {
            key: '17',
            label: 'Destination Booking Made On',
            children: dispatchOrder?.destinationBookingMadeOn,
          },
        ]
      : []),
    ...(dispatchOrder?.actualDateOfDeparture != null
      ? [
          {
            key: '18',
            label: 'Actual Date of Departure',
            children: getDateFromTimeStamp(dispatchOrder?.actualDateOfDeparture),
          },
        ]
      : []),
    ...(dispatchOrder?.destinationCarrierName != null
      ? [
          {
            key: '19',
            label: 'Destination Carrier Name',
            children: dispatchOrder?.destinationCarrierName,
          },
        ]
      : []),
    ...(dispatchOrder?.sentForTestingOn != null
      ? [
          {
            key: '20',
            label: 'Sent For Testing On',
            children: getDateFromTimeStamp(dispatchOrder?.sentForTestingOn),
          },
        ]
      : []),
    ...(dispatchOrder?.sentForTestingBy != null
      ? [
          {
            key: '21',
            label: 'Sent For Testing By',
            children: dispatchOrder?.sentForTestingBy,
          },
        ]
      : []),
    ...(dispatchOrder?.labCoaReceivedBy != null
      ? [
          {
            key: '22',
            label: 'Lab COA Received By',
            children: dispatchOrder?.labCoaReceivedBy,
          },
        ]
      : []),
    ...(dispatchOrder?.testingDate != null
      ? [
          {
            key: '23',
            label: 'Testing Date',
            children: getDateFromTimeStamp(dispatchOrder?.testingDate),
          },
        ]
      : []),
    ...(dispatchOrder?.destinationTrackingNumber != null
      ? [
          {
            key: '24',
            label: 'Destination Tracking Number',
            children: dispatchOrder?.destinationTrackingNumber,
          },
        ]
      : []),
    ...(dispatchOrder?.destinationTrackingUrl != null
      ? [
          {
            key: '25',
            label: 'Destination Tracking Url',
            children: dispatchOrder?.destinationTrackingUrl,
          },
        ]
      : []),
    ...(dispatchOrder?.packagingDoneBy != null
      ? [
          {
            key: 'packagingDoneBy',
            label: 'Packaging Done By',
            children: dispatchOrder?.packagingDoneBy,
          },
        ]
      : []),
    ...(dispatchOrder?.batchNumber != null
      ? [
          {
            key: 'batchNumber',
            label: 'Mstack Batch Number',
            children: dispatchOrder?.batchNumber,
          },
        ]
      : []),
    ...(dispatchOrder?.qcApprovedOn != null
      ? [
          {
            key: 'qcApprovedOn',
            label: 'QC Approved On',
            children: getDateFromTimeStamp(dispatchOrder?.qcApprovedOn),
          },
        ]
      : []),
    ...(dispatchOrder?.qcApprovedBy != null
      ? [
          {
            key: 'qcApprovedBy',
            label: 'QC Approved By',
            children: dispatchOrder?.qcApprovedBy,
          },
        ]
      : []),
    ...(dispatchOrder?.loadingCompletedOn != null
      ? [
          {
            key: 'loadingCompletedOn',
            label: 'Loading Completed On',
            children: getDateFromTimeStamp(dispatchOrder?.loadingCompletedOn),
          },
        ]
      : []),
    ...(dispatchOrder?.loadingCompletedBy != null
      ? [
          {
            key: 'loadingCompletedBy',
            label: 'Loading Completed By',
            children: dispatchOrder?.loadingCompletedBy,
          },
        ]
      : []),
    ...(dispatchOrder?.releasePaymentMadeBy != null
      ? [
          {
            key: 'releasePaymentMadeBy',
            label: 'Release Payment Made By',
            children: dispatchOrder?.releasePaymentMadeBy,
          },
        ]
      : []),
    ...(dispatchOrder?.releasePaymentMadeOn != null
      ? [
          {
            key: 'releasePaymentMadeOn',
            label: 'Release Payment Made On',
            children: getDateFromTimeStamp(dispatchOrder?.releasePaymentMadeOn),
          },
        ]
      : []),
    ...(dispatchOrder?.shippingCommunicatedOn != null
      ? [
          {
            key: 'shippingCommunicatedOn',
            label: 'Shipping Communicated On',
            children: getDateFromTimeStamp(dispatchOrder?.shippingCommunicatedOn),
          },
        ]
      : []),
    ...(dispatchOrder?.etaForDestination != null
      ? [
          {
            key: 'etaForDestination',
            label: 'ETA For Destination',
            children: getDateFromTimeStamp(dispatchOrder?.etaForDestination),
          },
        ]
      : []),
    ...(dispatchOrder?.isfCutoffDate != null
      ? [
          {
            key: 'isfCutoffDate',
            label: 'ISF Cutoff Date',
            children: getDateFromTimeStamp(dispatchOrder?.isfCutoffDate),
          },
        ]
      : []),
    ...(dispatchOrder?.isfActualDate != null
      ? [
          {
            key: 'isfActualDate',
            label: 'ISF Actual Date',
            children: getDateFromTimeStamp(dispatchOrder?.isfActualDate),
          },
        ]
      : []),
    ...(dispatchOrder?.customsReleasedOn != null
      ? [
          {
            key: 'customsReleasedOn',
            label: 'Customs Released On',
            children: getDateFromTimeStamp(dispatchOrder?.customsReleasedOn),
          },
        ]
      : []),
    ...(dispatchOrder?.customsReleaseConfirmedBy != null
      ? [
          {
            key: 'customsReleaseConfirmedBy',
            label: 'Customs Release Confirmed By',
            children: dispatchOrder?.customsReleaseConfirmedBy,
          },
        ]
      : []),
    ...(dispatchOrder?.carrierName != null
      ? [
          {
            key: 'carrierName',
            label: 'Carrier Name',
            children: dispatchOrder?.carrierName,
          },
        ]
      : []),
    ...(dispatchOrder?.trackingNumber != null
      ? [
          {
            key: 'trackingNumber',
            label: 'Tracking Number',
            children: dispatchOrder?.trackingNumber,
          },
        ]
      : []),
    ...(dispatchOrder?.trackingUrl != null
      ? [
          {
            key: 'trackingUrl',
            label: 'Tracking URL',
            children: dispatchOrder?.trackingUrl,
          },
        ]
      : []),
    ...(dispatchOrder?.customerAppointementDate != null
      ? [
          {
            key: 'customerAppointementDate',
            label: 'Customer Appointment Date',
            children: getDateFromTimeStamp(dispatchOrder?.customerAppointementDate),
          },
        ]
      : []),
    ...(dispatchOrder?.truckerPickUpDate != null
      ? [
          {
            key: 'truckerPickUpDate',
            label: 'Trucker Pick Up Date',
            children: getDateFromTimeStamp(dispatchOrder?.truckerPickUpDate),
          },
        ]
      : []),
    ...(dispatchOrder?.deliveryInstructions != null
      ? [
          {
            key: 'deliveryInstructions',
            label: 'Delivery Instructions',
            children: dispatchOrder?.deliveryInstructions,
          },
        ]
      : []),
    ...(dispatchOrder?.orderDeliveredOn != null
      ? [
          {
            key: 'orderDeliveredOn',
            label: 'Order Delivered On',
            children: getDateFromTimeStamp(dispatchOrder?.orderDeliveredOn),
          },
        ]
      : []),
    ...(dispatchOrder?.receiverName != null
      ? [
          {
            key: 'receiverName',
            label: 'Receiver Name',
            children: dispatchOrder?.receiverName,
          },
        ]
      : []),
    ...(dispatchOrder?.invoiceNumberGeneratedBy != null
      ? [
          {
            key: 'invoiceNumberGeneratedBy',
            label: 'Invoice Number Generated By',
            children: dispatchOrder?.invoiceNumberGeneratedBy,
          },
        ]
      : []),
    ...(dispatchOrder?.invoiceSentBy != null
      ? [
          {
            key: 'invoiceSentBy',
            label: 'Invoice Sent By',
            children: dispatchOrder?.invoiceSentBy,
          },
        ]
      : []),
    ...(dispatchOrder?.invoiceSentOn != null
      ? [
          {
            key: 'invoiceSentOn',
            label: 'Invoice Sent On',
            children: getDateFromTimeStamp(dispatchOrder?.invoiceSentOn),
          },
        ]
      : []),
    ...(dispatchOrder?.route != null ? [
      {
        key: 'route',
        label: 'Route',
        children: dispatchOrder?.route,
      }
    ] : []),
    ...(dispatchOrder?.arrivalDateAtOrigin != null ? [
      {
        key: 'arrivalDateAtOrigin',
        label: 'Sample Receiving Date at Origin',
        children: getDateFromTimeStamp(dispatchOrder?.arrivalDateAtOrigin),
      }
    ] : []),
    ...(dispatchOrder?.rePackagingConfirmedBy != null ? [
      {
        key: 'rePackagingConfirmedBy',
        label: 'Repackaging Confirmed By',
        children: dispatchOrder?.rePackagingConfirmedBy,
      }
    ] : []),
    ...(dispatchOrder?.movementApprovedOn != null ? [
      {
        key: 'movementApprovedOn',
        label: 'Movement Approved On',
        children: getDateFromTimeStamp(dispatchOrder?.movementApprovedOn),
      }
    ] : []),
    ...(dispatchOrder?.movementApprovedBy != null ? [
      {
        key: 'movementApprovedBy',
        label: 'Movement Approved By',
        children: dispatchOrder?.movementApprovedBy,
      }
    ] : []),
    ...(dispatchOrder?.dispatchToDestinationDate != null ? [
      {
        key: 'dispatchToDestinationDate',
        label: 'Dispatch To Destination Date',
        children: getDateFromTimeStamp(dispatchOrder?.dispatchToDestinationDate),
      }
    ] : []),
    ...(dispatchOrder?.destinationCarrierName != null ? [
      {
        key: 'destinationCarrierName',
        label: 'Destination Carrier Name',
        children: dispatchOrder?.destinationCarrierName,
      }
    ] : []),
    ...(dispatchOrder?.destinationTrackingNumber != null ? [
      {
        key: 'destinationTrackingNumber',
        label: 'Destination Tracking Number',
        children: dispatchOrder?.destinationTrackingNumber,
      }
    ] : []),
    ...(dispatchOrder?.etaForDestination != null ? [
      {
        key: 'etaForDestination',
        label: 'Planned Delivery Date',
        children: getDateFromTimeStamp(dispatchOrder?.etaForDestination),
      }
    ] : []),
    ...(dispatchOrder?.deliveryAtDestinationConfirmedBy != null ? [
      {
        key: 'deliveryAtDestinationConfirmedBy',
        label: 'Delivery At Destination Confirmed By',
        children: dispatchOrder?.deliveryAtDestinationConfirmedBy,
      }
    ] : []),
    ...(dispatchOrder?.deliveryAtDestinationPartnerDate != null ? [
      {
        key: 'deliveryAtDestinationPartnerDate',
        label: 'Delivery At Destination Partner Date',
        children: getDateFromTimeStamp(dispatchOrder?.deliveryAtDestinationPartnerDate),
      }
    ] : []),
    ...(dispatchOrder?.destinationRecievedDate != null ? [
      {
        key: 'destinationRecievedDate',
        label: 'Destination Received Date',
        children: getDateFromTimeStamp(dispatchOrder?.destinationRecievedDate),
      }
    ] : []),
    ...(dispatchOrder?.sampleApprovedBy != null ? [
      {
        key: 'sampleApprovedBy',
        label: 'Sample Approved By',
        children: dispatchOrder?.sampleApprovedBy,
      }
    ] : []),
    ...(dispatchOrder?.originCarrierName != null ? [
      {
        key: 'originCarrierName',
        label: 'Dispatch Carrier Name',
        children: dispatchOrder?.originCarrierName,
      }
    ] : []),
    ...(dispatchOrder?.originTrackingNumber != null ? [
      {
        key: 'originTrackingNumber',
        label: 'Dipsatch Tracking Number',
        children: dispatchOrder?.originTrackingNumber,
      }
    ] : []),
    ...(dispatchOrder?.originTrackingUrl != null ? [
      {
        key: 'originTrackingUrl',
        label: 'Dispatch Tracking URL',
        children: dispatchOrder?.originTrackingUrl,
      }
    ] : []),
    ...(dispatchOrder?.supplierDispatchedOn != null ? [
      {
        key: 'supplierDispatchedOn',
        label: 'Supplier Dispatched On',
        children: getDateFromTimeStamp(dispatchOrder?.supplierDispatchedOn),
      }
    ] : []),
    ...(dispatchOrder?.documents ? [
      {
        key: 'documents',
        label: 'Documents',
        children: <DocumentsList documents={dispatchOrder?.documents} />
      }
    ] : []),
  ];
  const extraColoumnHeaders = [
    {
      title: 'Linked Orders',
      dataIndex: 'linkedOrders',
      key: 'linkedOrders',
      width: 200,
      render: (_, { linkedOrders }) => (
        <>
          {linkedOrders?.map(item => (
            <div key={item.orderId} className="mb-2">
              <div>Order Id : {item?.id}</div>
              <div>Quantity : {item.quantity}</div>
            </div>
          ))}
        </>
      ),
    },
  ];

  const handleSupplierOrderDelete = id => {
    deleteSupplierDispatchOrderFromId(id)
      .then(() => {
        window.location.replace(new RouteFactory().dashboard().orderBook().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        messageApi.error(errMsg);
      });
  };

  const handleEditDispatchOrder = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.supplierDispatchOrder,
        mode: formModes.UPDATE,
        orderBook: orderbookData,
        formData: dispatchOrder,
        customerorderbookId: customerorderbookId,
      },
    });
  };
  const formatOrderProduct = () =>
    dispatchOrder.products.map(item => ({
      ...item,
      key: item.id,
    }));
  const handleBatchDetails = status => {
    setBatchModalVisible(status);
  };
  const fetchBatchDetails = () => {
    getBatchDetails(dispatchOrder.id)
      .then(res => {
        if (!ObjectUtil.isEmptyObject(res.data)) setBatchDetails(res.data);
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    // decide the form mode
    if (dispatchOrder && dispatchOrder.id) {
      fetchBatchDetails();
    }
  }, [dispatchOrder?.id]);

  return (
    <>
      <div>
        <div className="mb-4 flex justify-between items-center">
          {contextHolder}
          <Title level={4}> {`Supplier Name : ${dispatchOrder?.supplier?.name}`} </Title>
          <div className="flex gap-2">
            {hasPermission(userPermissionsList.updateSupplierOrder, user.permissions) ? (
              <Button
                type="default"
                icon={<EditOutlined />}
                onClick={() => handleEditDispatchOrder()}
                className="rounded-[4px]"
              >
                Edit
              </Button>
            ) : null}
            {!ObjectUtil.isEmptyObject(batchDetails?.productBatchMap) ? (
              <>
                <Button
                  type="default"
                  // icon={<EditOutlined />}
                  onClick={() => {
                    setBatchModalMode(formModes.READ);
                    handleBatchDetails(true);
                  }}
                  className="rounded-[4px]"
                >
                  View Batch Details
                </Button>
                <Button
                  type="default"
                  // icon={<EditOutlined />}
                  onClick={() => {
                    setBatchModalMode(formModes.UPDATE);
                    handleBatchDetails(true);
                  }}
                  className="rounded-[4px]"
                >
                  Edit Batch Details
                </Button>
              </>
            ) : (
              <>
                <Button
                  type="default"
                  // icon={<EditOutlined />}
                  onClick={() => handleBatchDetails(true)}
                  className="rounded-[4px]"
                >
                  Add Batch Details
                </Button>
              </>
            )}
            {hasPermission(userPermissionsList.deleteSupplierOrder, user.permissions) ? (
              <Button
                type="default"
                onClick={() => handleSupplierOrderDelete(dispatchOrder.id)}
                className="rounded-[4px]"
              >
                Delete
              </Button>
            ) : null}
          </div>
        </div>
        <Descriptions items={fieldItems} labelStyle={{ fontWeight: '700', color: '#23568A' }} />
        <Table
          columns={[...productColumnHeaders, ...extraColoumnHeaders]}
          dataSource={formatOrderProduct()}
          title={() => 'Product(S)'}
          pagination={false}
          scroll={{
            x: '100%',
            y: 600,
          }}
        />
      </div>
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={data => {
          setDispatchOrder(data);
        }}
      />
      {dispatchOrder.id ? (
        <BatchModal
          visible={batchModalVisible}
          mode={batchModalMode}
          onCancel={() => setBatchModalVisible(false)}
          onSumbit={newBatchData => {
            setBatchDetails(newBatchData);
          }}
          orderData={dispatchOrder}
          batchData={batchDetails}
          fetchBatchDetails={() => fetchBatchDetails()}
        />
      ) : null}
    </>
  );
};

export default SupplierDispatchOrderReadView;

SupplierDispatchOrderReadView.propTypes = {
  dispatchOrder: PropTypes.object.isRequired,
  orderbookData: PropTypes.object.isRequired,
  setDispatchOrder: PropTypes.func.isRequired,
  customerorderbookId: PropTypes.string.isRequired,
};
