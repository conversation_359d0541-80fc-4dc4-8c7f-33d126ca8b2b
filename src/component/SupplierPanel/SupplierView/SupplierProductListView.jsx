import { Collapse } from 'antd';
import React from 'react';
import PropTypes from 'prop-types';
import SupplierProductView from './SupplierProductView';

const SupplierProductListView = props => {
  const { productList } = props;

  const getProductItems = productList => {
    return productList.map((productData, index) => {
      return {
        key: index,
        label: `Product - ${index + 1} ( ${productData?.product?.tradeName} )`,
        span: 12,
        children: <SupplierProductView key={index} productData={productData} />,
      };
    });
  };
  return <Collapse items={getProductItems(productList)} />;
};

export default SupplierProductListView;

SupplierProductListView.propTypes = {
  productList: PropTypes.array,
};
