import React, { useEffect, useState } from 'react';
import { Form, Select, Col, InputNumber, Input } from 'antd';
import PropTypes from 'prop-types';
import {
  formModes,
} from '../../../constants/formConstant';
import { roundToTwoDecimalPlaces } from '../../../util/formatUtil';

const DispatchProduct = ({ index, productList, form, formView }) => {
  const [prevProductId, setPrevProductId] = useState(undefined);
  const productId = Form.useWatch([`items`, index, 'productId'], form);
  const cost = Form.useWatch([`items`, index, 'cost'], form);
  const taxInPercent = Form.useWatch([`items`, index, 'taxInPercent'], form);

  useEffect(() => {
    console.log(formModes, formView);
    console.log('product list ', productList);
    if (prevProductId != productId && prevProductId != undefined) {
      form.setFieldValue(['items', index, 'chargeType'], undefined);
      form.setFieldValue(['items', index, 'cost'], undefined);
      form.setFieldValue(['items', index, 'description'], undefined);
      form.setFieldValue(['items', index, 'taxInPercent'], undefined);
    }
    setPrevProductId(prevProductId);
  }, [productId]);

  useEffect(() => {
    if (cost && taxInPercent >= 0) {
      form.setFieldValue(
        ['items', index, 'costIncludingTax'],
        roundToTwoDecimalPlaces(cost + (taxInPercent / 100) * cost)
      );
    }
  }, [cost, taxInPercent]);

  return (
    <>
      <Col span={3}>
        <Form.Item
          label="Product Id"
          name={['items', index, 'productId']}
          rules={[{ required: true, message: 'Please select Product' }]}
        >
          <Select>
            {productList.map(product => (
              <Select.Option key={product.id} value={product.id}>
                {product.product.tradeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={3}>
        <Form.Item label="Description" name={['items', index, 'description']}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={3}>
        <Form.Item
          label="Cost"
          name={['items', index, 'cost']}
          rules={[
            { required: true, message: 'Please enter cost price' },
            {
              validator: (_, value) => {
                if (value <= 0) {
                  return Promise.reject('Cost price must be greater than zero');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber />
        </Form.Item>
      </Col>
      <Col span={3}>
        <Form.Item
          label="Tax (in %)"
          name={['items', index, 'taxInPercent']}
          rules={[
            { required: true, message: 'Please enter tax' },
            {
              validator: (_, value) => {
                if (value < 0) {
                  return Promise.reject('Tax percent must be greater than or equal zero');
                }
                if (value > 0 && (value * 100) % 1 !== 0) {
                  // Checks if more than 2 digits after decimal
                  return Promise.reject(
                    'Only up to two digits after the decimal point are allowed'
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber />
        </Form.Item>
      </Col>
      <Col span={3}>
        <Form.Item label="Cost (incl tax)" name={['items', index, 'costIncludingTax']}>
          <InputNumber disabled />
        </Form.Item>
      </Col>
    </>
  );
};

DispatchProduct.propTypes = {
  index: PropTypes.number.isRequired,
  productList: PropTypes.array.isRequired,
  form: PropTypes.object.isRequired,
  formView: PropTypes.string,
};

export default DispatchProduct;
