import React from 'react';
import { Form, Input, DatePicker, InputNumber, Row, Col, Button, Select } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import enUS from 'antd/es/date-picker/locale/en_US';
import { currencyTypeList } from '../../constants/formConstant';

const DispatchOrderInputFields = ({ form, initialValues, onValuesChange, onSubmit, loading }) => {
  // Convert date strings to dayjs objects for initial values
  const formattedInitialValues = {
    ...initialValues,
    invoiceDate: initialValues?.invoiceDate ? dayjs(initialValues.invoiceDate) : null,
    chemstackInvoiceDate: initialValues?.chemstackInvoiceDate
      ? dayjs(initialValues.chemstackInvoiceDate)
      : null,
    deliveryDate: initialValues?.deliveryDate ? dayjs(initialValues.deliveryDate) : null,
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // Convert dayjs dates back to ISO strings
      const formattedValues = {
        ...initialValues, // Keep other necessary values from initialValues
        ...values, // Override with new form values
        invoiceDate: values.invoiceDate?.toISOString() || initialValues?.invoiceDate || null,
        chemstackInvoiceDate:
          values.chemstackInvoiceDate?.toISOString() || initialValues?.chemstackInvoiceDate || null,
        deliveryDate: values.deliveryDate?.toISOString() || initialValues?.deliveryDate || null,
      };

      onSubmit?.(formattedValues);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      size="large"
      initialValues={formattedInitialValues}
      onValuesChange={(changedValues, allValues) => onValuesChange?.(changedValues, allValues)}
    >
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item label="Mstack Invoice Date" name="invoiceDate">
            <DatePicker
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
              locale={enUS}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Chemstack Invoice Date" name="chemstackInvoiceDate">
            <DatePicker
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
              locale={enUS}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Delivery Date" name="deliveryDate">
            <DatePicker
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
              locale={enUS}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item label="Freight Cost" name="freightCost">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Currency" name="buyerCurrency">
            <Select style={{ width: '100%' }}>
              {currencyTypeList.map(currency => (
                <Select.Option key={currency.key} value={currency.value}>
                  {currency.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Insurance Cost" name="insuranceCost">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item label="BL Number" name="bLNumber">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Vessel Number" name="vesselNumber">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Voyage Name" name="voyageName">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={8}>
          <Form.Item label="Custom Exchange Rate" name="customExchangeRate">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Trade Agreement Code" name="tradeAgreementCode">
            <Input />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Number of Pallets" name="numberOfPallets">
            <InputNumber style={{ width: '100%' }} min={0} />
          </Form.Item>
        </Col>
      </Row>
      <Row justify="end">
        <Col>
          <Button type="primary" size="large" loading={loading} onClick={handleSubmit}>
            Submit
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

DispatchOrderInputFields.propTypes = {
  form: PropTypes.object.isRequired,
  initialValues: PropTypes.object,
  onValuesChange: PropTypes.func,
  onSubmit: PropTypes.func,
  loading: PropTypes.bool,
};

export default DispatchOrderInputFields;
