import { Descriptions, Table, Button, Modal, List, Tag, message } from 'antd';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { 
  DownloadOutlined, 
  EyeOutlined 
} from '@ant-design/icons';
import { downloadFile } from '../../service/api/storageService';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import {
  billLadingTypes,
  formModes,
  orderStatusList,
  paymentTermsDate,
  productIncoTermsList,
} from '../../constants/formConstant';
import { camelCaseToTitle } from '../../util/stringUtils';
import { productColumnHeaders } from '../../constants/TableConstants';
// import { getEmployeeList } from '../../service/api/employee';
// import {  getEmployeesNameFromId } from '../../util/userUtils';


const styles = {
  documentItem: {
    marginBottom: '8px',
    padding: '12px',
    border: '1px solid #f0f0f0',
    borderRadius: '4px',
    backgroundColor: '#fafafa',
  },
  documentType: {
    fontSize: '16px',
    fontWeight: '500',
    color: '#1890ff',
    marginBottom: '8px',
  },
  fileInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '4px',
  },
  label: {
    fontWeight: '500',
    marginRight: '8px',
  },
};

const DocumentsList = ({ documents }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [api, contextHolder] = message.useMessage();

  const getFileUrl = async (file) => {
    try {
      api.loading({
        key: 'fileAction',
        content: 'Processing...'
      });
      
      const apiRes = await downloadFile(file.fileId);
      api.success({
        key: 'fileAction',
        content: 'Success'
      });
      return apiRes.data.url;
    } catch (error) {
      console.error('Error while getting file URL:', error);
      api.error({
        key: 'fileAction',
        content: 'Failed to process file'
      });
      throw error;
    }
  };

  const onDownload = async file => {
    try {
      const url = await getFileUrl(file);
      window.open(url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    }
  };

  const handleDocPreview = async (file) => {
    try {
      const url = await getFileUrl(file);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error while previewing file:', error);
    }
  };

  if (!documents || Object.keys(documents).length === 0) {
    return <span>No documents available</span>;
  }

  const totalDocs = Object.values(documents).reduce((sum, arr) => sum + arr.length, 0);

  return (
    <>
      {contextHolder}
      <Button type="link" onClick={() => setIsModalVisible(true)}>
        View Documents ({totalDocs})
      </Button>
      
      <Modal
        title="Documents"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <List
          dataSource={Object.entries(documents)}
          renderItem={([docType, files]) => (
            <List.Item>
              <div className="w-full">
                <h3 className="text-base font-medium mb-2">{docType}</h3>
                <div className="pl-4">
                  {files.map((file, index) => (
                    <div 
                      key={file.uid || index} 
                      className="mb-3 p-3 bg-gray-50 rounded-md flex justify-between items-center"
                    >
                      <div className="flex-grow">
                        <div className="font-medium">{file.name}</div>
                        {file.remark && (
                          <div className="text-gray-500 text-sm mt-1">
                            Remark: {file.remark}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-3">
                        {file.isApproved !== null && (
                          <Tag color={file.isApproved ? 'green' : 'red'}>
                            {file.isApproved ? 'Approved' : 'Not Approved'}
                          </Tag>
                        )}
                        {/* <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleDocPreview(file)}
                          title="Preview"
                        /> */}
                        <Button
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={() => onDownload(file)}
                          title="Download"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </List.Item>
          )}
        />
      </Modal>
    </>
  );
};

DocumentsList.propTypes = {
  documents: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.shape({
    uid: PropTypes.string,
    name: PropTypes.string.isRequired,
    isApproved: PropTypes.bool,
    remark: PropTypes.string
  }))).isRequired
};

const DispatchOrderReadView = props => {
  const { formValues, productList, formView } = props;

  // const [employeeList, setEmployeeList] = useState([]);

  // useEffect(() => {
  //   // if (formView === formModes.CREATE) setLoading(true);
  //   const fetchApiList = [getEmployeeList()];
  //   Promise.all(fetchApiList).then((responseList) => {
  //     if (responseList[0]?.data && responseList[0].data?.content) {
  //       setEmployeeList(responseList[0].data.content);
  //     }
  //     // setLoading(false);
  //   }).catch(error => {
  //     console.log(error);
  //     // if (formView === formModes.CREATE) setLoading(false);
  //   });
  // }, []);


  const fieldItems = [
    ...(formValues?.purchaseOrderNumber != null ? [
      {
        key: '1',
        label: 'PO number',
        children: formValues?.purchaseOrderNumber,
      }
    ] : []),
    ...(formValues?.purchaseOrderDate != null ? [
      {
        key: '2',
        label: 'PO Date',
        children: getDateFromTimeStamp(formValues?.purchaseOrderDate),
      }
    ] : []),
    ...(formValues?.status != null ? [
      {
        key: '3',
        label: 'Status',
        children: (
          <div
            style={{
              color: formValues?.status ? orderStatusList[formValues.status].color : '#000',
              fontWeight: '600',
            }}
          >
            {formValues?.status ? orderStatusList[formValues.status].label : '-'}
          </div>
        ),
      }
    ] : []),
    ...(formValues?.typeOfBL != null ? [
      {
        key: '4',
        label: 'Type of bill lading',
        children: getEnumValueFromList(formValues?.typeOfBL, billLadingTypes),
      }
    ] : []),
    ...(formValues?.shipmentDate != null ? [
      {
        key: '5',
        label: 'Shipment date (Sailing Date)',
        children: getDateFromTimeStamp(formValues?.shipmentDate),
      }
    ] : []),
    ...(formValues?.deliveryDate != null ? [
      {
        key: '6',
        label: 'Delivery date',
        children: getDateFromTimeStamp(formValues?.deliveryDate),
      }
    ] : []),
    ...(formValues?.incoterms != null ? [
      {
        key: '8',
        label: 'Incoterms',
        children: (
          <>
            <div>
              <span style={{ fontWeight: '500' }}>Type:</span>
              <span>{getEnumValueFromList(formValues?.incoterms?.type, productIncoTermsList)}</span>
            </div>
            <div>
              <span style={{ fontWeight: '500' }}>Country:</span>
              <span>{formValues?.incoterms?.country}</span>
            </div>
            {formValues?.incoterms?.data && Object.keys(formValues.incoterms.data).length > 0
              ? Object.keys(formValues.incoterms.data).map(key => (
                  <div key={key}>
                    <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                    <span>{formValues.incoterms.data[key]}</span>
                  </div>
                ))
              : ''}
          </>
        ),
      }
    ] : []),
    ...(formValues?.paymentTerms?.poPaymentTerms != null ? [
      {
        key: 'poPaymentTerms',
        label: 'PO Payment Terms',
        children: formValues?.paymentTerms?.poPaymentTerms,
      }
    ] : []),
    ...(formValues?.paymentTerms?.creditAmount != null ? [
      {
        key: '10',
        label: 'Credit amount(in %)',
        children: formValues?.paymentTerms?.creditAmount,
      }
    ] : []),
    ...(formValues?.paymentTerms?.creditorDays != null ? [
      {
        key: '11',
        label: 'Credit days',
        children: formValues?.paymentTerms?.creditorDays,
      }
    ] : []),
    ...(formValues?.paymentTerms?.startDate != null ? [
      {
        key: '12',
        label: 'Payment terms based on',
        children: getEnumValueFromList(formValues?.paymentTerms?.startDate, paymentTermsDate),
      }
    ] : []),
    ...(formValues?.shipmentCreatedAtDate != null ? [
      {
        key: '13',
        label: 'Shipment created at',
        children: getDateFromTimeStamp(formValues?.shipmentCreatedAtDate),
      }
    ] : []),
    ...(formValues?.igstAmt != null ? [
      {
        key: 'igst',
        label: 'IGST',
        children: formValues?.igstAmt,
      }
    ] : []),
    ...(formValues?.consignee != null ? [
      {
        key: '14',
        label: 'Consignee Details',
        children: formValues?.consignee,
      }
    ] : []),
    ...(formValues?.notifyParty != null ? [
      {
        key: '15',
        label: 'Notify Party Details',
        children: formValues?.notifyParty,
      }
    ] : []),
    ...(formValues?.tradeAgreementCode != null ? [
      {
        key: '16',
        label: 'Trade Agreement Code',
        children: formValues?.tradeAgreementCode,
      }
    ] : []),
    ...(formValues?.customExchangeRate != null ? [
      {
        key: '17',
        label: 'Custom exchange rate',
        children: formValues?.customExchangeRate,
      }
    ] : []),
    ...(formValues?.typeOfShipment != null ? [
      {
        key: '18',
        label: 'Type of Shipment',
        children: formValues?.typeOfShipment,
      }
    ] : []),
    ...(formValues?.invoiceDate != null ? [
      {
        key: '19',
        label: 'Invoice date',
        children: getDateFromTimeStamp(formValues?.invoiceDate),
      }
    ] : []),
    ...(formValues?.expectedDeliveryDate != null ? [
      {
        key: '20',
        label: 'Expected Delivery date',
        children: getDateFromTimeStamp(formValues?.expectedDeliveryDate),
      }
    ] : []),
    ...(formValues?.freightCost != null ? [
      {
        key: '21',
        label: 'Freight Cost',
        children: formValues?.freightCost,
      }
    ] : []),
    ...(formValues?.insuranceCost != null ? [
      {
        key: '22',
        label: 'Insurance Cost',
        children: formValues?.insuranceCost,
      }
    ] : []),
    ...(formValues?.bLNumber != null ? [
      {
        key: '23',
        label: 'Bill of Lading Number',
        children: formValues?.bLNumber,
      }
    ] : []),
    ...(formValues?.vesselNumber != null ? [
      {
        key: '24',
        label: 'Vessel Number',
        children: formValues?.vesselNumber,
      }
    ] : []),
    ...(formValues?.voyageName != null ? [
      {
        key: '25',
        label: 'Voyage Name',
        children: formValues?.voyageName,
      }
    ] : []),
    ...(formValues?.assignedAt != null ? [
      {
        key: '26',
        label: 'Assigned At',
        children: getDateFromTimeStamp(formValues?.assignedAt),
      }
    ] : []),
    ...(formValues?.assignedBy != null ? [
      {
        key: '27',
        label: 'Assigned By',
        children: formValues?.assignedBy,
      }
    ] : []),
    ...(formValues?.assignedTo != null ? [
      {
        key: '28',
        label: 'Assigned To',
        children: formValues?.assignedTo,
      }
    ] : []),
    ...(formValues?.approved != null ? [
      {
        key: '29',
        label: 'Approved',
        children: formValues?.approved,
      }
    ] : []),
    ...(formValues?.numberOfPallets != null ? [
      {
        key: '30',
        label: 'Number of pallets',
        children: formValues?.numberOfPallets,
      }
    ] : []),
    ...(formValues?.palletWt != null ? [
      {
        key: 'palletWt',
        label: 'Pallet Wt (Chemstack)',
        children: formValues?.palletWt,
      }
    ] : []),
    ...(formValues?.mstackPalletWt != null ? [
      {
        key: 'mstackPalletWt',
        label: 'Pallet Wt (Mstack)',
        children: formValues?.mstackPalletWt,
      }
    ] : []),
    ...(formValues?.sealNumber != null ? [
      {
        key: 'sealNumber',
        label: 'Seal Number',
        children: formValues?.sealNumber,
      }
    ] : []),
    ...(formValues?.customsReleasedOn != null ? [
      {
        key: 'customsReleasedOn',
        label: 'Customs Released On',
        children: getDateFromTimeStamp(formValues?.customsReleasedOn),
      }
    ] : []),
    ...(formValues?.customsReleaseConfirmedBy != null ? [
      {
        key: 'customsReleaseConfirmedBy',
        label: 'Customs Release Confirmed By',
        children: formValues?.customsReleaseConfirmedBy,
      }
    ] : []),
    ...(formValues?.carrierName != null ? [
      {
        key: 'carrierName',
        label: 'Carrier Name',
        children: formValues?.carrierName,
      }
    ] : []),
    ...(formValues?.trackingNumber != null ? [
      {
        key: 'trackingNumber',
        label: 'Tracking Number',
        children: formValues?.trackingNumber,
      }
    ] : []),
    ...(formValues?.trackingUrl != null ? [
      {
        key: 'trackingUrl',
        label: 'Tracking URL',
        children: formValues?.trackingUrl,
      }
    ] : []),
    ...(formValues?.dispatchFromDestinationOn != null ? [
      {
        key: 'dispatchFromDestinationOn',
        label: 'Dispatch From Destination On',
        children: getDateFromTimeStamp(formValues?.dispatchFromDestinationOn),
      }
    ] : []),
    ...(formValues?.sampleDispatchedOn != null ? [
      {
        key: 'sampleDispatchedOn',
        label: 'Sample Dispatched On',
        children: getDateFromTimeStamp(formValues?.sampleDispatchedOn),
      }
    ] : []),
    ...(formValues?.sampleDeliveredOn != null ? [
      {
        key: 'sampleDeliveredOn',
        label: 'Sample Delivered On',
        children: getDateFromTimeStamp(formValues?.sampleDeliveredOn),
      }
    ] : []),
    ...(formValues?.sampleDeliveryConfirmedBy != null ? [
      {
        key: 'sampleDeliveryConfirmedBy',
        label: 'Sample Delivery Confirmed By',
        children: formValues?.sampleDeliveryConfirmedBy,
      }
    ] : []),
    ...(formValues?.customerAppointementDate != null ? [
      {
        key: 'customerAppointementDate',
        label: 'Customer Appointment Date',
        children: getDateFromTimeStamp(formValues?.customerAppointementDate),
      }
    ] : []),
    ...(formValues?.truckerPickUpDate != null ? [
      {
        key: 'truckerPickUpDate',
        label: 'Trucker Pick Up Date',
        children: getDateFromTimeStamp(formValues?.truckerPickUpDate),
      }
    ] : []),
    ...(formValues?.deliveryInstructions != null ? [
      {
        key: 'deliveryInstructions',
        label: 'Delivery Instructions',
        children: formValues?.deliveryInstructions,
      }
    ] : []),
    ...(formValues?.orderDeliveredOn != null ? [
      {
        key: 'orderDeliveredOn',
        label: 'Order Delivered On',
        children: getDateFromTimeStamp(formValues?.orderDeliveredOn),
      }
    ] : []),
    ...(formValues?.receiverName != null ? [
      {
        key: 'receiverName',
        label: 'Receiver Name',
        children: formValues?.receiverName,
      }
    ] : []),
    ...(formValues?.marksAndContainers != null ? [
      {
        key: 'marksAndContainers',
        label: 'Marks & Containers',
        children: formValues?.marksAndContainers,
      }
    ] : []),
    ...(formValues?.remarks != null ? [
      {
        key: 'remarks',
        label: 'Remarks',
        children: formValues?.remarks,
      }
    ] : []),
    ...(formValues?.documents ? [
      {
        key: 'documents',
        label: 'Documents',
        children: <DocumentsList documents={formValues.documents} />
      }
    ] : []),
  ];

  const formatOrderProduct = () => {
    // console.log('form values ',formValues.customer.name)
    return formValues?.products?.length
      ? formValues.products.map(item => {
          const [productObj] = productList.filter(product => product.id === item.product);
          return {
            ...item,
            key: item.id,
            product: formView === formModes.READ ? item.product : productObj.product || {},
          };
        })
      : null;
  };

  return (
    <div>
       <Descriptions
        layout="vertical"
        bordered
        items={fieldItems}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
      />
      <Table
        columns={productColumnHeaders}
        dataSource={formatOrderProduct()}
        title={() => 'Product(S)'}
        scroll={{
          x: '100%',
          y: 600,
        }}
      />
    </div>
  );
};

export default DispatchOrderReadView;

DispatchOrderReadView.propTypes = {
  formValues: PropTypes.object.isRequired,
  productList: PropTypes.array,
  formView: PropTypes.string,
};

DocumentsList.propTypes = {
  documents: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.shape({
    uid: PropTypes.string,
    name: PropTypes.string.isRequired,
    isApproved: PropTypes.bool,
    remark: PropTypes.string
  }))).isRequired
};
