import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox } from 'antd';

const DocumentSelection = (props) => {
  const { value, onChange, productData } = props;

  const getCheckboxStatus = (docType, fileId) => {
    if (!value || !value.length) return false;
    const docList = value.find((doc) => doc.documentType === docType);
    if (docList && docList?.files?.length) {
      const file = docList.files.find((file) => file.fileId === fileId);
      if (file) return true;
    }
    return false;
  };
  const handleCheckboxClick = (e, docType, file) => {
    const checkboxVal = e.target.checked;
    let newValue = value?.length ? JSON.parse(JSON.stringify(value)) : [];
    if (checkboxVal) {
      const docIndex = newValue.findIndex((doc) => doc.documentType === docType);
      if (docIndex >= 0) {
        newValue[docIndex].files.push(file);
      } else {
        newValue = [
          ...newValue,
          {
            documentType: docType,
            files: [file],
          }
        ]
      }
    } else {
      const docIndex = newValue.findIndex((doc) => doc.documentType === docType);
      if (docIndex >= 0) {
        const fileIndex = newValue[docIndex].files.findIndex((item) => item.fileId === file.fileId);
        if (fileIndex > 0) {
          newValue[docIndex].files.splice(fileIndex, 1);
        } else if (fileIndex === 0) {
          newValue.splice(docIndex, 1);
        }
      }
    }
    if (onChange) {
      onChange({
        target: { value: newValue},
      })
    }
  };
  const getDocumentList = (title, list) => (
    <div className="mb-2.5">
      <div className="text-lg text-color-text-heading font-medium">
        {title}
      </div>
      {list.map((item) => (
        <div
          key={item.documentType}
          className="mt-1 pl-2"
        >
          <div className="text-base font-medium">{item.documentType}</div>
          <div className="mt-2 pl-2.5">
            {item.files.map((file) => (
              <div
                key={file.fileId}
                className="mb-1 p-4 flex items-center gap-2 rounded-xl border border-solid border-border-color"
              >
                <Checkbox
                  checked={getCheckboxStatus(item.documentType, file.fileId)}
                  onChange={(e) => handleCheckboxClick(e, item.documentType, file)}
                />
                <div className="flex flex-col items-start justify-center gap-2 flex-grow">
                  <div className="text-text-black text-[17px] tracking-[-0.68px]">
                    {file.name}
                  </div>
                  {file.remark
                    ? <div className="text-xs text-subtext-color tracking-[-0.24px]">
                        {file.remark}
                      </div>
                    : null
                  }
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <>
      {productData?.certificateDocuments?.length
        ? getDocumentList('Choose product certificate(s)', productData.certificateDocuments)
        : null
      }
      {productData?.documents?.length
        ? getDocumentList('Choose supplier product document(s)', productData.documents)
        : null
      }
      {productData?.mstackDocuments?.length
        ? getDocumentList('Choose Mstack product document(s)', productData.mstackDocuments)
        : null
      }
    </>
  )
}

export default DocumentSelection;

DocumentSelection.propTypes = {
  productData: PropTypes.object.isRequired,
  value: PropTypes.array,
  onChange: PropTypes.func,
}