import React, { useEffect, useState } from 'react';
// import css from './FilterModal.module.css';
import { Alert, Button, Form, Modal } from 'antd';
import PropTypes from 'prop-types';
import CustomerFilter from '../CustomerPanel/CustomerFilter';
import OrderBookFilter from '../OrderBookPanel/OrderBookFilter';
import SupplierFilter from '../SupplierPanel/SupplierFilter';
import SupplierOrderBookFilter from '../SupplierOrderBookPanel/SupplierOrderBookFilter';
import { getQeryParamsFromURL } from '../../util/route';

const FilterModal = props => {
  const { open, onApplyFilters, onCancel, view } = props;

  const [form] = Form.useForm();
  const [errorMsg, setErrorMsg] = useState('');

  const getFilterfromView = () => {
    // reset form if filter data is not present in url
    const { filters } = getQeryParamsFromURL();
    if (!isFilterValid(filters)){
       form.resetFields();
      }
    // setFormValues(formValues)
    switch (view) {
      case 'customer':
        return <CustomerFilter form={form} />;
      case 'orderbook':
        return <OrderBookFilter form={form} />;
      case 'supplier':
        return <SupplierFilter form={form} />;
      case 'supplierOrderBook':
        return <SupplierOrderBookFilter form={form} />;
      default:
        return <div>Nothing to view</div>;
    }
  };

  const isFilterValid = formObj => {
    return (
      formObj != null &&
      Object.keys(formObj).some(key => {
        const values = formObj[key];
        return values != null && Array.isArray(values) && values.length != 0;
      })
    );
  };
  // reset error message if modal open or closes 
  useEffect(()=>setErrorMsg(''),[open])

  return (
    <Modal
      open={open}
      title="Select Filters"
      okText="Apply"
      cancelText="Reset"
      onCancel={() => onCancel()}
      footer={[
        errorMsg ? (
          <Alert
            key="alert"
            style={{ margin: '6px', textAlign: 'left' }}
            message={errorMsg}
            type="error"
            showIcon
          />
        ) : (
          ''
        ),
        <Button
          key="reset"
          onClick={() => {
            setErrorMsg('');
            form.resetFields();
          }}
        >
          Reset
        </Button>,
        <Button
          key="apply"
          type="primary"
          onClick={() => {
            if (!isFilterValid(form.getFieldValue())) {
              setErrorMsg('Please select at least one filter ');
            } else onApplyFilters(form.getFieldValue());
          }}
        >
          Apply
        </Button>,
      ]}
    >
      {getFilterfromView()}
    </Modal>
  );
};

export default FilterModal;

FilterModal.propTypes = {
  view: PropTypes.string.isRequired,
  open: PropTypes.bool.isRequired,
  onApplyFilters: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
};
