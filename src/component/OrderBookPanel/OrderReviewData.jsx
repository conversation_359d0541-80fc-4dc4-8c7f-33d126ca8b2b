import React from 'react';
import PropTypes from 'prop-types';
import { Descriptions, Table } from 'antd';
import FileUpload from '../FileUpload';
import {
  paymentTermsDate,
  productIncoTermsList,
  productUOMList,
  incotermsDataList,
} from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const OrderReviewData = props => {
  const { orderBookEntry, fetchAssignedQuantity } = props;

  const getLabelForShipmentMethodOrDefault = key => {
    const label = getEnumValueFromList(key, incotermsDataList.FOB[0].child);
    return label ? label : key;
  };

  const testingRequired = orderBookEntry.products.filter(product => product.testingRequired === true);
  const testingRequiredValue = testingRequired.length > 0;
  
  const dispatchWithResults = orderBookEntry.products.filter(product => product.dispatchWithResults === true);
  const dispatchWithResultsValue = dispatchWithResults.length > 0;

  const orderFieldItems = [
    {
      key: '1',
      label: orderBookEntry?.inventoryId ? 'Inventory Name' : 'Customer Name',
      children: orderBookEntry?.inventoryId ? orderBookEntry?.inventoryData?.name || orderBookEntry?.inventory?.name : orderBookEntry?.customer?.name || orderBookEntry?.customerData?.name,
    },
    {
      key: '2',
      label: 'PO number',
      children: orderBookEntry?.purchaseOrderNumber,
    },
    {
      key: '3',
      label: 'PO date',
      children: getDateFromTimeStamp(orderBookEntry?.purchaseOrderDate,)
    },
    {
      key: '4',
      label: 'PO file',
      children: orderBookEntry?.inventoryId ? '-' : (
        <FileUpload
          value={orderBookEntry?.purchaseOrderFile}
          disabled
          deleteDisabled
        />
      ),
    },
    {
      key: '5',
      label: 'Testing Required',
      children: testingRequiredValue ? 'Yes' : 'No',
    },
    ...(testingRequired.length > 0 ? [{
      key: '6',
      label: 'Dispatch with Test results',
      children: dispatchWithResultsValue ? 'Yes' : 'No',
    }] : []),
  ];
  const paymentFieldItems= [
    {
      key: '5',
      label: 'Billing address',
      children: orderBookEntry?.billTo,
    },
    {
      key: 'poPaymentTerms',
      label: 'PO Payment Terms',
      children: orderBookEntry?.paymentTerms?.poPaymentTerms,
    },
    {
      key: '6',
      label: 'Credit amount(%)',
      children: orderBookEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '7',
      label: 'Credit days',
      children: orderBookEntry?.paymentTerms?.creditorDays,
    },
    // {
    //   key: '8',
    //   label: 'Payment Term Based on',
    //   children: getEnumValueFromList(orderBookEntry?.paymentTerms?.startDate, paymentTermsDate),
    // },
    // {
    //   key: '9',
    //   label: 'margin Percentage',
    //   children:orderBookEntry?.marginPercentage,
    // },
    // {
    //   key: '10',
    //   label: 'margin ApprovedBy',
    //   children: orderBookEntry?.marginApprovedBy,
    // },
    // {
    //   key: '11',
    //   label: 'margin ApprovedOn',
    //   children: getDateFromTimeStamp(orderBookEntry?.marginApprovedOn),
    // },
    {
      key: '9',
      label: 'Remarks',
      children: (
        <>{orderBookEntry?.remarks
            ? 
              <div>{orderBookEntry?.remarks}</div>
            : null}
        </>
      ),
    },
  ];
  const productFieldColumns = [
    {
      title: 'Product',
      dataIndex: 'product',
      key: 'product',
      render: (_, {product}) => product?.tradeName,
      fixed: 'left',
      width: 200,
    },
    {
      title: 'Assigned / Total Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 200,
      render:(_, record) => `${fetchAssignedQuantity(record.id)}/${record?.quantity} ${getEnumValueFromList(record?.uom, productUOMList)}`
    },
    {
      title: 'Price/unit($)',
      dataIndex: 'price',
      key: 'price',
      width: 150,
    },
    {
      key: 'incoterms',
      title: 'Incoterms',
      dataIndex: 'incoterms',
      width: 250,
      render: (_, record) =>(
        <>
          <div>
            <span style={{ fontWeight: '600' }}>Type:</span>
            <span>
              {getEnumValueFromList(record?.incoterms?.type, productIncoTermsList)}
            </span>
          </div>
          <div>
            <span style={{ fontWeight: '600' }}>Destination Country: </span>
            <span>{record?.incoterms?.country}</span>
          </div>
          {record?.incoterms?.data && Object.keys(record.incoterms.data).length > 0
            ? Object.keys(record.incoterms.data).map(key => (
                <div key={key}>
                  <span style={{ fontWeight: '600' }}>{camelCaseToTitle(key)}:</span>
                  <span>
                    {getLabelForShipmentMethodOrDefault(record.incoterms?.data?.[key])}
                  </span>
                </div>
              ))
            : ''}
        </>
      ),
    },
    {
      key: 'packaging',
      title: 'Packaging',
      dataIndex: 'packaging',
      width: 300,
      render: (_, record) => (
        <>
          <div>
            <span style={{ fontWeight: '600' }}>Type:</span>
            <span>{record?.packaging?.type}</span>
          </div>
          {record?.packaging?.type !== 'Others' && (
            <>
              <div>
                <span style={{ fontWeight: '600' }}>Pack Size:</span>
                <span>{record?.packaging?.packSize}</span>
              </div>
              <div>
                <span style={{ fontWeight: '600' }}>Tare Weight:</span>
                <span>{record?.packaging?.tareWeight}</span>
              </div>
              <div>
                <span style={{ fontWeight: '600' }}>Dimension:</span>
                <span>{record?.packaging?.dimension}</span>
              </div>
            </>
          )}
          {record?.packaging?.type === 'Others' && record?.packaging?.otherPackagingDetails && (
            <div>
              <span style={{ fontWeight: '600' }}>Other Packaging Details:</span>
              <span>{record.packaging.otherPackagingDetails}</span>
            </div>
          )}
        </>
      ),
    },
    {
      key: 'otherPackagingDetails',
      title: 'Other Packaging Details',
      dataIndex: 'packaging',
      width: 200,
      render: (_, record) => (
        record?.packaging?.type === 'Others' ? record?.packaging?.otherPackagingDetails : '-'
      ),
    },
    {
      key: 'units',
      title: 'Total number of packaging units',
      dataIndex: 'units',
      width: 150,
    },
    {
      key: 'deliveryDate',
      title: 'Expected Delivery date',
      dataIndex: 'deliveryDate',
      width: 150,
      render:(_, record) => getDateFromTimeStamp(record?.expectedDeliveryDate),
    },

    // {
    //   key: '9',
    //   title: 'Customer product code',
    //   dataIndex: 'customerProductCode',
    //   width: 150,
    // },
    {
      key: 'remarks',
      title: 'Remarks',
      dataIndex: 'remarks',
      width: 250,
      render:(_, record) => (
        <>
          {record?.remarks
            ?  (
              <div>{record?.remarks}</div>
            )
            : null
          }
        </>
      ),
    },
  ];

  return (
    <div className='flex flex-col gap-5'>
      <Descriptions
        title={<div className='text-lg font-semibold'>Order Details</div>}
        items={orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
      />
      <div className='text-lg font-semibold'>Product Detail</div>
      <Table
        columns={productFieldColumns}
        dataSource={orderBookEntry.products.map((product) => ({...product, key: product.id}))}
        pagination={false}
        scroll={{
          x: '100%',
        }}
      />
    </div>
  );
};

export default OrderReviewData;

OrderReviewData.propTypes = {
  orderBookEntry: PropTypes.object.isRequired,
  fetchAssignedQuantity: PropTypes.func,
};
