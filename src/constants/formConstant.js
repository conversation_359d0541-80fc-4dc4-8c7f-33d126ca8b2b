import dayjs from 'dayjs';

export const formModes = {
  CREATE: 'add',
  READ: 'view',
  UPDATE: 'edit',
  DELETE: 'delete',
};

export const customerSize = [
  { key: 1, label: 'SMB', label_desc: '<= $25m', value: 'SMB' },
  { key: 2, label: 'Mid Enterpriese', label_desc: '>$25m - <=$500m', value: 'MID_ENTERPRISE' },
  { key: 3, label: 'Large Enterprise', label_desc: '>= $500m', value: 'LARGE_ENTERPRISE' },
];
export const customerType = [
  { key: 1, label: 'Distributor', value: 'DISTRIBUTOR' },
  { key: 2, label: 'End Customer', value: 'END_CUSTOMER' },
];

export const formConfigName = {
  category: 'categories',
  packaging: 'packaging',
  documents: 'documents',
  taskCategories: 'taskCategories',
  productCertificates: 'productCertificates',
  productDocuments: 'productDocuments',
  supplierDocuments: 'supplierDocuments',
  hazardousLevel: 'hazardousLevel',
};

export const paymentTermsDate = [
  { key: 1, label: 'Delivery Date', value: 'DeliveryDate' },
  { key: 2, label: 'Invoice Date', value: 'InvoiceDate' },
  { key: 3, label: 'BL Date', value: 'BLDate' },
  { key: 4, label: 'LC from BL Date', value: 'LCFromBLDate' }, 
];

export const categoryOptions = [
  { key: 1, label: 'Coatings', value: 'Coatings' },
  { key: 2, label: 'Oil & Gas', value: 'OilGas' },
  { key: 3, label: 'Agro', value: 'Agro' },
  { key: 4, label: 'Food Additives', value: 'FoodAdditives' },
  { key: 5, label: 'Pharmaceuticals', value: 'Pharmaceuticals' },
  { key: 6, label: 'Personal Care', value: 'PersonalCare' },
  { key: 7, label: 'Construction', value: 'Construction' },
  { key: 8, label: 'CASE India', value: 'CaseIndia' },
  { key: 9, label: 'Water Treatment', value: 'WaterTreatment' },
];

export const productUOMList = [
  { key: 'mt', label: 'Metric Ton(mt)', value: 'METRIC_TON' },
  { key: 'lb', label: 'Pound(lb)', value: 'POUND' },
  { key: 'gal', label: 'Gallon(gal)', value: 'GALLON' },
  { key: 'l', label: 'Litre(l)', value: 'LITRE' },
  { key: 'kl', label: 'Kilolitre(kl)', value: 'KILOLITRE' },
  { key: 'kg', label: 'Kilogram(kg)', value: 'KILOGRAM' },
];

export const productIncoTermsList = [
  { key: 'exb', label: 'EXW (Ex Works)', value: 'EXWORKS' },
  { key: 'fob', label: 'FOB (Free on board)', value: 'FOB' },
  { key: 'ddp', label: 'DDP (Delivery Duty Paid)', value: 'DDP' },
  { key: 'cif', label: 'CIF (Cost, Insurance, & Freight)', value: 'CIF' },
  { key: 'cfr', label: 'CFR (Cost & Freight)', value: 'CFR' },
  { key: 'for', label: 'FOR (Free on road)', value: 'FOR' },
];

export const billLadingTypes = [
  { key: 1, label: 'Original BL', value: 'ORIGINAL_BL' },
  { key: 2, label: 'Seaway BL', value: 'SEAWAY_BL' },
  { key: 3, label: 'Telex release', value: 'TELEX_RELEASE' },
];

export const incotermsDataList = {
  EXWORKS: [{ key: 1, label: 'Place of pickup', name: 'placeOfPickup', type: 'textarea' }],
  FOB: [
    {
      key: 1,
      label: 'Shipment method',
      name: 'shipmentMethod',
      type: 'select',
      child: [
        { key: 1, label: 'Sea', value: 'sea' },
        { key: 2, label: 'Air', value: 'air' },
        { key: 3, label: 'Road', value: 'road' },
      ],
    },
    { key: 2, label: 'Port of loading', name: 'portOfLoading', type: 'text' },
    { key: 3, label: 'Port of discharge', name: 'portOfDischarge', type: 'text' },
  ],
  DDP: [
    { key: 1, label: 'Place of delivery', name: 'placeOfDelivery', type: 'textarea' },
    { key: 2, label: 'City', name: 'city', type: 'text' },
    { key: 3, label: 'Port of discharge', name: 'portOfDischarge', type: 'text' },
    { key: 4, label: 'Port of loading', name: 'portOfLoading', type: 'text' },
  ],
  CIF: [
    {
      key: 1,
      label: 'Shipment method',
      name: 'shipmentMethod',
      type: 'select',
      child: [
        { key: 1, label: 'Sea', value: 'sea' },
        { key: 2, label: 'Air', value: 'air' },
      ],
    },
    { key: 2, label: 'Port of loading', name: 'portOfLoading', type: 'text' },
    { key: 3, label: 'Port of discharge', name: 'portOfDischarge', type: 'text' },
  ],
  CFR: [
    {
      key: 1,
      label: 'Shipment method',
      name: 'shipmentMethod',
      type: 'select',
      child: [
        { key: 1, label: 'Sea', value: 'sea' },
        { key: 2, label: 'Air', value: 'air' },
      ],
    },
    { key: 2, label: 'Port of loading', name: 'portOfLoading', type: 'text' },
    { key: 3, label: 'Port of discharge', name: 'portOfDischarge', type: 'text' },
  ],
  FOR: [
  ],
};

export const criticalPathStatus = [
  { key: 1, label: 'Todo', value: 'TODO' },
  { key: 2, label: 'In progress', value: 'IN_PROGRESS' },
  { key: 3, label: 'Completed', value: 'COMPLETED' },
];

export const orderStatusList = {
  INITIATED: { key: '1', value: 'INITIATED', label: 'Initiated', color: '4A5269', pillLen: 0 },
  UNDER_PRODUCTION: {
    key: '2',
    value: 'UNDER_PRODUCTION',
    label: 'Under Production',
    color: '4A5269',
    pillLen: 1,
  },
  PRODUCTION_COMPLETED: {
    key: '3',
    value: 'PRODUCTION_COMPLETED',
    label: 'Production Completed',
    color: '4A5269',
    pillLen: 1,
  },
  UNDER_TESTING: {
    key: '4',
    value: 'UNDER_TESTING',
    label: 'Under Testing',
    color: '#7A020C',
    pillLen: 2,
  },
  MANUFACTURED_AND_TESTED: {
    key: '5',
    value: 'MANUFACTURED_AND_TESTED',
    label: 'Manufactured & Tested',
    color: '#7A020C',
    pillLen: 2,
  },
  DISPATCHED: { key: '6', value: 'DISPATCHED', label: 'Dispatched', color: '#027A18', pillLen: 3 },
  DELIVERED: { key: '7', value: 'DELIVERED', label: 'Delivered', color: '#027A18', pillLen: 3 },
  PAYMENT_COMPLETED: {
    key: '8',
    value: 'PAYMENT_COMPLETED',
    label: 'Payment Completed',
    color: '#1D2743',
    pillLen: 4,
  },
};

export const DateFormat = 'DD/MMM/YYYY';

export const enquiryStatusList = {
  INITIATED: { key: '1', value: 'INITIATED', label: 'Initiated' },
  UNASSIGNED: { key: '2', value: 'UNASSIGNED', label: 'Unassigned' },
  ASSIGNED: { key: '3', value: 'ASSIGNED', label: 'Assigned' },
  QUOTED: { key: '4', value: 'QUOTED', label: 'Quoted' },
  QUOTATION_APPROVED: { key: '5', value: 'QUOTATION_APPROVED', label: 'Quote Approved' },
  NEGOTIATE: { key: '6', value: 'NEGOTIATE', label: 'Negotiate' },
  ACCEPTED: { key: '7', value: 'ACCEPTED', label: 'Accepted' },
  REJECTED: { key: '8', value: 'REJECTED', label: 'Rejected' },
};

export const enquiryPriorityList = {
  P0: { key: '1', value: 'P0', label: 'P0', tagColor: 'red' },
  P1: { key: '2', value: 'P1', label: 'P1', tagColor: 'orange' },
  P2: { key: '3', value: 'P2', label: 'P2', tagColor: 'yellow' },
};

export const supplierQuotationStatusList = {
  REJECTED: { key: '1', value: 'REJECTED', label: 'Rejected' },
  WAITING_FOR_APPROVAL: { key: '2', value: 'WAITING_FOR_APPROVAL', label: 'Waiting for approval' },
  QUOTED_TO_CUSTOMER: { key: '3', value: 'QUOTED_TO_CUSTOMER', label: 'Quoted to customer' },
};

export const billingAddressList = [
  {
    key: 1,
    label: 'MSTACK US',
    value:
      'Mstack Inc. , 21st Floor, Wework 2700 Post Oak Blvd Galleria Office Tower I, Houston, TX 77056 | Tel. No.: +91-9726939356 ',
  },
  {
    key: 2,
    label: 'CHEMSTACK GUJRAT',
    value:
      'Second Floor, Office No. SF-11,12, Omkar Complex,Valia Chokdi,GIDC Ankleshwar, Ankleshwar, Bharuch, Gujarat, 393002',
  },
  {
    key: 3,
    label: 'CHEMSTACK MUMBAI',
    value:
      '8th Floor, RCity Office, Awfis, Lal Bahadur Shastri Marg, adjoining RCity Mall, Mumbai, Maharashtra, Pin Code 400086',
  },
];

export const currencyTypeList = [
  { key: 1, label: 'US DOLLARS (USD $)', value: 'DOLLAR' },
  { key: 2, label: 'INDIAN RUPEES (INR ₹)', value: 'RUPEE' },
  { key: 3, label: 'EURO (EUR €)', value: 'EURO' },
  { key: 4, label: 'YUAN (CNY ¥)', value: 'YUAN' },
  { key: 5, label: 'YEN (JPY ¥)', value: 'YEN' },
  { key: 6, label: 'AED (AED د.إ)', value: 'AED' },
];

export const gstList = [
  { key: 1, label: '0.1 %', value: 0.1 },
  { key: 2, label: '18 %', value: 18 },
];

export const invoiceTypes = [
  { key: 'SupplierInvoice', label: 'Supplier Invoice', value: 'SupplierInvoice' },
  { key: 'PackagingInvoice', label: 'Packaging Invoice', value: 'PackagingInvoice' },
  { key: 'LogisticsInvoice', label: 'Logistic Invoice', value: 'LogisticsInvoice' },
  { key: 'TestingInvoice', label: 'Testing Invoice', value: 'TestingInvoice' },
  { key: 'MiscellaneousInvoice', label: 'Miscellaneous Invoice', value: 'MiscellaneousInvoice' },
];

export const chargeTypeList = [
  { key: 'PORT_CHARGES', label: 'Port Charges', value: 'PORT_CHARGES' },
  { key: 'OCEAN_FREIGHT_CHARGES', label: 'Ocean Freight Charges', value: 'OCEAN_FREIGHT_CHARGES' },
  { key: 'WAREHOUSE_CHARGES', label: 'Warehouse Charges', value: 'WAREHOUSE_CHARGES' },
  {
    key: 'LAST_MILE_LOGISTICS_CHARGES',
    label: 'Last Mile Logistics Charges',
    value: 'LAST_MILE_LOGISTICS_CHARGES',
  },
  { key: 'CUSTOMS_DUTY_CHARGES', label: 'Customs Duty Charges', value: 'CUSTOMS_DUTY_CHARGES' },
  {
    key: 'INLAND_LOGISTICS_CHARGES',
    label: 'Inland Logistics Charges',
    value: 'INLAND_LOGISTICS_CHARGES',
  },
];

export const miscChargeTypeList = [
  { key: 'PORT_CHARGES', label: 'Port Charges', value: 'PORT_CHARGES' },
  { key: 'OCEAN_FREIGHT_CHARGES', label: 'Ocean Freight Charges', value: 'OCEAN_FREIGHT_CHARGES' },
  { key: 'WAREHOUSE_CHARGES', label: 'Warehouse Charges', value: 'WAREHOUSE_CHARGES' },
  {
    key: 'LAST_MILE_LOGISTICS_CHARGES',
    label: 'Last Mile Logistics Charges',
    value: 'LAST_MILE_LOGISTICS_CHARGES',
  },
  { key: 'CUSTOMS_DUTY_CHARGES', label: 'Customs Duty Charges', value: 'CUSTOMS_DUTY_CHARGES' },
  {
    key: 'INLAND_LOGISTICS_CHARGES',
    label: 'Inland Logistics Charges',
    value: 'INLAND_LOGISTICS_CHARGES',
  },
  { key: 'COGS', label: 'COGS', value: 'COGS' },
  { key: 'PACKAGING', label: 'Packaging', value: 'PACKAGING' },
  { key: 'TESTING', label: 'Testing', value: 'TESTING' },
];

export const orderForList = [
  { key: '1', label: 'Inventory', value: 'inventory' },
  { key: '2', label: 'Customer', value: 'customer' },
];

export const orderTypeList = [
  { key: '1', label: 'Sample Order', value: 'SAMPLE' },
  { key: '2', label: 'Purchase Order', value: 'CUSTOMER_ORDER' },
];

export const modeOfDeliveryList = [
  { key: '1', label: 'By Road', value: 'Road' },
  { key: '2', label: 'By Sea', value: 'Sea' },
  { key: '3', label: 'By Air', value: 'Air' },
];

export const deliveyTermList = [
  { key: 'exb', label: 'EXW (Ex Works)', value: 'EXWORKS' },
  { key: 'fob', label: 'FOB (Free on board)', value: 'FOB' },
  { key: 'ddp', label: 'DDP (Delivery Duty Paid)', value: 'DDP' },
  { key: 'cif', label: 'CIF (Cost, Insurance, & Freight)', value: 'CIF' },
  { key: 'for', label: 'FOR (Free on road)', value: 'FOR' },
];

export const taxTypeList = [
  { key: '1', label: 'CGST', value: 'cgst' },
  { key: '2', label: 'SGST', value: 'sgst' },
  { key: '3', label: 'IGST', value: 'igst' },
];

export const countryList = [
  {
    dial_code: 1,
    flag: '🇨🇦',
    code: 'CA',
    name: 'Canada',
  },
  {
    dial_code: 1,
    flag: '🇺🇸',
    code: 'US',
    name: 'United States',
  },
  {
    dial_code: 1242,
    flag: '🇧🇸',
    code: 'BS',
    name: 'Bahamas',
  },
  {
    dial_code: 1246,
    flag: '🇧🇧',
    code: 'BB',
    name: 'Barbados',
  },
  {
    dial_code: 1264,
    flag: '🇦🇮',
    code: 'AI',
    name: 'Anguilla',
  },
  {
    dial_code: 1268,
    flag: '🇦🇬',
    code: 'AG',
    name: 'Antigua and Barbuda',
  },
  {
    dial_code: 1284,
    flag: '🇻🇬',
    code: 'VG',
    name: 'Virgin Islands, British',
  },
  {
    dial_code: 1340,
    flag: '🇻🇮',
    code: 'VI',
    name: 'Virgin Islands, U.S.',
  },
  {
    dial_code: 1441,
    flag: '🇧🇲',
    code: 'BM',
    name: 'Bermuda',
  },
  {
    dial_code: 1473,
    flag: '🇬🇩',
    code: 'GD',
    name: 'Grenada',
  },
  {
    dial_code: 1649,
    flag: '🇹🇨',
    code: 'TC',
    name: 'Turks and Caicos Islands',
  },
  {
    dial_code: 1664,
    flag: '🇲🇸',
    code: 'MS',
    name: 'Montserrat',
  },
  {
    dial_code: 1670,
    flag: '🇲🇵',
    code: 'MP',
    name: 'Northern Mariana Islands',
  },
  {
    dial_code: 1671,
    flag: '🇬🇺',
    code: 'GU',
    name: 'Guam',
  },
  {
    dial_code: 1684,
    flag: '🇦🇸',
    code: 'AS',
    name: 'American Samoa',
  },
  {
    dial_code: 1758,
    flag: '🇱🇨',
    code: 'LC',
    name: 'Saint Lucia',
  },
  {
    dial_code: 1767,
    flag: '🇩🇲',
    code: 'DM',
    name: 'Dominica',
  },
  {
    dial_code: 1784,
    flag: '🇻🇨',
    code: 'VC',
    name: 'Saint Vincent and the Grenadines',
  },
  {
    dial_code: 1849,
    flag: '🇩🇴',
    code: 'DO',
    name: 'Dominican Republic',
  },
  {
    dial_code: 1868,
    flag: '🇹🇹',
    code: 'TT',
    name: 'Trinidad and Tobago',
  },
  {
    dial_code: 1869,
    flag: '🇰🇳',
    code: 'KN',
    name: 'Saint Kitts and Nevis',
  },
  {
    dial_code: 1876,
    flag: '🇯🇲',
    code: 'JM',
    name: 'Jamaica',
  },
  {
    dial_code: 1939,
    flag: '🇵🇷',
    code: 'PR',
    name: 'Puerto Rico',
  },
  {
    dial_code: 20,
    flag: '🇪🇬',
    code: 'EG',
    name: 'Egypt',
  },
  {
    dial_code: 211,
    flag: '🇸🇸',
    code: 'SS',
    name: 'South Sudan',
  },
  {
    dial_code: 212,
    flag: '🇲🇦',
    code: 'MA',
    name: 'Morocco',
  },
  {
    dial_code: 213,
    flag: '🇩🇿',
    code: 'DZ',
    name: 'Algeria',
  },
  {
    dial_code: 216,
    flag: '🇹🇳',
    code: 'TN',
    name: 'Tunisia',
  },
  {
    dial_code: 218,
    flag: '🇱🇾',
    code: 'LY',
    name: 'Libyan Arab Jamahiriya',
  },
  {
    dial_code: 220,
    flag: '🇬🇲',
    code: 'GM',
    name: 'Gambia',
  },
  {
    dial_code: 221,
    flag: '🇸🇳',
    code: 'SN',
    name: 'Senegal',
  },
  {
    dial_code: 222,
    flag: '🇲🇷',
    code: 'MR',
    name: 'Mauritania',
  },
  {
    dial_code: 223,
    flag: '🇲🇱',
    code: 'ML',
    name: 'Mali',
  },
  {
    dial_code: 224,
    flag: '🇬🇳',
    code: 'GN',
    name: 'Guinea',
  },
  {
    dial_code: 225,
    flag: '🇨🇮',
    code: 'CI',
    name: "Cote d'Ivoire",
  },
  {
    dial_code: 226,
    flag: '🇧🇫',
    code: 'BF',
    name: 'Burkina Faso',
  },
  {
    dial_code: 227,
    flag: '🇳🇪',
    code: 'NE',
    name: 'Niger',
  },
  {
    dial_code: 228,
    flag: '🇹🇬',
    code: 'TG',
    name: 'Togo',
  },
  {
    dial_code: 229,
    flag: '🇧🇯',
    code: 'BJ',
    name: 'Benin',
  },
  {
    dial_code: 230,
    flag: '🇲🇺',
    code: 'MU',
    name: 'Mauritius',
  },
  {
    dial_code: 231,
    flag: '🇱🇷',
    code: 'LR',
    name: 'Liberia',
  },
  {
    dial_code: 232,
    flag: '🇸🇱',
    code: 'SL',
    name: 'Sierra Leone',
  },
  {
    dial_code: 233,
    flag: '🇬🇭',
    code: 'GH',
    name: 'Ghana',
  },
  {
    dial_code: 234,
    flag: '🇳🇬',
    code: 'NG',
    name: 'Nigeria',
  },
  {
    dial_code: 235,
    flag: '🇹🇩',
    code: 'TD',
    name: 'Chad',
  },
  {
    dial_code: 236,
    flag: '🇨🇫',
    code: 'CF',
    name: 'Central African Republic',
  },
  {
    dial_code: 237,
    flag: '🇨🇲',
    code: 'CM',
    name: 'Cameroon',
  },
  {
    dial_code: 238,
    flag: '🇨🇻',
    code: 'CV',
    name: 'Cape Verde',
  },
  {
    dial_code: 239,
    flag: '🇸🇹',
    code: 'ST',
    name: 'Sao Tome and Principe',
  },
  {
    dial_code: 240,
    flag: '🇬🇶',
    code: 'GQ',
    name: 'Equatorial Guinea',
  },
  {
    dial_code: 241,
    flag: '🇬🇦',
    code: 'GA',
    name: 'Gabon',
  },
  {
    dial_code: 242,
    flag: '🇨🇬',
    code: 'CG',
    name: 'Congo',
  },
  {
    dial_code: 243,
    flag: '🇨🇩',
    code: 'CD',
    name: 'Congo, The Democratic Republic of the Congo',
  },
  {
    dial_code: 244,
    flag: '🇦🇴',
    code: 'AO',
    name: 'Angola',
  },
  {
    dial_code: 245,
    flag: '🇬🇼',
    code: 'GW',
    name: 'Guinea-Bissau',
  },
  {
    dial_code: 246,
    flag: '🇮🇴',
    code: 'IO',
    name: 'British Indian Ocean Territory',
  },
  {
    dial_code: 248,
    flag: '🇸🇨',
    code: 'SC',
    name: 'Seychelles',
  },
  {
    dial_code: 249,
    flag: '🇸🇩',
    code: 'SD',
    name: 'Sudan',
  },
  {
    dial_code: 250,
    flag: '🇷🇼',
    code: 'RW',
    name: 'Rwanda',
  },
  {
    dial_code: 251,
    flag: '🇪🇹',
    code: 'ET',
    name: 'Ethiopia',
  },
  {
    dial_code: 252,
    flag: '🇸🇴',
    code: 'SO',
    name: 'Somalia',
  },
  {
    dial_code: 253,
    flag: '🇩🇯',
    code: 'DJ',
    name: 'Djibouti',
  },
  {
    dial_code: 254,
    flag: '🇰🇪',
    code: 'KE',
    name: 'Kenya',
  },
  {
    dial_code: 255,
    flag: '🇹🇿',
    code: 'TZ',
    name: 'Tanzania, United Republic of Tanzania',
  },
  {
    dial_code: 256,
    flag: '🇺🇬',
    code: 'UG',
    name: 'Uganda',
  },
  {
    dial_code: 257,
    flag: '🇧🇮',
    code: 'BI',
    name: 'Burundi',
  },
  {
    dial_code: 258,
    flag: '🇲🇿',
    code: 'MZ',
    name: 'Mozambique',
  },
  {
    dial_code: 260,
    flag: '🇿🇲',
    code: 'ZM',
    name: 'Zambia',
  },
  {
    dial_code: 261,
    flag: '🇲🇬',
    code: 'MG',
    name: 'Madagascar',
  },
  {
    dial_code: 262,
    flag: '🇹🇫',
    code: 'TF',
    name: 'French Southern Territories',
  },
  {
    dial_code: 262,
    flag: '🇾🇹',
    code: 'YT',
    name: 'Mayotte',
  },
  {
    dial_code: 262,
    flag: '🇷🇪',
    code: 'RE',
    name: 'Reunion',
  },
  {
    dial_code: 263,
    flag: '🇿🇼',
    code: 'ZW',
    name: 'Zimbabwe',
  },
  {
    dial_code: 264,
    flag: '🇳🇦',
    code: 'NA',
    name: 'Namibia',
  },
  {
    dial_code: 265,
    flag: '🇲🇼',
    code: 'MW',
    name: 'Malawi',
  },
  {
    dial_code: 266,
    flag: '🇱🇸',
    code: 'LS',
    name: 'Lesotho',
  },
  {
    dial_code: 267,
    flag: '🇧🇼',
    code: 'BW',
    name: 'Botswana',
  },
  {
    dial_code: 268,
    flag: '🇸🇿',
    code: 'SZ',
    name: 'Swaziland',
  },
  {
    dial_code: 269,
    flag: '🇰🇲',
    code: 'KM',
    name: 'Comoros',
  },
  {
    dial_code: 27,
    flag: '🇿🇦',
    code: 'ZA',
    name: 'South Africa',
  },
  {
    dial_code: 290,
    flag: '🇸🇭',
    code: 'SH',
    name: 'Saint Helena, Ascension and Tristan Da Cunha',
  },
  {
    dial_code: 291,
    flag: '🇪🇷',
    code: 'ER',
    name: 'Eritrea',
  },
  {
    dial_code: 297,
    flag: '🇦🇼',
    code: 'AW',
    name: 'Aruba',
  },
  {
    dial_code: 298,
    flag: '🇫🇴',
    code: 'FO',
    name: 'Faroe Islands',
  },
  {
    dial_code: 299,
    flag: '🇬🇱',
    code: 'GL',
    name: 'Greenland',
  },
  {
    dial_code: 30,
    flag: '🇬🇷',
    code: 'GR',
    name: 'Greece',
  },
  {
    dial_code: 31,
    flag: '🇳🇱',
    code: 'NL',
    name: 'Netherlands',
  },
  {
    dial_code: 32,
    flag: '🇧🇪',
    code: 'BE',
    name: 'Belgium',
  },
  {
    dial_code: 33,
    flag: '🇫🇷',
    code: 'FR',
    name: 'France',
  },
  {
    dial_code: 34,
    flag: '🇪🇸',
    code: 'ES',
    name: 'Spain',
  },
  {
    dial_code: 345,
    flag: '🇰🇾',
    code: 'KY',
    name: 'Cayman Islands',
  },
  {
    dial_code: 350,
    flag: '🇬🇮',
    code: 'GI',
    name: 'Gibraltar',
  },
  {
    dial_code: 351,
    flag: '🇵🇹',
    code: 'PT',
    name: 'Portugal',
  },
  {
    dial_code: 352,
    flag: '🇱🇺',
    code: 'LU',
    name: 'Luxembourg',
  },
  {
    dial_code: 353,
    flag: '🇮🇪',
    code: 'IE',
    name: 'Ireland',
  },
  {
    dial_code: 354,
    flag: '🇮🇸',
    code: 'IS',
    name: 'Iceland',
  },
  {
    dial_code: 355,
    flag: '🇦🇱',
    code: 'AL',
    name: 'Albania',
  },
  {
    dial_code: 356,
    flag: '🇲🇹',
    code: 'MT',
    name: 'Malta',
  },
  {
    dial_code: 357,
    flag: '🇨🇾',
    code: 'CY',
    name: 'Cyprus',
  },
  {
    dial_code: 358,
    flag: '🇦🇽',
    code: 'AX',
    name: 'Åland Islands',
  },
  {
    dial_code: 358,
    flag: '🇫🇮',
    code: 'FI',
    name: 'Finland',
  },
  {
    dial_code: 359,
    flag: '🇧🇬',
    code: 'BG',
    name: 'Bulgaria',
  },
  {
    dial_code: 36,
    flag: '🇭🇺',
    code: 'HU',
    name: 'Hungary',
  },
  {
    dial_code: 370,
    flag: '🇱🇹',
    code: 'LT',
    name: 'Lithuania',
  },
  {
    dial_code: 371,
    flag: '🇱🇻',
    code: 'LV',
    name: 'Latvia',
  },
  {
    dial_code: 372,
    flag: '🇪🇪',
    code: 'EE',
    name: 'Estonia',
  },
  {
    dial_code: 373,
    flag: '🇲🇩',
    code: 'MD',
    name: 'Moldova',
  },
  {
    dial_code: 374,
    flag: '🇦🇲',
    code: 'AM',
    name: 'Armenia',
  },
  {
    dial_code: 375,
    flag: '🇧🇾',
    code: 'BY',
    name: 'Belarus',
  },
  {
    dial_code: 376,
    flag: '🇦🇩',
    code: 'AD',
    name: 'Andorra',
  },
  {
    dial_code: 377,
    flag: '🇲🇨',
    code: 'MC',
    name: 'Monaco',
  },
  {
    dial_code: 378,
    flag: '🇸🇲',
    code: 'SM',
    name: 'San Marino',
  },
  {
    dial_code: 379,
    flag: '🇻🇦',
    code: 'VA',
    name: 'Holy See (Vatican City State)',
  },
  {
    dial_code: 380,
    flag: '🇺🇦',
    code: 'UA',
    name: 'Ukraine',
  },
  {
    dial_code: 381,
    flag: '🇷🇸',
    code: 'RS',
    name: 'Serbia',
  },
  {
    dial_code: 382,
    flag: '🇲🇪',
    code: 'ME',
    name: 'Montenegro',
  },
  {
    dial_code: 383,
    flag: '🇽🇰',
    code: 'XK',
    name: 'Kosovo',
  },
  {
    dial_code: 385,
    flag: '🇭🇷',
    code: 'HR',
    name: 'Croatia',
  },
  {
    dial_code: 386,
    flag: '🇸🇮',
    code: 'SI',
    name: 'Slovenia',
  },
  {
    dial_code: 387,
    flag: '🇧🇦',
    code: 'BA',
    name: 'Bosnia and Herzegovina',
  },
  {
    dial_code: 389,
    flag: '🇲🇰',
    code: 'MK',
    name: 'North Macedonia',
  },
  {
    dial_code: 39,
    flag: '🇮🇹',
    code: 'IT',
    name: 'Italy',
  },
  {
    dial_code: 40,
    flag: '🇷🇴',
    code: 'RO',
    name: 'Romania',
  },
  {
    dial_code: 41,
    flag: '🇨🇭',
    code: 'CH',
    name: 'Switzerland',
  },
  {
    dial_code: 420,
    flag: '🇨🇿',
    code: 'CZ',
    name: 'Czech Republic',
  },
  {
    dial_code: 421,
    flag: '🇸🇰',
    code: 'SK',
    name: 'Slovakia',
  },
  {
    dial_code: 423,
    flag: '🇱🇮',
    code: 'LI',
    name: 'Liechtenstein',
  },
  {
    dial_code: 43,
    flag: '🇦🇹',
    code: 'AT',
    name: 'Austria',
  },
  {
    dial_code: 44,
    flag: '🇬🇬',
    code: 'GG',
    name: 'Guernsey',
  },
  {
    dial_code: 44,
    flag: '🇮🇲',
    code: 'IM',
    name: 'Isle of Man',
  },
  {
    dial_code: 44,
    flag: '🇯🇪',
    code: 'JE',
    name: 'Jersey',
  },
  {
    dial_code: 44,
    flag: '🇬🇧',
    code: 'GB',
    name: 'United Kingdom',
  },
  {
    dial_code: 45,
    flag: '🇩🇰',
    code: 'DK',
    name: 'Denmark',
  },
  {
    dial_code: 46,
    flag: '🇸🇪',
    code: 'SE',
    name: 'Sweden',
  },
  {
    dial_code: 47,
    flag: '🇧🇻',
    code: 'BV',
    name: 'Bouvet Island',
  },
  {
    dial_code: 47,
    flag: '🇳🇴',
    code: 'NO',
    name: 'Norway',
  },
  {
    dial_code: 47,
    flag: '🇸🇯',
    code: 'SJ',
    name: 'Svalbard and Jan Mayen',
  },
  {
    dial_code: 48,
    flag: '🇵🇱',
    code: 'PL',
    name: 'Poland',
  },
  {
    dial_code: 49,
    flag: '🇩🇪',
    code: 'DE',
    name: 'Germany',
  },
  {
    dial_code: 500,
    flag: '🇫🇰',
    code: 'FK',
    name: 'Falkland Islands (Malvinas)',
  },
  {
    dial_code: 500,
    flag: '🇬🇸',
    code: 'GS',
    name: 'South Georgia and the South Sandwich Islands',
  },
  {
    dial_code: 501,
    flag: '🇧🇿',
    code: 'BZ',
    name: 'Belize',
  },
  {
    dial_code: 502,
    flag: '🇬🇹',
    code: 'GT',
    name: 'Guatemala',
  },
  {
    dial_code: 503,
    flag: '🇸🇻',
    code: 'SV',
    name: 'El Salvador',
  },
  {
    dial_code: 504,
    flag: '🇭🇳',
    code: 'HN',
    name: 'Honduras',
  },
  {
    dial_code: 505,
    flag: '🇳🇮',
    code: 'NI',
    name: 'Nicaragua',
  },
  {
    dial_code: 506,
    flag: '🇨🇷',
    code: 'CR',
    name: 'Costa Rica',
  },
  {
    dial_code: 507,
    flag: '🇵🇦',
    code: 'PA',
    name: 'Panama',
  },
  {
    dial_code: 508,
    flag: '🇵🇲',
    code: 'PM',
    name: 'Saint Pierre and Miquelon',
  },
  {
    dial_code: 509,
    flag: '🇭🇹',
    code: 'HT',
    name: 'Haiti',
  },
  {
    dial_code: 51,
    flag: '🇵🇪',
    code: 'PE',
    name: 'Peru',
  },
  {
    dial_code: 52,
    flag: '🇲🇽',
    code: 'MX',
    name: 'Mexico',
  },
  {
    dial_code: 53,
    flag: '🇨🇺',
    code: 'CU',
    name: 'Cuba',
  },
  {
    dial_code: 54,
    flag: '🇦🇷',
    code: 'AR',
    name: 'Argentina',
  },
  {
    dial_code: 55,
    flag: '🇧🇷',
    code: 'BR',
    name: 'Brazil',
  },
  {
    dial_code: 56,
    flag: '🇨🇱',
    code: 'CL',
    name: 'Chile',
  },
  {
    dial_code: 57,
    flag: '🇨🇴',
    code: 'CO',
    name: 'Colombia',
  },
  {
    dial_code: 58,
    flag: '🇻🇪',
    code: 'VE',
    name: 'Venezuela, Bolivarian Republic of Venezuela',
  },
  {
    dial_code: 590,
    flag: '🇬🇵',
    code: 'GP',
    name: 'Guadeloupe',
  },
  {
    dial_code: 590,
    flag: '🇧🇱',
    code: 'BL',
    name: 'Saint Barthelemy',
  },
  {
    dial_code: 590,
    flag: '🇲🇫',
    code: 'MF',
    name: 'Saint Martin',
  },
  {
    dial_code: 591,
    flag: '🇧🇴',
    code: 'BO',
    name: 'Bolivia, Plurinational State of bolivia',
  },
  {
    dial_code: 592,
    flag: '🇬🇾',
    code: 'GY',
    name: 'Guyana',
  },
  {
    dial_code: 593,
    flag: '🇪🇨',
    code: 'EC',
    name: 'Ecuador',
  },
  {
    dial_code: 594,
    flag: '🇬🇫',
    code: 'GF',
    name: 'French Guiana',
  },
  {
    dial_code: 595,
    flag: '🇵🇾',
    code: 'PY',
    name: 'Paraguay',
  },
  {
    dial_code: 596,
    flag: '🇲🇶',
    code: 'MQ',
    name: 'Martinique',
  },
  {
    dial_code: 597,
    flag: '🇸🇷',
    code: 'SR',
    name: 'Suriname',
  },
  {
    dial_code: 598,
    flag: '🇺🇾',
    code: 'UY',
    name: 'Uruguay',
  },
  {
    dial_code: 599,
    flag: '🇳🇱',
    code: 'AN',
    name: 'Netherlands Antilles',
  },
  {
    dial_code: 60,
    flag: '🇲🇾',
    code: 'MY',
    name: 'Malaysia',
  },
  {
    dial_code: 61,
    flag: '🇦🇺',
    code: 'AU',
    name: 'Australia',
  },
  {
    dial_code: 61,
    flag: '🇨🇽',
    code: 'CX',
    name: 'Christmas Island',
  },
  {
    dial_code: 61,
    flag: '🇨🇨',
    code: 'CC',
    name: 'Cocos (Keeling) Islands',
  },
  {
    dial_code: 62,
    flag: '🇮🇩',
    code: 'ID',
    name: 'Indonesia',
  },
  {
    dial_code: 63,
    flag: '🇵🇭',
    code: 'PH',
    name: 'Philippines',
  },
  {
    dial_code: 64,
    flag: '🇳🇿',
    code: 'NZ',
    name: 'New Zealand',
  },
  {
    dial_code: 64,
    flag: '🇵🇳',
    code: 'PN',
    name: 'Pitcairn',
  },
  {
    dial_code: 65,
    flag: '🇸🇬',
    code: 'SG',
    name: 'Singapore',
  },
  {
    dial_code: 66,
    flag: '🇹🇭',
    code: 'TH',
    name: 'Thailand',
  },
  {
    dial_code: 670,
    flag: '🇹🇱',
    code: 'TL',
    name: 'Timor-Leste',
  },
  {
    dial_code: 672,
    flag: '🇦🇶',
    code: 'AQ',
    name: 'Antarctica',
  },
  {
    dial_code: 672,
    flag: '🇭🇲',
    code: 'HM',
    name: 'Heard Island and Mcdonald Islands',
  },
  {
    dial_code: 672,
    flag: '🇳🇫',
    code: 'NF',
    name: 'Norfolk Island',
  },
  {
    dial_code: 673,
    flag: '🇧🇳',
    code: 'BN',
    name: 'Brunei Darussalam',
  },
  {
    dial_code: 674,
    flag: '🇳🇷',
    code: 'NR',
    name: 'Nauru',
  },
  {
    dial_code: 675,
    flag: '🇵🇬',
    code: 'PG',
    name: 'Papua New Guinea',
  },
  {
    dial_code: 676,
    flag: '🇹🇴',
    code: 'TO',
    name: 'Tonga',
  },
  {
    dial_code: 677,
    flag: '🇸🇧',
    code: 'SB',
    name: 'Solomon Islands',
  },
  {
    dial_code: 678,
    flag: '🇻🇺',
    code: 'VU',
    name: 'Vanuatu',
  },
  {
    dial_code: 679,
    flag: '🇫🇯',
    code: 'FJ',
    name: 'Fiji',
  },
  {
    dial_code: 680,
    flag: '🇵🇼',
    code: 'PW',
    name: 'Palau',
  },
  {
    dial_code: 681,
    flag: '🇼🇫',
    code: 'WF',
    name: 'Wallis and Futuna',
  },
  {
    dial_code: 682,
    flag: '🇨🇰',
    code: 'CK',
    name: 'Cook Islands',
  },
  {
    dial_code: 683,
    flag: '🇳🇺',
    code: 'NU',
    name: 'Niue',
  },
  {
    dial_code: 685,
    flag: '🇼🇸',
    code: 'WS',
    name: 'Samoa',
  },
  {
    dial_code: 686,
    flag: '🇰🇮',
    code: 'KI',
    name: 'Kiribati',
  },
  {
    dial_code: 687,
    flag: '🇳🇨',
    code: 'NC',
    name: 'New Caledonia',
  },
  {
    dial_code: 688,
    flag: '🇹🇻',
    code: 'TV',
    name: 'Tuvalu',
  },
  {
    dial_code: 689,
    flag: '🇵🇫',
    code: 'PF',
    name: 'French Polynesia',
  },
  {
    dial_code: 690,
    flag: '🇹🇰',
    code: 'TK',
    name: 'Tokelau',
  },
  {
    dial_code: 691,
    flag: '🇫🇲',
    code: 'FM',
    name: 'Micronesia, Federated States of Micronesia',
  },
  {
    dial_code: 692,
    flag: '🇲🇭',
    code: 'MH',
    name: 'Marshall Islands',
  },
  {
    dial_code: 7,
    flag: '🇰🇿',
    code: 'KZ',
    name: 'Kazakhstan',
  },
  {
    dial_code: 7,
    flag: '🇷🇺',
    code: 'RU',
    name: 'Russia',
  },
  {
    dial_code: 81,
    flag: '🇯🇵',
    code: 'JP',
    name: 'Japan',
  },
  {
    dial_code: 82,
    flag: '🇰🇷',
    code: 'KR',
    name: 'Korea, Republic of South Korea',
  },
  {
    dial_code: 84,
    flag: '🇻🇳',
    code: 'VN',
    name: 'Vietnam',
  },
  {
    dial_code: 850,
    flag: '🇰🇵',
    code: 'KP',
    name: "Korea, Democratic People's Republic of Korea",
  },
  {
    dial_code: 852,
    flag: '🇭🇰',
    code: 'HK',
    name: 'Hong Kong',
  },
  {
    dial_code: 853,
    flag: '🇲🇴',
    code: 'MO',
    name: 'Macao',
  },
  {
    dial_code: 855,
    flag: '🇰🇭',
    code: 'KH',
    name: 'Cambodia',
  },
  {
    dial_code: 856,
    flag: '🇱🇦',
    code: 'LA',
    name: 'Laos',
  },
  {
    dial_code: 86,
    flag: '🇨🇳',
    code: 'CN',
    name: 'China',
  },
  {
    dial_code: 880,
    flag: '🇧🇩',
    code: 'BD',
    name: 'Bangladesh',
  },
  {
    dial_code: 886,
    flag: '🇹🇼',
    code: 'TW',
    name: 'Taiwan',
  },
  {
    dial_code: 90,
    flag: '🇹🇷',
    code: 'TR',
    name: 'Türkiye',
  },
  {
    dial_code: 91,
    flag: '🇮🇳',
    code: 'IN',
    name: 'India',
  },
  {
    dial_code: 92,
    flag: '🇵🇰',
    code: 'PK',
    name: 'Pakistan',
  },
  {
    dial_code: 93,
    flag: '🇦🇫',
    code: 'AF',
    name: 'Afghanistan',
  },
  {
    dial_code: 94,
    flag: '🇱🇰',
    code: 'LK',
    name: 'Sri Lanka',
  },
  {
    dial_code: 95,
    flag: '🇲🇲',
    code: 'MM',
    name: 'Myanmar',
  },
  {
    dial_code: 960,
    flag: '🇲🇻',
    code: 'MV',
    name: 'Maldives',
  },
  {
    dial_code: 961,
    flag: '🇱🇧',
    code: 'LB',
    name: 'Lebanon',
  },
  {
    dial_code: 962,
    flag: '🇯🇴',
    code: 'JO',
    name: 'Jordan',
  },
  {
    dial_code: 963,
    flag: '🇸🇾',
    code: 'SY',
    name: 'Syrian Arab Republic',
  },
  {
    dial_code: 964,
    flag: '🇮🇶',
    code: 'IQ',
    name: 'Iraq',
  },
  {
    dial_code: 965,
    flag: '🇰🇼',
    code: 'KW',
    name: 'Kuwait',
  },
  {
    dial_code: 966,
    flag: '🇸🇦',
    code: 'SA',
    name: 'Saudi Arabia',
  },
  {
    dial_code: 967,
    flag: '🇾🇪',
    code: 'YE',
    name: 'Yemen',
  },
  {
    dial_code: 968,
    flag: '🇴🇲',
    code: 'OM',
    name: 'Oman',
  },
  {
    dial_code: 970,
    flag: '🇵🇸',
    code: 'PS',
    name: 'Palestinian Territory, Occupied',
  },
  {
    dial_code: 971,
    flag: '🇦🇪',
    code: 'AE',
    name: 'United Arab Emirates',
  },
  {
    dial_code: 972,
    flag: '🇮🇱',
    code: 'IL',
    name: 'Israel',
  },
  {
    dial_code: 973,
    flag: '🇧🇭',
    code: 'BH',
    name: 'Bahrain',
  },
  {
    dial_code: 974,
    flag: '🇶🇦',
    code: 'QA',
    name: 'Qatar',
  },
  {
    dial_code: 975,
    flag: '🇧🇹',
    code: 'BT',
    name: 'Bhutan',
  },
  {
    dial_code: 976,
    flag: '🇲🇳',
    code: 'MN',
    name: 'Mongolia',
  },
  {
    dial_code: 977,
    flag: '🇳🇵',
    code: 'NP',
    name: 'Nepal',
  },
  {
    dial_code: 98,
    flag: '🇮🇷',
    code: 'IR',
    name: 'Iran, Islamic Republic of Persian Gulf',
  },
  {
    dial_code: 992,
    flag: '🇹🇯',
    code: 'TJ',
    name: 'Tajikistan',
  },
  {
    dial_code: 993,
    flag: '🇹🇲',
    code: 'TM',
    name: 'Turkmenistan',
  },
  {
    dial_code: 994,
    flag: '🇦🇿',
    code: 'AZ',
    name: 'Azerbaijan',
  },
  {
    dial_code: 995,
    flag: '🇬🇪',
    code: 'GE',
    name: 'Georgia',
  },
  {
    dial_code: 996,
    flag: '🇰🇬',
    code: 'KG',
    name: 'Kyrgyzstan',
  },
  {
    dial_code: 998,
    flag: '🇺🇿',
    code: 'UZ',
    name: 'Uzbekistan',
  },
];

export const docStatusLabelMap = {
  PENDING_FOR_REVIEW: 'Pending For Review',
  PENDING_FOR_APPROVAL: 'Pending For Approval',
  APPROVED: 'Approved',
  SENT_TO_CUSTOMER: 'Sent To Customer',
};

export const disabledInvoiceTypesForUpload = [
  'MSTACK_INVOICE',
  'CHEMSTACK_TAX_INVOICE',
  'BILL_OF_LADING',
  'MSTACK_PACKING_LIST',
  'CHEMSTACK_PACKAGING_LIST',
  'MSTACK_TO_CHEMSTACK_PO',
  'CHEMSTACK_COMMERCIAL_INVOICE',
];

export const docStatus = {
  PENDING_FOR_REVIEW: 'PENDING_FOR_REVIEW',
  PENDING_FOR_APPROVAL: 'PENDING_FOR_APPROVAL',
  APPROVED: 'APPROVED',
  SENT_TO_CUSTOMER: 'SENT_TO_CUSTOMER',
};

export const SupplierFormBasicInfo = [
  {
    id: 'supplier-details',
    title: 'Supplier Details',
    fields: [
      {
        id: 'name',
        label: 'Supplier Name',
        type: 'input',
        inputType: 'text',
        rules: [{ required: true, message: 'Supplier name required!' }],
      },
      {
        id: ['address', 'country'],
        label: 'Country',
        required: true,
        type: 'country',
        rules: [
          {
            required: true,
            message: 'Country required!',
          },
        ],
      },
      {
        id: ['address', 'state'],
        label: 'State',
        required: true,
        type: 'input',
        inputType: 'text',
        rules: [
          {
            required: true,
            message: 'State required!',
          },
        ],
      },
      {
        id: ['address', 'city'],
        label: 'City',
        required: true,
        type: 'input',
        inputType: 'text',
        rules: [
          {
            required: true,
            message: 'City required!',
          },
        ],
      },
      {
        id: 'mobile',
        label: 'Phone Number',
        required: true,
        type: 'input',
        inputType: 'number',
        showprefix: true,
      },
      { id: ['address', 'postalCode'], label: 'Postal Code', required: true, type: 'input' },
      { id: 'email', label: 'Email Id', required: false, type: 'input', inputType: 'text' },
      { id: 'gstin', label: 'GSTIN', required: false, type: 'input', inputType: 'text' },
      { id: 'pan', label: 'PAN', required: false, type: 'input', inputType: 'text' },
      {
        id: ['address', 'street'],
        label: 'Corporate Address',
        required: true,
        type: 'textarea',
        rows: 4,
        // rules: [
        //   {
        //     required: true,
        //     message: 'Address required!',
        //   },
        // ],
      },
    ],
  },
];

export const OrderBookFormBasicInfo = [
  {
    id: 'supplier-details',
    title: 'Supplier Details',
    fields: [
      {
        id: 'orderType',
        label: 'Order Type',
        required: true,
        type: 'radio',
        optionListKey: 'orderTypeList',
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Order Type required!' }],
        disabled: ({ mode }) => mode === formModes.UPDATE  // Disable in edit mode
      },
      {
        id: 'customer',
        label: 'Customer Name',
        required: true,
        type: 'select',
        optionListKey: 'customerList',
        optionMapping: { label: 'name', value: 'id', key: 'id' }, // <-- custom mapping
        showIfFilledAndValueIn: { orderType: [] }, // only show if OrderType === 'A'
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Customer Name required!',
          },
        ],
      },
      {
        id: 'purchaseOrderNumber',
        label: 'PO Number',
        required: true,
        type: 'text',
        showIfFilledAndValueIn: { orderType: [] },
        style: { width: '200px', marginBottom: '0px' },
        getRules: ({ mode, formCustomerIdWatch, validatePONumber, orderType }) => [
          {
            required: true,
            message: 'PO Number required!',
          },
          {
            validator: async (_, value) => {
              if (mode === formModes.UPDATE) return Promise.resolve('');
              if (!formCustomerIdWatch) return Promise.reject('please add customer');
              if (!value) return Promise.reject('');
              
              // For SAMPLE order type, validate SR- format with WH suffix
              if (orderType === 'SAMPLE') {
                if (value.startsWith("SR-")) {
                  return Promise.resolve('');
                } else {
                  return Promise.reject('For sample orders, PO number must start with "SR-"');
                }
              }
              
              try {
                const isPoNumberValid = await validatePONumber(
                  formCustomerIdWatch,
                  value,
                  'CUSTOMER'
                );
                return isPoNumberValid
                  ? Promise.resolve('')
                  : Promise.reject('PO number already registered');
              } catch (err) {
                console.log('po validation error occurred ', err);
                return Promise.reject('Please provide a valid PO number');
              }
            },
          },
        ],
      },
      {
        id: 'purchaseOrderDate',
        label: 'PO Date',
        required: true,
        type: 'date',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: [] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'PO Date required!',
          },
          {
            validator: async (_, currentDate) => {
              return currentDate && currentDate <= dayjs().endOf('day') 
                ? Promise.resolve() 
                : Promise.reject('Date must be less than or equal to current date');
            }
          }
        ],
      },
      {
        id: ['paymentTerms', 'poPaymentTerms'],
        label: 'PO Payment Terms',
        required: true,
        type: 'select',
        optionListKey: 'paymentTermsDate',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Payment terms required!' }],
      },
      {
        id: ['paymentTerms', 'advanceAmount'],
        label: 'PO Advance (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          { required: true, message: 'Payment terms required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error('PO Advance amount required!'));
              }
              if (value > 100) {
                return Promise.reject(new Error(`Advance can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Advance can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'creditAmount'],
        label: 'PO Credit (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '200px', marginBottom: '0px' },
        formItemProps: {
          style: {
            '--form-error-width': '200px'
          }
        },
        rules: [
          { required: true, message: 'Credit Amount required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              console.log('credit amount ', value);
              if (value === undefined || value === null) {
                return Promise.reject(new Error());
              }
              if (value > 100) {
                return Promise.reject(new Error(`Credit amount can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Credit amount can't be negative!`));
              }
              const advanceAmount = Number(getFieldValue(['paymentTerms', 'advanceAmount']) || 0);
              if (Number(value) + advanceAmount !== 100) {
                return Promise.reject(new Error('Advance and Credit amount must sum to 100%'));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'creditorDays'],
        label: 'Credit Days',
        required: true,
        type: 'number',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          { required: true, message: 'Credit Days required!' },
          () => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error());
              }
              if (value < 0) {
                return Promise.reject(new Error(`Credit Days can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'remarks'],
        label: 'Payment Remarks',
        required: false,
        type: 'text',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ["CUSTOMER_ORDER"] },
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'buyerCurrency',
        label: 'Currency Type',
        required: true,
        type: 'select',
        optionListKey: 'currencyList',
        optionMapping: { label: 'label', value: 'value', key: 'value' }, // <-- custom mapping
        showIfFilledAndValueIn: { orderType: [] },
        style: { width: '200px', marginBottom: '0px' },
        // rules: [{ required: true, message: 'Currency Type required!' }],
      },
      {
        id: 'poValue',
        label: 'PO Value',
        required: true,
        type: 'number',
        showIfFilledAndValueIn: { orderType: ["CUSTOMER_ORDER"] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'PO Value required!',
          },
        ],
      },
      {
        id: 'taxPercent',
        label: 'Tax Percentage',
        required: false,
        type: 'number',
        showIfFilledAndValueIn: { orderType: ["CUSTOMER_ORDER"] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            validator(_, value) {
              if (value === undefined || value === null) return Promise.resolve();
              if (value > 100) {
                return Promise.reject(new Error('Tax percentage cannot be greater than 100%'));
              }
              if (value < 0) {
                return Promise.reject(new Error('Tax percentage cannot be negative'));
              }
              return Promise.resolve();
            }
          }
        ]
      },
      {
        id: 'category',
        label: 'Category',
        required: true,
        type: 'select',
        optionListKey: 'categoryOptions',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        showIfFilledAndValueIn: { orderType: [] },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Category required!',
          },
        ],
      },
      {
        id: 'billTo',
        label: 'Billing Address',
        required: true,
        type: 'textarea',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: [] }, // only show if OrderType === 'A'
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'deliveryAddress',
        label: 'Delivery Address',
        required: true,
        type: 'textarea',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: [] }, // only show if OrderType === 'A'
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Delivery Address required!',
          },
        ],
      },
      {
        id: 'purchaseOrderFile',
        label: 'PO File',
        required: true,
        type: 'file',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] }, // only show if OrderType === 'A'
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'PO File required!',
          },
        ],
      },
      {
        id: 'countryOfDelivery',
        label: 'Country of Delivery',
        required: false,
        type: 'text',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['SAMPLE'] },
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'sampleRequestedBy',
        label: 'Sample Requested By',
        required: false,
        type: 'select',
        showprefix: true,
        optionListKey: 'sampleRequestedByList',
        optionMapping: { label: 'label', value: 'value', key: 'key' },
        showIfFilledAndValueIn: { orderType: ['SAMPLE'] },
        style: { width: '200px', marginBottom: '0px' },
      },
    ],
  },
];

export const DispatchOrderFormBasicInfo = [
  {
    id: 'supplier-details',
    title: 'Supplier Details',
    fields: [
      {
        id: 'shipmentCreatedAtDate',
        label: 'Shipment Created On',
        required: true,
        type: 'date',
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'consignee',
        label: 'Consignee Details',
        required: true,
        type: 'text',
        showprefix: true,
        disable : false,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'notifyParty',
        label: 'Notify Party Details',
        required: true,
        type: 'select',
        optionListKey: 'notifyPartyList',
        optionMapping: { label: 'label', value: 'value', key: 'key' },
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Notify Party Details required!'
          }
        ]
      },
      {
        id: ['paymentTerms', 'advanceAmount'],
        label: 'PO Advance (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        disable : true,
        rules: [
          { required: true, message: 'Payment terms required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error('PO Advance amount required!'));
              }
              if (value > 100) {
                return Promise.reject(new Error(`Advance can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Advance can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'poPaymentTerms'],
        label: 'PO Payment Terms',
        required: true,
        type: 'text',
        showprefix: true,
        disable : true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'PO Payment Terms required!',
          },
        ],
      },
      {
        id: ['paymentTerms', 'creditAmount'],
        label: 'Credit amount(in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        disable : true,
        rules: [
          {
            required: true,
            message: 'Credit Amount required!',
          },
        ],
      },
      {
        id: ['paymentTerms', 'creditorDays'],
        label: 'Credit Days',
        required: true,
        disable : true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Credit Days required!',
          },
        ],
      },
      // {
      //   id: ['paymentTerms', 'startDate'],
      //   label: 'Payment terms Based On?',
      //   required: true,
      //   type: 'select',
      //   optionListKey: 'paymentTermsDate',
      //   optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
      //   showprefix: true,
      //   style: { width: '200px', marginBottom: '0px' },
      //   disable : true,
      //   rules: [
      //     {
      //       required: true,
      //       message: 'Payment terms required!',
      //     },
      //   ],
      // },

      {
        id: 'buyerCurrency',
        label: 'Currency Type',
        required: true,
        type: 'select',
        // disable : true,
        optionListKey: 'currencyList',
        optionMapping: { label: 'label', value: 'value', key: 'value' }, // <-- custom mapping
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Currency Type required!' }],
      },
      {
        id: 'taxPercent',
        label: 'Tax Percentage',
        required: false,
        type: 'number',
        disable : true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            validator(_, value) {
              if (value === undefined || value === null) return Promise.resolve();
              if (value > 100) {
                return Promise.reject(new Error('Tax percentage cannot be greater than 100%'));
              }
              if (value < 0) {
                return Promise.reject(new Error('Tax percentage cannot be negative'));
              }
              return Promise.resolve();
            }
          }
        ]
      },
      {
        id: 'igstAmt',
        label: 'IGST Amount',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'palletWt',
        label: 'Pallet Wt (Chemstack)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'mstackPalletWt',
        label: 'Pallet Wt (Mstack)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'marksAndContainers',
        label: 'Marks & Containers',
        required: true,
        type: 'text',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'deliveryAddress',
        label: 'Delivery Address',
        required: true,
        type: 'textarea',
        showprefix: true,
        // showIfFilledAndValueIn: { orderType: [] }, // only show if OrderType === 'A'
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Delivery Address required!',
          },
        ],
      },
      {
        id: 'remarks',
        label: 'Remarks',
        required: true,
        type: 'textarea',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
    ],
  },
];

export const SupplierOrderBookFormBasicInfo = [
  {
    id: 'supplier-details',
    title: 'Supplier Details',
    fields: [
      {
        id: 'supplier',
        label: 'Supplier Name',
        required: true,
        type: 'select',
        optionListKey: 'supplierList',
        optionMapping: { label: 'name', value: 'id', key: 'id' }, // <-- custom mapping
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Supplier required!' }],
      },
      {
        id: 'supplierRefNumber',
        label: 'Supplier Ref. No.',
        required: true,
        type: 'text',
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'quotationNumber',
        label: 'Quotation No.',
        required: true,
        type: 'text',
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'currencyType',
        label: 'Currency Type',
        required: true,
        type: 'select',
        optionListKey: 'currencyList',
        optionMapping: { label: 'label', value: 'value', key: 'value' }, // <-- custom mapping
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Currency Type required!' }],
      },
      {
        id: ['paymentTerms', 'poPaymentTerms'],
        label: 'PO Payment Terms',
        required: true,
        type: 'select',
        optionListKey: 'paymentTermsDate',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Payment terms required!' }],
      },
      {
        id: ['paymentTerms', 'advanceAmount'],
        label: 'PO Advance (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          { required: true, message: 'Payment terms required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error('PO Advance amount required!'));
              }
              if (value > 100) {
                return Promise.reject(new Error(`Advance can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Advance can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'creditAmount'],
        label: 'PO Credit (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        formItemProps: {
          style: {
            '--form-error-width': '200px'
          }
        },
        rules: [
          { required: true, message: 'Credit Amount required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              console.log('credit amount ', value);
              if (value === undefined || value === null) {
                return Promise.reject(new Error());
              }
              if (value > 100) {
                return Promise.reject(new Error(`Credit amount can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Credit amount can't be negative!`));
              }
              const advanceAmount = Number(getFieldValue(['paymentTerms', 'advanceAmount']) || 0);
              if (Number(value) + advanceAmount !== 100) {
                return Promise.reject(new Error('Advance and Credit amount must sum to 100%'));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'creditorDays'],
        label: 'Credit Days',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          { required: true, message: 'Credit Days required!' },
          () => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error());
              }
              if (value < 0) {
                return Promise.reject(new Error(`Credit Days can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        id: ['paymentTerms', 'remarks'],
        label: 'Payment Remarks',
        required: false,
        type: 'text',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'mrd',
        label: 'Material Ready Date',
        required: true,
        type: 'date',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Material Ready Date required!',
          },
        ],
      },
      {
        id: 'billingAddress',
        label: 'Billing Address',
        required: true,
        type: 'select',
        optionListKey: 'billingAddressList',
        optionMapping: { label: 'label', value: 'value', key: 'value' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Billing Address required!',
          },
        ],
      },
      {
        id: 'modeOfDelivery',
        label: 'Mode Of Delivery',
        required: true,
        type: 'select',
        optionListKey: 'modeOfDeliveryList',
        optionMapping: { label: 'label', value: 'value', key: 'value' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'deliveryTerm',
        label: 'Delivery Term',
        required: true,
        type: 'select',
        optionListKey: 'deliveyTermList',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'deliveryLocation',
        label: 'Delivery Location',
        required: true,
        type: 'text',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'pickupLocation',
        label: 'Pickup Location',
        required: true,
        type: 'text',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'materialSpecFile',
        label: 'Material SpecFile',
        required: true,
        type: 'file',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [{ required: true, message: 'Material SpecFile required!' }],
      },
      {
        id: 'partialShipment',
        label: 'Partial Shipment',
        required: true,
        type: 'switch',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '50px', marginBottom: '0px' },
      },
      {
        id: 'transShipment',
        label: 'Trans Shipment',
        required: true,
        type: 'switch',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '50px', marginBottom: '0px' },
      },
      {
        id: 'excludeDefaultTc',
        label: 'Disable default t&c',
        required: true,
        type: 'switch',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '50px', marginBottom: '0px' },
      },
      {
        id: 'shippingAddress',
        label: 'Shipping Address',
        required: true,
        type: 'textarea',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Shipping Address required!',
          },
        ],
      },
      {
        id: 'additionalCondition',
        label: 'Additional Condition',
        required: true,
        type: 'textarea',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'termAndCondition',
        label: 'Terms & Condition',
        required: true,
        type: 'textarea',
        showprefix: true,
        showIfFilledAndValueIn: { orderType: ['CUSTOMER_ORDER'] },
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id:'tax',
        label: 'Tax',
        required: true,
        type: 'tax',
        optionListKey: 'taxTypeList',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id:'deliverySchedule',
        label: 'Delivery Schedule',
        required: true,
        type: 'deliverySchedule',
        optionListKey: 'taxTypeList',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      }
    ],
  },
];

export const SupplierDispatchOrderFormBasicInfo = [
  {
    id: 'supplier-details',
    title: 'Supplier Details',
    fields: [
      {
        id: 'arnNumber',
        label: 'Supplier ARN Number',
        required: true,
        type: 'number',
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: 'shipmentDate',
        label: 'Shipment date ( Sailing Date )',
        required: true,
        type: 'date',
        style: { width: '200px', marginBottom: '0px' },
      },
      {
        id: ['paymentTerms', 'poPaymentTerms'],
        label: 'Payment terms based on?',
        required: true,
        type: 'select',
        optionListKey: 'paymentTermsDate',
        optionMapping: { label: 'label', value: 'value', key: 'key' }, // <-- custom mapping
        showprefix: true,
        disable : true,
        style: { width: '200px', marginBottom: '0px' },
        rules: [
          {
            required: true,
            message: 'Payment terms required!',
          },
        ],
      },
      {
        id: ['paymentTerms', 'creditAmount'],
        label: 'Credit amount(in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        disable : true,
        rules: [
          {
            required: true,
            message: 'Credit Amount required!',
          },
        ],
      },
      {
        id: ['paymentTerms', 'creditorDays'],
        label: 'Credit Days',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        disable : true,
        rules: [
          {
            required: true,
            message: 'Credit Days required!',
          },
        ],
      },
      {
        id: ['paymentTerms', 'advanceAmount'],
        label: 'PO Advance (in %)',
        required: true,
        type: 'number',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
        disable : true,
        rules: [
          { required: true, message: 'Payment terms required!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (value === undefined || value === null) {
                return Promise.reject(new Error('PO Advance amount required!'));
              }
              if (value > 100) {
                return Promise.reject(new Error(`Advance can't be greater than 100!`));
              }
              if (value < 0) {
                return Promise.reject(new Error(`Advance can't be negative!`));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
    
      {
        id: 'remarks',
        label: 'Remarks',
        required: true,
        type: 'textarea',
        showprefix: true,
        style: { width: '200px', marginBottom: '0px' },
      },
    ],
  },
];

export const SupplierOrderBookDocumentGroups = [
  { name: 'COA', required: false },
  { name: 'TDS', required: true },
  { name: 'SDS', required: true },
  { name: 'MOA', required: false },
  { name: 'Other', required: false },
];

export const notifyPartyList = [
  { label: 'Chemstack', value: 'CHEMSTACK', key: 'CHEMSTACK' },
  { label: 'Mstack', value: 'MSTACK', key: 'MSTACK' },
  { label: 'Others', value: 'others', key: 'OTHERS' },
];

export const productLabelTypes = [
  { value: 'NEUTRAL', label: 'Neutral label' },
  { value: 'CHEMSTACK', label: 'Chemstack label' },
  { value: 'MSTACK', label: 'Mstack label' },
  { value: 'CUSTOMER', label: 'Customer label' },
  { label: 'Others', value: 'others', key: 'OTHERS' },
];

export const sampleRequestedByList = [
  { label: 'Internal', value: 'internal', key: 'internal' },
  { label: 'Customer', value: 'customer', key: 'customer' }
];
