import React from 'react';
import { Card , Table } from 'antd';
import PropTypes from 'prop-types';

const COST_TYPE_MAPPING = {
  'Supplier Invoice': 'Cost Price From Supplier',
  'First mile logistics': 'First Mile Logistics (If Applicable)',
  'Sea Freight': 'Sea Freight (If Applicable)',
  'Last mile logistics': 'Last Mile Logistics (If Applicable)',
};

const MarginAnalysisTable = ({
  currency,
  marginData,
  targetContributionMargin = 0,
  masterCurrency,
  overallMarginDeviation
}) => {

  const formatCurrency = amount => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatCurrencyWithCode = (amount, currencyCode) => {
    if (!currencyCode) return formatCurrency(amount); // fallback to default
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2, // Add this to round to 2 decimal places
    }).format(amount);
  };

  const columns = [
    {
      title: `Cost Type`,
      dataIndex: 'costType',
      key: 'costType',
      onHeaderCell: () => ({
        style: {
          fontSize: '18px',
          fontWeight: 600,
          padding: '16px 8px'
        }
      }),
      render: text => (
        <span style={{ fontWeight: 500 }}>
          {COST_TYPE_MAPPING[text] || text}
        </span>
      ),
    },
    {
      title: `RFQ Value (${masterCurrency})`,
      dataIndex: 'rfqValue',
      key: 'rfqValue',
      align: 'right',
      onHeaderCell: () => ({
        style: {
          fontSize: '17px',
          fontWeight: 600,
          padding: '16px 8px'
        }
      }),
      render: (value, record) => {
      if (value > 0) {
        return value?.toFixed(2);
      }
      return '-';
    },
    },
    {
      title: `Invoice Value (${masterCurrency})`,
      dataIndex: 'invoiceValue',
      key: 'invoiceValue',
      align: 'right',
      onHeaderCell: () => ({
        style: {
          fontSize: '18px',
          fontWeight: 600,
          padding: '16px 8px'
        }
      }),
      render: (value, record) => {
        if (value > 0) {
          const currencyToUse = masterCurrency; // Use record's currency or fallback
          return value?.toFixed(2);

        }
        return 'Pending';
      },
    },
    {
      title: `Variance (${masterCurrency})`,
      key: 'variance',
      align: 'right',
      onHeaderCell: () => ({
        style: {
          fontSize: '18px',
          fontWeight: 600,
          padding: '16px 8px'
        }
      }),
      render: record => {
        if (record.invoiceValue > 0 && record?.rfqValue > 0) {
          return record?.variance?.toFixed(2);
        }
        return '-';
      },
    },
    {
      title: '% Deviation',
      key: 'deviation',
      align: 'right',
      onHeaderCell: () => ({
        style: {
          fontSize: '18px',
          fontWeight: 600,
          padding: '16px 8px'
        }
      }),
      render: record => {
        if (record.invoiceValue > 0 && record?.rfqValue > 0 && record?.deviation !== null && record?.deviation !== undefined) {
          const deviationValue = record.deviation.toFixed(2);
          const isPositive = record.deviation >= 0;
          
          return (
            <span style={{ 
              color: isPositive ? '#52c41a' : '#ff4d4f',
              fontWeight: '500'
            }}>
              {isPositive ? '+' : ''}{deviationValue}%
            </span>
          );
        }
        return "-";
      },
    }
  ];

  const dataSource = [...marginData.map((item, index) => ({ ...item, key: index }))];

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>Margin Analysis</span>
          <div style={{ display: 'flex', gap: '24px', fontSize: '14px' }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#8c8c8c' }}>Projected Margin</div>
              <div style={{ fontWeight: 600, color: '#1890ff' }}> {targetContributionMargin && parseFloat(targetContributionMargin?.toFixed(2))} %</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#8c8c8c' }}>Actual Margin</div>
              <div
                style={{
                  fontWeight: 600,
                  color: overallMarginDeviation ? '#1890ff' : '#ff4d4f',
                }}
              >
                {overallMarginDeviation && parseFloat(overallMarginDeviation?.toFixed(2))} %
              </div>
            </div>
          </div>
        </div>
      }
      style={{ width: '100%', maxWidth: '56rem', margin: 'auto', marginTop: '5rem' }}
    >
      <Table
        columns={columns}
        dataSource={dataSource} // No summary row here
        pagination={false}
        scroll={{ x: true }}
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row
              style={{
                borderTop: '2px solid #d9d9d9',
                fontWeight: 600,
                backgroundColor: '#fafafa',
              }}
            >
              <Table.Summary.Cell index={0}>Total</Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                {targetContributionMargin && parseFloat(targetContributionMargin?.toFixed(2))} %
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2} align="right"></Table.Summary.Cell>
              <Table.Summary.Cell index={3} align="right"></Table.Summary.Cell>
              <Table.Summary.Cell index={4} align="right">
              {overallMarginDeviation && parseFloat(overallMarginDeviation?.toFixed(2))} %              </Table.Summary.Cell>
              {/* ... rest of summary cells */}
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </Card>
  );
};

MarginAnalysisTable.propTypes = {
  currency: PropTypes.string.isRequired,
  marginData: PropTypes.arrayOf(PropTypes.object).isRequired,
  targetContributionMargin: PropTypes.number,
  masterCurrency: PropTypes.string,
  overallMarginDeviation: PropTypes.number,
};

export default MarginAnalysisTable;
