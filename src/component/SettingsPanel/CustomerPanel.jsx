import React, { useEffect, useState } from 'react'
import <PERSON><PERSON>oa<PERSON> from '../Loaders/PageLoader'
import { getCustomerList, resetCustomerPassword } from '../../service/api/customerApi';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';

const CustomerPanel = () => {

  const [loading, setLoading] = useState(false);
  const [customerList, setCustomerList] = useState([]);

  const [form] = Form.useForm();
  const passwordWatch = Form.useWatch("password", form);
  const { api } = useNotificationContext();

  const handleCustomerResetPassword = (values) => {
    setLoading(true);
    resetCustomerPassword(values.customerId, values.password).then(()=>{
      setLoading(false);
      form.resetFields();
      api.success({
        messgae: 'Success',
        description: 'Sucessfully reset password',
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        )
      })
      
    }).catch((err)=>{
      // handle message using error
      setLoading(false);
      const errMsg = err?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    })
  }

  useEffect(() => {
    if (!customerList || !customerList.length) {
      setLoading(true);
      getCustomerList()
        .then(res => {
          if (res.data && res.data.content) {
            setCustomerList(res.data.content);
          }
          setLoading(false);
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [])

  return (
    <div
      style={{
        borderTop: '1px solid rgba(30, 30, 30, 0.1)'
      }}
      className='h-full flex justify-center bg-neutral-200'
    >
      <div className='m-[5%] p-8 w-3/5 h-fit bg-base-white rounded-lg'>
        {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
        <Form
          name="customer_reset_password"
          layout="vertical"
          scrollToFirstError
          preserve={false}
          form={form}
          size="middle"
          onFinish={handleCustomerResetPassword}
          validateTrigger="onBlur"
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="customerId" label="Customer Name">
                <Select showSearch optionFilterProp='label'>
                  {customerList?.map(customer => (
                    <Select.Option key={customer.id} value={customer.id} label={customer.name}>
                      {customer.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="password" label="Enter new Password" rules={[{ required: true }]}>
                <Input.Password placeholder="Input password" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="rePassword"
                label="Confirm new Password"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (value !== passwordWatch ) {
                        return Promise.reject('Password do not match');
                      }                    
                      return Promise.resolve();
                    },
                  },
                ]}              
              >
                <Input.Password placeholder="Confirm password" />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Reset Password
              </Button>
            </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
  )
}

export default CustomerPanel