/* eslint-disable react/react-in-jsx-scope */
import dayjs from 'dayjs';
import { DateFormat, customerSize, customerType, incotermsDataList } from '../constants/formConstant';
import { getCustomerData } from '../service/api/customerApi';
import { getProductById } from '../service/api/productApi';
import { getSupplierData } from '../service/api/supplierService';
import { validateOrderPONumber } from '../service/api/orderBookApi';
import { validateSupplierOrderPONumber } from '../service/api/supplierOrderBookApi';
import ObjectUtil from './objectUtil';
import { Button, Card, DatePicker, Form, Input, InputNumber, Select } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import FileUpload from '../component/FileUpload';

export const getCustomerDisplayValue = (key, sourceList) => {
  const [matchedItem] = sourceList.filter(item => item.value === key);
  return matchedItem?.label;
};

export const getTotalOrderValue = order => {
  const total = order.products.reduce(
    (total, product) => total + product.quantity * product.price,
    0
  );
  return total ? total.toFixed(2) : total;
};
export const getPackagingDetailFromId = (id, packagingList) => {
  if (id && packagingList?.length) {
    const [item] = packagingList.filter(pkgItem => pkgItem.id === id);
    return item;
  }
  return null;
};

export const formatCustomerData = customerData => {
  return customerData?.length
    ? customerData.map(customer => ({
        ...customer,
        key: customer.id,
        type: getCustomerDisplayValue(customer.type, customerType),
        size: getCustomerDisplayValue(customer.size, customerSize),
        country: customer?.address?.country,
      }))
    : [];
};

export const formatOrderBookList = orderBookData => {
  let newOrderBookList = [];
  if (orderBookData?.length) {
    // for (let orderIndx = 0; orderIndx < orderBookData.length; orderIndx++) {
    //   if (orderBookData[orderIndx]?.products?.length)
    //     for (let prodIndx = 0; prodIndx < orderBookData[orderIndx]?.products?.length; prodIndx++) {
    //       const orderBookTableEntry = {
    //         key: orderBookData[orderIndx].products[prodIndx].id,
    //         id: orderBookData[orderIndx].products[prodIndx].id,
    //         purchaseOrderNumber: orderBookData[orderIndx].purchaseOrderNumber,
    //         customerName: orderBookData[orderIndx].customer.name,
    //         accountOwner: orderBookData[orderIndx].customer.accountOwner,
    //         productName: orderBookData[orderIndx].products[prodIndx].product.tradeName,
    //         quantity: orderBookData[orderIndx].products[prodIndx].quantity,
    //         packagingType: orderBookData[orderIndx].products[prodIndx].packaging?.type,
    //         purchaseOrderDate: orderBookData[orderIndx].purchaseOrderDate,
    //       };
    //       newOrderBookList.push(orderBookTableEntry);
    //     }
    // }
    newOrderBookList = orderBookData.map(order => ({
      key: order.id,
      id: order.id,
      status: order.status,
      purchaseOrderNumber: order.purchaseOrderNumber,
      customerName: order?.customer?.name,
      sourcingPOC: order.sourcingPOC,
      products: order.products.map(item => item.product?.tradeName),
      purchaseOrderDate: order.purchaseOrderDate,
      totalOrderValue: getTotalOrderValue(order),
      inventoryId: order?.inventoryId,
      inventoryName: order?.inventory?.name,
    }));
  }
  return newOrderBookList;
};

export const formatSupplierOrderBookList = orderBookData => {
  let newOrderBookList = [];
  if (orderBookData?.length) {
    for (let orderIndx = 0; orderIndx < orderBookData.length; orderIndx++) {
      if (orderBookData[orderIndx]?.products?.length)
        for (let prodIndx = 0; prodIndx < orderBookData[orderIndx]?.products?.length; prodIndx++) {
          const orderBookTableEntry = {
            orderId: orderBookData[orderIndx]?.id,
            // key : order_id+_+prod_id - to avoid duplicate key warnings
            key:
              orderBookData[orderIndx]?.id + '_' + orderBookData[orderIndx]?.products[prodIndx]?.id,
            id: orderBookData[orderIndx].products[prodIndx].id,
            purchaseOrderNumber: orderBookData[orderIndx].purchaseOrderNumber,
            supplierName: orderBookData[orderIndx].supplier.name,
            productName: orderBookData[orderIndx].products[prodIndx].product.tradeName,
            totalOrderValue: getTotalOrderValue(orderBookData[orderIndx]),
            margin: orderBookData[orderIndx].products[prodIndx].margin,
            purchaseOrderDate: orderBookData[orderIndx].purchaseOrderDate,
          };
          newOrderBookList.push(orderBookTableEntry);
        }
    }
  }
  return newOrderBookList;
};

export const formatOrderBookDataForForm = orderBookData => {
  let newOrderBookList = {
    ...orderBookData,
    customer: orderBookData.customer?.id,
    purchaseOrderDate: dayjs(orderBookData.purchaseOrderDate),
    products: [
      ...orderBookData.products.map(prod => ({
        ...prod,
        product: prod.product.id,
        deliveryDate: prod.deliveryDate ? dayjs(prod.deliveryDate) : '',
      })),
    ],
  };
  return newOrderBookList;
};

export const formatSupplierOrderBookDataForForm = orderBookData => {
  let newOrderBookList = {
    ...orderBookData,
    supplier: orderBookData.supplier?.id,
    purchaseOrderDate: dayjs(orderBookData.purchaseOrderDate),
    products: [
      ...orderBookData.products.map(prod => ({
        ...prod,
        product: prod.product.id,
        deliveryDate: prod.deliveryDate ? dayjs(prod.deliveryDate) : '',
      })),
    ],
    mrd: dayjs(orderBookData.mrd),
  };
  return newOrderBookList;
};

export const getOrderBookDataForApi = order => {
  const customerId = order?.customer;
  const prodListLen = order?.products?.length;
  let promiseList = [];
  if (customerId) {
    const customerPromise = getCustomerData(customerId);
    promiseList.push(customerPromise);
  }
  if (prodListLen) {
    const productsPromise = order?.products?.map(prod => getProductById(prod.product));
    promiseList = [...promiseList, ...productsPromise];
  }
  if (promiseList.length) return Promise.all(promiseList);
  console.log('Error in getting customer and product data from order');
  return null;
};

export const getSupplierOrderBookDataForApi = order => {
  const supplierId = order?.supplier;
  const prodListLen = order?.products?.length;
  if (supplierId && prodListLen) {
    const supplierPromise = getSupplierData(supplierId);
    const productsPromise = order?.products?.map(prod => getProductById(prod.product));
    return Promise.all([supplierPromise, ...productsPromise]);
  } else {
    console.log('Error in getting customer and product data from order');
    return null;
  }
};

export const formatDispatchOrderList = dispatchOrderList => {
  let newOrderBookList = [];
  if (dispatchOrderList?.length) {
    for (let orderIndx = 0; orderIndx < dispatchOrderList.length; orderIndx++) {
      if (dispatchOrderList[orderIndx]?.products?.length) {
        const orderBookTableEntry = {
          key: dispatchOrderList[orderIndx].id,
          orderId: dispatchOrderList[orderIndx].orderId,
          purchaseOrderNumber: dispatchOrderList[orderIndx].purchaseOrderNumber,
          customerName: dispatchOrderList[orderIndx].customer.name,
          accountOwner: dispatchOrderList[orderIndx].customer.accountOwner,
          status: dispatchOrderList[orderIndx].status,
          productNames: dispatchOrderList[orderIndx].products.map(prod => prod.product.tradeName),
          totalOrderValue: getTotalOrderValue(dispatchOrderList[orderIndx]),
          createdAt: dispatchOrderList[orderIndx].createdAt,
        };
        newOrderBookList.push(orderBookTableEntry);
      }
    }
  }
  return newOrderBookList;
};

export const formatSupplierDispatchOrderList = dispatchOrderList => {
  let newOrderBookList = [];
  if (dispatchOrderList?.length) {
    for (let orderIndx = 0; orderIndx < dispatchOrderList.length; orderIndx++) {
      if (dispatchOrderList[orderIndx]?.products?.length) {
        const orderBookTableEntry = {
          key: dispatchOrderList[orderIndx].id,
          orderId: dispatchOrderList[orderIndx].orderId,
          purchaseOrderNumber: dispatchOrderList[orderIndx].purchaseOrderNumber,
          supplierName: dispatchOrderList[orderIndx].supplier?.name,
          productNames: dispatchOrderList[orderIndx].products?.map(prod => prod.product.tradeName),
          totalOrderValue: getTotalOrderValue(dispatchOrderList[orderIndx]),
          createdAt: dispatchOrderList[orderIndx].createdAt,
        };
        newOrderBookList.push(orderBookTableEntry);
      }
    }
  }
  return newOrderBookList;
};

export const formatDispatchOrderDataForForm = dispatchOrderData => {
  let newDispatchOrderList = {
    ...dispatchOrderData,
    purchaseOrderDate: dispatchOrderData.purchaseOrderDate
      ? dayjs(dispatchOrderData.purchaseOrderDate)
      : '',
    deliveryDate: dispatchOrderData.deliveryDate ? dayjs(dispatchOrderData.deliveryDate) : '',
    shipmentDate: dispatchOrderData.shipmentDate ? dayjs(dispatchOrderData.shipmentDate) : '',
    invoiceDate: dispatchOrderData.invoiceDate ? dayjs(dispatchOrderData.invoiceDate) : '',
    expectedDeliveryDate: dispatchOrderData.expectedDeliveryDate
      ? dayjs(dispatchOrderData.expectedDeliveryDate)
      : '',
    shipmentCreatedAtDate: dispatchOrderData.shipmentCreatedAtDate
      ? dayjs(dispatchOrderData.shipmentCreatedAtDate)
      : '',
    invoiceSentOn: dispatchOrderData.invoiceSentOn ? dayjs(dispatchOrderData.invoiceSentOn) : '',
    dispatchDate: dispatchOrderData.dispatchDate ? dayjs(dispatchOrderData.dispatchDate) : '',
    customsReleasedOn: dispatchOrderData.customsReleasedOn
      ? dayjs(dispatchOrderData.customsReleasedOn)
      : '',
    products: [
      ...dispatchOrderData.products.map(prod => ({
        ...prod,
        product: prod.id,
      })),
    ],
  };
  return newDispatchOrderList;
};

export const formatInvoiceData = invoiceData => {
  let newInvoiceData = {
    ...invoiceData,
    invoiceDate: invoiceData.invoiceDate ? dayjs(invoiceData.invoiceDate) : '',
    paymentDate: invoiceData.paymentDate ? dayjs(invoiceData.paymentDate) : '',
    invoiceAmount: invoiceData.invoiceAmount,
    items: invoiceData.items?.map(item => {
      return { ...item };
    }),
  };
  if (invoiceData && invoiceData.blDate) {
    newInvoiceData = { ...newInvoiceData, blDate: dayjs(invoiceData.blDate) };
  }
  if (invoiceData && invoiceData.deliveryDate) {
    newInvoiceData = { ...newInvoiceData, deliveryDate: dayjs(invoiceData.deliveryDate) };
  }
  return newInvoiceData;
};

export const formatSupplierDispatchOrderDataForForm = dispatchOrderData => {
  let newDispatchOrderList = {
    ...dispatchOrderData,
    purchaseOrderDate: dispatchOrderData.purchaseOrderDate
      ? dayjs(dispatchOrderData.purchaseOrderDate)
      : '',
    deliveryDate: dispatchOrderData.deliveryDate ? dayjs(dispatchOrderData.deliveryDate) : '',
    shipmentDate: dispatchOrderData.shipmentDate ? dayjs(dispatchOrderData.shipmentDate) : '',
    bookingMadeOn: dispatchOrderData.bookingMadeOn ? dayjs(dispatchOrderData.bookingMadeOn) : '',
    qcApprovedOn: dispatchOrderData.qcApprovedOn ? dayjs(dispatchOrderData.qcApprovedOn) : '',
    loadingCompletedOn: dispatchOrderData.loadingCompletedOn
      ? dayjs(dispatchOrderData.loadingCompletedOn)
      : '',
    sampleReceivingDate: dispatchOrderData.sampleReceivingDate
      ? dayjs(dispatchOrderData.sampleReceivingDate)
      : '',
    dispatchToDestinationDate: dispatchOrderData.dispatchToDestinationDate
      ? dayjs(dispatchOrderData.dispatchToDestinationDate)
      : '',
    destinationRecievedDate: dispatchOrderData.destinationRecievedDate
      ? dayjs(dispatchOrderData.destinationRecievedDate)
      : '',
    dispatchFromDestinationOn: dispatchOrderData.dispatchFromDestinationOn
      ? dayjs(dispatchOrderData.dispatchFromDestinationOn)
      : '',
    supplierDispatchedOn: dispatchOrderData.supplierDispatchedOn
      ? dayjs(dispatchOrderData.supplierDispatchedOn)
      : '',
    etaForDestination: dispatchOrderData.etaForDestination
      ? dayjs(dispatchOrderData.etaForDestination)
      : '',

    invoiceDate: dispatchOrderData.invoiceDate ? dayjs(dispatchOrderData.invoiceDate) : '',
    expectedDeliveryDate: dispatchOrderData.expectedDeliveryDate
      ? dayjs(dispatchOrderData.expectedDeliveryDate)
      : '',
    shipmentCreatedAtDate: dispatchOrderData.shipmentCreatedAtDate
      ? dayjs(dispatchOrderData.shipmentCreatedAtDate)
      : '',
    products: [
      ...dispatchOrderData.products.map(prod => ({
        ...prod,
        product: prod.id,
      })),
    ],
  };
  return newDispatchOrderList;
};

export const getDispatchOrderDataForApi = (order,productList) => {
  console.log('order data for dispatch ',order,productList);
  const prodListLen = order?.products?.length;
  if (prodListLen) {
    const productsPromise = order?.products?.map(prod => {
      const [product] = productList
        ? productList.filter(item => item.id === prod.product)
        : [];
      console.log('product data for dispatch ',product);
      return getProductById(product.product.id);
    });
    return Promise.all([...productsPromise]);
  } else {
    console.log('Error in getting product data from order');
    return null;
  }
};

export const formatSupplierData = (supplierData, productList = [], packagingList = []) => {
  const formattedData = JSON.parse(JSON.stringify(supplierData));
  // console.log('given packaging list ',packagingList);
  formattedData.products?.forEach(product => {
    product.product = productList.find(e => e.id === product.product);
    product.packaging =
      product && product.packaging
        ? product.packaging.map(packageId => {
            // console.log('package id ',packageId);
            // if package is string
            if (!ObjectUtil.isObject(packageId)) {
              // parse product packaging
              // check if package in list , else add in packagingList
              const packageMeta = packagingList.find(packaging => packaging.id == packageId);
              // const packageArr = packageId.split('_');
              // console.log('prepared  package ',packageMeta, 'packaging id ',packageId, 'package list ',packagingList);
              const preparedPackage = {
                type: packageMeta.type,
                packSize: packageMeta.packSize,
                tareWeight: packageMeta.tareWeight,
                dimension: packageMeta.dimension,
              };
              return preparedPackage;
            } else return packageId;
          })
        : null;
  });
  return formattedData;
};

export const formatTaskData = record => {
  return {
    ...record,
    dueDate: dayjs(record.dueDate) || '',
    displayDate: dayjs(record.displayDate) || '',
  };
};

export const getEnumValueFromList = (key, list) => {
  if (!key || !list || !list.length) return null;
  const field = list.find(item => item?.value === key);
  return field && field.label ? field.label : key;
};

export const validatePONumber = async (userId, poNumber, userType) => {
  console.log('supplier id for po validation ', userId, userType);
  if (!userId || !poNumber || !userType) {
    return false;
  }
  let response = null;
  if (userType == 'CUSTOMER') {
    response = await validateOrderPONumber(userId, poNumber);
  } else {
    console.log('calling supplier ');
    response = await validateSupplierOrderPONumber(userId, poNumber);
  }
  return response && response.data && response.data.isValid;
};

export const getInputElementfromType = (
  inputData,
  isFormItem,
  inputStore,
  inputChangeHandler,
  isArray,
  arIndex,
  arrayItemValue
) => {
  switch (inputData.inputType) {
    case 'text':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please enter a valid value', type: 'string' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue[inputData.key] : inputData?.value
            }
          >
            <Input type="text" style={{ width: '100%' }} />
          </Form.Item>
        );
      else
        return (
          <Input
            key={inputData.key}
            type="text"
            value={inputStore[inputData.key] || inputData?.value}
            onChange={e => inputChangeHandler(e.target.value, inputData.key)}
            style={{ width: '100%' }}
          />
        );
    case 'number':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please enter a valid value', type: 'number' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue[inputData.key] : inputData?.value
            }
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        );
      else
        return (
          <InputNumber
            key={inputData.key}
            value={inputStore[inputData.key] || inputData?.value}
            onChange={e => inputChangeHandler(e.target.value, inputData.key)}
            style={{ width: '100%' }}
          />
        );
    case 'date':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please enter a valid value', type: 'date' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue?.[inputData.key] : inputData?.value
            }
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        );
      else
        return (
          <DatePicker
            key={inputData.key}
            format={DateFormat}
            value={dayjs(inputStore[inputData.key]) || inputData?.value}
            onChange={(date, dateString) => inputChangeHandler(dateString, inputData.key)}
            style={{ width: '100%' }}
          />
        );
    case 'file':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please input a valid file' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue[inputData.key] : inputData?.value
            }
          >
            <FileUpload
              category={inputData.category}
              meta={inputData.meta}
              maxFileLimit={inputData?.fileCount}
              accept={inputData?.fileType}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );
      else
        return (
          <FileUpload
            key={inputData.key}
            category={inputData.category}
            meta={inputData.meta}
            maxFileLimit={inputData?.fileCount}
            accept={inputData?.fileType}
            value={inputStore[inputData.key] || inputData?.value}
            onChange={e => {
              const [file] = e.target.value;
              inputChangeHandler(file, inputData.key);
            }}
          />
        );
    case 'dropdown':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please select a valid value' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue[inputData.key] : inputData?.value
            }
          >
            <Select options={inputData.options} mode={inputData?.mode} />
          </Form.Item>
        );
      else
        return (
          <Select
            key={inputData.key}
            options={inputData.options}
            value={inputStore[inputData.key] || inputData?.value}
            onChange={value => inputChangeHandler(value, inputData.key)}
          />
        );
    case 'textarea':
      if (isFormItem)
        return (
          <Form.Item
            key={inputData.key}
            label={inputData.label}
            name={isArray ? [arIndex, inputData.key] : inputData.key}
            rules={
              inputData.mandatory
                ? [{ required: true, message: 'Please enter a valid value' }]
                : null
            }
            initialValue={
              isArray && arrayItemValue ? arrayItemValue[inputData.key] : inputData?.value
            }
          >
            <Input.TextArea style={{ width: '100%' }} />
          </Form.Item>
        );
      else
        return (
          <Input.TextArea
            key={inputData.key}
            value={inputStore[inputData.key] || inputData?.value}
            onChange={e => inputChangeHandler(e.target.value, inputData.key)}
          />
        );
    // case 'multiTypeInputCard':
    //   if (isFormItem) {
    //     return (
    //       <Form.List
    //         name={inputData.key}
    //         rules={inputData.mandatory ? [
    //           {
    //             required: true,
    //             message: 'At least 1 item details is required!',
    //           },
    //         ] : null}
    //       >
    //         {(fields, { add, remove }, { errors }) => (
    //           <div
    //             style={{
    //               display: 'flex',
    //               rowGap: 16,
    //               flexDirection: 'column',
    //             }}
    //           >
    //             {fields.map((field) => (
    //               <Card
    //                 size="small"
    //                 title={`${inputData.label} ${field.name + 1}`}
    //                 key={field.key}
    //                 extra={
    //                   <CloseOutlined
    //                     onClick={() => {
    //                       remove(field.name);
    //                     }}
    //                   />
    //                 }
    //               >
    //                 {inputData.cardInputsList.map((item) => getInputElementfromType(item, true, inputData?.value?.[field.name]))}
    //               </Card>
    //             ))}
    //             <Button type="dashed" onClick={() => add()} block>
    //               + Add
    //             </Button>
    //             <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
    //           </div>
    //         )}
    //       </Form.List>
    //     );
    //   } else return null;
  }
};

export const getInputFiledsFromList = (
  inputList,
  isFormItem,
  inputStore,
  inputChangeHandler,
  disableList
) => {
  let inputFieldsList = [];
  inputFieldsList = inputList.map(item => {
    if (item.inputType === 'multiTypeInputCard') {
      return (
        <Form.List
          key={item.key}
          name={item.key}
          rules={
            item.mandatory
              ? [
                  {
                    required: true,
                    message: 'At least 1 item details is required!',
                  },
                ]
              : null
          }
        >
          {(fields, { add, remove }, { errors }) => (
            <div
              style={{
                display: 'flex',
                rowGap: 16,
                flexDirection: 'column',
              }}
            >
              {fields.map(field => (
                <Card
                  size="small"
                  title={`${item.label} ${field.name + 1}`}
                  key={field.key}
                  extra={
                    disableList ? null : (
                      <CloseOutlined
                        onClick={() => {
                          remove(field.name);
                        }}
                      />
                    )
                  }
                >
                  {item.cardInputsList.map(cardInput =>
                    getInputElementfromType(
                      cardInput,
                      true,
                      null,
                      null,
                      true,
                      field.name,
                      item?.value?.[field.name]
                    )
                  )}
                </Card>
              ))}
              {disableList ? null : (
                <Button type="dashed" onClick={() => add()} block>
                  + Add
                </Button>
              )}
              <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
            </div>
          )}
        </Form.List>
      );
    } else {
      const inputElement = getInputElementfromType(
        item,
        isFormItem,
        inputStore,
        inputChangeHandler,
        false
      );
      return inputElement;
    }
  });
  return inputFieldsList;
};

/**
 * Validates incoterms with specific required fields based on type
 */
export const validateIncoterms = (product) => {
  if (!product.incoterms) return { isValid: false, missingFields: ['Incoterms type and country'] };
  
  const { type, country, data } = product.incoterms;
  
  // Check if basic required fields exist
  if (!type || !country) {
    const missing = [];
    if (!type) missing.push('Incoterms type');
    if (!country) missing.push('Destination country');
    return { isValid: false, missingFields: missing };
  }
  
  // Get required fields for this incoterms type
  const requiredFields = incotermsDataList[type];
  if (!requiredFields) return { isValid: false, missingFields: ['Valid incoterms type'] };
  
  // Check if all required fields for this type are filled
  const missingFields = [];
  const allFieldsValid = requiredFields.every(field => {
    const fieldValue = data?.[field.name];
    let isValid = false;
    
    if (field.type === 'select') {
      isValid = fieldValue && fieldValue !== '';
    } else {
      isValid = fieldValue && fieldValue.toString().trim() !== '';
    }
    
    if (!isValid) {
      missingFields.push(field.label);
    }
    
    return isValid;
  });
  
  return { isValid: allFieldsValid, missingFields };
};
