:root aside.asideBar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  overflow: auto;
  height: 100vh;
  border-right: 1px solid rgba(35, 86, 138, 0.20);
  transition-duration: 0.5s;
}

.asideBar > div {
  display: flex;
  flex-direction: column;
}

.asideBar {
  padding: 10px 10px 10px 0;
}
.asideBar .logo > img {
  width: 100%;
  height: auto;
  object-fit: contain;;
}

.profileHolder {
  margin-top: auto;
  padding: 12px 0;
  border-top: 1px solid rgba(33, 42, 66, 0.10);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.profileHolder > span {
  margin-right: 8px;
}