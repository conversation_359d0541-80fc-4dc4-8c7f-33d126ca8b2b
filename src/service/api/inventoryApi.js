import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig';


export const getInventoryList = (pageSize, pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: pageSize || 1000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      name: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.inventorySearch, customerConfig);
};

export const getProductsFromInventory = (pageSize, pageNumber, filters, inventoryId,) => {
  const productConfig = {
    size: pageSize || 1000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      inventoryId: inventoryId || '',
      // "product.tradeName": product || '',
    },
  };
  return axiosInstance.post(apiEndpoints.inventoryProductSearch, productConfig);
}

export const fetchInventoryProductStockData = (productId) => {
  return axiosInstance.get(`${apiEndpoints.inventoryStock}/${productId}`);
}

export const fetchInventoryProductBatches = (pageSize, pageNumber, productId) => {
  const config = {
    inventoryProductId: productId,
    number: pageNumber || 0,
    size: pageSize || 100,
  }
  return axiosInstance.post(apiEndpoints.inventoryBatch, config);
}

export const fetchInventoryProductTransaction = (pageSize, pageNumber, productId) => {
  const config = {
    filters: {
      inventoryProductId: [productId],
    },
    number: pageNumber || 0,
    size: pageSize || 100,
  }
  return axiosInstance.post(apiEndpoints.inventoryTransactions, config);
}

export const getInventoryOutOrderForOrderbook = (orderId) => {
  return axiosInstance.get(`${apiEndpoints.fetchinventoryOutOrder}/${orderId}`);
}

export const addInventoryOutOrderForOrderBook = (orderData) => {
  return axiosInstance.post(apiEndpoints.inventoryOutOrder, orderData);
}

export const deleteInventoryOutOrder = (id) => {
  return axiosInstance.delete(`${apiEndpoints.inventoryOutOrder}/${id}`);
}

export const getAllocatedInventoryForOrderbook = (orderbookId) => {
  return axiosInstance.get(`${apiEndpoints.fetchOBAllocatedInventory}/${orderbookId}`);
}

export const checkInventoryForOrderbook = (orderbookId) => {
  return axiosInstance.get(`${apiEndpoints.checkInventory}/${orderbookId}`);
}

export const fetchInventoryAvailableUnits = (orderbookId) => {
  return axiosInstance.get(`${apiEndpoints.inventoryUnits}/${orderbookId}`);
}

export const allocateInventoryUnits = (orderbookId, allocatedData) => {
  return axiosInstance.post(`${apiEndpoints.inventoryUnits}/${orderbookId}`, allocatedData);
}

export const fetchInventoryForOrderBookProduct = (orderbookId, productId, packagingId) => {
  return axiosInstance.get(`${apiEndpoints.productInventoryUnits}/${orderbookId}/${productId}/${packagingId}`);
}

export const editAllocatedProductInventoryUnits = (orderbookId, allocatedData) => {
  return axiosInstance.put(`${apiEndpoints.inventoryUnits}/${orderbookId}`, allocatedData);
}

export const unallocatedInventoryFromOrderbook = (batchList) => {
  return axiosInstance.post(`${apiEndpoints.unallocateInventory}`, batchList);
}

