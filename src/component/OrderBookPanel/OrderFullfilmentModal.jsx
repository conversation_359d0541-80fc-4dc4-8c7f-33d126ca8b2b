import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Checkbox, Modal, Select } from 'antd';
import PageLoader from '../Loaders/PageLoader';
import { allocateInventoryUnits, checkInventoryForOrderbook, editAllocatedProductInventoryUnits, fetchInventoryAvailableUnits, fetchInventoryForOrderBookProduct, unallocatedInventoryFromOrderbook } from '../../service/api/inventoryApi';
import productMark from '../../assets/icons/product_mark.svg';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { useSelector } from 'react-redux';

const OrderFullfilmentModal = (props) => {

  const {
    visible, onCancel, addSupplierOrder, orderId, isProductEdit, productId, packagingId, unallocateView,
    allocatedUnitDetails,
  } = props;

  const [loading, setLoading] = useState(false);
  const [showAllocateUnitPanel, setShowAllocateUnitPanel] = useState((isProductEdit || unallocateView) ? true : false);
  const [checkInventoryData, setCheckInventoryData] = useState([]);
  const [productInventoryUnits, setProductIventoryUnits] = useState([]);
  const [unallocateSelection, setUnallocatedSelection] = useState({});

  const { api } = useNotificationContext();
  const user = useSelector(state => state.user);


  const cancelModal = () => {
    if (loading) return;
    onCancel();
  };
  const handleAddSupplierOrder = () => {
    cancelModal();
    addSupplierOrder();
  }
  const handleAllocatedUnit = (allocatedUnits, productId, orderBookId) => {
    
    setProductIventoryUnits((prevState) => {
      const newProductInventoryUnits = prevState.map((item) => {
        if (item?.productId === productId) {
          const newSOBs = item?.availableSOBs.map((orderItem) => {
            if (orderItem?.orderBookId === orderBookId) {
              return {
                ...orderItem,
                selectedUnits: allocatedUnits,
              }
            }
            return orderItem;
          });
          return {
            ...item,
            availableSOBs: newSOBs,
          };
        }
        return item;
      });
      return newProductInventoryUnits;
    });
  }
  const saveAllocateInventoryUnits = () => {
    setLoading(true);
    if (isProductEdit) {
      editAllocatedProductInventoryUnits(orderId, productInventoryUnits).then(() => {
        setLoading(false);
        onCancel(true);
      }).catch((err) => {
        setLoading(false);
        api.error({
          messgae: 'Error',
          description: err?.response?.data?.message || err?.message,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        });
      });
    } else if (unallocateView) {
      const unallocatedList = Object.keys(unallocateSelection).filter((id) => unallocateSelection[id]);
      unallocatedInventoryFromOrderbook(unallocatedList).then(() => {
        setLoading(false);
        onCancel(true);
      }).catch((err) => {
        setLoading(false);
        api.error({
          messgae: 'Error',
          description: err?.response?.data?.message || err?.message,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        });
      });
    } else {
      allocateInventoryUnits(orderId, productInventoryUnits).then(() => {
        setLoading(false);
        onCancel(true);
      }).catch((err) => {
        setLoading(false);
        api.error({
          messgae: 'Error',
          description: err?.response?.data?.message || err?.message,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        });
      });
    }

  }
  const getFooterButton = () => {
    if (showAllocateUnitPanel) {
      return [
        <Button key="cancel" onClick={cancelModal}>
          Cancel
        </Button>,
        <Button
          key="allocate unit"
          type="primary"
          onClick={saveAllocateInventoryUnits}
          loading={loading}
          disabled={!hasPermission(
            unallocateView ? userPermissionsList.unallocateInventory : userPermissionsList.allocateInventory, user.permissions
          )}
        >
          {unallocateView ? 'Unallocate' : 'Allocate'}
        </Button>
      ]
    } else {
      if (!checkInventoryData?.length) {
        return [
          <Button key="cancel" onClick={cancelModal}>
            Cancel
          </Button>,
          <Button
            key="add order"
            type="primary"
            onClick={handleAddSupplierOrder}
            disabled={!hasPermission(
              userPermissionsList.createsupplierOrderBook, user.permissions
            )}
          >
            Add Supplier Order
          </Button>
        ];
      } else {
        return [
          <Button
            key="add order"
            type="default"
            onClick={handleAddSupplierOrder}
            disabled={!hasPermission(
              userPermissionsList.createsupplierOrderBook, user.permissions
            )}
          >
            Skip & Add Supplier Order
          </Button>,
          <Button
            key="allocate unit"
            type="primary"
            onClick={() => setShowAllocateUnitPanel(true)}
            disabled={!hasPermission(
              userPermissionsList.allocateInventory, user.permissions
            )}
          >
            Yes, Allocate Inventory
          </Button>
        ];
      }
    }
  }
  const getModalTitle = () => {
    if (showAllocateUnitPanel) {
      return unallocateView ? 'Inventory Unallocation' : 'Inventory Allocation';
    }
    return checkInventoryData?.length ? 'Inventory already exists' : 'Inventory doesn\'t exists';
  }
  const getSelectOptions = (units) => {
    let optionsList = [];
    [...Array(units + 1).keys()].forEach((_, index) => {
      if(index) optionsList.push({
        key: index,
        value: index,
        label: index,
      })
    })
    return optionsList;
  }
  const handleUnallocateSelection = (e, id) => {
    const checkboxVal = e.target.checked;
    setUnallocatedSelection((prevState) => {
      return {
        ...prevState,
        [id]: checkboxVal,
      }
    })
  }
  const getBatchIdFromOrderBookId = (orderbookId) => {
    if (allocatedUnitDetails?.length) {
      const batchData = allocatedUnitDetails.find((item) => item.orderBookId === orderbookId);
      return batchData?.obBatchId;
    } 
    return;
  }

  useEffect(() => {
    if (orderId && !isProductEdit && !unallocateView) {
      setLoading(true);
      checkInventoryForOrderbook(orderId).then((res) => {
        if (res?.data?.inventoryProductList) {
          setCheckInventoryData(res.data.inventoryProductList);
          setLoading(false);
        }
      }).catch((err) => {
        setLoading(false);
        api.error({
          messgae: 'Error',
          description: err?.response?.data?.message || err?.message,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        });
      });
    }    
  }, [])
  useEffect(() => {
    if (showAllocateUnitPanel && orderId) {
      setLoading(true);
      if ((isProductEdit || unallocateView) && productId && packagingId) {
        fetchInventoryForOrderBookProduct(orderId, productId, packagingId).then((res) => {
          if(res?.data) {
            setProductIventoryUnits([res.data]);
            setLoading(false);
          }
        }).catch((err) => {
          setLoading(false);
          api.error({
            messgae: 'Error',
            description: err?.response?.data?.message || err?.message,
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            )
          });
        });
      } else {
        fetchInventoryAvailableUnits(orderId).then((res) => {
          if(res?.data) {
            setProductIventoryUnits(res.data);
            setLoading(false);
          }
        }).catch((err) => {
          setLoading(false);
          api.error({
            messgae: 'Error',
            description: err?.response?.data?.message || err?.message,
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            )
          });
        });
      }
    }
  }, [showAllocateUnitPanel])

  return (
    <Modal
      open={visible}
      title={getModalTitle()}
      onCancel={cancelModal}
      destroyOnClose={true}
      width="80vh"
      footer={getFooterButton()}
      styles={{
        body: {
          height: '50vh',
          maxHeight: '70vh',
          overflow: 'scroll',
        },
      }}
    >
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
      <div className="mt-3">
        {!showAllocateUnitPanel ? (
          <div>
            {checkInventoryData?.length ? (
              <div>
                <div className='mb-9'>
                  Following product(s) already exists in our inventory. 
                  Do you want to allocate the existing inventory to this purchase order?
                </div>
                <div>
                  {checkInventoryData.map((item) => (
                    <div key={item.id} className='flex items-center gap-2.5'>
                      <div>
                        <img src={productMark} alt="mark" />
                      </div>
                      <div>
                        <div className='text-text-black font-semibold'>{item?.product?.tradeName}, {item?.packaging?.packagingName}</div>
                        <div className='text-sm text-gray-500'>
                          <div>Total Available Units: <span className='text-text-black'>{item?.units}</span></div>
                          <div>Inventory: <span  className='text-text-black'>{item?.inventory?.name}</span></div>
                        </div>
                      </div>
                      
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                Either inventory for the product doesnt exist or the have already been allocated before.
                Please add supplier order or edit allocated inventory.
              </div>
            )}
          </div>
        ) : (
          <div>
            <div className='mb-5 text-sm'>
              <div className='font-semibold'>Inventory Details</div>
              <div className='text-gray-500'>
                {unallocateView ? 'Select units to unallocate' : 'Select number of units to be allocated to the purchase order'}
              </div>
            </div>
            <div>
              {productInventoryUnits.map((item) => (
                <div key={item?.productId}>
                  <div className='flex items-center gap-2.5'>
                    <div>
                      <img src={productMark} alt="mark" />
                    </div>
                    <div>
                      <div className='text-text-black font-semibold'>{item?.productName}, {item?.packagingName}</div>
                      {!unallocateView
                        ? <div className='text-sm text-gray-500'>Availablle units: <span className='text-text-black'>{item?.units}</span></div>
                        : null
                      }
                    </div>
                  </div>
                  <div>
                    {item?.availableSOBs?.map((orderItem) => (
                      <div
                        key={orderItem?.id}
                        style={{
                          padding: '20px 0',
                          borderBottom: '1px solid rgba(30, 30, 30, 0.20)',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: unallocateView ? 'center' : 'flex-start'
                        }}
                      >
                        <div>
                          <div className='font-medium'>{orderItem?.orderBookId}</div>
                          <div className='text-sm text-gray-500'>Supplier name: <span className='text-text-black'>{orderItem?.supplierName}</span></div>
                          <div className='text-sm text-gray-500'>{unallocateView ? 'Allocated units:' : 'Available unit:'} <span className='text-text-black'>{unallocateView ? orderItem?.selectedUnits : orderItem?.units}</span></div>
                        </div>
                        <div>
                          {unallocateView ? null : <div className='text-sm mb-1'>Allocated units</div>}
                          {unallocateView
                          ? <Checkbox
                              label
                              checked={unallocateSelection[getBatchIdFromOrderBookId(orderItem?.orderBookId)]}
                              onChange={(e) => handleUnallocateSelection(e, getBatchIdFromOrderBookId(orderItem?.orderBookId))}
                            >
                              Unallocate
                            </Checkbox>
                          : <Select
                              value={orderItem?.selectedUnits}
                              onSelect={(value) => handleAllocatedUnit(value, item?.productId, orderItem?.orderBookId)}
                              options={getSelectOptions(orderItem?.units)}
                              style={{ width: '100px' }}
                            />
                          }                
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Modal>
  )
}

export default OrderFullfilmentModal;

OrderFullfilmentModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  addSupplierOrder: PropTypes.func.isRequired,
  orderId: PropTypes.string.isRequired,
  productId: PropTypes.string,
  packagingId: PropTypes.string,
  isProductEdit: PropTypes.bool,
  unallocateView: PropTypes.bool,
  allocatedUnitDetails: PropTypes.array
};