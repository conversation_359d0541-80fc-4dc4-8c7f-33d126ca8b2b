import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import PageLoader from '../Loaders/PageLoader';
import { Link, useNavigate } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import close from '../../assets/icons/close.svg';
import { Descriptions, Radio, Table } from 'antd';
import { fetchInventoryProductStockData, fetchInventoryProductTransaction } from '../../service/api/inventoryApi';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const InventorySideBar = (props) => {

  const { inventoryData, hide } = props;

  const transactioRadioOptns= [
    { label: 'Incoming', value: 'IN' },
    { label: 'Outgoing', value: 'OUT' },
  ];

  const [loading, setLoading] = useState(false);
  const [stockData, setStockData] = useState({});
  // const [batchList, setBatchList] = useState([]);
  const [transactions, setTransactions] = useState({});
  const [transactionRadioValue, setTransactionRadioValue] = useState(transactioRadioOptns[0].value);

  const navigate = useNavigate();

  const stockFieldItems = [
    // {
    //   key: '1',
    //   label: 'Stock in Inventory',
    //   children: stockData?.stockInInventory,
    // },
    // {
    //   key: '2',
    //   label: 'Stock in Transit',
    //   children: stockData?.stockInTransit,
    // },
    // {
    //   key: '3',
    //   label: 'Stock under Production',
    //   children: stockData?.stockUnderProduction,
    // },
    {
      key: '4',
      label: 'Total Stock',
      children: stockData?.totalStock,
    },
  ];
  const packagingFieldItems = [
    {
      key: '1',
      label: 'Name',
      children: inventoryData?.inventoryProduct?.packaging?.packagingName,
    },
    {
      key: '2',
      label: 'Type',
      children: inventoryData?.inventoryProduct?.packaging?.type,
    },
    {
      key: '3',
      label: 'Pack Size',
      children: inventoryData?.inventoryProduct?.packaging?.packSize,
    },
    {
      key: '4',
      label: 'Tare Weight',
      children: inventoryData?.inventoryProduct?.packaging?.tareWeight,
    },
  ];
  // const batchTableHeader = [
  //   {
  //     title: 'Batch No',
  //     dataIndex: 'batchNumber',
  //     key: 'batchNumber',
  //   },
  //   {
  //     title: 'Total Units',
  //     dataIndex: 'units',
  //     key: 'units',
  //   },
  //   {
  //     title: 'Net Wt./ Unit',
  //     dataIndex: 'netWeightPerUnit',
  //     key: 'netWeightPerUnit',
  //   },
  //   {
  //     title: 'Mfg',
  //     dataIndex: 'mfgDate',
  //     key: 'mfgDate',
  //     render: (_, {mfgDate}) => mfgDate ? getDateFromTimeStamp(mfgDate) : '-',
  //   },
  //   {
  //     title: 'Expiry',
  //     dataIndex: 'expDate',
  //     key: 'expDate',
  //     render: (_, {expDate}) => expDate ? getDateFromTimeStamp(expDate) : '-',
  //   },
  // ];
  const transactionTableHeader = [
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_, {createdAt}) => createdAt ? getDateFromTimeStamp(createdAt) : '-',
    },
    {
      title: 'PO#',
      dataIndex: 'purchaseOrderNumber',
      key: 'purchaseOrderNumber',
      render: (_, {meta: {purchaseOrderNumber, orderBookId}}) => 
        <Link
          to={new RouteFactory().dashboard().orderBook().setId(orderBookId).view().build()}
        >
          {purchaseOrderNumber}
        </Link>
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
    },
    {
      title: 'Entity Name',
      dataIndex: 'entity',
      key: 'entity',
    },
  ];

  const hideSideDrawer = () => {
    hide();
    navigate(new RouteFactory().dashboard().inventory().build());
  };

  useEffect(() => {
    if (inventoryData?.inventoryProduct?.id) {
      setLoading(true)
      Promise.all([
        fetchInventoryProductStockData(inventoryData?.inventoryProduct?.id),
        // fetchInventoryProductBatches(null, null, inventoryData?.inventoryProduct?.id),
        fetchInventoryProductTransaction(null, null, inventoryData?.inventoryProduct?.id),
      ]).then((res) => {
        if (res[0]) {
          setStockData(res[0].data);
        }
        // if (res[1]) {
        //   setBatchList(res[1].data);
        // }
        if (res[1]) {
          setTransactions(res[1].data);
        }
        setLoading(false);
      }).catch((err) => {
      console.log(err);
      setLoading(false);
    })
    }
  }, [inventoryData?.inventoryProduct?.id])

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} /> : null}
      <div className="pb-4 px-3">
        <div className="py-4 flex items-center justify-between sticky top-0 z-[99] bg-base-white">
          <div>
            <div className='text-xl font-medium'>{inventoryData?.inventoryProduct?.product?.tradeName}</div>
            <div className='text-sm text-[#616161]'>
              {inventoryData?.inventory?.name}
            </div>
          </div>
          <div className='flex items-center gap-4'>
            <div onClick={hideSideDrawer}>
              <img src={close} alt="close" />
            </div>
          </div>        
        </div>
        <div className='flex flex-col gap-4'>
          <Descriptions
            title={<div className='text-lg font-semibold'>Stock Details</div>}
            items={stockFieldItems}
            column={2}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
            style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
          />
          <Descriptions
            title={<div className='text-lg font-semibold'>Packaging Details</div>}
            items={packagingFieldItems}
            column={2}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
            style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
          />
          {/* <div>
            <div className='mb-4 text-lg font-semibold'>Batch Details</div>
            <Table
              columns={batchTableHeader}
              dataSource={batchList?.content?.map((batch) => ({...batch, key: batch.id}))}
              pagination={false}
            />
          </div> */}
        </div>
        <div>
          <div className='mt-4 mb-4 text-lg font-semibold'>Transactions</div>
          <Radio.Group
            className='mb-3'
            options={transactioRadioOptns}
            onChange={({ target: { value } }) =>  setTransactionRadioValue(value)}
            value={transactionRadioValue}
            optionType="button"
          />
          <Table
            columns={transactionTableHeader}
            dataSource={transactions?.content?.filter(
                (item) => transactionRadioValue ? item.transactionType === transactionRadioValue : item
              ).map((batch) => ({...batch, key: batch.id}))}
            pagination={false}
          />
        </div>
      </div>
    </>
  )
}

export default InventorySideBar;

InventorySideBar.propTypes = {
  inventoryData: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
};