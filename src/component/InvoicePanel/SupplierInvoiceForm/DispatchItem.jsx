import React, { useEffect, useState } from 'react';
import { Card, Button, Row, Col, Select, Form } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
// import { productUOMList } from '../../../constants/formConstant';
import { getDispatchOrderFromPoNumber } from '../../../service/api/invoiceApi';
import DispatchProduct from './DispatchProduct';
import { formModes } from '../../../constants/formConstant';

const DispatchItem = ({ form, formView, purchaseOrderList, index, handleRemoveItem, loading }) => {
  const [dispatchOrdersList, setDispatchOrdersList] = useState([]);
  const [productList, setProductList] = useState({});
  const [prevPoNum, setPrevPoNum] = useState(undefined);
  const [prevOrder, setPreOrder] = useState(undefined);

  const poNum = Form.useWatch([`items`, index, 'poNumber'], form);
  const dispatchOrderId = Form.useWatch([`items`, index, 'dispatchOrderId'], form);

  const handlePoSelect = (value, index) => {
    if (poNum != prevPoNum && prevPoNum != undefined)
      form.setFieldValue(['items', index, 'dispatchOrderId'], undefined);
    // setLoading(true);
    getDispatchOrderFromPoNumber(value)
      .then(res => {
        if (res.data && res.data.length > 0) {
          console.log(loading);
          setDispatchOrdersList([...res.data]);
        }
        // setLoading(false);
      })
      .catch(error => {
        console.log(error);
        // setLoading(false);
      });
    setPrevPoNum(poNum);
  };

  const handleDispatchOrderIdSelect = orderId => {
    // reset product value
    if (prevOrder != orderId && prevOrder != undefined) {
      form.setFieldValue(['items', index, 'productId'], undefined);
      form.setFieldValue(['items', index, 'description'], undefined);
      form.setFieldValue(['items', index, 'taxInPercent'], undefined);
      form.setFieldValue(['items', index, 'uom'], undefined);
      form.setFieldValue(['items', index, 'quantity'], undefined);
      form.setFieldValue(['items', index, 'cpPerUnit'], undefined);
      form.setFieldValue(['items', index, 'cost'], undefined);
    }

    // update proudct list
    const dispatchOrder =
      dispatchOrdersList && dispatchOrdersList
        ? dispatchOrdersList.find(order => order.id === orderId)
        : null;
    console.log('calling ', dispatchOrdersList);
    setPreOrder(orderId);
    setProductList(dispatchOrder ? dispatchOrder.products : []);
  };

  useEffect(() => {
    handlePoSelect(poNum, index);
  }, [poNum]);

  useEffect(() => {
    handleDispatchOrderIdSelect(dispatchOrderId);
  }, [dispatchOrderId]);

  return (
    <Card
      size="small"
      title={`Item ${index + 1}`}
      extra={
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveItem(index)}
        />
      }
      style={{ marginBottom: 16 }}
    >
      <Row gutter={24}>
        <Col span={3}>
          {purchaseOrderList.length ? (
            <Form.Item
              label="PO number"
              name={['items', index, 'poNumber']}
              rules={[{ required: true, message: 'Please select Po number' }]}
            >
              <Select showSearch disabled={formView === formModes.READ}>
                {purchaseOrderList.map(order => (
                  <Select.Option key={order.id} value={order.purchaseOrderNumber}>
                    {order.purchaseOrderNumber}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
        <Col span={3}>
          {dispatchOrdersList && dispatchOrdersList.length > 0 ? (
            <Form.Item
              label="Dispatch order Id"
              name={['items', index, 'dispatchOrderId']}
              rules={[{ required: true, message: 'Please select Dispatch Order Id' }]}
            >
              <Select showSearch disabled={formView === formModes.READ}>
                {dispatchOrdersList.map(order => (
                  <Select.Option key={order.id} value={order.id}>
                    {order.orderId}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
        {dispatchOrderId && dispatchOrdersList.length > 0 ? (
          <DispatchProduct
            formView={formView}
            productList={productList}
            index={index}
            form={form}
          />
        ) : null}
      </Row>
    </Card>
  );
};

DispatchItem.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  purchaseOrderList: PropTypes.array.isRequired,
  dispatchOrdersList: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  handleRemoveItem: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};

export default DispatchItem;
