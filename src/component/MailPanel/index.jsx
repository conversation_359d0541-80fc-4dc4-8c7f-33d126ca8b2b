import {
  Button,
  Input,
  // DatePicker,
  Form,
  Select,
  message,
} from 'antd';
import { Option } from 'antd/es/mentions';
import React, { useEffect, useState } from 'react';
// import { DateFormat } from '../../constants/formConstant';
// import enUS from 'antd/es/calendar/locale/en_US';
// import { CalendarOutlined } from '@ant-design/icons';
// import { generateReport } from '../../service/api/reportApi';
import { getCustomerList } from '../../service/api/customerApi';
import { getOrderBookListByCustomerId } from '../../service/api/orderBookApi';
import { getDispatchOrderForOrderbook } from '../../service/api/dispatchOrderApi';
import { getDocumentsForDispatchOrder } from '../../service/api/documentApi';
import { sendMailToCustomer } from '../../service/api/mailApi';
import { getEmployeeList } from '../../service/api/employee';
import PageLoader from '../Loaders/PageLoader';
import Typography from 'antd/es/typography/Typography';
import {  useWatch } from 'antd/es/form/Form';

export const MailPanel = () => {
  const [form] = Form.useForm();
  const [customerList, setCustomerList] = useState([]);
  const [orderBookList, setOrderBookList] = useState([]);
  const [dispatchOrderList, setdispatchOrderList] = useState([]);
  const [messageApi, contextHolder] = message.useMessage();
  // const [selectedDispatchOrderIds, setSelectedDispatchOrders] = useState([]);
  const customer = Form.useWatch('customerId', form);
  const selectedDispatchOrderIds = Form.useWatch('selectedDispatchOrderIds', form);
  const selectedOrderBookIds = Form.useWatch('selectedOrderBookIds', form);
  const [documentsMap, setDocumentsMap] = useState({});
  const [accountOwnerList, setAccountOwnerList] = useState([]);
  const [loading, setLoading] = useState(false);
  const salesMailId = useWatch('salesMailId', form);
  const accountMailId = useWatch('accountMailId', form);
  const optionalEmails = useWatch('optionalEmails', form);

  const isSubmitDisabled =
    !salesMailId?.trim() &&
    !accountMailId?.trim() &&
    (!optionalEmails || optionalEmails.length === 0);

  // const [customerMailId, setCustomerMailId] = useState([]);

  useEffect(() => {
    if (!accountOwnerList || !accountOwnerList.length) {
      getEmployeeList()
        .then(res => {
          if (res.data && res.data.content) {
            setAccountOwnerList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  // const handleDispatchOrderChange = value => {
  //   setSelectedDispatchOrders(value);
  // };

  const handleFinish = () => {
    setLoading(true);
    form.validateFields().then(values => {
      // console.log(values)
      sendMailToCustomer(values)
        // .then(() => form.resetFields())
        .then(() => setLoading(false))
        .then(() => {
          messageApi.success('mail sent to customer ', 3);
        })
        .then(() => form.resetFields())
        .catch(err => {
          console.log('err ', err);
          setLoading(false);
        });
    });
  };

  useEffect(() => {
    if (!customerList.length) {
      getCustomerList()
        .then(response => {
          if (response.data && response.data.content) {
            setCustomerList(response.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  useEffect(() => {
    if (customer) {
      let customerObj = customerList.find(obj => obj.id == customer);
      // console.log(customerObj);
      // reset po number
      getOrderBookListByCustomerId(customer).then(res => {
        setOrderBookList(res.data);
      });
      form.resetFields(['selectedOrderBookIds', 'selectedDispatchOrderIds', 'attachments', 'salesMailId', 'accountMailId', 'optionalEmails']);
      if(customerObj?.emailInfo?.salesEmail && customerObj?.emailInfo?.salesEmail.length>0)
        form.setFieldsValue({ salesMailId: customerObj?.emailInfo?.salesEmail });
      if(customerObj?.email && customerObj.email.length>0)
        form.setFieldsValue({ salesMailId: customerObj?.email });     
      if(customerObj?.emailInfo?.accountEmail && customerObj?.emailInfo?.accountEmail.length>0)
        form.setFieldsValue({ accountMailId: customerObj?.emailInfo?.accountEmail });      
      if(customerObj?.emailInfo?.optionalEmails && customerObj?.emailInfo?.optionalEmails.length>0)
        form.setFieldsValue({ optionalEmails: customerObj?.emailInfo?.optionalEmails });
    }
  }, [customer]);

  useEffect(() => {
    // console.log('selected order book ids ', selectedOrderBookIds);
    if (selectedOrderBookIds && selectedOrderBookIds.length > 0) {
      form.resetFields(['selectedDispatchOrderIds', 'attachments']);
      const promises = selectedOrderBookIds.map(id => getDispatchOrderForOrderbook(id));

      Promise.all(promises)
        .then(responses => {
          const updatedList = responses.reduce((acc, res) => {
            if (res.data && res.data.length > 0) {
              return [...acc, ...res.data];
            }
            return acc;
          }, []);

          setdispatchOrderList(updatedList); // Update the list after all promises are resolved
        })
        .catch(error => {
          console.error('Error fetching dispatch orders:', error);
          // Handle error if needed
        });
    }
  }, [selectedOrderBookIds]);

  useEffect(() => {
    if (selectedDispatchOrderIds && selectedDispatchOrderIds.length > 0) {
      const fetchDocumentsPromises = selectedDispatchOrderIds.map(id =>
        getDocumentsForDispatchOrder(id)
      );
      form.resetFields(['attachments']);
      Promise.all(fetchDocumentsPromises)
        .then(responses => {
          const updatedDocumentsMap = responses.reduce((acc, res, index) => {
            const orderId = selectedDispatchOrderIds[index];
            if (res.data && res.data.length > 0) {
              acc[orderId] = res.data;
            }
            return acc;
          }, {});
          console.log(updatedDocumentsMap);
          setDocumentsMap(updatedDocumentsMap);
        })
        .catch(error => {
          console.error('Error fetching documents:', error);
          messageApi.error('Failed to fetch documents.');
        });
    } else {
      setDocumentsMap({});
    }
  }, [selectedDispatchOrderIds]);

  const getLabelForDispatchOrder = id => {
    const order = dispatchOrderList.find(order => order.id == id);
    if (order) return order?.purchaseOrderNumber + ' , ' + order?.orderId;
    else return '';
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
      }}
    >
      {contextHolder}
      {loading ? (
        <PageLoader />
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          style={{ width: '80%' }}
          // style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}
        >
          {/* Customer (Single Select) */}
           {/* TO Section Header */}
           <div style={{ width: '100%', margin: '10px 0 20px 0' }}>
            <Typography.Text strong style={{ fontSize: '16px', color : 'rgba(17, 8, 64, 0.7)' }}>Customer Details</Typography.Text>
          </div>

          <Form.Item
            label="Customer"
            name="customerId"
            rules={[{ required: true, message: 'Please select a customer!' }]}
            style={{ width: '100%' }}
          >
            <Select
              showSearch
              placeholder="Select a customer"
              optionFilterProp="children" // Search on the displayed label
              filterOption={(input, option) =>
                option?.children?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {customerList.map(customer => (
                <Select.Option key={customer.id} value={customer.id}>
                  {customer.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          {/* PO Numbers (Multi Select with Search) */}
          <Form.Item
            label="PO Numbers"
            name="selectedOrderBookIds"
            rules={[{ required: true, message: 'Please select PO numbers!' }]}
            style={{ flex: '1 1 100%' }}
          >
            <Select
              showSearch
              placeholder="Select PO numbers "
              optionFilterProp="children" // Search on the displayed label
              filterOption={(input, option) =>
                option?.children?.toLowerCase().includes(input.toLowerCase())
              }
              mode="multiple"
            >
              {orderBookList.map(orderBook => (
                <Select.Option key={orderBook.id} value={orderBook.id}>
                  {orderBook.purchaseOrderNumber}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Dispatch Order Numbers (Multi Select with Search) */}
          <Form.Item
            label="Dispatch Order Numbers"
            name="selectedDispatchOrderIds"
            rules={[{ required: true, message: 'Please select dispatch orders!' }]}
            style={{ flex: '1 1 100%' }}
          >
            <Select
              mode="multiple"
              placeholder="Select dispatch orders "
              optionFilterProp="children" // Search on the displayed label
              filterOption={(input, option) =>
                option?.children?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {/* {console.log('dispatch order list ', dispatchOrderList)} */}
              {dispatchOrderList.map((dispatchOrder, idx) => (
                <Select.Option key={idx} value={dispatchOrder.id}>
                  {dispatchOrder.orderId}
                  {/* {console.log(dispatchOrder)} */}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Document Map */}
          {selectedDispatchOrderIds
            ? selectedDispatchOrderIds.map(id => (
                <Form.Item
                  key={id}
                  label={`Documents for ${getLabelForDispatchOrder(id)}`}
                  name={['attachments', id]}
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: `Please select documents for ${getLabelForDispatchOrder(id)}`,
                  //   },
                  // ]}
                  style={{ flex: '1 1 100%' }}
                >
                  <Select
                    mode="multiple"
                    placeholder="Select documents  "
                    optionFilterProp="children" // Search on the displayed label
                    filterOption={(input, option) =>
                      option?.children?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {documentsMap[id] &&
                      documentsMap[id]
                        .filter(doc => doc?.status == 'APPROVED')
                        .map(doc => (
                          <Option key={doc.id} value={doc.id}>
                            {doc.docType}
                          </Option>
                        ))}
                  </Select>
                </Form.Item>
              ))
            : ''}

          {/* Mail Template (Single Select) */}
          <Form.Item
            label="Mail Template"
            name="mailTemplate"
            rules={[{ required: true, message: 'Please select a mail template!' }]}
            style={{ flex: '1 1 100%' }}
          >
            <Select placeholder="Select a mail template">
              {/* TODO THIS SHOULD BE IN DASHBOARD CONFIG  */}
              <Option disabled value="CHEMSTACK_INVOICE_SUMMARY">
                CEHMSTACK INVOICE SUMMARY
              </Option>
              <Option value="MSTACK_INVOICE_SUMMARY">MSTACK INVOICE SUMMARY</Option>
              {/* Add more options as needed */}
            </Select>
          </Form.Item>

         {/* TO Section Header */}
          <div style={{ width: '100%', margin: '10px 0 20px 0' }}>
            <Typography.Text strong style={{ fontSize: '16px', color : 'rgba(17, 8, 64, 0.7)' }}>Email Recipients</Typography.Text>
          </div>

          <Form.Item
            label="Customer / Sales SPOC Mail Id"
            name="salesMailId"
            style={{ width: '100%' }}
          >
            <Input  placeholder="Email will be auto-filled" />
          </Form.Item>
          <Form.Item
            label="Accounts / Controller Mail Id"
            name="accountMailId"
            style={{ width: '100%' }}
            dependencies={['customerId']}
          >
            <Input 
                 placeholder="Email will be auto-filled" />
          </Form.Item>
          <Form.Item
            label="Optional Emails"
            name="optionalEmails"
            style={{ flex: '1 1 100%' }}
          >
            <Select mode="tags" showSearch>
              {accountOwnerList.map(emp => (
                <Select.Option key={emp.id} value={emp?.email}>
                  {emp?.email}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Optional Internal Mail IDs */}
          <Form.Item
            label="CC:"
            name="ccs"
            style={{ flex: '1 1 100%' }}
            // rules={[
            //   {
            //     validator: (_, value) => {
            //       const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            //       const invalidEmails = value.filter(email => !emailPattern.test(email));
            //       return invalidEmails.length > 0
            //         ? Promise.reject(new Error(`Invalid email(s): ${invalidEmails.join(', ')}`))
            //         : Promise.resolve();
            //     },
            //   },
            // ]}
          >
            <Select mode="tags" showSearch>
              {accountOwnerList.map(emp => (
                <Select.Option key={emp.id} value={emp?.email}>
                  {emp?.email}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Submit Button */}
          <Form.Item style={{ flex: '1 1 100%', textAlign: 'right' }}>
            <Button type="primary" htmlType="submit"  disabled={isSubmitDisabled}>
              Submit
            </Button>
          </Form.Item>
        </Form>
      )}
    </div>
  );
};
