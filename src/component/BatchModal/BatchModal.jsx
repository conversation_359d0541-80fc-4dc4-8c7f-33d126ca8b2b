import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Collapse, Form, Modal, message } from 'antd';
import { addProductBatchDetails, updateBatchDetails } from '../../service/api/batchApi';
import BatchCard from './BatchCard';
import { formModes } from '../../constants/formConstant';
import dayjs from 'dayjs';

const BatchModal = props => {
  const { visible, mode, onCancel, onSumbit, orderData, batchData ,fetchBatchDetails } = props;

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    if (batchData && batchData.productBatchMap) form.setFieldsValue(getFormPrefilldValues());
  }, [batchData]);

  const getFormPrefilldValues = () => {
    let formValue = {};
    let productBatchMapKeys = Object.keys(batchData.productBatchMap);
    productBatchMapKeys.forEach(key => {
      let productBatches = [...batchData.productBatchMap[key]];
      formValue[key] = productBatches.map(batch => {
        return {
          ...batch,
          mfgDate: dayjs(batch.mfgDate),
          expDate: dayjs(batch.expDate),
        };
      });
    });
    return formValue;
  };

  const accordianFormList = orderData.products.map(product => ({
    key: product?.id,
    label:`${product?.product?.tradeName} , ${product?.packaging?.packagingName}`,
    children: (
      <Form.List key={product?.id} name={product?.id}>
        {(fields, { add, remove }, { errors }) => (
          <div
            style={{
              display: 'flex',
              rowGap: 16,
              flexDirection: 'column',
            }}
          >
            {fields.map((field, index) => {
              return (
                <BatchCard
                  key={field.name}
                  field={field}
                  index={index}
                  remove={mode != formModes.READ ? remove : () => {}}
                />
              );
            })}
            <Button type="dashed" onClick={() => add()} block>
              + Add Batch
            </Button>
            <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
          </div>
        )}
      </Form.List>
    ),
  }));

  const handleFormSubmit = () => {
    // console.log(form.getFieldValue());
    form.validateFields().then(values => {
      setLoading(true);
      const batchDetails = {
        supplierDispatchOrderId: orderData.id,
        productBatchMap: values,
      };
      if (mode == formModes.UPDATE) {
        updateBatchDetails(batchDetails)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
              fetchBatchDetails();
            }
          })
          .catch(err => {
            const errMsg = err?.response?.data?.message;
            messageApi.error(errMsg);
            setLoading(false)
          });
      } else {
        addProductBatchDetails(batchDetails)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
              fetchBatchDetails();
            }
          })
          .catch(err => {
            const errMsg = err?.response?.data?.message;
            messageApi.error(errMsg);
            setLoading(false);
          });
      }
    });
  };
  const cancelModal = () => {
    if (loading) return;
    if (mode == formModes.CREATE) form.resetFields();
    onCancel();
  };
  const getTitleFromMode = mode => {
    switch (mode) {
      case formModes.READ:
        return 'Batch Details';
      case formModes.UPDATE:
        return 'Update Batch Details';
      case formModes.CREATE:
        return 'Add Batch Details';
      default:
        return 'Default Title';
    }
  };

  return (
    <Modal
      open={visible}
      title={getTitleFromMode(mode)}
      onCancel={cancelModal}
      destroyOnClose={true}
      width="80vh"
      footer={[
        <Button key="cancel" onClick={cancelModal}>
          Cancel
        </Button>,
        <Button
          disabled={mode == formModes.READ}
          loading={loading}
          key="submit"
          type="primary"
          onClick={handleFormSubmit}
        >
          Submit
        </Button>,
      ]}
      styles={{
        body: {
          maxHeight: '70vh',
          overflow: 'scroll',
        },
      }}
    >
      {contextHolder}
      <div>Supplier Dispatch Order: {orderData.orderId}</div>
      <div className="mt-3">
        <Form
          disabled={mode == formModes.READ}
          name="batchDetails"
          layout="vertical"
          scrollToFirstError
          size="middle"
          form={form}
        >
          <Collapse accordion items={accordianFormList} />
        </Form>
      </div>
    </Modal>
  );
};

export default BatchModal;

BatchModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSumbit: PropTypes.func.isRequired,
  mode: PropTypes.string.isRequired,
  orderData: PropTypes.object.isRequired,
  batchData: PropTypes.object,
  fetchBatchDetails: PropTypes.object,
};
