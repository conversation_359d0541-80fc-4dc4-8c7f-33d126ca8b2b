import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import css from '../CustomerPanel/CustomerListView.module.css';
import { Badge, Button, Input, Pagination, Table } from 'antd';
import filterIcon from '../../assets/icons/filter_icon.svg';
import addIcon from '../../assets/icons/add_circle.svg';
import { useDispatch, useSelector } from 'react-redux';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import FilterModal from '../FilterModal/FilterModal';
import { orderBookHeader, orderBookTablePageSize } from '../../constants/TableConstants';
import { formatOrderBookList } from '../../util/formUtil';
import { getOrderBookList, getOrderDataFromId } from '../../service/api/orderBookApi';
import HeaderPanel from '../headerPanel';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { userEntity, userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import PageLoader from '../Loaders/PageLoader';
import { formModes } from '../../constants/formConstant';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import useFormTimeTracker from '../PostHog/useFormTimeTracker';

const OrderBookListView = props => {

  const { collapseSideDrawerHandler } = props;

  const user = useSelector(state => state.user);
  const collapseSideDrawer = useSelector(state => state.collapseSideDrawer);

  const [orderBookList, setOrderBookList] = useState({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  })
  const addOrderFormOpened = useFormTimeTracker("Add Order");

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { orderId } = useParams();

  const tableActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    render: (_, record) => (
      <>
        {/* {hasPermission(userPermissionsList.createCustomerOrder, user.permissions) ? (
          <Button type='text' onClick={() => handleAddDispatchOrder(record.id)}>
            Add DO
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createsupplierOrderBook, user.permissions) ? (
          <Button type='text' onClick={() => handleAddSupplierOrder(record.id)}>
            Add SO
          </Button>
        ) : null} */}
        {hasPermission(userPermissionsList.viewOrderBook, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().orderBook().setId(record.id).view().build() + location.search}>
            <Button type='text'>
              View
            </Button>
          </Link>
        ) : null}
      </>
    )
  };

  const handleAddDispatchOrder = (orderId) => {
    const record = orderBookList.content.find((order) => order.id === orderId);
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.dispatchOrder,
        mode: formModes.CREATE,
        orderBook: record,
      },
    });
  }
  const handleAddSupplierOrder = (orderId) => {
    const orderBookObj = orderBookList?.content?.find((item) => item.id === orderId)
    if (orderBookObj?.products) {
      setOverlayData({
        show: true,
        drawerData: {
          entity: userEntity.supplierOrderBook,
          mode: formModes.CREATE,
          orderBookId: orderId,
          orderBookProducts: orderBookObj?.products,
        },
      });
    }
  }
  
  const handleAddOrderbook = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.orderBook,
        mode: formModes.CREATE,
        formTimeTracker: addOrderFormOpened,
      },
    });
    addOrderFormOpened.startTracking()
  }
  // const handleEditOrderbook = (orderId) => {
  //   const record = orderBookList.content.find((order) => order.id === orderId);
  //   setOverlayData({
  //     show: true,
  //     drawerData: {
  //       entity: userEntity.orderBook,
  //       mode: formModes.UPDATE,
  //       formData: record,
  //     },
  //   });
  // }
  const getOrderBookListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getOrderBookList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        // TODO: proper check for orderBookList
        setOrderBookList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const fetchOrderBookList = () => {
    const { filters, searchkey, page } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey.length != 0) {
      setShowClear(true);
    }
    getOrderBookListFromConfig(orderBookTablePageSize, page - 1, filters, searchkey);  
  }
  const fetchOrderBookFromId = () => {
    getOrderDataFromId
    setLoading(true);
    getOrderDataFromId(orderId)
      .then(response => {
        dispatch(setSideDrawerData({
          isNewDisplay: true,
          sourceRecord: response.data,
        }));
        collapseSideDrawerHandler(false);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
        navigate(new RouteFactory().dashboard().orderBook().build());
      });
  }

  useEffect(() => {
    fetchOrderBookList();
  }, [location.search]);

  useEffect(() => {
    if (orderId && orderBookList?.content?.length) {
      const [sourceRecord] = orderBookList.content.filter(
        order => orderId === order.id
      );
      if (sourceRecord) {
        dispatch(setSideDrawerData({
          isNewDisplay: true,
          sourceRecord,
        }));
        collapseSideDrawerHandler(false);
      } else {
        fetchOrderBookFromId()
        // navigate(new RouteFactory().dashboard().orderBook().build());
      }
    }
  }, [location.pathname, orderBookList?.content?.length > 0])

  return (
    <>
      <HeaderPanel
        name="Orderbook"
        sideButtons={!collapseSideDrawer ? (
          <div className='flex'>
            <Button
              type="text"
              className={css.btnFlexStyle}
              onClick={() => setShowFiltersModal(true)}
              icon={<img src={filterIcon} />}
            />              
            {hasPermission(userPermissionsList.createOrderBook, user.permissions) ? (
              <Button
                onClick={() =>handleAddOrderbook()}
                type="primary"
                icon={<img src={addIcon} />}
              />
            ) : null}
          </div>
        ) : null}
      />
      <div
        className={css.functionalBar}
        style={!collapseSideDrawer ? { padding: '16px 4px' } : null}
      >
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by PO Number"
            className={css.searchInput}
            loading={false}
            size={collapseSideDrawer ? "large" : "middle"}
            allowClear
            onSearch={value => {
              updateParamsAndNavigate(navigate, location, { searchkey: value });
            }}
            variant="borderless"
            enterButton
          />
        </div>
        {collapseSideDrawer ? (
          <>
            <Badge color="#23568A" count={filterCount}>
              <Button
                size="large"
                className={css.btnFlexStyle}
                onClick={() => setShowFiltersModal(true)}
              >
                <img src={filterIcon} />
                <span>Filter</span>
              </Button>
            </Badge>
            {showClear ? (
              <Button
                size="large"
                className={css.btnFlexStyle}
                onClick={() => {
                  updateParamsAndNavigate(navigate, location, {});
                  getOrderBookListFromConfig();
                  setShowClear(false);
                  setFilterCount(0);
                }}
              >
                <span>Clear Filter</span>
              </Button>
            ) : null}
            {hasPermission(userPermissionsList.createOrderBook, user.permissions) ? (
              <Button
                onClick={() => handleAddOrderbook()}
                type="primary"
                size="large"
                className={css.btnFlexStyle}
              >
                <img src={addIcon} />
                <span>Add Order</span>
              </Button>
            ) : null}
          </>
        ) : null}
      </div>
      {collapseSideDrawer ? (
        <Table
          bordered={false}
          columns={[
            ...orderBookHeader,
            tableActions,
          ]}
          dataSource={formatOrderBookList(orderBookList?.content)}
          className={css.tableContainer}
          pagination={{
            showSizeChanger: false,
            current: (orderBookList?.pageable?.pageNumber + 1 || 1),
            pageSize: orderBookTablePageSize,
            total: orderBookList?.totalElements,
            onChange: page => {
              updateParamsAndNavigate(navigate, location, { page }); 
            },
          }}
          loading={loading}
          scroll={{
            x: '100%',
            y: 600,
          }}
        />
      ) : (
        <>
          <div className='overflow-auto h-[80vh]'>
            {loading ? (
              <PageLoader style={{ height: '100%', width: '100%', position: 'absolute' }}/>
            ) : null}
            {orderBookList?.content?.map((order) => (
              <Link
                key={order.id}
                to={new RouteFactory().dashboard().orderBook().setId(order.id).view().build()+ location.search}
                style={{ color: 'unset' }}
              >
                <div
                  className='p-4 flex justify-between hover:bg-neutral-50 cursor-pointer'
                >
                  <div>
                    <div>{order.customer.name}</div>
                    <div>{order.purchaseOrderNumber}</div>
                  </div>
                  <div>{getDateFromTimeStamp(order.purchaseOrderDate)}</div>
                </div>
              </Link>
            ))}
          </div>
          <Pagination
            style={{ margin: '15px 0' }}
            showSizeChanger={false}
            current={orderBookList?.pageable?.pageNumber + 1}
            pageSize={orderBookTablePageSize}
            total={orderBookList?.totalElements}
            onChange={(page) => {
              updateParamsAndNavigate(navigate, location, { page }); 
            }}
          />
        </>
      )}
      <FilterModal
        view="orderbook"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={(entity="",submitted=false) => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
 
          if(entity && entity === userEntity?.orderBook){
            addOrderFormOpened.endTracking(submitted)
          }
        }}
        onSubmit={fetchOrderBookList}
      />
    </>
  );
};

export default OrderBookListView;

OrderBookListView.propTypes = {
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};
