import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import { Button, Form, Steps } from 'antd';
import { addInventoryOutOrderForOrderBook } from '../../service/api/inventoryApi';
// import { getFormConfigFromName } from '../../service/api/formConfig';
import { setPackagingList } from '../../store/actions/packagingList';
import InventoryOutBasicInfo from './InventoryOutBasicInfo';
import InventoryOutProductInfo from './InventoryOutProductInfo';
import InventoryOutFormReview from './InventoryOutFormReview';
import { getAllPackagingDetails } from '../../service/api/packagingApi';

const InventoryOutOrderForm = props => {
  const { mode, order, customerDispatchList, hide } = props;

  const packagingList = useSelector(state => state.packagingList);
  const dispatch = useDispatch();

  const [inventoryOutEntry, setInventoryOutEntry] = useState(
    (mode === formModes.UPDATE || mode === formModes.READ) ? order : {}
  );
  const [inventoryOutFormCurrentStep, setInventoryOutFormCurrentStep] = useState(0);
  const [submittingForm, setSubmittingForm] = useState(false);

  const [basicInfoForm] = Form.useForm();
  const [productsForm] = Form.useForm();

  const inventoryOutFormStepsList = [
    {
      key: 'basic information',
      title: <div className="whitespace-normal">Order</div>,
      content: (
        <InventoryOutBasicInfo
          form={basicInfoForm}
          initialValue={inventoryOutEntry}
          mode={mode}
          customerDispatchList={customerDispatchList}
          setInitialValue={setInventoryOutEntry}
        />
      ),
    },
    {
      key: 'product info',
      title: <div className="whitespace-normal">Product</div>,
      content: <InventoryOutProductInfo form={productsForm} initialValue={inventoryOutEntry} />,
    },
    {
      key: 'final review',
      title: 'Preview',
      content: <InventoryOutFormReview inventoryOutEntry={inventoryOutEntry} />,
    },
  ];

  const saveOrderDetails = orderData => {
    if (mode === formModes.CREATE) {
      addInventoryOutOrderForOrderBook(orderData)
        .then(response => {
          console.log('Order api response:', response);
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.log(error);
          setSubmittingForm(false);
        });
    }
    // if (mode === formModes.UPDATE) {
    //   updateSupplierOrderBook(orderData, order.id)
    //     .then(response => {
    //       console.log('Order api response:', response);
    //       setSubmittingForm(false);
    //       hide(response.data);
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       setSubmittingForm(false);
    //     });
    // }
  };
  const submitFormHandler = () => {
    setSubmittingForm(true);
    const formattedData = JSON.parse(JSON.stringify(inventoryOutEntry));
    // modify data in required.
    saveOrderDetails(formattedData);
  };
  const nextStep = () => {
    setInventoryOutFormCurrentStep(inventoryOutFormCurrentStep + 1);
  };
  const prevStep = () => {
    setInventoryOutFormCurrentStep(inventoryOutFormCurrentStep - 1);
  };
  const validateAndSaveFormFields = () => {
    let formObj = {};
    if (inventoryOutFormCurrentStep === 0) {
      formObj = basicInfoForm;
    } else if (inventoryOutFormCurrentStep === 1) {
      formObj = productsForm;
    }
    formObj
      .validateFields()
      .then(values => {
        let valuesCopy = JSON.parse(JSON.stringify(values));

        // if (inventoryOutFormCurrentStep === 0) {
        //   console.log('step 0 values ',valuesCopy,' value intial ',inventoryOutEntry)

        // } else if (inventoryOutFormCurrentStep === 1) {
        //   // console.log('step 1 values ',valuesCopy,' value intial ',inventoryOutEntry)
        // }
        setInventoryOutEntry({
          ...inventoryOutEntry,
          ...valuesCopy,
        });
        nextStep();
      })
      .catch(error => {
        console.log(error);
      });
  };

  useEffect(() => {
    if (!packagingList?.length && mode !== formModes.READ) {
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  return (mode === formModes.READ ? (
    <InventoryOutFormReview inventoryOutEntry={inventoryOutEntry} />
  ) : (
    <>
      <div>
        <Steps
          current={inventoryOutFormCurrentStep}
          items={inventoryOutFormStepsList}
          responsive
          size="small"
        />
        <div style={{ marginTop: '30px' }}>
          {inventoryOutFormStepsList[inventoryOutFormCurrentStep].content}
        </div>
      </div>
      <div className="absolute left-0 bottom-0 w-full p-2 flex justify-between bg-base-white z-10 box-border">
        {inventoryOutFormCurrentStep > 0 ? (
          <Button size="large" type="default" onClick={() => prevStep()}>
            Previous
          </Button>
        ) : null}
        {inventoryOutFormCurrentStep < inventoryOutFormStepsList.length - 1 ? (
          <Button
            type="primary"
            size="large"
            onClick={() => {
              validateAndSaveFormFields();
            }}
          >
            Next
          </Button>
        ) : null}
        {inventoryOutFormCurrentStep === inventoryOutFormStepsList.length - 1 ? (
          <Button
            type="primary"
            size="large"
            loading={submittingForm}
            onClick={() => submitFormHandler()}
          >
            {mode === formModes.CREATE ? 'Add Order' : 'Save Changes'}
          </Button>
        ) : null}
      </div>
    </>
  )
  
  );
};

export default InventoryOutOrderForm;

InventoryOutOrderForm.propTypes = {
  mode: PropTypes.string.isRequired,
  order: PropTypes.object,
  hide: PropTypes.func.isRequired,
  customerDispatchList: PropTypes.array,
};
