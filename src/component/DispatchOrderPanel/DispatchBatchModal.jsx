import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Collapse, Form, Modal, Select } from 'antd';
import { addAllocatedBatchDetails, fetchAllocatedBatchDetails, getUnallocatedBatchDetails, resetAllocatedBatchDetails } from '../../service/api/batchApi';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import PageLoader from '../Loaders/PageLoader';

const DispatchBatchModal = (props) => {

  const { visible, onCancel, onSumbit, orderData, } = props;

  const [loading, setLoading] = useState(false);
  const [allocatedBatchDetails, setAllocatedBatchDetails] = useState([]);
  const [unAllocatedBatchDetails, setUnAllocatedBatchDetails] = useState([]);
  
  const [form] = Form.useForm();

  const accordianFormList = orderData?.products?.map((item) => ({
    key: '1',
    label: item?.product?.tradeName,
    children: (
      <Collapse
        accordion
        bordered={false}
        items={getBatchesForProduct(item.product.id).map((item, index) => ({
          key: index,
          label: item.supplierName,
          children:item.productBatchDetailList.map((batch) =>(
            <div
              key={batch.id}
              className='flex items-start justify-center'
            >
              <div className='w-[70%]'>
                <div className='mb-2 font-medium'>Batch Number: {batch.batchNumber}</div>
                <div className='grid grid-cols-2 text-sm gap-1 text-gray-600'>
                  {!allocatedBatchDetails.length ? (<div className='flex items-center'>
                    <div>Available Units: </div>
                    <div className='ml-1 font-medium'>{batch.units} </div>
                  </div>) : null}
                  <div className='flex items-center'>
                    <div>Net Weight/ Unit: </div>
                    <div className='ml-1 font-medium'>{batch.netWeightPerUnit} </div>
                  </div>
                  <div className='flex items-center'>
                    <div>Mfg Date: </div>
                    <div className='ml-1 font-medium'>{getDateFromTimeStamp(batch.mfgDate)} </div>
                  </div>
                  <div className='flex items-center'>
                    <div>Expiry Date: </div>
                    <div className='ml-1 font-medium'>{getDateFromTimeStamp(batch.expDate)} </div>
                  </div>
                </div>
              </div>
              <div>
                <Form.Item
                  name={[batch.id, 'allotedUnits']}
                  label={allocatedBatchDetails.length ? "Allocated Units" : "Allocate Number of Unit"}
                  required
                >
                  <Select style={{ width: '100px' }}>
                    {[...Array(batch.units + 1).keys()].map((_, index) => (
                      <Select.Option key={index} value={index}>
                        {index}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          ))
        }))}
      />
    ),
  }))
  function getBatchesForProduct (productId) {
    const batchList = allocatedBatchDetails?.length ? allocatedBatchDetails : unAllocatedBatchDetails;
    const productBatchList = [];
    batchList.forEach((item) => {
      const list = item.productBatchDetailList.filter((batch) => batch.productId === productId);
      productBatchList.push({
        ...item,
        productBatchDetailList: list
      });
    });
    return productBatchList;    
  }
  const handleFormSubmit = () => {
    // console.log(form.getFieldValue());
    form.validateFields().then(values => {
      let batchData = [];
      const allotedBatchIdList = Object.keys(values);
      unAllocatedBatchDetails.forEach((item) => {
        item.productBatchDetailList.forEach((batch) => {
          const batchExist = allotedBatchIdList.indexOf(batch.id);
          if (batchExist >= 0) {
            batchData.push({
              ...batch,
              units: values[batch.id].allotedUnits,
            })
          }
        });
      });      
      setLoading(true);
      addAllocatedBatchDetails(orderData.id, batchData).then(res => {
        onSumbit(res.data);
        setLoading(false);
        cancelModal();
      })
      .catch(err => {
        console.log(err);
        setLoading(false);
      });
    });
  };
  const cancelModal = () => {
    if (loading) return;
    form.resetFields();
    onCancel();
  };
  const handleResetBatches = () => {
    setLoading(true);
    resetAllocatedBatchDetails(orderData.id).then(() => {
      setAllocatedBatchDetails([]);
      setUnAllocatedBatchDetails([]);
      fetchUnallocatedBatchData();
    }).catch(err => {
      console.log(err);
      setLoading(false);
    });
  }
  const fetchUnallocatedBatchData = () => {
    setLoading(true);
    getUnallocatedBatchDetails(orderData.id).then((res) => {
      if (res.data) {
        setUnAllocatedBatchDetails(res.data);
        setLoading(false);
      }
    }).catch(err => {
      console.log(err);
      setLoading(false);
    });
  }
  const setFormPrefilledData = (batchData) => {
    let formData = {};
    batchData.forEach((item) => {
      item.productBatchDetailList.map((batch) => {
        formData = {
          ...formData,
          [batch.id]: {allotedUnits: batch.units},
        }
      })
    })
    form.setFieldsValue(formData);
  } 

  useEffect(() => {
    if (!allocatedBatchDetails.length) {
      setLoading(true);
      fetchAllocatedBatchDetails(orderData.id).then((res) => {
        if (res.data) {
          setAllocatedBatchDetails(res.data);
          setFormPrefilledData(res.data);
          if (!res.data.length) {
            fetchUnallocatedBatchData()
          }
          else setLoading(false);
        }
      }).catch(err => {
        console.log(err);
        setLoading(false);
      });
    }
  }, []);
  useEffect(() => {
    if (allocatedBatchDetails.length && visible) {
      setFormPrefilledData(allocatedBatchDetails);
    }
  }, [allocatedBatchDetails, visible])


  return (
    <Modal
      open={visible}
      title={'Allocated Batch Details'}
      onCancel={cancelModal}
      destroyOnClose={true}
      width="80vh"
      footer={[
        <Button key="cancel" onClick={cancelModal}>
          Cancel
        </Button>,
        (allocatedBatchDetails.length ? (
          <Button
            loading={loading}
            key="submit"
            type="primary"
            onClick={handleResetBatches}
          >
            Reset Batch Details
          </Button>
        ) : (
          <Button
            loading={loading}
            key="submit"
            type="primary"
            onClick={handleFormSubmit}
          >
            Submit Batch Details
          </Button>
        )),
      ]}
      styles={{
        body: {
          maxHeight: '70vh',
          overflow: 'scroll',
        },
      }}
    >
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
      <div className="mt-3">
        <Form
          disabled={allocatedBatchDetails?.length}
          name="allocated_batchDetails"
          layout="vertical"
          scrollToFirstError
          size="middle"
          form={form}
        >
          <Collapse accordion items={accordianFormList} />
        </Form>
      </div>
    </Modal>
  )
}

export default DispatchBatchModal;

DispatchBatchModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSumbit: PropTypes.func.isRequired,
  orderData: PropTypes.object.isRequired,
};