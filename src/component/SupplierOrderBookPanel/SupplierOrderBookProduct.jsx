import React, { useEffect } from 'react'
import PropTypes from 'prop-types';
import { Card, Col, DatePicker, Form, Input, Row, Select, Button } from 'antd';
import { CloseOutlined, CalendarOutlined } from '@ant-design/icons';
import { DateFormat, formModes, productUOMList } from '../../constants/formConstant';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';
import IncotermInput from '../IncotermInput/IncotermInput';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import DocumentUpload from './DocumentUpload';

const SupplierOrderBookProduct = (props) => {
  const {
    form,
    field,
    index,
    mode,
    remove,
    productList,
    packagingList,
    setInitialValue,
  } = props;

  const formProductIdWatch = Form.useWatch(['products', field.name, 'product'], form);

  useEffect(() => {
    if (formProductIdWatch) {
      const productObj = productList.find(prod => prod.id === formProductIdWatch);
      if (productObj && productObj.tradeName) setInitialValue(productObj);
    }
  }, [formProductIdWatch]);

  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={(
        <CloseOutlined
          onClick={() => {
            remove(field.name);
          }}
        />
      )}
    >
      <Row gutter={16}>
        <Col span={24}>
          {productList.length ? (
            <Form.Item name={[field.name, 'product']} label="Product Name" rules={[{ required: true, message: 'Please select Product' }]}>
              <Select showSearch optionFilterProp="label" disabled={mode === formModes.UPDATE}>
                {productList.map(product => (
                  <Select.Option
                    key={product.id}
                    value={product.id}
                    label={product.tradeName}
                  >
                    {product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'uom']}
            rules={[
              {
                required: true,
                message: 'Please input unit of measurement!',
              },
            ]}
            label="Unit of measurement(UOM)"
          >
            <Select disabled={mode === formModes.UPDATE}>
              {productUOMList.map(uom => (
                <Select.Option key={uom.key} value={uom.value}>
                  {uom.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <PackagingSelect
            packagingList={packagingList}
            field={field}
            mode={mode}
            disableCustomPackaging={true}
            disabled={mode === formModes.UPDATE}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Price per unit"
            rules={[
              {
                required: true,
                message: 'Please input price!',
                validator(_, value) {
                  if (value === undefined || value === null) return Promise.resolve();
                  if (value < 0) {
                    return Promise.reject(new Error('Price cannot be less then zero'));
                  }
                  return Promise.resolve();
                }
              },
            ]}
            name={[field.name, 'price']}
          >
            <Input 
            // disabled={mode === formModes.UPDATE}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Quantity"
            rules={[
              {
                required: true,
                message: 'Please input quantity!',
              },
            ]}
            name={[field.name, 'quantity']}
          >
            <Input disabled={mode === formModes.UPDATE}/>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="No. of Packages"
            rules={[
              {
                required: true,
                message: 'Please provide no. of Packages!',
              },
            ]}
            name={[field.name, 'units']}
          >
            <Input disabled={mode === formModes.UPDATE}/>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="HS Code"
            rules={[
              {
                required: true,
                message: 'Please input hscode!',
              },
            ]}
            name={[field.name, 'hsCode']}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Delivery date" name={[field.name, 'deliveryDate']}>
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <IncotermInput field={field} form={form} parentKey="products" />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'productDescription']}
            label="Product Description"
          >
            <Input.TextArea />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Label"
            rules={[
              {
                // required: true,
                message: 'Please input label!',
              },
            ]}
            name={[field.name, 'label']}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name, 'remarks']} label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={mode}
              savetimeStamp
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.List label="Add Documents" name={[field.name, 'documents']}>
            {(subFields, { add, remove }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                  marginBottom: '24px',
                }}
              >
                <h2 className="text-xl font-semibold text-gray-800">
                  Documents{' '}
                  <span className="text-sm text-gray-500">
                    (Attach COA, costing document, and others as required)
                  </span>
                </h2>
                <Button type="dashed" onClick={() => add()} block>
                  + Add Documents
                </Button>
                {subFields.map((subField, index) => {
                  return (
                    <DocumentUpload
                      key={index}
                      field={field}
                      index={index}
                      remove={remove}
                      subField={subField}
                      productCertificatesType={['select']}
                    />
                  );
                })}
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="moa"
            // rules={[
            //   {
            //     // required: true,
            //     message: 'Please input label!',
            //   },
            // ]}
            name={[field.name, 'moa']}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )
}

export default SupplierOrderBookProduct;

SupplierOrderBookProduct.propTypes = {
  form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  mode: PropTypes.string.isRequired,
  remove: PropTypes.func.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  setInitialValue: PropTypes.func,
  updateDefaultIncoterms: PropTypes.func,
};