import React from 'react';
import PropTypes from 'prop-types';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import { paymentTermsDate, productIncoTermsList, productUOMList } from '../../constants/formConstant';
import { camelCaseToTitle } from '../../util/stringUtils';
import { Collapse, Descriptions } from 'antd';

const SupplierDispatchOrderFormReview = (props) => {
  const { dispatchOrderEntry } = props;

  const orderFieldItems = [
    {
      key: '1',
      label: 'Supplier Name',
      children: dispatchOrderEntry?.supplier?.name || dispatchOrderEntry?.supplierData?.name,
    },
    {
      key: '2',
      label: 'PO number',
      children: dispatchOrderEntry?.purchaseOrderNumber,
    },
    {
      key: '3',
      label: 'PO Date',
      children: getDateFromTimeStamp(dispatchOrderEntry?.purchaseOrderDate),
    },
  ];
  const shipmentFieldItems = [
    {
      key: '1',
      label: 'Supplier ARN Number',
      children: dispatchOrderEntry?.arnNumber,
    },
    {
      key: '2',
      label: 'Shipment date ( Sailing Date )',
      children: getDateFromTimeStamp(dispatchOrderEntry?.shipmentDate),
    },
    {
      key: '3',
      label: 'Incoterms',
      children: (
        <div className='flex flex-col'>
          <div>
            <span style={{ fontWeight: '500' }}>Type:</span>
            <span>{getEnumValueFromList(dispatchOrderEntry?.incoterms?.type, productIncoTermsList)}</span>
          </div>
          <div>
            <span style={{ fontWeight: '500' }}>Country:</span>
            <span>{dispatchOrderEntry?.incoterms?.country}</span>
          </div>
          {dispatchOrderEntry?.incoterms?.data && Object.keys(dispatchOrderEntry.incoterms.data).length > 0
            ? Object.keys(dispatchOrderEntry.incoterms.data).map(key => (
                <div key={key}>
                  <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                  <span>{dispatchOrderEntry.incoterms.data[key]}</span>
                </div>
              ))
            : ''}
        </div>
      ),
    },
  ];
  const paymentFieldItems = [
    {
      key: '10',
      label: 'Credit amount(in %)',
      children: dispatchOrderEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '11',
      label: 'Credit days',
      children: dispatchOrderEntry?.paymentTerms?.creditorDays,
    },
    {
      key: '12',
      label: 'Payment terms based on',
      children: getEnumValueFromList(dispatchOrderEntry?.paymentTerms?.startDate, paymentTermsDate),
    },
  ];
  const dispatchProductsList = dispatchOrderEntry?.products?.length
  ? dispatchOrderEntry.products.map((product, index) => ({
      key: index,
      label: product?.product?.tradeName || dispatchOrderEntry?.productList[index]?.product?.tradeName,
      children: (
        <Descriptions
          layout="vertical"
          column={2}
          items={getProductFields(product)}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      )
    }))
  : [];

  function getProductFields (productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Price per unit',
        children: productData?.price,
      },     
      {
        key: '5',
        label: 'Packaging',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>{productData?.packaging?.type}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Pack Size:</span>
              <span>{productData?.packaging?.packSize}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Tare Weight:</span>
              <span>{productData?.packaging?.tareWeight}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Dimension:</span>
              <span>{productData?.packaging?.dimension}</span>
            </div>
          </div>
        ),
      },
      {
        title: 'Linked Orders',
        dataIndex: 'linkedOrders',
        key: 'linkedOrders',
        width: 200,
        render: (_, { product,linkedOrders }) => {
          const ordersList = linkedOrders?linkedOrders:product?.linkedOrders
          return (
            <>
              {ordersList?.map((linkedOrder) => {
                const labelId = dispatchOrderEntry?.cdoList?.find((order) => order.id == linkedOrder.orderId)?.orderId
                return (
                  <div key={linkedOrder.orderId}>
                  Order Id : {labelId}, Order Qty : {linkedOrder.quantity}
                   <br></br> 
                   <br></br> 
                  </div>
                );
              })}
            </>
          );
  
        },
      },
      {
        key: '6',
        label: 'Units',
        children: productData?.units,
      },
    ];
  }

  return (
    <div className='flex flex-col gap-5'>
      <Descriptions
        title={<div className='text-lg font-semibold'>Order Details</div>}
        items={orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Shipment Details</div>}
        items={shipmentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <div className='text-lg font-semibold'>Product Detail</div>
      <Collapse
        style={{ marginTop: '20px' }}
        items={dispatchProductsList} defaultActiveKey={['0']}
      />
    </div>
  );
}

export default SupplierDispatchOrderFormReview;

SupplierDispatchOrderFormReview.propTypes = {
  dispatchOrderEntry: PropTypes.object.isRequired,
};