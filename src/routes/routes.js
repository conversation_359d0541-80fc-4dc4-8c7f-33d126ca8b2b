import CustomerDashboard from '../component/CustomerPanel/CustomerDashboard';
import CustomerForm from '../component/CustomerPanel/CustomerForm';
import CustomerReadView from '../component/CustomerPanel/CustomerReadView';
import Dashboard from '../component/Dashboard/Dashboard';
// import DispatchOrderDashboard from '../component/DispatchOrderPanel/DispatchOrderDashboard';
// import DispatchOrderForm from '../component/DispatchOrderPanel/DispatchOrderForm';
import DispatchOrderReview from '../component/DispatchOrderPanel/DispatchOrderReview';
import OrderBookDashboard from '../component/OrderBookPanel/OrderBookDashboard';
// import OrderBookForm from '../component/OrderBookPanel/OrderBookForm';
// import OrderReviewData from '../component/OrderBookPanel/OrderReviewData';
import ProductAddPage from '../component/Products/ProductAddPage';
import ProductViewPage from '../component/Products/ProductViewPage';
import ProductEditPage from '../component/Products/ProductEditPage';
import Product from '../component/Products/ProductListPage';
// import SupplierDispatchOrderDashboard from '../component/SupplierDispatchOrderPanel/SupplierDispatchOrderDashboard';
// import SupplierOrderBookDashboard from '../component/SupplierOrderBookPanel/SupplierOrderBookDashboard';
import SupplierPanel from '../component/SupplierPanel';
import SupplierForm from '../component/SupplierPanel/SupplierForm';
import SupplierReadView from '../component/SupplierPanel/SupplierView/SupplierView';
import { userPermissionsList } from '../constants/UserTabsConstants';
import RouteFactory from '../service/RouteFactory';
import EnquiryDashboard from '../component/EnquiryPanel/EnquiryDashboard';
import EnquiryForm from '../component/EnquiryPanel/EnquiryForm';
import EnquiryReview from '../component/EnquiryPanel/EnquiryReview';
// import DispatchDocumentReview from '../component/DispatchOrderPanel/DispatchDocumentReview';
import SupplierOrderReview from '../component/SupplierOrderBookPanel/SupplierOrderReview';
// import SupplierDispatchOrderReadView from '../component/SupplierDispatchOrderPanel/SupplierDispatchOrderReadView';
// import SupplierDispatchOrderForm from '../component/SupplierDispatchOrderPanel/SupplierDispatchOrderForm';
// import SupplierOrderBookForm from '../component/SupplierOrderBookPanel/SupplierOrderBookForm';
import InvoiceDashboard from '../component/InvoicePanel/InvoiceDashboard';
import InvoiceDashBoard2 from '../component/InvoiceUpload/index';
import InvoiceForm from '../component/InvoicePanel/InvoiceForm';
import InventoryDashboard from '../component/InventoryPanel/InventoryDashboard';
import SettingDashboard from '../component/SettingsPanel/SettingDashboard';
import { ReportPanel } from '../component/ReportsPanel';
import { MailPanel } from '../component/MailPanel';
// import InvoiceReview from '../component/InvoicePanel/InvoiceReview';
/* 
  All route param are standard props defined in react router
  Custom Params (Define them as needed):
  allowedView: view defined for access control.
  authReq: check for if user is logged in, it can be redundant use if neccessary.
*/
const routes = [
  {
    path: new RouteFactory().dashboard().build(),
    component: Dashboard,
    exact: true,
    authReq: true,
  },
  // START Customer Routes
  {
    path: new RouteFactory().dashboard().customer().build(),
    component: CustomerDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomerTab,
  },
  {
    path: new RouteFactory().dashboard().customer().add().build(),
    component: CustomerForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.createCustomer,
  },
  {
    path: new RouteFactory().dashboard().customer().setId(':customerId').edit().build(),
    component: CustomerForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.updateCustomer,
  },
  {
    path: new RouteFactory().dashboard().customer().setId(':customerId').view().build(),
    component: CustomerReadView,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomer,
  },
  // END Customer Routes

  // START Enquiry Routes
  {
    path: new RouteFactory().dashboard().enquiry().build(),
    component: EnquiryDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewEnquiryTab,
  },
  {
    path: new RouteFactory().dashboard().enquiry().add().build(),
    component: EnquiryForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.createEnquiry,
  },
  {
    path: new RouteFactory().dashboard().enquiry().setId(':enquiryId').edit().build(),
    component: EnquiryForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.updateEnquiry,
  },
  {
    path: new RouteFactory().dashboard().enquiry().setId(':enquiryId').view().build(),
    component: EnquiryReview,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewEnquiry,
  },
  // END Enquiry Routes
  
  // START Orderbook Routes
  {
    path: new RouteFactory().dashboard().orderBook().build(),
    component: OrderBookDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomerOrderBookTab,
  },
  // {
  //   path: new RouteFactory().dashboard().orderBook().add().build(),
  //   component: OrderBookForm,
  //   exact: true,
  //   authReq: true,
  // },
  // {
  //   path: new RouteFactory().dashboard().orderBook().setId(':orderId').edit().build(),
  //   component: OrderBookForm,
  //   exact: true,
  //   authReq: true,
  // },
  {
    path: new RouteFactory().dashboard().orderBook().setId(':orderId').view().build(),
    component: OrderBookDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewOrderBook,
  },
  // END Orderbook Routes

  // START Dispatch Routes
  // {
  //   path: new RouteFactory().dashboard().dispatchOrder().build(),
  //   component: DispatchOrderDashboard,
  //   exact: true,
  //   authReq: true,
  //   allowedView: userPermissionsList.viewCustomerOrder,
  // },
  // {
  //   path: new RouteFactory().dashboard().dispatchOrder().add().build(),
  //   component: DispatchOrderForm,
  //   exact: true,
  //   authReq: true,
  // },
  // {
  //   path: new RouteFactory().dashboard().dispatchOrder().setId(':dispatchId').edit().build(),
  //   component: DispatchOrderForm,
  //   exact: true,
  //   authReq: true,
  // },
  {
    path: new RouteFactory().dashboard().orderBook().setId(':orderId').dispatchOrder().setId(':dispatchId').view().build(),
    component: DispatchOrderReview,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomerOrder,
  },
  // {
  //   path: new RouteFactory().dashboard().dispatchOrder().setId(':dispatchId').view().reviewDocument(':type').build(),
  //   component: DispatchDocumentReview,
  //   exact: true,
  //   authReq: true,
  // },
  // END Dispatch Routes

  // START INVOICE ROUTES 
  {
    path: new RouteFactory().dashboard().invoice().build(),
    component: InvoiceDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewInvoiceTab,
  },

  {
    path: new RouteFactory().dashboard().invoice().add().build(),
    component: InvoiceForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.createInvoice,
  },
  {
    path: new RouteFactory().dashboard().invoice().setId(':invoiceId').edit().build(),
    component: InvoiceForm,
    exact: true,
    authReq: true,
  },
  {
    path: new RouteFactory().dashboard().invoice().setId(':invoiceId').view().build(),
    component: InvoiceForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.getInvoice,
  },
  // END INVOICE ROUTES

  // START Product Routes
  {
    path: RouteFactory.buildProduct(),
    component: Product,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewProductTab,
  },
  {
    path: new RouteFactory().dashboard().product().add().build(),
    component: ProductAddPage,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.createProduct,
  },
  {
    path: new RouteFactory().dashboard().product().setId(':productId').view().build(),
    component: ProductViewPage,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewProduct,
  },
  {
    path: new RouteFactory().dashboard().product().setId(':productId').edit().build(),
    component: ProductEditPage,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.updateProduct,
  },
  // END Product Routes

  // START supplier Routes
  {
    path: new RouteFactory().dashboard().supplier().build(),
    component: SupplierPanel,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewSupplierTab,
  },
  {
    path: new RouteFactory().dashboard().supplier().add().build(),
    component: SupplierForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.createSupplier,
  },
  {
    path: new RouteFactory().dashboard().supplier().setId(':supplierId').edit().build(),
    component: SupplierForm,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.updateSupplier,
  },
  {
    path: new RouteFactory().dashboard().supplier().setId(':supplierId').view().build(),
    component: SupplierReadView,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewSupplier,
  },
  // END supplier Routes

  //START SUPPLIER ORDER routes

  // {
  //   path: new RouteFactory().dashboard().supplierOrderBook().build(),
  //   component: SupplierOrderBookDashboard,
  //   exact: true,
  //   authReq: true,
  //   allowedView: userPermissionsList.viewSupplierOrderBook,
  // },
  // {
  //   path: new RouteFactory().dashboard().supplierOrderBook().add().build(),
  //   component: SupplierOrderBookForm,
  //   exact: true,
  //   authReq: true,
  // },
  {
    path: new RouteFactory().dashboard().orderBook().setId(':orderId').supplierOrderBook().setId(':supplierOrderId').view().build(),
    component: SupplierOrderReview,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewSupplierOrderBook,
  },
  // {
  //   path: new RouteFactory().dashboard().supplierOrderBook().setId(':Id').edit().build(),
  //   component: SupplierOrderBookForm,
  //   exact: true,
  //   authReq: true,
  // },
  // END SUPPLIER ORDER ROUTES
  // START SUPPLIER DISPTACH ORDER ROUTES
  // {
  //   path: new RouteFactory().dashboard().supplierDispatchOrder().build(),
  //   component: SupplierDispatchOrderDashboard,
  //   exact: true,
  //   authReq: true,
  //   allowedView: userPermissionsList.viewSupplierOrder,
  // },
  // {
  //   path: new RouteFactory().dashboard().supplierDispatchOrder().add().build(),
  //   component: SupplierDispatchOrderForm,
  //   exact: true,
  //   authReq: true,
  // },
  // {
  //   path: new RouteFactory().dashboard().supplierDispatchOrder().setId(':dispatchId').edit().build(),
  //   component: SupplierDispatchOrderForm,
  //   exact: true,
  //   authReq: true,
  // },
  // {
  //   path: new RouteFactory().dashboard().supplierDispatchOrder().setId(':dispatchId').view().build(),
  //   component: SupplierDispatchOrderReadView,
  //   exact: true,
  //   authReq: true,
  // },

  // END SUPPLIER DISPATCH ORDER ROUTES

  //START Inventory Route
  {
    path: new RouteFactory().dashboard().inventory().build(),
    component: InventoryDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewInventoryTab,
  },
  {
    path: new RouteFactory().dashboard().inventory().setId(':inventoryId').setId(':productId').view().build(),
    component: InventoryDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewInventoryProduct,
  },
  //END Inventory Route
  //START settings route
  {
    path: new RouteFactory().dashboard().settings().build(),
    component: SettingDashboard,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.mstackAdmin,
  },
  //END settings route

  // REPORT ROUTE START
  {
    path: new RouteFactory().dashboard().reports().build(),
    component: ReportPanel,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewReportTab,
  },
  // REPORT ROUTE END 
  // MAIL ROUTE START 
  {
    path: new RouteFactory().dashboard().mails().build(),
    component: MailPanel,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomerOrderBookTab,
  },
  // MAIL ROUTE END
  {
    path: new RouteFactory().invoice().build(),
    component: InvoiceDashBoard2,
    exact: true,
    authReq: true,
    allowedView: userPermissionsList.viewCustomerOrderBookTab,
  },
];

export default routes;
