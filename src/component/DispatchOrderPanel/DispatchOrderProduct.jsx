import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Card, Checkbox, Col, Form, Input, InputNumber, Row, Select } from 'antd';
import { formModes, productUOMList } from '../../constants/formConstant';
import { CloseOutlined } from '@ant-design/icons';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import dayjs from 'dayjs';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';
import ObjectUtil from '../../util/objectUtil';
import { validateProductOrderQuantity } from '../../service/api/dispatchOrderApi';

const DispatchOrderProduct = props => {
  const {
    form,
    field,
    index,
    formView,
    remove,
    productList,
    initialValue,
    setInitialValue,
    packagingList,
  } = props;

  const formIdWatch = Form.useWatch(['products', field.name, 'product'], form);

  useEffect(() => {
    if (
      formView !== formModes.READ &&
      formIdWatch &&
      (!initialValue?.products?.length ||
        !initialValue?.products[index] ||
        initialValue?.products[index]?.id !== formIdWatch)
    ) {
      let newInitialValue = JSON.parse(JSON.stringify(initialValue));
      if (!initialValue?.products?.length) initialValue.products = [];
      let newValue = {};
      const [productInfo] = productList.filter(prod => prod.id === formIdWatch);
      if (productInfo && productInfo.uom) {
        newValue = {
          ...productInfo,
          uom: productInfo.uom,
          quantity: productInfo.quantity,
          units: productInfo.units,
          product: productInfo.id,
          packaging: productInfo.packaging,
          price: productInfo.price,
        };
        if (!newInitialValue?.products?.length) newInitialValue.products = [];
        newInitialValue.products[index] = newValue;
        newInitialValue = {
          ...newInitialValue,
          products: [
            ...newInitialValue.products.map((item, idx) => {
              const newProductFormField = form.getFieldValue(['products', idx]);
              return ObjectUtil.mergeWithoutNull(item, newProductFormField);
            }),
          ],
          incoterms: productInfo.incoterms,
          purchaseOrderDate: dayjs(newInitialValue.purchaseOrderDate),
          shipmentDate: productInfo.shipmentDate ? dayjs(productInfo.shipmentDate) : '',
          deliveryDate: productInfo.deliveryDate ? dayjs(productInfo.deliveryDate) : '',
          invoiceDate: productInfo.invoiceDate ? dayjs(productInfo.invoiceDate) : '',
        };
      }
      setInitialValue(newInitialValue);
    }
  }, [formIdWatch]);

  const validateProductQuantity = async (poNumber, productId, productQuantity) => {
    console.log(initialValue);
    const response = await validateProductOrderQuantity(
      poNumber,
      productId,
      productQuantity,
      initialValue?.id,
      initialValue?.customer?.id,
      initialValue?.inventoryId
    );
    const isValid = response.data.isValid;
    const availableQuantity = response.data.availableQuantity;
    if (availableQuantity == 0) return Promise.reject('No more quantity left for usage');
    return isValid
      ? Promise.resolve()
      : Promise.reject(`quantity must not exceed more than 5% of the available quantity, which is ${availableQuantity}`);
  };

  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={
        formView !== formModes.READ ? (
          <CloseOutlined
            onClick={() => {
              remove(field.name);
            }}
          />
        ) : null
      }
    >
      <Row gutter={16}>
        <Col span={24}>
          {productList.length ? (
            <Form.Item
              name={[field.name, 'product']}
              label="Product Name"
              rules={[{ required: false, message: 'Please select a product' }]}
            >
              <Select showSearch optionFilterProp="label" disabled={formView === formModes.UPDATE}>
                {productList.map(product => (
                  <Select.Option
                    key={product.id}
                    value={product.id}
                    label={product.product.tradeName}
                  >
                    {product.product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      {formIdWatch ? (
        <>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name={[field.name, 'uom']}
                label="Unit of measurement(UOM)"
                rules={[{ required: false, message: 'Please select a Uom' }]}
              >
                <Select disabled={formView === formModes.UPDATE}>
                  {productUOMList.map(uom => (
                    <Select.Option key={uom.key} value={uom.value}>
                      {uom.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Price per unit"
                rules={[
                  {
                    required: true,
                    message: 'Please input price!',
                    type: 'number',
                    validator(_, value) {
                      if (value === undefined || value === null) return Promise.resolve();
                      if (value < 0) {
                        return Promise.reject(new Error('Price cannot be less then zero'));
                      }
                      return Promise.resolve();
                    }
                  },
                ]}
                name={[field.name, 'price']}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Chemstack Price per unit"
                name={[field.name, 'chemstackPrice']}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Per Unit Kg value (for chemstack invoice ) "
                name={[field.name, 'perUnitKgValue']}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Batch Date visibility"
                name={[field.name, 'batchDateVisible']}
                valuePropName="checked"
              >
                <Checkbox style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="HS Code" name={[field.name, 'hsCode']}>
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Product Name Alias" name={[field.name, 'productNameAlias']}>
                <Input style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="District of origin" name={[field.name, 'districtOfOrigin']}>
                <Input style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="State of origin" name={[field.name, 'stateOfOrigin']}>
                <Input style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Quantity (Total Weight)"
                name={[field.name, 'quantity']}
                validateTrigger="onBlur"
                // rules={[
                //   {
                //     required: true,
                //     message: 'Please input quantity!',
                //     type: 'number',
                //   },
                //   () => ({
                //     async validator(_, value) {
                //       const [prod] = initialValue.productList.filter(
                //         item => item.id === formIdWatch
                //       );
                //       const poNumber = initialValue.purchaseOrderNumber;

                //       if (prod && prod.quantity) {
                //         if (value && value <= 0) {
                //           return Promise.reject(
                //             new Error('value can never be less then or equal to zero')
                //           );
                //         } else if (value) {
                //           return await validateProductQuantity(poNumber, formIdWatch, value);
                //         }
                //       }
                //       return Promise.reject(new Error('Unable to validate!'));
                //     },
                //   }),
                // ]}
              >
                <InputNumber style={{ width: '100%' }}  />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Units"
                name={[field.name, 'units']}
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: 'Please input units!',
                    type: 'number',
                  },
                  () => ({
                    async validator(_, value) {
                      const [prod] = initialValue.productList.filter(
                        item => item.id === formIdWatch
                      );

                      if (prod && prod.units) {
                        if (value && value <= 0) {
                          return Promise.reject(
                            new Error('value can never be less then or equal to zero')
                          );
                        } else return Promise.resolve();
                      }
                    },
                  }),
                ]}
              >
                <InputNumber style={{ width: '100%' }} disabled={formView === formModes.UPDATE} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Quantity per packaging type"
                name={[field.name, 'quantityPerUnit']}
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: 'Please input quantity per unit!',
                    type: 'number',
                  },
                ]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <PackagingSelect
                packagingList={packagingList}
                field={field}
                disableCustomPackaging
                disabled={formView === formModes.UPDATE}
              />
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Label Description" name={[field.name, 'label']}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name={[field.name, 'remarks']} label="Remarks">
                <MultipleTextFieldInput
                  type="TextArea"
                  mode={formView}
                  placeholder="Enter a remark"
                  savetimeStamp
                  showDelete={formView === formModes.CREATE}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ) : null}
    </Card>
  );
};

export default DispatchOrderProduct;

DispatchOrderProduct.propTypes = {
  form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  formView: PropTypes.string.isRequired,
  remove: PropTypes.func.isRequired,
  productList: PropTypes.array.isRequired,
  initialValue: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func,
  packagingList: PropTypes.array.isRequired,
};
