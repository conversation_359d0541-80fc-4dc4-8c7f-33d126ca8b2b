import React, { useState, useEffect, useRef } from 'react';
import { Form } from 'antd';
import PropTypes from 'prop-types';
import css from './ProductTable.module.css';
import ProductRow from './ProductRow';

const ProductTable = ({ form, formView, packagingList, productList,initialValue,setInitialValue, orderType, productsData, poRequirement }) => {

  const products = Form.useWatch('products', form);

  const getPackagingDetailFromId = (id, packagingList) => {
    if (id && packagingList?.length) {
      const [item] = packagingList.filter(pkgItem => pkgItem.id === id);
      return item;
    }
    return null;
  };

  const packagingDetails = products?.map(prod => getPackagingDetailFromId(prod.packaging, packagingList));
  const packagingType = packagingDetails?.some(pkg => pkg && pkg.type === "Others");

  const [headers] = useState([
    { label: 'Product Name', required: true },
    { label: 'Unit of Measurement', required: true },
    { label: 'Quantity', required: true },
    { label: 'Quantity per packaging type', required: true },
    { label: 'Price per unit', required: true},
    { label: 'Packaging Details ', required: true },
    { label: 'Other Packaging Details', required: packagingType },
    { label: 'Total number of packaging units', required: true },
    { label: 'HS code', required: true },
    { label: 'Delivery date', required: true }, // need to add
    ...(orderType === 'CUSTOMER_ORDER' ? [{ label: 'Incoterms', required: true }] : [] ),
    { label: 'Product Description', required: false },
    { label: 'Documents', required: true }, // need to add
    { label: 'Label', required: false }, // need to add
    {
      label: 'Special delivery instructions',
      required: false,
      info: 'label/packing, shipping marks, docs, delivery appointment',
    }, // added
  ]);

  const scrollRef = useRef(null);
  const [indicatorWidth, setIndicatorWidth] = useState(5); // Initial width
  const [indicatorLeft, setIndicatorLeft] = useState(0); // Initial position
  useEffect(() => {
    if (!scrollRef.current) return;

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      const visibleRatio = clientWidth / scrollWidth; // How much of the table is visible
      const newWidth = visibleRatio * 100; // Convert to percentage
      const newLeft = (scrollLeft / scrollWidth) * 100; // Position of indicator

      setIndicatorWidth(Math.max(newWidth, 10)); // Ensure it's always visible
      setIndicatorLeft(newLeft);
    };

    const scrollDiv = scrollRef.current;
    scrollDiv?.addEventListener('scroll', handleScroll);
    handleScroll(); // Run once initially

    return () => scrollDiv?.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div style={{ position: 'relative', padding: '16px', margin: '0px 0px 12px 0px' }}>
      <div
        style={{
          position: 'absolute',
          bottom: '4px',
          width: '95%',
          height: '6px',
          background: '#e0e0e0',
          borderRadius: '2px',
          overflow: 'hidden',
          margin: '0px 8px',
          left: '50%', // Move to center
          transform: 'translateX(-50%)', // Shift back by half of its width
        }}
      >
        <div
          style={{
            width: `${indicatorWidth}%`,
            left: `${indicatorLeft}%`,
            height: '100%',
            background: 'rgba(35, 86, 138, 0.7)',
            position: 'absolute',
            transition: 'left 0.3s ease-out, width 0.3s ease-out', // Smoother easing
            borderRadius: '2px',
          }}
        ></div>
        {/* Scroll text with subtle bounce animation */}
      </div>
      <div
        style={{
          position: 'absolute',
          right: '16px', // Align text to the right end
          top: '0px', // Move slightly above the bar
          fontSize: '16px',
          color: 'rgba(35, 86, 138, 0.7)',
          padding: '4px',
          fontWeight: 'bold',
          animation: 'bounceText 1.5s infinite alternate ease-in-out', // Bouncing effect
          cursor: 'pointer',
        }}
        onClick={() => {
          const scrollContainer = document.getElementById('scrollableTable');
          if (scrollContainer) {
            scrollContainer.scrollTo({
              left: scrollContainer.scrollWidth, // Scroll to the end
              behavior: 'smooth', // Smooth scrolling effect
            });
          }
        }}
      >
        Scroll →
      </div>

      {/* Keyframes for bounce animation */}
      <style>
        {`
      @keyframes bounceText {
        0% { transform: translateY(0); }
        100% { transform: translateY(-3px); } /* Moves text slightly up */
      }
    `}
      </style>
      <div className={css.ScrollContainer} ref={scrollRef} id="scrollableTable">
        <table
          border="1"
          style={{ width: '100%', borderCollapse: 'collapse' }}
          className={css.productTable}
        >
          <thead>
            <tr>
              {headers.map((header, index) => (
                <th
                  key={index}
                  style={{
                    width: '150px',
                    fontSize: 16,
                    fontWeight: '500',
                    color: 'rgba(35, 86, 138, 0.7)',
                    padding: '0px 6px',
                    textAlign: 'center',
                  }}
                >
                  {header.required ? (
                    <>
                      <span style={{ color: 'red' }}>*</span>{header.label}
                    </>
                  ) : (
                    header.label
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <Form.List name="products">
              {fields => (
                <>
                  {fields.map(({ key, name }, index) => {
                    return (
                      <ProductRow
                        key={key}
                        name={name}
                        index={index}
                        form={form}
                        productList={productList}
                        packagingList={packagingList}
                        formView={formView}
                        initialValue={initialValue}
                        setInitialValue={setInitialValue}
                        orderType = {orderType}
                        productData={productsData}
                        poRequirement={poRequirement}
                      />
                    );
                  })}
                </>
              )}
            </Form.List>
          </tbody>
        </table>
      </div>
    </div>
  );
};

ProductTable.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  initialValue: PropTypes.object,
  setInitialValue: PropTypes.func,
  orderType: PropTypes.string.isRequired,
  productsData: PropTypes.object,
  poRequirement: PropTypes.string,
};

export default ProductTable;
