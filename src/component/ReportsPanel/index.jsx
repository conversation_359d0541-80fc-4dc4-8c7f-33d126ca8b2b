import { Button, DatePicker, Form, Select, message } from 'antd';
import { Option } from 'antd/es/mentions';
import React, { useState } from 'react';
import { DateFormat } from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';
import { CalendarOutlined } from '@ant-design/icons';
import { generateReport } from '../../service/api/reportApi';

export const ReportPanel = () => {
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  const handleFormSubmit = () => {
    // console.log(form.getFieldValue());
    form.validateFields().then(values => {
      generateReport(values)
        .then(res => {
          if (res.status == '200') messageApi.success('request queued please check mail');
          console.log('data ', res.data);
        })
        .catch(err => {
          const errMsg = err?.response?.data?.message;
          messageApi.error(errMsg);
        });
    });
  };
  const [showDateType, setShowDateType] = useState(false);

  const handleReportTypeChange = (value) => {
    setShowDateType(value === 'mstack_invoice' || value === 'chemstack_invoice');
    if (value !== 'mstack_invoice' && value !== 'chemstack_invoice') {
      form.setFieldValue('dateType', undefined);
    }
  };
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
      }}
    >
      {contextHolder}
      <Form name="report-gen-form" form={form}>
        <h4>Please note date range filter will apply on po date </h4>
        <Form.Item
          label="Report Type"
          name="reportType"
          rules={[
            {
              required: true,
              message: 'Please select report type!',
            },
          ]}
        >
          <Select
            placeholder="Select report type"
            onChange={handleReportTypeChange}
            allowClear
          >
            <Option value="mstack_invoice">Mstack Invoice Report</Option>
            <Option value="chemstack_invoice">ChemStack Invoice Report</Option>
            <Option value="salesorder_invoice">Sales Order Report</Option>
            <Option value="opensalesorder_invoice">Open Sales Order Report</Option>
          </Select>
        </Form.Item>
        {showDateType && (
          <Form.Item
            label="Date Type"
            name="dateType"
            rules={[
              {
                required: true,
                message: 'Please select date type!',
              },
            ]}
          >
            <Select placeholder="Select date type" allowClear>
              <Option value="PO_DATE">PO Date</Option>
              <Option value="INVOICE_DATE">Invoice Date</Option>
            </Select>
          </Form.Item>
        )}
        <Form.Item
          label="From"
          name="from"
          rules={[
            {
              required: true,
              message: 'Please select from date!',
            },
          ]}
        >
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={{ width: '100%' }}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
          />
        </Form.Item>
        <Form.Item
          label="To"
          name="to"
          rules={[
            {
              required: true,
              message: 'Please select to date!',
            },
          ]}
        >
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={{ width: '100%' }}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
          />
        </Form.Item>
        <Button onClick={() => handleFormSubmit()} block>
          Generate and Send Report
        </Button>
      </Form>
    </div>
  );
};
