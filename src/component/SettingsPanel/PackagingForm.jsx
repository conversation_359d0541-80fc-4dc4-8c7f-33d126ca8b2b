import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Form, Input, InputNumber, Row, Select } from 'antd';
import { formModes, productUOMList } from '../../constants/formConstant';
import { createPackaging } from '../../service/api/packagingApi';
import PageLoader from '../Loaders/PageLoader';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons'

const PackagingForm = (props) => {

  const { mode, packaging, hide } = props;

  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();
  const {api} = useNotificationContext();

  const savePackagingDetails = (packagingData) => {
    setLoading(true);
    if (mode === formModes.CREATE) {
      createPackaging(packagingData).then(() => {
        setLoading(false);
        hide();
      })
      .catch(error => {
        setLoading(false);
        const errMsg = error?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        })
      });
    } 
    // else if (mode === formModes.UPDATE) {
    //   updatePackaging(packagingData, packaging.id).then(() => {
    //     setLoading(false);
    //     hide();
    //   })
    //   .catch(error => {
    //     console.log(error);
    //     setLoading(false);
    //   });
    // }
  }
  const validateFormandSubmit = () => {
    form.validateFields().then((values) => {
      savePackagingDetails(values);
    }).catch(error => console.log(error));
  }

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
      <Form
        name="customer_form"
        layout="vertical"
        scrollToFirstError
        initialValues={mode === formModes.CREATE ? {} : packaging}
        preserve={false}
        size="middle"
        form={form}
        validateTrigger="onBlur"
      >
        <div className='mb-1 font-semibold'>Packaging Details</div>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Packaging Type" name="type" rules={[{ required: true }]}>
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Pack size" name="psize" rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="puom"
              label="Unit of measurement(UOM)"
              rules={[{ required: false, message: 'Please select a Uom' }]}
            >
              <Select>
                {productUOMList.map(uom => (
                  <Select.Option key={uom.key} value={uom.value}>
                    {uom.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Tare Weight" name="tweight" rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="tweightUom"
              label="Unit of measurement(UOM)"
              rules={[{ required: false, message: 'Please select a Uom' }]}
            >
              <Select>
                {productUOMList.map(uom => (
                  <Select.Option key={uom.key} value={uom.value}>
                    {uom.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Dimension" name="dimension">
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div className='absolute left-0 bottom-0 w-full p-2 flex justify-between bg-base-white z-10 box-border'>
        <Button
          type="primary"
          size="large"
          onClick={() => validateFormandSubmit()}
          loading={loading}
        >
          {mode === formModes.CREATE ? 'Add Packaging' : 'Save Changes'}
        </Button>
      </div>
    </>
  )
}

export default PackagingForm;

PackagingForm.propTypes = {
  mode: PropTypes.string.isRequired,
  packaging: PropTypes.object,
  hide: PropTypes.func.isRequired,
}