import React from 'react';
import { Input, Button, List } from 'antd';
import { DeleteOutlined, EnterOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import { formModes } from '../../constants/formConstant';
import ObjectUtil from '../../util/objectUtil';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { isStringEmpty } from '../../util/stringUtils';

const MultipleTextFieldInput = props => {
  const {
    value = [],
    onChange,
    type,
    mode,
    placeholder = 'Enter value',
    savetimeStamp = false,
    showDelete = true,
  } = props;

  // const [fieldValue, setFieldValue] = useState('');

  const handleInputChange = e => {
    e.preventDefault();
    const newTextField = e.target.value;
    const fieldTimeStamp = new Date();
    const tempValue= value || [];
    if (!isStringEmpty(newTextField)) {
      onChange([
        ...tempValue,
        savetimeStamp
          ? {
              value: newTextField,
              timeStamp: fieldTimeStamp,
            }
          : newTextField,
      ]);
      // setFieldValue('')
    }
    
  };
  const handleRemoveTextField = index => {
    const updatedTextField = [...value];
    updatedTextField.splice(index, 1);
    onChange(updatedTextField);
  };

  return (
    <div>
      {mode !== formModes.READ ? (
        type === 'TextArea' ? (
          <Input.TextArea
            // value={fieldValue}
            rows={3}
            placeholder={placeholder}
            onPressEnter={handleInputChange}
            allowClear
          />
        ) : (
          <Input
            // value={fieldValue}
            placeholder={placeholder}
            onPressEnter={handleInputChange}
            addonAfter={<EnterOutlined />}
            allowClear
          />
        )
      ) : null}
      {value?.length ? (
        <List
          style={{ marginTop: '16px' }}
          dataSource={value}
          renderItem={(item, index) => (
            <List.Item
              actions={
                showDelete
                  ? [
                      <Button
                        key={index}
                        type="text"
                        icon={<DeleteOutlined />}
                        onClick={() => handleRemoveTextField(index)}
                      />,
                    ]
                  : null
              }
            >
              {ObjectUtil.isObject(item) ? (
                <div>
                  <div>{item.value}</div>
                  {savetimeStamp ? (
                    <div style={{ color: ' #23568a', fontWeight: '600' }}>
                      {getDateFromTimeStamp(item.timeStamp)}
                    </div>
                  ) : null}
                </div>
              ) : (
                <div>{item}</div>
              )}
            </List.Item>
          )}
        />
      ) : null}
    </div>
  );
};

export default MultipleTextFieldInput;

MultipleTextFieldInput.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
  type: PropTypes.string,
  mode: PropTypes.string,
  placeholder: PropTypes.string,
  savetimeStamp: PropTypes.bool,
  showDelete: PropTypes.bool,
};
