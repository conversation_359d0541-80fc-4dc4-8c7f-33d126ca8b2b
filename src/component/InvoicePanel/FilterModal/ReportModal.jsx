import React, { useEffect, useState } from 'react';
import { Al<PERSON>, Button, DatePicker, Form, Modal } from 'antd';
import PropTypes from 'prop-types';

const ReportModal = props => {
  const {disabled, open, onCancel,onSubmit,form } = props;
  const [errorMsg, setErrorMsg] = useState('');

  // reset error message if modal open or closes
  useEffect(() => setErrorMsg(''), [open]);

  return (
    <Modal
      open={open}
      title="Generate Report"
      okText="Generate & Send to mail"
      cancelText="Cancel"
      onCancel={() => onCancel()}
      footer={[
        errorMsg ? (
          <Alert
            key="alert"
            style={{ margin: '6px', textAlign: 'left' }}
            message={errorMsg}
            type="error"
            showIcon
          />
        ) : (
          ''
        ),
        <Button
          key="reset"
          onClick={() => {
            setErrorMsg('');
            form.resetFields();
          }}
        >
          Reset
        </Button>,
        <Button
          key="apply"
          type="primary"
          onClick={() => {
            onSubmit()
          }}
        >
          Generate & Send Mail
        </Button>,
      ]}
    >
      <Form disabled={disabled} form={form} layout="vertical">
        <Form.Item
          name="from"
          label="From"
          rules={[{ required: true, message: 'Please select a From date' }]}
        >
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name="to"
          label="To"
          rules={[{ required: true, message: 'Please select a To date' }]}
        >
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReportModal;

ReportModal.propTypes = {
  disabled: PropTypes.bool.isRequired,
  open: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  form: PropTypes.func.isRequired
};
