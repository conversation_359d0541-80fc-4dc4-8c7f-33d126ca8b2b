import React, { useEffect } from 'react'
import PropTypes from 'prop-types';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import css from './Loader.module.css';

const PageLoader = (props) => {
  const { style } = props;
  
  const antIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
      spin
    />
  );

  useEffect(() => {
    document.body.classList.add('no-scroll');

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, []);

  return (
    <div className={css.pageLoader} style={style}>
      <Spin indicator={antIcon} />
    </div>
  )
}

export default PageLoader;

PageLoader.propTypes = {
  style: PropTypes.object,
}