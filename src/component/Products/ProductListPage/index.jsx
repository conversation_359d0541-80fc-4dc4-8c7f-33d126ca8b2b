import React, { useEffect, useState } from 'react';
import { getProductList } from '../../../service/api/productApi';
import ProductList from './listView';
import HeaderPanel from '../../headerPanel';
import { getQeryParamsFromURL } from '../../../util/route';
import { useLocation } from 'react-router-dom';

const Product = () => {
  const [loading, setLoading] = useState(false);
  const [productList, setProductList] = useState({});
  const location =useLocation()

  const getProducts = async (pageSize,pageNumber, filters, searchkey) => {
    try {
      setLoading(true);
      const response = await getProductList(pageSize,pageNumber, filters, searchkey);
      setProductList(response.data);
      setLoading(false);
    } catch (error) {
      console.log('Error while retriving product list');
      console.log(error);
      setLoading(false);
    }
  };
  useEffect(() => {
    //api call to get product list
    const pageNumber = 0;
    const {filters,searchkey}= getQeryParamsFromURL()
    const pageSize=10;
    // console.log('products ',searchkey)
    getProducts(pageSize,pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Product Details" />
      <ProductList
        productList={productList}
        getProductHandler={getProducts}
        loading={loading}
      />
    </>
  );
};

export default Product;
