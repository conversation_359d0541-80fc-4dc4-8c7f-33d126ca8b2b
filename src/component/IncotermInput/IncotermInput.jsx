/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { Form, Input, Select } from 'antd';
import { incotermsDataList, productIncoTermsList, countryList } from '../../constants/formConstant';

const IncotermInput = props => {
  const { name, form, parentKey, prefilledValue,hideLabel } = props;
  const watchPath =
    name != undefined && parentKey ? [parentKey, name, 'incoterms', 'type'] : ['incoterms', 'type'];

  const incotermType = Form.useWatch(watchPath, form);


  const getFieldFromType = formItem => {
    if (formItem.type === 'textarea') return <Input.TextArea rows={4} />;
    else if (formItem.type === 'text') return <Input />;
    else if (formItem.type === 'select' && formItem.child)
      return (
        <Select>
          {formItem.child.map(child => (
            <Select.Option key={child.key} value={child.value}>
              {child.label}
            </Select.Option>
          ))}
        </Select>
      );
  };

  useEffect(() => {
    if (prefilledValue) {
      const path = name != undefined && parentKey ? [parentKey, name, 'incoterms'] : 'incoterms';
      form.setFieldsValue({
        [path]: prefilledValue,
      });
    }
  }, [prefilledValue, name, parentKey, form]);

  return (
    <Form.Item>
      <Form.Item
        label={"Pick one incoterm"}
        name={name != undefined ? [name, 'incoterms', 'type'] : ['incoterms', 'type']}
        rules={[
          {
            required: true,
            message: 'Please select Incoterm!',
          },
        ]}
      >
        <Select>
          {productIncoTermsList?.map(term => (
            <Select.Option key={term.key} value={term.value}>
              {term.label}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        label="Destination Country"
        name={name != undefined ? [name, 'incoterms', 'country'] : ['incoterms', 'country']}
        rules={[
          {
             required: true,
            message: 'Please select Country!',
          },
        ]}
      >
        <Select showSearch optionLabelProp="value">
          {countryList.map((country, index) => (
            <Select.Option key={index} value={country.name}>
              {`${country.flag} ${country.name}`}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      {incotermType && incotermType.length ? (
        <>
          {incotermType
            ? incotermsDataList[incotermType]?.map(formItem => (
                <Form.Item
                  key={formItem.key}
                  name={
                    name != undefined
                      ? [name, 'incoterms', 'data', formItem.name]
                      : ['incoterms', 'data', formItem.name]
                  }
                  label={formItem.label}
                  rules={[
                    {
                      required: true,
                      message: `Please input ${formItem.label}!`,
                    },
                  ]}
                >
                  {getFieldFromType(formItem)}
                </Form.Item>
              ))
            : null}
        </>
      ) : null}
    </Form.Item>
  );
};

export default IncotermInput;
