import React from 'react'
import PropTypes from 'prop-types';
import DocumentItem from './DocumentItem';

const DocumentList = (props) => {

  const { 
    document, setSelectedDocument, selectedDocument,
  } = props;

  return (
    <div className='overflow-auto flex flex-col items-start self-stretch'>
      <DocumentItem
        key={document?.docType}
        documentData={document}
        setSelectedDocument={setSelectedDocument}
        selectedDocument={selectedDocument}
      />
    </div>
  )
}

export default DocumentList;

DocumentList.propTypes = {
  document: PropTypes.array.isRequired,
  setSelectedDocument: PropTypes.func.isRequired,
  selectedDocument: PropTypes.object.isRequired,
}