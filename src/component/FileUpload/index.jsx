import { UploadOutlined, DeleteOutlined, DownloadOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Upload, notification } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import EnvKeys from '../../constants/EnviornmentKeys';
import apiEndpoints from '../../constants/apiConstant';
import { downloadFile } from '../../service/api/storageService';
import PageLoader from '../Loaders/PageLoader';

export default function FileUpload({
  accept = 'application/pdf, image/*',
  meta = {},
  category = '',
  buttonText = 'Upload File',
  onChange,
  value,
  maxFileLimit = 10,
  isSingle = false,
  disabled = false,
  deleteDisabled = false,
  onDelete,
  btnStyle,
  multiple = false
}) {
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(null);

  // Handle initializing value properly based on isSingle
  useEffect(() => {
    if (value) {
      const valuesArray = Array.isArray(value) ? value : [value];
      const newFileList = valuesArray.map(e => ({
        uid: e?.fileId,
        name: e?.name,
        status: e?.status || 'done',
        remark: e?.remark,
      }));
      setFileList(newFileList);
    } else {
      setFileList([]);
    }
  }, [value]);

  // Data formatter
  const setDataForRequest = file => ({
    fileDto: JSON.stringify({
      meta,
      category,
      name: file.name,
    }),
  });

  // Handle upload change
  const handleChange = async info => {
    let updatedFileList = info.fileList;

    if (isSingle) {
      updatedFileList = updatedFileList.slice(-1); // Keep only one file
    }

    updatedFileList = updatedFileList.map(file => {
      if (file.status === 'error') {
        notification.error({
          message: 'Upload Failed',
          description: `Failed to upload ${file.name}`,
        });
        return null;
      }
      return {
        ...file,
        uid: file.response?.id || file.uid,
      };
    }).filter(Boolean);

    setFileList(updatedFileList);

    const finalValue = isSingle
      ? updatedFileList.length > 0
        ? {
            name: updatedFileList[0].name,
            fileId: updatedFileList[0].uid,
            status: updatedFileList[0].status,
            remark: updatedFileList[0].remark,
          }
        : null
      : updatedFileList.map(file => ({
          name: file.name,
          fileId: file.uid,
          status: file.status,
          remark: file.remark,
        }));

    if (onChange) {
      onChange({
        target: { value: finalValue },
      });
    }
  };

  // Download handler
  const onDownload = async file => {
    try {
      setLoading(file.uid);
      const apiRes = await downloadFile(file.uid);
      window.open(apiRes.data.url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    } finally {
      setLoading(null);
    }
  };

  // Delete handler
  const handleFileDelete = index => {
    const updatedFileList = [...fileList];
    updatedFileList.splice(index, 1);
    setFileList(updatedFileList);

    const finalValue = isSingle
      ? null
      : updatedFileList.map(file => ({
          name: file.name,
          fileId: file.uid,
          status: file.status,
          remark: file.remark,
        }));

    if (onChange) {
      onChange({
        target: { value: finalValue },
      });
    }

    if (onDelete) onDelete(index);
  };

  return (
    <>
      <Upload
        accept={accept}
        action={EnvKeys.apiHost + apiEndpoints.storageFileUpload}
        withCredentials={true}
        data={setDataForRequest}
        maxCount={maxFileLimit}
        onChange={handleChange}
        fileList={fileList}
        showUploadList={false}
        style={btnStyle}
        multiple={multiple}
      >
        {!disabled && (
          <Button icon={<UploadOutlined />} style={btnStyle}>
            {buttonText}
          </Button>
        )}
      </Upload>

      <div style={!disabled ? { marginTop: '16px' } : null}>
        {fileList.length > 0 &&
          fileList.map((file, index) =>
            file.status !== 'uploading' ? (
              <div key={file.uid} className="flex gap-2 flex-wrap">
                <div>
                  <div style={file.status === 'error' ? { color: 'red' } : null}>
                    {file.name}
                    {file.status === 'error' && <span>: Failed to Upload</span>}
                  </div>
                  {file.remark && (
                    <div>
                      <b>Remark:</b>&nbsp;{file.remark}
                    </div>
                  )}
                </div>
                <span style={{ display: 'flex' }}>
                  {!deleteDisabled && !disabled && (
                    <DeleteOutlined
                      onClick={() => handleFileDelete(index)}
                      style={{ color: 'red' }}
                    />
                  )}
                  {loading === file.uid ? (
                    <LoadingOutlined style={{ marginLeft: '15px' }} />
                  ) : (
                    <DownloadOutlined
                      onClick={() => onDownload(file)}
                      style={{ marginLeft: '15px' }}
                    />
                  )}
                </span>
              </div>
            ) : (
              <PageLoader
                key={file.uid}
                style={{
                  position: 'fixed',
                  width: '100vw',
                  height: '100vh',
                  top: 0,
                  left: 0,
                }}
              />
            )
          )}
      </div>
    </>
  );
}

FileUpload.propTypes = {
  accept: PropTypes.string,
  meta: PropTypes.object,
  category: PropTypes.string,
  buttonText: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.any,
  maxFileLimit: PropTypes.number,
  isSingle: PropTypes.bool, // ✅ NEW PROP TO CONTROL OBJECT vs ARRAY
  disabled: PropTypes.bool,
  deleteDisabled: PropTypes.bool,
  onDelete: PropTypes.func,
  btnStyle: PropTypes.object,
  multiple: PropTypes.bool
};
