/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Button, Space, message } from 'antd';
import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import { useDispatch } from 'react-redux';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { useSelector } from 'react-redux';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { Link } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { deleteInvoice } from '../../service/api/invoiceApi';

const InvoiceSideBar = props => {
  const { invoice, hide } = props;
  const user = useSelector(state => state.user);
  const dispatch = useDispatch();
  const [messageApi, contextHolder] = message.useMessage();

  const openFormView = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };
  const hideSideDrawer = () => {
    hide();
  };
  const handleDelete = id => {
    deleteInvoice(id)
      .then(() => {
        console.log('sucessfully deleted');
        window.location.replace(new RouteFactory().dashboard().invoice().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        messageApi.error(errMsg);
      });
  };

  console.log('invoice data ', invoice);

  return invoice && invoice?.items?.length ? (
    <div className={css.customerSider}>
      {contextHolder}
      <div className={css.titleBar}>
        {/* TODO:change the heading to order id */}
        <div className={css.title}>{`${invoice.invoiceNumber}`}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().invoice().setId(invoice.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => openFormView()}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        {hasPermission(userPermissionsList.updateCustomerOrder, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().invoice().setId(invoice.id).edit().build()}>
            <Button
              onClick={() => openFormView()}
              style={{}}
              type="primary"
              icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Edit
            </Button>
          </Link>
        ) : null}
        {hasPermission(userPermissionsList.deleteInvoice, user.permissions) ? (
          <Button onClick={() => handleDelete(invoice.id)}>Delete</Button>
        ) : null}
      </div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Invoice Currency
          </div>
          <div className={css.value}>{invoice?.currencyType}</div>
        </Space.Compact>
        {/* <Space.Compact block direction="vertical"	size="medium">
          <div className={css.label} style={{color:'rgba(35, 86, 138, 0.70)'}}>Order Id</div>
          <div className={css.value}>{order.orderid}</div>
        </Space.Compact> */}
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Created
          </div>
          <div className={css.value}>{getDateFromTimeStamp(invoice.createdAt)}</div>
        </Space.Compact>
        {invoice?.items?.map((invoiceItem, index) => (
          <div
            key={index}
            style={{
              padding: '15px',
              borderRadius: '12px',
              border: '1px solid rgba(30, 30, 30, 0.20)',
            }}
          >
            <Space.Compact block direction="vertical">
              <div
                style={{ color: 'rgba(35, 86, 138, 0.70)', fontSize: '20px', marginBottom: '10px' }}
              >{`Product ${index + 1}`}</div>
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Product Name
              </div>
              <div className={css.value}>{invoiceItem?.product?.tradeName}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Quantity
              </div>
              <div className={css.value}>{invoiceItem.quantity}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                CP/Unit
              </div>
              <div className={css.value}>{invoiceItem.cpPerUnit}</div>
            </Space.Compact>
          </div>
        ))}
      </Space>
    </div>
  ) : null;
};

export default InvoiceSideBar;

InvoiceSideBar.proptypes = {
  invoice: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
};
