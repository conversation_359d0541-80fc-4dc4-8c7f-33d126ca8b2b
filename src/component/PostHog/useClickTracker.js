import { usePostHog } from "posthog-js/react";
import { useSelector } from "react-redux";

const useClickTracker = () => {
  const posthog = usePostHog();
  const user = useSelector((state) => state.user);

  const trackClick = (eventName = "button_clicked", metadata = {}) => {
    if (posthog && user?.id) {
      posthog.capture(eventName, {
        user_id: user.id,
        email: user.username,
        timestamp: new Date().toISOString(),
        ...metadata,
      });
      console.log(`[Click Tracker] Event '${eventName}' sent`, metadata);
    }
  };

  return { trackClick };
};

export default useClickTracker;
