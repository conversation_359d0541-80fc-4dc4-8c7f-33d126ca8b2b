import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Table, Select, Radio, Button, Input } from 'antd';
import css from './Dashboard.module.css';
import TaskCompletionPopup from './TaskCompletionPopup';
import OrderLink from './OrderLink';
import { getTasks } from '../../service/api/taskService';
import { getEmployeeList } from '../../service/api/employee';
import PageLoader from '../Loaders/PageLoader';
import PropTypes from 'prop-types';
import { formatEntityType } from '../../util/formUtil';

const OrderTaskTable = ({ orderId }) => {
  const [taskData, setTaskData] = useState({ content: [], totalElements: 0 });
  const [loading, setLoading] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);
  const [filters, setFilters] = useState({});

  // Fetch employees
  useEffect(() => {
    getEmployeeList().then(response => {
      if (response?.data?.content) setEmployeeList(response.data.content);
    });
  }, []);

  // Fetch tasks for this order
  const fetchTasks = async (newFilters = null) => {
    if (loading) return; // Prevent duplicate calls
    setLoading(true);
    let currentFilters = newFilters !== null ? newFilters : filters;
    if (newFilters !== null) {
      setFilters(newFilters);
    }
    const payload = {
      size: 1000, // Large size to get all tasks
      number: 0,
      filters: {},
      search: {
        ...currentFilters,
        orderId: orderId,
      },
    };
    const response = await getTasks(payload);
    const newData = response?.data || { content: [], totalElements: 0 };
    setTaskData(newData);
    setLoading(false);
  };

  useEffect(() => {
    fetchTasks();
  }, [orderId]);



  // Table columns (copy from Dashboard.jsx, but remove group/my logic)
  const columns = [
    {
      title: 'Orders',
      dataIndex: 'orderId',
      key: 'orderId',
      width: 100,
      render: (_, { entityType }) => formatEntityType(entityType),
    },
    {
      title: 'Order no.',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 100,
      render: (_, { entityId }) => entityId,
    },
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (_, { name }) => name,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 125,
      render: (_, { status }) => status,
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 150,
      render: (_, record) => (
        <Select
          showSearch
          value={record.assignedTo ?? 'NONE'}
          style={{ width: '120px' }}
          disabled
        >
          <Select.Option value="NONE">None</Select.Option>
          {employeeList.map(employee => (
            <Select.Option key={employee.id} value={employee.id}>
              {employee.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Customer Name',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
      render: (_, { customerName }) => customerName,
    },
    {
      title: 'Product Name',
      dataIndex: 'productName',
      key: 'productName',
      width: 140,
      render: (_, { productName }) => productName,
    },
    {
      title: 'Action',
      key: 'action',
      width: 90,
              render: (_, record) => (
          <TaskCompletionPopup taskData={record} taskUpdated={() => fetchTasks()} />
        ),
    },
  ];

  return (
    <div style={{ height: '100%' }}>
      {/* Show PO number and Order Type above the table */}
      {taskData?.content?.length > 0 && (
        <div style={{ marginBottom: 16, display: 'flex', gap: 32, alignItems: 'center', fontSize: 16 }}>
          <div><b>PO Number:</b> {taskData.content[0].secondaryId}</div>
          <div><b>Order Type:</b> {(() => {
            const entityID = taskData.content[0].orderId || '';
            if (entityID.includes('COB')) return 'CUSTOMER';
            if (entityID.includes('SAMPLEOB')) return 'SAMPLE';
            return 'Unknown';
          })()}</div>
        </div>
      )}
      <Table
        loading={{
          indicator: <PageLoader style={{ backgroundColor: 'transparent' }} />, spinning: loading
        }}
        columns={columns}
        dataSource={taskData?.content}
        rowKey="id"
        tableLayout="fixed"
        pagination={false}
        scroll={{ x: '100%', y: 'calc(100vh - 200px)', scrollToFirstRowOnChange: false }}
        bordered
      />
      <div style={{ textAlign: 'center', padding: '10px 0', color: '#999' }}>
        {taskData?.content?.length === 0 ? 'No tasks found' : ''}
      </div>
    </div>
  );
};

export default OrderTaskTable; 


OrderTaskTable.propTypes = {
  orderId: PropTypes.string.isRequired,
};
  