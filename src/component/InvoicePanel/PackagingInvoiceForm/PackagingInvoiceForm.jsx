import React, { useEffect, useState } from 'react';
import { Form, Row, Col, Input, DatePicker, Button, Select, InputNumber } from 'antd';
import { CalendarOutlined, PlusOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import FileUpload from '../../FileUpload';
import DispatchItem from './DispatchItem';
import { currencyTypeList } from '../../../constants/formConstant';
import { getOrderBookList } from '../../../service/api/orderBookApi';
import { formModes } from '../../../constants/formConstant';
import { roundToTwoDecimalPlaces } from '../../../util/formatUtil';

const PackagingInvoiceForm = ({ form, formView }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [purchaseOrderList, setPurchaseOrderList] = useState([]);
  const productItems = Form.useWatch([`items`], form);

  const handleAddItem = () => {
    setItems([...items, {}]);
  };

  const handleRemoveItem = index => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  useEffect(() => {
    // existing dispatch items
    const tempForm = form.getFieldValue();
    const tempItems = tempForm && tempForm['items'] ? tempForm['items'] : [];
    if (tempItems && tempItems.length > 0) {
      setItems([...tempItems]);
    }
  }, []);

  useEffect(() => {
    if (productItems) {
      let totalCostPrice = 0;
      productItems.forEach(product => {
        if (product['cost'] >= 0) {
          totalCostPrice += roundToTwoDecimalPlaces(
            product.cost + ((product.taxInPercent ? product.taxInPercent : 0) / 100) * product.cost
          );
        }
      });
      form.setFieldValue(['totalCost'], roundToTwoDecimalPlaces(totalCostPrice));
      if (formView == formModes.DELETE) console.log(setLoading);
    } else form.setFieldValue(['totalCost'], undefined);
  }, [productItems]);

  useEffect(() => {
    if (!purchaseOrderList || !purchaseOrderList.length) {
      setLoading(true);
      //api call to get order list TODO: format api params
      const pageNumber = 0;
      const filters = {};
      const searchkey = '';
      getOrderBookList(null, pageNumber, filters, searchkey)
        .then(res => {
          if (res.data && res.data.content) {
            setPurchaseOrderList(res.data.content);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);

  return (
    <>
      <Row gutter={24}>
        <Col span={3}>
          <Form.Item
            label="Vendor Name"
            name="vendorName"
            rules={[{ required: true, message: 'Please enter Vendor Name' }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={3}>
          <Form.Item
            label="Invoice Number"
            name="invoiceNumber"
            rules={[{ required: true, message: 'Please enter Invoice Number' }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={3}>
          <Form.Item
            label="Invoice Amount"
            name="invoiceAmount"
            rules={[{ required: true, message: 'Please enter Invoice Amount' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={3}>
          <Form.Item
            label="Payment Date"
            name="paymentDate"
            rules={[{ required: true, message: 'Please select Payment Date' }]}
          >
            <DatePicker
              format="YYYY-MM-DD" // Change date format as needed
              disabled={formView === formModes.READ}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
        <Col span={3}>
          <Form.Item
            label="Invoice Date"
            name="invoiceDate"
            rules={[{ required: true, message: 'Please select Invoice Date' }]}
          >
            <DatePicker
              format="YYYY-MM-DD" // Change date format as needed
              disabled={formView === formModes.READ}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item
            label="Invoice File"
            name="invoiceFile"
            rules={[{ required: true, message: 'Please upload Invoice File' }]}
          >
            <FileUpload
              category="Orders"
              meta={{ poId: 123, documentType: 'PurchaseOrder' }}
              // disabled={formView === formModes.UPDATE}
            />
          </Form.Item>
        </Col>
        <Col span={3}>
          <Form.Item
            label="Currency Type"
            name="currencyType"
            rules={[{ required: true, message: 'Please enter Currency Type' }]}
          >
            <Select>
              {currencyTypeList.map((currencyType, index) => {
                return (
                  <Select.Option key={index} value={currencyType.value} label={currencyType.label}>
                    {currencyType.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      {items.map((item, index) => (
        <DispatchItem
          key={index}
          form={form}
          formView={formView}
          index={index}
          handleRemoveItem={handleRemoveItem}
          loading={loading}
          purchaseOrderList={purchaseOrderList}
        />
      ))}
      <Row gutter={16} justify={'center'}>
        <Col span={8}>
          <Button
            type="dashed"
            onClick={handleAddItem}
            style={{ width: '100%', marginBottom: 16 }}
            icon={<PlusOutlined />}
          >
            Add Item
          </Button>
        </Col>
      </Row>
      <Row gutter={16} justify={'center'}>
        <Col span={12}>
          <Form.Item
            label="Total Cost"
            name="totalCost"
            rules={[
              { required: true },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const invoiceAmount = getFieldValue('invoiceAmount');
                  if (invoiceAmount == null || invoiceAmount == undefined)
                    return Promise.reject(new Error('Please enter invoice amount'));
                  if (value >= invoiceAmount - 1 && value <= invoiceAmount + 1) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('Total cost must be equal to invoice amount'));
                },
              }),
            ]}
          >
            <Input disabled />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

PackagingInvoiceForm.propTypes = {
  form: PropTypes.func.isRequired,
  formView: PropTypes.string,
};

export default PackagingInvoiceForm;
