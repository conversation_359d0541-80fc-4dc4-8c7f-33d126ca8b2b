import React from 'react';
import headerCss from './header.module.css';
import { Button } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';

const HeaderPanel = ({ sideButtons, name }) => {
  const dispatch = useDispatch();
  const collapseAsideBar = useSelector(state => state.collapseAsideBar);

  const toggleAsideBar = () => {
    dispatch(setCollapseAsideBar(!collapseAsideBar));
  };

  return (
    <div className={headerCss.header}>
      <Button
        onClick={toggleAsideBar}
        className={headerCss.foldMenuBtn}
        type="text"
        size="large"
        icon={
          collapseAsideBar ? (
            <MenuUnfoldOutlined style={{ color: '#000' }} />
          ) : (
            <MenuFoldOutlined style={{ color: '#000' }} />
          )
        }
      />
      <div className={headerCss.title}>{name}</div>
      <div className={headerCss.formBtnContainer}>{sideButtons}</div>
    </div>
  );
};

export default HeaderPanel;

HeaderPanel.propTypes = {
  name: PropTypes.string.isRequired,
  sideButtons: PropTypes.node,
};
