import { Descriptions } from 'antd';
import React from 'react';
import PropTypes from 'prop-types';

const SupplierBasicDetailsView = props => {
  const { supplierData } = props;

  const getAddrFromSupplierData = data => {
    const street = data?.street;
    const country = data?.country;
    const state = data?.state;
    const city = data?.city;
    const postalCode = data?.postalCode;
    let addrStr = street ? street : '';
    addrStr += city ? ' , ' + city : '';
    addrStr += state ? ' , ' + state : '';
    addrStr += country ? ' , ' + country : '';
    addrStr += postalCode ? ' - ' + postalCode : '';
    return addrStr;
  };

  const items = [
    {
      key: '1',
      label: 'Supplier Name',
      children: supplierData?.name,
    },
    {
      key: '2',
      label: 'Email Id',
      children: supplierData?.email,
    },
    {
      key: '3',
      label: 'Phone Number',
      children: supplierData?.mobile,
    },
    {
      key: '4',
      label: 'Revenue ',
      children: supplierData?.revenue,
    },
    {
      key: '5',
      label: 'Corporate Address ',
      children: getAddrFromSupplierData(supplierData?.address),
    },
    {
      key: '6',
      label: 'GSTIN ',
      children: supplierData?.gstin,
    },
    {
      key: '4',
      label: 'PAN ',
      children: supplierData?.pan,
    },
  ];

  return supplierData ? <Descriptions colon={false} layout="vertical" items={items} labelStyle={{ fontWeight: '700', color: '#23568A' }}/> : null;
};

export default SupplierBasicDetailsView;

SupplierBasicDetailsView.propTypes = {
  supplierData: PropTypes.object,
};
