/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Button, Space } from 'antd';
import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import { useSelector } from 'react-redux';
// import { setFormView } from '../../store/actions/formView';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import { getDateFromTimeStamp } from '../../util/dateUtils';
// import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { Link, 
  // useNavigate
 } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';

const SupplierOrderBookSideBar = props => {
  const { order, productId, hide } = props;
  const [productDetail] = order?.products
    ? order.products.filter(item => item.id === productId)
    : [];

  // const dispatch = useDispatch();
  // const navigate = useNavigate();
  const user = useSelector(state => state.user);

  // const openFormViewFromMode = mode => {
  //   const status = {
  //     show: true,
  //     mode,
  //   };
  //   dispatch(setFormView(status));
  //   dispatch(setCollapseAsideBar(true));
  //   hide();
  // };
  const hideSideDrawer = () => {
    hide();
  };

  return order && productDetail ? (
    <div className={css.customerSider}>
      <div className={css.titleBar}>
        <div className={css.title}>{`${order.purchaseOrderNumber}`}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().supplierOrderBook().setId(order.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => {
              hideSideDrawer(true);
              // navigate(new RouteFactory().dashboard().supplierOrderBook().setId(order.id).view().build())
            }}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        {hasPermission(userPermissionsList.createsupplierOrderBook, user.permissions) ? (
          <Link
            to={new RouteFactory().dashboard().supplierOrderBook().setId(order.id).edit().build()}
            target="_blank"
          >
            <Button
              onClick={() => {
                hideSideDrawer(true);
                // navigate(
                //   new RouteFactory().dashboard().supplierOrderBook().setId(order.id).edit().build()
                // );
              }}
              style={{}}
              type="primary"
              icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Edit
            </Button>
          </Link>
        ) : null}
      </div>
      <Space direction="vertical" size="large">
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Supplier Name
          </div>
          <div className={css.value}>{order.supplier?.name}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            PO Number
          </div>
          <div className={css.value}>{order.purchaseOrderNumber}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            PO Date
          </div>
          <div className={css.value}>{getDateFromTimeStamp(order.purchaseOrderDate)}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Product
          </div>
          <div className={css.value}>{productDetail.product?.tradeName}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Quantity
          </div>
          <div className={css.value}>{productDetail.quantity}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Price
          </div>
          <div className={css.value}>{productDetail.price}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Incoterms
          </div>
          <div className={css.value}>{productDetail.incoterms?.type}</div>
        </Space.Compact>
      </Space>
    </div>
  ) : null;
};

export default SupplierOrderBookSideBar;

SupplierOrderBookSideBar.proptypes = {
  order: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
  productId: PropTypes.string.isRequired,
};
