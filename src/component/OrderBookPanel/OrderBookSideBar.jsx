import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Dropdown, Modal, Space, Table, Drawer } from 'antd';
import close from '../../assets/icons/close.svg';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import OrderReviewData from './OrderReviewData';
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import {
  deleteDispatchOrderFromId,
  getDispatchOrderForOrderbook
} from '../../service/api/dispatchOrderApi';
import PageLoader from '../Loaders/PageLoader';
import {
  deleteSupplierOrderBook,
  getSupplierOrderFromOrderBook,
} from '../../service/api/supplierOrderBookApi';
import { deleteOrderBook, getOrderDataFromId } from '../../service/api/orderBookApi';
import {
  allocatedInventoryHeader,
  dispatchOrderHeaderV2,
  inventoryOutOrderHeader,
  supplierDispatchColumns,
  supplierOrderBookHeaderV2,
} from '../../constants/TableConstants';
import { userEntity, userPermissionsList } from '../../constants/UserTabsConstants';
import {
  EditOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
  LoadingOutlined,
} from '@ant-design/icons';
import { formModes } from '../../constants/formConstant';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { hasPermission } from '../../util/userUtils';
import { useSelector } from 'react-redux';
import {
  deleteInventoryOutOrder,
  getAllocatedInventoryForOrderbook,
  getInventoryOutOrderForOrderbook,
} from '../../service/api/inventoryApi';
import DocumentGeneration from '../DocumentGeneration/DocumentGeneration';
import DocumentViewer from '../DocumentViewer/DocumentViewer';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { downloadFile } from '../../service/api/storageService';
import OrderFullfilmentModal from './OrderFullfilmentModal';
import TaskTable from '../Task/TaskTable';
import OrderTaskTable from '../Dashboard/OrderTaskTable';
import './OrderBookSummaryTable.css';
import OrderBookSummaryTable from './OrderBookSummaryTable';
import useFormTimeTracker from '../PostHog/useFormTimeTracker';
import { getSupplierDispatchOrderList } from '../../service/api/supplierDispatchOrderApi';

const OrderBookSideBar = props => {
  const { hide } = props;

  const [loading, setLoading] = useState(false);
  const [orderbookData, setOrderBookData] = useState({});
  const [dispatchOrders, setDispatchOrder] = useState([]);
  const [supplierOrder, setSupplierOrder] = useState([]);
  const [supplierDispatchOrder, setSupplierDispatchOrder] = useState([]);
  const [supplierDispatchOrderList, setSupplierDispatchOrderList] = useState({})
  const [inventoryOutOrders, setInventoryOutOrders] = useState([]);
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });
  const [showDocGeneration, setShowDocGeneration] = useState(false);
  const [showDocPreview, setShowDocPreview] = useState(false);
  const [docData, setDocData] = useState(null);
  const [allotedInventoryList, setAllotedInventoryList] = useState([]);
  const [showFullfillmentModal, setFullFilmentModal] = useState(false);
  const [editAllocatedProduct, setEditAllocatedProduct] = useState(null);
  const [unallocateProduct, setUnallocatedProduct] = useState(null);
  const [confirmationModalVisible, setConfirmationModalVisible] = useState({
    show: false,
    entity: null,
    id: null,
  });
  const [taskTableModalOpen, setTaskTableModalOpen] = useState(false);

  const supplierOrderFormOpened = useFormTimeTracker("Supplier Order");
  const dispatchOrderFormOpened = useFormTimeTracker("Dispatch Order");
  const supplierDispatchOrderFormOpened = useFormTimeTracker("Supplier Dispatch Order");

  const user = useSelector(state => state.user);
  const navigate = useNavigate();
  const location = useLocation();
  const { orderId } = useParams();
  const { api } = useNotificationContext();

  const dispatchTableActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    render: (_, record) => (
      <>
        {hasPermission(userPermissionsList.updateCustomerOrder, user.permissions) ? (
          <Button type="text" onClick={() => handleEditDispatchOrder(record)}>
            Edit
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.viewCustomerOrder, user.permissions) ? (
          <Link
            to={new RouteFactory()
              .dashboard()
              .orderBook()
              .setId(orderId)
              .dispatchOrder()
              .setId(record.id)
              .view()
              .build()}
          >
            <Button type="text">See Detail</Button>
          </Link>
        ) : null}
        {hasPermission(userPermissionsList.deleteCustomerOrder, user.permissions) ? (
          <Button
            type="text"
            onClick={() => {
              setConfirmationModalVisible({
                show: true,
                entity: userEntity.dispatchOrder,
                id: record.id,
              });
            }}
          >
            Delete
          </Button>
        ) : null}
      </>
    ),
  };
  const supplierTableActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    width: 250,
    render: (_, record) => (
      <div className="grid grid-cols-1 items-center">
        {hasPermission(userPermissionsList.createSupplierOrder, user.permissions) ? (
          <Button type="text" onClick={() => handleAddSupplierDispatchOrder(record)}>
            Add SDO
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createSupplierOrder, user.permissions)
          ? getSupplierPOFileButton(record)
          : null}
        {hasPermission(userPermissionsList.viewSupplierOrderBook, user.permissions) ? (
          <Button type="text">
            <Link
              to={`${new RouteFactory()
                .dashboard()
                .orderBook()
                .setId(orderId)
                .supplierOrderBook()
                .setId(record.id)
                .view()
                .build()}?poGenrationRequired=${record?.poGenrationRequired}`}
            >
              See Detail
            </Link>
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.supplierDeleteOrderBook, user.permissions) ? (
          <Button
            type="text"
            onClick={() => {
              setConfirmationModalVisible({
                show: true,
                entity: userEntity.supplierOrderBook,
                id: record.id,
              });
            }}
          >
            Delete
          </Button>
        ) : null}
      </div>
    ),
  };
  const allocatedInventoryActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    render: (_, record) => (
      <>
        {/* <Button
          type='text'
          onClick={() => {
            setEditAllocatedProduct({
              productId: record?.proudctDetail?.productId,
              packagingId: record?.packagingDetail?.packagingId,
            });
            setFullFilmentModal(true);
          }}
        >
          Edit
        </Button> */}
        {hasPermission(userPermissionsList.unallocateInventory, user.permissions) ? (
          <Button
            type="text"
            onClick={() => {
              setUnallocatedProduct({
                productId: record?.proudctDetail?.productId,
                packagingId: record?.packagingDetail?.packagingId,
                allocatedUnitDetails: record?.allocatedUnitDetails,
              });
              setFullFilmentModal(true);
            }}
          >
            Unallocate
          </Button>
        ) : null}
      </>
    ),
  };
  const inventoryTableActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    render: (_, record) => (
      <div className="flex items-center">
        {hasPermission(userPermissionsList.viewInventoryLinkOrder, user.permissions) ? (
          <Button type="text" onClick={() => showInventoryLinkDetails(record)}>
            See Detail
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.deleteInventoryLinkOrder, user.permissions) ? (
          <Button
            type="text"
            onClick={() => {
              setConfirmationModalVisible({
                show: true,
                entity: userEntity.inventoryOut,
                id: record.id,
              });
            }}
          >
            Delete
          </Button>
        ) : null}
      </div>
    ),
  };

    // Function to check if all products in all dispatch orders have marginApproved
    const checkAllProductsMarginApproved = () => {
      if (!dispatchOrders || dispatchOrders.length === 0) {
        return false;
      }
  
      // Check all dispatch orders
      return dispatchOrders.every(dispatchOrder => {
        // Check all products in each dispatch order
        return dispatchOrder.products && dispatchOrder.products.length > 0 && 
               dispatchOrder.products.every(product => 
                 product.marginApprovedBy && product.marginApprovedOn
               );
      });
    };

    
  const addMenuItems = [
    // Conditionally add item if not SAMPLE
    ...(orderbookData?.orderType !== 'SAMPLE' && (true || (dispatchOrders?.length == 0 || (hasPermission(userPermissionsList.createMultipleOrders, user.permissions) && checkAllProductsMarginApproved())))
      ? [
          {
            label: 'Dispatch Order',
            key: 1,
            disabled: !hasPermission(userPermissionsList.createCustomerOrder, user.permissions),
            onClick: () => {dispatchOrderFormOpened.startTracking();
              handleAddDispatchOrder()},
          },
        ]
      : []), // If SAMPLE, add nothing
    ...(((checkAllProductsMarginApproved() ||
    orderbookData?.orderType === 'SAMPLE') &&  (true || (supplierOrder?.length == 0 || (hasPermission(userPermissionsList.createMultipleOrders, user.permissions)))))
      ? [
          {
            label: 'Supplier Order',
            key: 2,
            disabled: !hasPermission(userPermissionsList.createsupplierOrderBook, user.permissions),
            onClick: () => {supplierOrderFormOpened.startTracking();
              handleAddSupplierOrder()},
          },
        ]
      : []),
    // Always add this
    // getInventoryOutOrderOption(),
  ];

  function showInventoryLinkDetails(inventoryLinkOrder) {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.inventoryOut,
        mode: formModes.READ,
        formData: inventoryLinkOrder,
      },
    });
  }
  function getSupplierPOFileButton(order) {
    let btns = [];
    if (order?.purchaseOrderFile?.[0].fileId) {
      btns.push(
        <Button type="text" onClick={() => handleDocPreview(order.purchaseOrderFile[0])}>
          View PO
        </Button>
      );
    }
    if(order?.poGenrationRequired == null || order?.poGenrationRequired === true || order?.poGenrationRequired === 'true'){
      btns.push(
        <Button type="text" onClick={() => handleDocGeneration(order?.id)}>
          {btns && btns.length > 0 ? 'Re-' : ''}Generate PO
        </Button>
      );
     }
    return btns;
  }
  function getInventoryOutOrderOption() {
    return orderbookData?.inventoryId
      ? null
      : {
          label: 'Inventory Link Order',
          key: 3,
          disabled: !hasPermission(userPermissionsList.createInventoryLinkOrder, user.permissions),
          onClick: () => handleAddInventoryOutOrder(),
        };
  }
  function handleInventoryOutOrderBookDelete(inventoryId) {
    setLoading(true);
    deleteInventoryOutOrder(inventoryId)
      .then(() => {
        setLoading(false);
        api.success({
          messgae: 'Success',
          description: 'Sucessfully deleted',
          icon: (
            <CheckCircleFilled
              style={{
                color: '#107C10',
              }}
            />
          ),
        });
        navigate(0);
      })
      .catch(err => {
        setLoading(false);
        const errMsg = err?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
      });
  }
  function handleSupplierOrderBookDelete(id) {
    deleteSupplierOrderBook(id)
      .then(() => {
        api.success({
          messgae: 'Success',
          description: 'Sucessfully deleted',
          icon: (
            <CheckCircleFilled
              style={{
                color: '#107C10',
              }}
            />
          ),
        });
        navigate(0);
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
      });
  }
  function handleCustomerOrderDelete(id) {
    setLoading(true);
    deleteDispatchOrderFromId(id)
      .then(() => {
        setLoading(false);
        api.success({
          messgae: 'Success',
          description: 'Sucessfully deleted',
          icon: (
            <CheckCircleFilled
              style={{
                color: '#107C10',
              }}
            />
          ),
        });
        navigate(0);
      })
      .catch(err => {
        setLoading(false);
        const errMsg = err?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
      });
  }
  const handleOrderBookDelete = id => {
    deleteOrderBook(id)
      .then(() => {
        api.success({
          messgae: 'Success',
          description: 'Sucessfully deleted',
          icon: (
            <CheckCircleFilled
              style={{
                color: '#107C10',
              }}
            />
          ),
        });
        navigate(new RouteFactory().dashboard().orderBook().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
      });
  };
  const handleDeleteConfirmationModal = () => {
    switch (confirmationModalVisible?.entity) {
      case userEntity.orderBook:
        return handleOrderBookDelete(confirmationModalVisible?.id);
      case userEntity.dispatchOrder:
        return handleCustomerOrderDelete(confirmationModalVisible?.id);
      case userEntity.supplierOrderBook:
        return handleSupplierOrderBookDelete(confirmationModalVisible?.id);
      case userEntity.inventoryOut:
        return handleInventoryOutOrderBookDelete(confirmationModalVisible?.id);
      default:
        return;
    }
  };
  const handleCancelConfirmationModal = () => {
    setConfirmationModalVisible({
      show: false,
      entity: null,
      id: null,
    });
  };
  const hideSideDrawer = () => {
    hide();
    navigate(new RouteFactory().dashboard().orderBook().build() + location.search);
  };
  const handleAddDispatchOrder = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.dispatchOrder,
        mode: formModes.CREATE,
        orderBook: orderbookData,
        formTimeTracker: dispatchOrderFormOpened,
        orderType: orderbookData?.orderType,
      },
    });
  };
  const handleEditOrderbook = record => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.orderBook,
        mode: formModes.UPDATE,
        formData: record,
      },
    });
  };
  const handleEditDispatchOrder = record => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.dispatchOrder,
        mode: formModes.UPDATE,
        orderBook: orderbookData,
        formData: record,
      },
    });
  };
  const handleAddSupplierOrder = () => {
    // if (orderbookData?.inventoryId) showSupplierOrderOverlay();
    // else setFullFilmentModal(true);
    showSupplierOrderOverlay();
  };

  const showSupplierOrderOverlay = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.supplierOrderBook,
        mode: formModes.CREATE,
        orderBookId: orderId,
        orderBookProducts: orderbookData?.products,
        orderType: orderbookData?.orderType,
        orderBook: orderbookData,
        formTimeTracker: supplierOrderFormOpened
      },
    });
  };
  const handleAddSupplierDispatchOrder = record => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.supplierDispatchOrder,
        mode: formModes.CREATE,
        orderBook: record,
        customerorderbookId: orderId,
        poGenrationRequired: record?.poGenrationRequired,
        formTimeTracker: supplierDispatchOrderFormOpened,
        orderType: orderbookData?.orderType,
      },
    });
    supplierDispatchOrderFormOpened.startTracking()
  };
  const handleAddInventoryOutOrder = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.inventoryOut,
        mode: formModes.CREATE,
        customerDispatchList: dispatchOrders,
      },
    });
  };
  const fetchAssignedQuantity = product => {
    return dispatchOrders.reduce(
      (totalQty, order) =>
        totalQty +
        order.products.reduce((qty, item) => qty + (item.id == product ? item.quantity : 0), 0),
      0
    );
  };
  const fetchOrderDetails = () => {
    setLoading(true);
    const apiArray = [getOrderDataFromId(orderId)];

    console.log("helllll",apiArray)

    if (apiArray?.[0].data?.orderType === 'CUSTOMER_ORDER') {
      apiArray.push(getInventoryOutOrderForOrderbook(orderId));
    } else {
      apiArray.push(Promise.resolve({ data: [] }));
    }
    if (hasPermission(userPermissionsList.viewCustomerOrder, user.permissions)) {
      apiArray.push(getDispatchOrderForOrderbook(orderId));
    }
    if (hasPermission(userPermissionsList.viewSupplierOrderBook, user.permissions)) {
      apiArray.push(getSupplierOrderFromOrderBook(orderId));
    }
    Promise.all(apiArray)
      .then(res => {
        if (res?.[0].data) {
          setOrderBookData(res[0].data);
        }
        if (res?.[1].data) {
          setInventoryOutOrders(res?.[1].data);
        }
        if (res?.[2]?.data) {
          setDispatchOrder(res[2].data);
        }
        if (res?.[3]?.data) {
          setSupplierOrder(res[3].data);
          if (res?.[0].data?.orderType === 'SAMPLE') {
            const searchkey = res[3]?.data?.[0]?.purchaseOrderNumber
            getSupplierDispatchOrderList(null, null, null, searchkey).then(data => {
              setSupplierDispatchOrderList({
                shippingAddress: res[3]?.data?.[0]?.shippingAddress || 'N/A',
                destinationPartnerAddress: data?.data?.content[0]?.destinationPartnerAddress || 'N/A',
                dispatchToDestinationDate: data?.data?.content[0]?.dispatchToDestinationDate
                  ? new Date(data.data.content[0].dispatchToDestinationDate).toLocaleDateString() : 'N/A',
                deliveryAtDestinationPartnerDate: data?.data?.content[0]?.deliveryAtDestinationPartnerDate
                  ? new Date(data.data.content[0].deliveryAtDestinationPartnerDate).toLocaleDateString() : 'N/A'
              });
            })
          }
        }
        setLoading(false);
      })
      .catch(err => {
        console.log(err);
        setLoading(false);
      });
  };
  const handleDocGeneration = orderId => {
    setDocData({
      docType: 'SUPPLIER_PO',
      entityId: orderId,
      entityType: 'SUPPLIER_ORDER_BOOK',
      label: 'Supplier PO',
      meta: {
        supplierOrderbookId: orderId,
      },
    });
    setShowDocGeneration(true);
  };
  const hideDocumentGeneration = () => {
    setShowDocGeneration(false);
    setDocData(null);
  };
  const handleDocPreview = docData => {
    setDocData(docData);
    setShowDocPreview(true);
  };
  const hideDocPreview = () => {
    setShowDocPreview(false);
    setDocData({});
  };
  const onDocumentGeneration = (file, err, orderId) => {
    setTimeout(() => {
      api.open({
        key: 'fileGenerate',
        message: file ? 'File Generated' : 'Generation Failed',
        description: (
          <div>
            <div className="text-xs font-medium">
              {file ? file.label : 'Unable to generated file'}
            </div>
            <div className="text-xs text-sub-text-black">
              {file ? file.fileName : err?.response?.data?.message || err?.message}
            </div>
          </div>
        ),
        duration: 5,
        icon: file ? (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ) : (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
      if (file) {
        const newSupplierOrders = supplierOrder.map(order => {
          if (order.id === orderId) {
            return {
              ...order,
              purchaseOrderFile: [file],
            };
          }
          return order;
        });
        setSupplierOrder(newSupplierOrders);
      }
    }, 500);
  };
  const onDownload = async file => {
    try {
      api.open({
        key: 'fileDownload',
        message: 'Downloading File...',
        description: (
          <div>
            <div className="text-xs font-medium">{file.label}</div>
            <div className="text-xs text-sub-text-black">{file.fileName}</div>
          </div>
        ),
        duration: 0,
        icon: (
          <LoadingOutlined
            style={{
              color: '#0F6CBD',
            }}
          />
        ),
      });
      const fileId = file?.fileId;
      const apiRes = await downloadFile(fileId);
      window.open(apiRes.data.url, '_self');
      api.open({
        key: 'fileDownload',
        message: 'File Downloaded',
        description: (
          <div>
            <div className="text-xs font-medium">{file.label}</div>
            <div className="text-xs text-sub-text-black">{file.fileName}</div>
          </div>
        ),
        duration: 3,
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ),
      });
    } catch (error) {
      console.log('Error while fetching url from storage service');
      api.open({
        key: 'fileDownload',
        message: 'Download Failed!!',
        description: (
          <div>
            <div className="text-xs font-medium">{file.label}</div>
            <div className="text-xs text-sub-text-black">{file.fileName}</div>
          </div>
        ),
        duration: 3,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
      console.log(error);
    }
  };
  const fetchAllocatedInventory = () => {
    getAllocatedInventoryForOrderbook(orderId)
      .then(res => {
        if (res?.data) {
          setAllotedInventoryList(res.data);
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
      fetchAllocatedInventory();
    }
  }, [orderId]);

  return (
    <>
      {showDocGeneration && docData ? (
        <div className="py-8 px-5 h-full">
          <DocumentGeneration
            hide={() => hideDocumentGeneration()}
            docData={docData}
            onDocumentGeneration={onDocumentGeneration}
            height="h-full"
          />
        </div>
      ) : showDocPreview && docData ? (
        <div className="py-8 px-5 h-full">
          <DocumentViewer
            document={docData}
            previewOnly={true}
            hide={hideDocPreview}
            onDownload={onDownload}
            height="h-full"
          />
        </div>
      ) : orderbookData?.id && !loading ? (
        <div className={css.customerSider}>
          <div className={css.titleBar}>
            <div className={css.title}>
              <div className="text-xl font-medium">{orderbookData?.purchaseOrderNumber}</div>
              <div className="text-sm text-[#616161]">
                {!orderbookData?.inventoryId
                  ? orderbookData?.customer?.name
                  : orderbookData?.inventory?.name}
              </div>
            </div>
            <div className="flex items-center gap-4">
              {hasPermission(userPermissionsList.updateOrderBook, user.permissions) ? (
                <Button
                  type="default"
                  icon={<EditOutlined />}
                  onClick={() => handleEditOrderbook(orderbookData)}
                  className="rounded-[4px]"
                >
                  Edit
                </Button>
              ) : null}
              {hasPermission(userPermissionsList.deleteOrderBook, user.permissions) ? (
                <Button
                  type="default"
                  onClick={() => {
                    setConfirmationModalVisible({
                      show: true,
                      entity: userEntity.orderBook,
                      id: orderbookData.id,
                    });
                  }}
                  className="rounded-[4px]"
                >
                  Delete OrderBook
                </Button>
              ) : null}
              {hasPermission(userPermissionsList.updateOrderBook, user.permissions) ? (
                 <Button
                 type="default"
                 onClick={() => setTaskTableModalOpen(true)}
                 className="rounded-[4px]"
               >
                 Task Table
               </Button>
              ) : null}
              <Drawer
                open={taskTableModalOpen}
                onClose={() => setTaskTableModalOpen(false)}
                width="100%"
                title="Order Tasks"
                destroyOnClose
                placement="right"
              >
                <OrderTaskTable orderId={orderbookData.orderBookId} />
              </Drawer>
              <Dropdown
                menu={{
                  items: addMenuItems,
                }}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined style={{ color: '#fff' }} />}
                  className="rounded-[4px]"
                >
                  <Space>
                    Add
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
              <div className={css.closeBtn} onClick={hideSideDrawer}>
                <img src={close} alt="close" />
              </div>
            </div>
          </div>
          {/* Order Summary Table */}
          {orderbookData?.orderType === 'CUSTOMER_ORDER' ? (
          <OrderBookSummaryTable orderbook={orderbookData} dispatchOrders={dispatchOrders} supplierOrder={supplierOrder}  supplierDispatchOrder={supplierDispatchOrder}/>
          ) : null}
          <OrderReviewData
            orderBookEntry={orderbookData}
            fetchAssignedQuantity={fetchAssignedQuantity}
          />
          {hasPermission(userPermissionsList.viewCustomerOrder, user.permissions) &&
          dispatchOrders?.length &&
          orderbookData?.orderType !== 'SAMPLE' ? (
            <div>
              <div className="mt-4 mb-4 text-lg font-semibold">Customer Dispatch Orders</div>
              <Table
                columns={[...dispatchOrderHeaderV2, dispatchTableActions]}
                dataSource={dispatchOrders.map(order => ({ ...order, key: order.id }))}
                pagination={false}
              />
            </div>
          ) : null}
          {hasPermission(userPermissionsList.viewSupplierOrderBook, user.permissions) &&
          supplierOrder.length ? (
            <>
            <div>
              <div className="mt-4 mb-4 text-lg font-semibold">Supplier Orders</div>
              <Table
                columns={[...supplierOrderBookHeaderV2, supplierTableActions]}
                dataSource={supplierOrder.map(order => ({ ...order, key: order.id }))}
                pagination={false}
              />
            </div>
          {orderbookData?.orderType === 'SAMPLE' ? (
            <div className="mt-6 border-t border-gray-300 ">
              <div className="mt-4 mb-4 text-lg font-semibold">Destination Partner Details</div>
                <Table
                  columns={supplierDispatchColumns}
                  dataSource={[
                    { ...supplierDispatchOrderList, key: supplierDispatchOrderList.id || '1' },
                  ]}
                  pagination={false}
                />
            </div>
          ) : null}
            </>
          ) : null}
          {hasPermission(userPermissionsList.viewInventoryLinkOrder, user.permissions) &&
          inventoryOutOrders?.length ? (
            <div>
              <div className="mt-4 mb-4 text-lg font-semibold">Inventory Link Orders</div>
              <Table
                columns={[...inventoryOutOrderHeader, inventoryTableActions]}
                dataSource={inventoryOutOrders.map(order => ({ ...order, key: order.id }))}
                pagination={false}
              />
            </div>
          ) : null}
          {hasPermission(userPermissionsList.viewAllocatedInventory, user.permissions) &&
          allotedInventoryList?.length ? (
            <div>
              <div className="mt-4 mb-4 text-lg font-semibold">Allocated Inventory</div>
              <Table
                columns={[...allocatedInventoryHeader, allocatedInventoryActions]}
                dataSource={allotedInventoryList.map((order, index) => ({ ...order, key: index }))}
                pagination={false}
              />
            </div>
          ) : null}
        </div>
      ) : (
        <PageLoader style={{ position: 'absolute', width: '100%' }} />
      )}
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={(entity="",submitted=false) => {
                 setOverlayData({
                   show: false,
                   drawerData: {},
                 });
               
                supplierOrderFormOpened.endTracking(submitted)
                dispatchOrderFormOpened.endTracking(submitted)
                supplierDispatchOrderFormOpened.endTracking(submitted)
        }}
        onSubmit={fetchOrderDetails}
      />
      {showFullfillmentModal ? (
        <OrderFullfilmentModal
          visible={showFullfillmentModal}
          onCancel={fetchAllocatedData => {
            setFullFilmentModal(false);
            if (fetchAllocatedData) fetchAllocatedInventory();
            setEditAllocatedProduct(null);
            setUnallocatedProduct(null);
          }}
          orderId={orderId}
          addSupplierOrder={showSupplierOrderOverlay}
          isProductEdit={editAllocatedProduct?.productId ? true : false}
          unallocateView={unallocateProduct?.productId ? true : false}
          productId={editAllocatedProduct?.productId || unallocateProduct?.productId}
          packagingId={editAllocatedProduct?.packagingId || unallocateProduct?.packagingId}
          allocatedUnitDetails={unallocateProduct?.allocatedUnitDetails}
        />
      ) : null}
      <Modal
        title="Do you confirm to delete the entity?"
        open={confirmationModalVisible?.show}
        onOk={handleDeleteConfirmationModal}
        onCancel={() => handleCancelConfirmationModal()}
        destroyOnClose
      />
    </>
  );
};

export default OrderBookSideBar;

OrderBookSideBar.propTypes = {
  hide: PropTypes.func.isRequired,
};
