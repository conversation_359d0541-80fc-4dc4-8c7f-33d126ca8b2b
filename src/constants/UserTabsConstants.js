import React from 'react';
import RouteFactory from '../service/RouteFactory';
import customerIcon from '../assets/icons/Customer_Regular.svg';
import customerIconFilled from '../assets/icons/Customer_Filled.svg';
import enquiryIcon from '../assets/icons/Enquiry_Regular.svg';
import enquiryIconFilled from '../assets/icons/Enquiry_Filled.svg';
import orderBookIcon from '../assets/icons/Orderbook_Regular.svg';
import orderBookIconFilled from '../assets/icons/Orderbook_Filled.svg';
import productIcon from '../assets/icons/Products_Regular.svg';
import productIconFilled from '../assets/icons/Products_Filled.svg';
import supplierIcon from '../assets/icons/Suppliers_Regular.svg';
import supplierIconFilled from '../assets/icons/Suppliers_Filled.svg';
import invoiceIcon from '../assets/icons/Invoice_Regular.svg';
import invoiceIconFilled from '../assets/icons/Invoice_Filled.svg';
import inventoryIcon from '../assets/icons/Inventory_Regular.svg';
import inventoryIconFilled from '../assets/icons/Inventory_Filled.svg';
import reportIcon from '../assets/icons/report-icon.svg';
import mailIcon from '../assets/icons/mail-icon.svg';
import dashboardIcon from '../assets/icons/dashboard-calender.svg';
export const userPermissionsList = {
  mstackAdmin: 'MSTACK_ADMIN',
  createCustomer: 'CREATE_CUSTOMER',
  updateCustomer: 'UPDATE_CUSTOMER',
  viewCustomer: 'VIEW_CUSTOMER',
  deleteCustomer: 'DELETE_CUSTOMER',
  createSupplier: 'CREATE_SUPPLIER',
  updateSupplier: 'UPDATE_SUPPLIER',
  viewSupplier: 'VIEW_SUPPLIER',
  deleteSupplier: 'DELETE_SUPPLIER',
  createOrderBook: 'CREATE_ORDER_BOOK',
  updateOrderBook: 'UPDATE_ORDER_BOOK',
  viewOrderBook: 'VIEW_ORDER_BOOK',
  deleteOrderBook: 'DELETE_ORDER_BOOK',
  createsupplierOrderBook: 'SUPPLIER_CREATE_ORDER_BOOK',
  updatesupplierOrderBook: 'SUPPLIER_UPDATE_ORDER_BOOK',
  viewSupplierOrderBook: 'SUPPLIER_VIEW_ORDER_BOOK',
  supplierDeleteOrderBook: 'SUPPLIER_DELETE_ORDER_BOOK',
  createCustomerOrder: 'CREATE_CUSTOMER_ORDER',
  updateCustomerOrder: 'UPDATE_CUSTOMER_ORDER',
  viewCustomerOrder: 'VIEW_CUSTOMER_ORDER',
  deleteCustomerOrder: 'DELETE_CUSTOMER_ORDER',
  createSupplierOrder: 'CREATE_SUPPLIER_ORDER',
  updateSupplierOrder: 'UPDATE_SUPPLIER_ORDER',
  viewSupplierOrder: 'VIEW_SUPPLIER_ORDER',
  deleteSupplierOrder: 'DELETE_SUPPLIER_ORDER',
  createProduct: 'CREATE_PRODUCT',
  updateProduct: 'UPDATE_PRODUCT',
  viewProduct: 'VIEW_PRODUCT',
  deleteProduct: 'DELETE_PRODUCT',
  viewCriticalPath: 'VIEW_CRITICAL_PATH',
  createTask: 'CREATE_TASK',
  updateTask: 'UPDATE_TASK',
  deleteTask: 'DELETE_TASK',
  generateCriticalPath: 'GENERATE_CRITICAL_PATH',
  updateCriticalPath: 'UPDATE_CRITICAL_PATH',
  getConfig: 'GET_CONFIG',
  uploadFile: 'UPLOAD_FILE',
  downloadFile: 'DOWNLOAD_FILE',
  createEmployee: 'CREATE_EMPLOYEE',
  updateEmployee: 'UPDATE_EMPLOYEE',
  deleteEmployee: 'DELETE_EMPLOYEE',
  viewEmployee: 'DELETE_EMPLOYEE',
  viewCustomerTab: 'VIEW_CUSTOMER_TAB',
  viewSupplierTab: 'VIEW_SUPPLIER_TAB',
  viewSupplierOrderBookTab: 'VIEW_SUPPLIER_ORDERBOOK_TAB',
  viewSupplierOrderTab: 'VIEW_SUPPLIER_DISPATCH_TAB',
  viewProductTab: 'VIEW_PRODUCT_TAB',
  viewCustomerOrderBookTab: 'VIEW_CUSTOMER_ORDERBOOK_TAB',
  viewCustomerDispatchTab: 'VIEW_CUSTOMER_DISPATCH_TAB',
  viewEnquiryTab: 'VIEW_ENQUIRY_TAB',
  viewEnquiry: 'VIEW_ENQUIRY',
  createEnquiry: 'CREATE_ENQUIRY',
  updateEnquiry: 'UPDATE_ENQUIRY',
  deleteEnquiry: 'DELETE_ENQUIRY',
  approveEnquiry: 'APPROVE_ENQUIRY',
  assignEnquiry: 'ASSIGN_ENQUIRY',
  createQuotation: 'CREATE_QUOTATION',
  negotiateEnquiry: 'NEGOTIATE_ENQUIRY',
  approveQuotation: 'APPROVE_QUOTATION',
  closeEnquiry: 'CLOSE_ENQUIRY',
  generateReport:'GENERATE_REPORT',
  deleteInvoice:'DELETE_INVOICE',
  viewInvoiceTab: 'VIEW_INVOICE_TAB',
  createInvoice: 'CREATE_INVOICE',
  getInvoice: 'GET_INVOICE',
  viewInventoryTab: 'VIEW_INVENTORY_TAB',
  viewReportTab:'VIEW_REPORT_TAB',
  viewInventoryProduct: 'VIEW_INVENTORY_PRODUCT',
  createInventoryLinkOrder: 'CREATE_INVENTORY_LINK_ORDER',
  viewInventoryLinkOrder: 'VIEW_INVENTORY_LINK_ORDER',
  deleteInventoryLinkOrder: 'DELETE_INVENTORY_LINK_ORDER',
  viewAllocatedInventory: 'VIEW_ALLOCATED_INVENTORY',
  allocateInventory: 'ALLOCATE_INVENTORY',
  unallocateInventory: 'UNALLOCATE_INVENTORY',
  createMultipleOrders:"CREATE_MULTIPLE_ORDERS",
  uploadInvoice:"UPLOAD_INVOICE"
};


export const tabsList = [
  {
    key: 'dashboard',
    view: 'ALL',
    label: 'Dashboard',
    path: new RouteFactory().dashboard().build(),
    icon: dashboardIcon,
    activeIcon: dashboardIcon,
  },
  // {
  //   key: 'enquiry',
  //   view: userPermissionsList.viewEnquiryTab,
  //   label: 'Enquiry',
  //   path: new RouteFactory().dashboard().enquiry().build(),
  //   icon: enquiryIcon,
  //   activeIcon: enquiryIconFilled,
  // },
  {
    key: 'orderBook',
    view: userPermissionsList.viewCustomerOrderBookTab,
    label: 'Order Book',
    path: new RouteFactory().dashboard().orderBook().build(),
    icon: orderBookIcon,
    activeIcon: orderBookIconFilled,
  },
  // {
  //   key: 'dispatchOrder',
  //   view: userPermissionsList.viewCustomerDispatchTab,
  //   label: 'Dispatch Order',
  //   path: new RouteFactory().dashboard().dispatchOrder().build(),
    
  // },
  {
    key: 'invoice',
    view: userPermissionsList.viewInvoiceTab,
    label: 'Invoice',
    path: new RouteFactory().dashboard().invoice().build(),
    icon: invoiceIcon,
    activeIcon: invoiceIconFilled,
  },
  // {
  //   key: 'products',
  //   view: userPermissionsList.viewProductTab,
  //   label: 'Products',
  //   path: RouteFactory.buildProduct(),
  //   icon: productIcon,
  //   activeIcon: productIconFilled,
  // },
  // {
  //   key: 'supplier',
  //   view: userPermissionsList.viewSupplierTab,
  //   label: 'Supplier',
  //   path: new RouteFactory().dashboard().supplier().build(),
  //   icon: supplierIcon,
  //   activeIcon: supplierIconFilled,
  // },
  {
    key: 'inventory',
    view: userPermissionsList.viewInventoryTab,
    label: 'Inventory',
    path: new RouteFactory().dashboard().inventory().build(),
    icon: inventoryIcon,
    activeIcon: inventoryIconFilled,
  },
  {
    key: 'reports',
    view: userPermissionsList.viewReportTab,
    label: 'Reports',
    path: new RouteFactory().dashboard().reports().build(),
    icon: reportIcon,
    activeIcon: reportIcon,
  },
  {
    key: 'mails',
    view: userPermissionsList.viewCustomerOrderBookTab,
    label: 'Mails',
    path: new RouteFactory().dashboard().mails().build(),
    icon: mailIcon,
    activeIcon: mailIcon,
  },
  {
    key: 'customer',
    view: userPermissionsList.viewCustomerTab,
    label: 'Customers',
    path: new RouteFactory().dashboard().customer().build(),
    icon: customerIcon,
    activeIcon: customerIconFilled,
  },
  // {
  //   key: 'supplierOrderBook',
  //   view: userPermissionsList.viewSupplierOrderBookTab,
  //   label: 'Supplier Order Book',
  //   path: new RouteFactory().dashboard().supplierOrderBook().build(),
  // },
  // {
  //   key: 'supplierDispatchOrder',
  //   view: userPermissionsList.viewSupplierOrderTab,
  //   label: 'Supplier Dispatch Order',
  //   path: new RouteFactory().dashboard().supplierDispatchOrder().build(),
  // },
];

export const userEntity = {
  customer: 'Customer',
  enquiry: 'Enquiry',
  orderBook: 'Purchase order',
  dispatchOrder: 'Dispatch Order',
  products: 'Products',
  supplier: 'Supplier',
  supplierOrderBook: 'Supplier Order Book',
  supplierDispatchOrder: 'Supplier Dispatch Order',
  inventoryOut: 'Inventory Link',
  employeeUser: 'User',
  userRole: 'User Role',
  packaging: 'Packaging',
}
