import React from 'react'
import PropTypes from 'prop-types';
// import { Select } from 'antd';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const DocumentItem = (props) => {
  const {
    documentData, setSelectedDocument, selectedDocument,
  } = props;

  
  const getFileVersions = () => {
    if (!documentData || !documentData?.length)
      return [];
    const options = documentData.map((doc, index) =>(
      <div
        key={doc.fileId}
        onClick={() => setSelectedDocument(doc)}
        className={`${selectedDocument.id === doc.id ? 'bg-hover-gray before:border-text-black' : 'before:border-border-color'} relative py-1.5 px-6 text-xs cursor-pointer before:absolute before:left-1 before:top-1/2 before:-translate-y-1/2 before:block before:rounded-full before:content-[''] before:w-1.5 before:h-1.5 before:border-solid`}
      >
        <div>{doc?.fileName || doc?.name}</div>
        <div className='text-gray-500'>{`${getDateFromTimeStamp(doc.createdAt) || ''} ${index === 0 ? '(Latest Version)' : ''}`}</div>
      </div>
    ));
    return options;
  }

  // const handleDocumentSelection = (value) => {
  //   const document = documentData.find((doc) => doc.fileId === value);
  //   if (document) {
  //     setSelectedDocument(document);
  //   } 
  // }

  return (
    <>
      <div
        id={documentData?.docType}
        key={documentData?.docType}
        className={`w-full flex flex-col items-start justify-between gap-4`}
      >
        <div className='pr-4 break-all text-dark-gray-600 text-lg font-medium'>{documentData?.[0]?.label}</div>
        <div className='pr-4 w-full flex justify-between items-center'>
          {/* <Select
            options={getDropdownOptions()}
            placeholder="Select a file"
            value={selectedDocument?.fileId}
            onChange={(value) => handleDocumentSelection(value)}
            className='w-4/5'
            
          /> */}
          <div className='w-full'>
            {getFileVersions()}
          </div>
        </div>
      </div>
    </>
  )
}

export default DocumentItem;

DocumentItem.propTypes = {
  documentData: PropTypes.array.isRequired,
  setSelectedDocument: PropTypes.func.isRequired,
  selectedDocument: PropTypes.object.isRequired,
}