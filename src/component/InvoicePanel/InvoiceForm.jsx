import React, { useState, useEffect } from 'react';
import { Button, Col, Form, Row, Select, Space } from 'antd';
import {
  useDispatch,
  // useSelector
} from 'react-redux';
import { formModes, invoiceTypes } from '../../constants/formConstant';
import css from '../CustomerPanel/CustomerForm.module.css';

import HeaderPanel from '../headerPanel';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { getFormModeFromPathV2 } from '../../util/route';
import RouteFactory from '../../service/RouteFactory';
import PageLoader from '../Loaders/PageLoader';
import SupplierInvoiceForm from './SupplierInvoiceForm/SupplierInvoiceForm';
import { createInvoice, getInvoiceFromId, updateInvoice } from '../../service/api/invoiceApi';
import { formatInvoiceData } from '../../util/formUtil';
import PackagingInvoiceForm from './PackagingInvoiceForm/PackagingInvoiceForm';
import LogisticsInvoiceForm from './LogisticsInvoiceForm/LogisticsInvoiceForm';
import MiscellaneousInvoice from './MiscellaneousInvoiceForm/MiscellaneousInvoiceForm';
import TestingInvoice from './TestingInvoiceForm/TestingInvoiceForm';
const InvoiceForm = () => {
  const route = new RouteFactory();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { invoiceId } = useParams();
  const { pathname } = useLocation();
  const [loading, setLoading] = useState(false);
  const [submittingForm, setSubmittingForm] = useState(false);
  const [invoiceForm] = Form.useForm();
  const currentFormMode = getFormModeFromPathV2(pathname);
  const invoiceFormType = Form.useWatch(['invoiceType'], invoiceForm);

  useEffect(() => {
    if (invoiceId && (currentFormMode === formModes.UPDATE || currentFormMode == formModes.READ)) {
      setLoading(true);
      getInvoiceFromId(invoiceId)
        .then(res => {
          if (res.data) {
            console.log(res.data);
            invoiceForm.setFieldsValue({ ...formatInvoiceData(res.data) });
            console.log('after set ', invoiceForm.getFieldsValue());
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [invoiceId]);

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };

  const goToDashBoard = () => {
    collapseAsideBarHandler(false);
    navigate(route.dashboard().invoice().build());
  };

  const saveInvoice = async (invoiceData, mode) => {
    if (mode === formModes.CREATE) {
      createInvoice(invoiceData)
        .then(response => {
          console.log('Customer api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(error => {
          console.log(error);
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE && invoiceId) {
      const oldInvoiceData = (await getInvoiceFromId(invoiceId)).data;
      const newInvoiceData = { ...oldInvoiceData, ...invoiceData };
      updateInvoice(newInvoiceData)
        .then(response => {
          console.log('Customer api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(error => {
          setSubmittingForm(false);
          console.log(error);
        });
    }
  };

  const submitFormHandler = () => {
    setSubmittingForm(true);
    invoiceForm
      .validateFields()
      .then(values => {
        // setLoading(true);
        console.log('final json obj ', values);
        saveInvoice(values, currentFormMode);
        // setLoading(false);
      })
      .catch(error => {
        console.log(setLoading);
        console.log(error);
        setSubmittingForm(false);
      });
  };

  const handleFormChange = value => {
    if (value && currentFormMode == formModes.CREATE) {
      // reset values in case of add
      console.log('reseting fields');
      invoiceForm.resetFields();
      invoiceForm.setFieldValue(['invoiceType'], value);
    }
  };

  const getFormType = () => {
    if (invoiceFormType == 'SupplierInvoice') {
      return <SupplierInvoiceForm form={invoiceForm} formView={currentFormMode} />;
    }
    if (invoiceFormType == 'PackagingInvoice') {
      return <PackagingInvoiceForm form={invoiceForm} formView={currentFormMode} />;
    }
    if (invoiceFormType == 'LogisticsInvoice') {
      return <LogisticsInvoiceForm form={invoiceForm} formView={currentFormMode} />;
    }
    if (invoiceFormType == 'MiscellaneousInvoice') {
      return <MiscellaneousInvoice form={invoiceForm} formView={currentFormMode} />;
    }
    if (invoiceFormType == 'TestingInvoice') {
      return <TestingInvoice form={invoiceForm} formView={currentFormMode} />;
    }
    return '';
  };

  return (
    <>
      <HeaderPanel
        name="Invoice"
        sideButtons={
          <>
            <Space>
              {currentFormMode != formModes.READ ? (
                <Button
                  type="primary"
                  size="large"
                  onClick={() => submitFormHandler()}
                  loading={submittingForm}
                >
                  {currentFormMode === formModes.CREATE ? 'Add Invoice' : 'Save Changes'}
                </Button>
              ) : (
                ''
              )}

              <Button
                type="default"
                onClick={() => {
                  collapseAsideBarHandler(false);
                  navigate(new RouteFactory().dashboard().invoice().build());
                }}
                size="large"
                disabled={submittingForm}
              >
                Cancel
              </Button>
            </Space>
          </>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <div style={{ margin: '16px auto', width: '100%' }}>
          <div style={{ marginTop: '48px' }}>
            <Form
              className={css.formContainer}
              name="invoice_form"
              layout="vertical"
              scrollToFirstError
              // preserve={false}
              disabled={currentFormMode === formModes.READ}
              size="large"
              form={invoiceForm}
            >
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    label="Select Invoice Type"
                    name="invoiceType"
                    rules={[{ required: true, message: 'Please select an invoice form type' }]}
                  >
                    <Select onChange={value => handleFormChange(value)}>
                      {invoiceTypes.map(item => (
                        <Select.Option key={item.key} value={item.value}>
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              {getFormType()}
            </Form>
          </div>
        </div>
      )}
    </>
  );
};

export default InvoiceForm;
