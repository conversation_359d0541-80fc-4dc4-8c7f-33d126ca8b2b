import { Tabs } from 'antd';
import React from 'react'
import UserPanel from './UserPanel';
import RolesPanel from './RolesPanel';
import css from './EmployeeTabs.module.css';

const EmployeePanel = () => {

  const employeePanelTabs = [
    {
      key: '1',
      label: 'Users',
      children: <UserPanel />,
    },
    {
      key: '2',
      label: 'Roles',
      children: <RolesPanel />,
    }
  ]

  return (
    <div
      style={{
        height: '100%',
        borderTop: '1px solid rgba(30, 30, 30, 0.1)'
      }}
    >
      <Tabs
        tabPosition="left"
        items={employeePanelTabs}
        defaultActiveKey={'1'}
        destroyInactiveTabPane={true}
        className={css.tabContainer}
        size="middle"
      />
    </div>
  )
}

export default EmployeePanel;