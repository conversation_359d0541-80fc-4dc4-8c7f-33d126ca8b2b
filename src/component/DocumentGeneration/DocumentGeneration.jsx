import React, { useEffect, useState } from 'react';
import DocumentForm from './DocumentForm';
import DocumentPreview from './DocumentPreview';
import PropTypes from 'prop-types';
import {
  generateDocumentFromTemplate,
  getDocumentAggregratedData,
} from '../../service/api/documentApi';
import PageLoader from '../Loaders/PageLoader';
import { message } from 'antd';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';

const DocumentGeneration = props => {
  const { docData, hide, onDocumentGeneration, height } = props;

  const [formData, setFormData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const { api } = useNotificationContext();

  const formatFormData = data => {
    const newFormObj = {};
    for (let i = 0; i < data.length; i += 1) {
      newFormObj[data[i].key] = data[i].value;
    }
    return newFormObj;
  };
  const generateDocument = loaderFucntion => {
    const previewElement = document.getElementsByClassName('previewContainer');
    if (previewElement && previewElement[0]) {
      const documentTemplate = previewElement?.[0]?.innerHTML;
      if (documentTemplate) {
        if (loaderFucntion) loaderFucntion(true);
        const postData = {
          htmlContent: documentTemplate,
          docType: docData.docType,
          entityId: docData.entityId,
          entityType: docData.entityType,
          fileName: docData.label,
          meta: docData.meta,
        };
        generateDocumentFromTemplate(postData)
          .then(res => {
            if (res.data) {
              if (loaderFucntion) loaderFucntion(false);
              hide();
              onDocumentGeneration(res.data, null, docData.entityId);
            }
          })
          .catch(err => {
            const errMsg = err?.response?.data?.message;
            messageApi.error(errMsg);
            console.log(err);
            if (loaderFucntion) loaderFucntion(false);
            hide();
            onDocumentGeneration(false, err);
          });
      }
    }
  };

  useEffect(() => {
    if (docData && docData.docType) {
      setLoading(true);
      getDocumentAggregratedData(docData.docType, docData.meta)
        .then(res => {
          if (res.data) {
            setFormData(res.data);
          }
        })
        .catch(err => {
          api.open({
            key: 'customerSaveError',
            message: 'Error!!',
            description: (
              <>
                <div className="text-xs font-medium">
                  {err?.response?.data?.message || err?.message}
                </div>
              </>
            ),
            duration: 5,
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            ),
          });
        })
        .finally(() => setLoading(false));
    }
  }, [docData]);

  return loading ? (
    <PageLoader />
  ) : (
    <div className={`flex ${height || 'h-[75vh]'}`}>
      {contextHolder}
      <DocumentForm
        docLabel={docData.label}
        formData={formData}
        hidePanel={hide}
        generateDocument={generateDocument}
      />
      <DocumentPreview docType={docData.docType} formData={formatFormData(formData)} />
    </div>
  );
};

export default DocumentGeneration;

DocumentGeneration.propTypes = {
  docData: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
  onDocumentGeneration: PropTypes.func.isRequired,
  height: PropTypes.string,
};
