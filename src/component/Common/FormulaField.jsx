import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Form, Input } from 'antd';
import { useFormulaCalculation } from '../../hooks/useFormulaCalculation';

const FormulaField = ({ form, path, formula, dependencies }) => {
  // Convert dot notation path to array
  const formPath = path?.split('.') || [];
  
  // Convert dependency paths to arrays
  const processedDependencies = dependencies.map(dep => ({
    ...dep,
    path: dep.path?.split('.') || [],
    variable: dep.variable
  }));
  const calculatedValue = useFormulaCalculation(form, formula, processedDependencies);
  useEffect(() => {
    form.setFieldValue(formPath, calculatedValue);
  }, [calculatedValue, form, path]);

  return (
    <Form.Item
      name={formPath}
      validateTrigger={[]}
    >
      <Input 
        disabled={true} 
        value={calculatedValue}
        defaultValue={0}
        style={{ backgroundColor: '#f5f5f5' }}
      />
    </Form.Item>
  );
};

FormulaField.propTypes = {
  form: PropTypes.shape({
    setFieldValue: PropTypes.func.isRequired,
    getFieldValue: PropTypes.func.isRequired,
    getFieldsValue: PropTypes.func.isRequired
  }).isRequired,
  path: PropTypes.string.isRequired,
  formula: PropTypes.string.isRequired,
  dependencies: PropTypes.arrayOf(
    PropTypes.shape({
      path: PropTypes.string.isRequired,
      variable: PropTypes.string.isRequired,
      label: PropTypes.string
    })
  ).isRequired
};

export default FormulaField;
