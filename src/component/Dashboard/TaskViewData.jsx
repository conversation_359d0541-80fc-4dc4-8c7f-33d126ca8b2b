import React from 'react';
import { CalendarOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import PropTypes from 'prop-types'; // Import PropTypes at top
import { downloadFile } from '../../service/api/storageService';
import MarginApprovalTable from './MarginApprovalTable';

const TaskViewData = ({ record = {}, formData = {} }) => {
  const marginApproveData = [
    {
      patch: true,
      path: 'supplierRfq.incoterms',
      inputType: 'incoterms',
      label: 'INCO Term aligned with supplier',
      mandatory: true,
      key: 'incoterms',
    },
    {
      patch: true,
      path: 'supplierRfq.uom',
      inputType: 'text',
      label: 'Unit of Measurement of Quantity for which price is quoted by Supplier (Kg/LBS etc)',
      mandatory: true,
      key: 'uom',
    },
    {
      patch: true,
      path: 'supplierRfq.currency',
      inputType: 'text',
      label: 'Currency in which Price is quoted by Supplier of Raw Materials',
      mandatory: true,
      key: 'currency',
    },
    {
      patch: true,
      path: 'supplierRfq.costPrice',
      inputType: 'number',
      label: "Cost Price from supplier (in UOM mentioned in 'S')",
      mandatory: true,
      key: 'costPrice',
    },
    {
      patch: true,
      path: 'supplierRfq.quotedQuantity',
      inputType: 'number',
      label: "Quantity for which price is quoted by Supplier (in UOM Mentioned in 'S')",
      mandatory: true,
      key: 'quotedQuantity',
    },
    {
      patch: true,
      path: 'supplierRfq.firstMileLogistics',
      inputType: 'number',
      label: 'First Mile Logistics (If Applicable) (in USD only)',
      mandatory: true,
      key: 'firstMileLogistics',
    },
    {
      patch: true,
      path: 'supplierRfq.seaFreight',
      inputType: 'number',
      label: 'Sea Freight (If Applicable) (in USD only)',
      mandatory: true,
      key: 'seaFreight',
    },
    {
      patch: true,
      path: 'supplierRfq.lastMileLogistics',
      inputType: 'number',
      label: 'Last Mile - Inland Logistics (If Applicable) (in USD only)',
      mandatory: true,
      key: 'lastMileLogistics',
    },
    {
      patch: true,
      path: 'supplierRfq.destinationCharges',
      inputType: 'number',
      label: 'Destination Charges (in USD only)',
      mandatory: true,
      key: 'destinationCharges',
    },
    {
      patch: true,
      path: 'supplierRfq.customerCountry',
      inputType: 'text',
      label: "Customer's country",
      mandatory: true,
      key: 'customerCountry',
    },
    {
      patch: true,
      path: 'supplierRfq.cityPlaceOfSupply',
      inputType: 'text',
      label: 'City / Place of supply (to customer)',
      mandatory: true,
      key: 'cityPlaceOfSupply',
    },
    {
      patch: true,
      path: 'supplierRfq.hsnCode',
      inputType: 'text',
      label: 'HSN Code (as per supplier)',
      mandatory: true,
      key: 'hsnCode',
    },
    {
      patch: true,
      path: 'supplierRfq.countryOfOrigin',
      inputType: 'text',
      label: "Product's country of origin (As per supplier)",
      mandatory: true,
      key: 'countryOfOrigin',
    },
    {
      patch: true,
      path: 'supplierRfq.dutyPercentage',
      inputType: 'number',
      label: 'Duty applicable in % for the Customer’s Import',
      mandatory: true,
      key: 'dutyPercentage',
    },
    {
      patch: true,
      path: 'supplierRfq.dutyCheckScreenshot',
      inputType: 'file',
      label: 'Link of the screenshot for the duty check done by you on the ACE portal',
      mandatory: true,
      key: 'dutyCheckScreenshot',
    },
    {
      patch: true,
      path: 'supplierRfq.packagingType',
      inputType: 'text',
      label: "Packaging type as per the quotation (in alignment with customer's enquiry)",
      mandatory: true,
      key: 'packagingType',
    },
    {
      patch: true,
      path: 'supplierRfq.targetContributionMargin',
      inputType: 'number',
      label: 'Target Contribution Margin (Pull from the quotation tracker)',
      mandatory: true,
      key: 'targetContributionMargin',
    },
    {
      patch: true,
      path: 'supplierRfq.leadTime',
      inputType: 'number',
      label: 'Expected lead time for material/product readiness by the supplier? (in days)',
      mandatory: true,
      key: 'leadTime',
    },
    {
      patch: true,
      path: 'supplierRfq.supplierQuotationValidity',
      inputType: 'date',
      label: "Validity of supplier's quotation",
      mandatory: true,
      key: 'supplierQuotationValidity',
    },
    {
      patch: true,
      path: 'supplierRFQRecordedBy',
      inputType: 'text',
      label: 'Supplier RFQ Recorded By',
      mandatory: true,
      key: 'supplierRFQRecordedBy',
    },
    {
      patch: true,
      path: 'supplierRFQRecordedOn',
      inputType: 'date',
      label: 'Supplier RFQ Recorded On',
      mandatory: true,
      key: 'supplierRFQRecordedOn',
    },
    {
      "key": "salesOrderValue",
      "label": "Sales Order Value",
      "inputType": "formulaInput",
      "path": "supplierRfq.salesOrderValue",
      "mandatory": true,
      "group": [
        {
          "key": "value",
          "label": "Cost Value",
          "type": "number"
        },
        {
          "key": "currency",
          "label": "Currency",
          "type": "select"
        },
        {
          "key": "conversionRate",
          "label": "Conversion Rate",
          "type": "number"
        }
      ]
    },
    {
      "key": "purchaseOrderValue",
      "label": "Purchase Order Value",
      "inputType": "formulaInput",
      "path": "supplierRfq.purchaseOrderValue",
      "mandatory": true,
      "group": [
        {
          "key": "value",
          "label": "Cost Value",
          "type": "number"
        },
        {
          "key": "currency",
          "label": "Currency",
          "type": "select"
        },
        {
          "key": "conversionRate",
          "label": "Conversion Rate",
          "type": "number"
        }
      ]
    },
    {
      "key": "dutyAmountValue",
      "label": "Duty applicable in Amount for the Customer’s Import",
      "inputType": "formulaInput",
      "path": "supplierRfq.dutyAmountValue",
      "mandatory": true,
      "group": [
        {
          "key": "value",
          "label": "Cost Value",
          "type": "number"
        },
        {
          "key": "currency",
          "label": "Currency",
          "type": "select"
        },
        {
          "key": "conversionRate",
          "label": "Conversion Rate",
          "type": "number"
        }
      ]
    },
    {
      "key": "logisticsCost",
      "label": "Total logistics cost considered",
      "inputType": "formulaInput",
      "path": "supplierRfq.logisticsCost",
      "mandatory": true,
      "group": [
        {
          "key": "value",
          "label": "Cost Value",
          "type": "number"
        },
        {
          "key": "currency",
          "label": "Currency",
          "type": "select"
        },
        {
          "key": "conversionRate",
          "label": "Conversion Rate",
          "type": "number"
        }
      ]
    },
  ];

  const DocumentsData = [
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: [
        'SDS',
        'TDS',
        'COA',
        'invoice',
        'DGD',
        'COO',
        'productPackagingPhotos',
        'supplierInvoice',
      ],
    },
  ];

  const SampleDocumentsData = [
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'incoTerm',
    },
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: [
        'labCOA',
        'SUPPLIER_COA',
        'SUPPLIER_TDS',
        'SUPPLIER_SDS',
        'MSTACK_COA',
        'MSTACK_TDS',
        'MSTACK_SDS',
        'relabelledPhotos',
        'bookingDocuments',
      ],
    },
  ];

  const DestinationApprovalData = [
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'incoTerm',
    },
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: ['destinationRelablledPhotos','labCOA'],
    },
  ];

  const data =
    record.orderType === 'SAMPLE'
      ? record.taskId === '11'
        ? SampleDocumentsData
        : DestinationApprovalData
      : record.taskId === '4'
      ? []
      : DocumentsData;
  console.log('data', data);
  const onDownload = async file => {
    try {
      // setLoading(file.uid);
      const apiRes = await downloadFile(file.fileId);
      window.open(apiRes.data.url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    }
  };

  const renderValue = (inputType, value, group = []) => {
    
    if (!value || (typeof value === 'object' && Object.keys(value).length === 0)) return 'N/A';

    switch (inputType) {
      case 'text':
      case 'number':
      case 'supplierSelect':
        return <span className="font-semibold text-gray-800">{value}</span>;

      case 'date':
        return (
          <span className="flex items-center gap-2 font-semibold text-gray-800">
            <CalendarOutlined />
            {dayjs(value).format('DD-MM-YYYY')}
          </span>
        );

      case 'file': {
        const files = Array.isArray(value)
          ? value
          : value && typeof value === 'object'
          ? [value]
          : [];
        return (
          <div className="flex flex-col gap-2">
            {files.length > 0
              ? files.map((file, index) => (
                  <div key={file.fileId || index} className="flex gap-2 flex-wrap items-center">
                    <div>
                      <div>{file.name || 'Unnamed File'}</div>
                    </div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))
              : 'No Files'}
          </div>
        );
      }

      case 'document': {
        // CASE when group is provided - show only specific groups
        const groupsToShow = group.length > 0 ? group : Object.keys(value);

        return groupsToShow
          .filter(docType => Array.isArray(value[docType]) && value[docType].length > 0) // Only keep non-empty groups
          .map((docType, idx) => {
            const files = value[docType];
            return (
              <div key={idx} className="flex flex-col gap-2 mb-3">
                <h4 className="font-medium text-gray-600">{docType}</h4>
                {files.map((file, index) => (
                  <div
                    key={file.fileId || index}
                    className="flex gap-2 flex-wrap items-center bg-gray-100 p-2 rounded-md"
                  >
                    <div>{file.name || `File ${index + 1}`}</div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))}
              </div>
            );
          });
      }

      case 'incoterms':
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Type:</span>
              <span className="font-semibold text-gray-800">{value.type}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Country:</span>
              <span className="font-semibold text-gray-800">{value.country}</span>
            </div>
            {value.data && Object.entries(value.data).map(([key, val]) => (
              <div key={key} className="flex items-center gap-2">
                <span className="text-gray-600">{key}:</span>
                <span className="font-semibold text-gray-800">{val}</span>
              </div>
            ))}
          </div>
        );

      case 'formulaInput':
        return (
          <div className="space-y-2">
            {value && (
              <>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Value:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.value || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Currency:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.currency || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Conversion Rate:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.conversionRate || 'N/A'}
                  </span>
                </div>
              </>
            )}
          </div>
        );

      default:
        return <span>{value}</span>;
    }
  };

  return (
    <>
      {record.orderType !== 'SAMPLE' && record.taskId === '4' ? (
        <div>
          <MarginApprovalTable
          formData={formData}
          onDownload={onDownload}
          />
        </div>
      ) : (
        <div className="space-y-4 mt-4">
          {data.map(({ key, label, inputType, group, path }) => {
            const fieldPath = path?.split('.') || [];
            let value = formData;
            fieldPath.forEach(p => {
              value = value?.[p];
            });
            return (
              <div
                key={key}
                className="flex flex-col p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <h4 className="text-sm font-semibold text-gray-700 mb-2">{label}</h4>
                {renderValue(inputType, value, group)}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

TaskViewData.propTypes = {
  record: PropTypes.object.isRequired,
  formData: PropTypes.object.isRequired, // You can shape it more if you want, but object is fine for now
};

export default TaskViewData;
