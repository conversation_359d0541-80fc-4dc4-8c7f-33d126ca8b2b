import {
  Button,
  Form,
  Input,
  Select,
  DatePicker,
  Radio,
  Divider,
  message,
  Switch,
  InputNumber,
  Modal,
  Tag,
  Card,
  Row,
  Col,
} from 'antd';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import {
  createSupplierOrderBook,
  updateSupplierOrderBook,
} from '../../service/api/supplierOrderBookApi';
import { getPackagingDetailFromId, getSupplierOrderBookDataForApi } from '../../util/formUtil';
import { setPackagingList } from '../../store/actions/packagingList';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import {
  SupplierOrderBookFormBasicInfo,
  DateFormat,
  currencyTypeList,
  billingAddressList,
  paymentTermsDate,
  modeOfDeliveryList,
  deliveyTermList,
  taxTypeList,
  SupplierOrderBookDocumentGroups,
} from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';
import { CalendarOutlined, CloseOutlined } from '@ant-design/icons';
import FileUpload from '../FileUpload';
import ProductTable from './ProductTable';
import { getSupplierList } from '../../service/api/supplierService';
import { getProductList } from '../../service/api/productApi';
import { addPackaging } from '../../store/actions/packagingList';
import PageLoader from '../Loaders/PageLoader';
import { getProductById } from '../../service/api/productApi';

const SupplierOrderBookForm = props => {
  const { mode, order, orderBookId, hide, COBProducts,orderType,orderBook,poGenrationRequired } = props;

  
  const packagingList = useSelector(state => state.packagingList);
  const [taxTypes, setTaxTypes] = useState([...taxTypeList]);
  const dispatch = useDispatch();
  const [poRequirement, setPoRequirement] = useState(poGenrationRequired === 'false' || poGenrationRequired === false ? "poNotRequired" : 'poRequired' );
  const [orderBookEntry, setOrderBookEntry] = useState(
    mode === formModes.UPDATE
      ? poRequirement === "poRequired" ? order : {...order, paymentTerms: {} }
      : {
          products: [
            ...Array(Math.max(1)).fill({}), // Add only the required number of empty objects
          ],
        }
  );
  const [submittingForm, setSubmittingForm] = useState(false);
  const [productList, setProductList] = useState(COBProducts || []);
  const [supplierList, setSupplierList] = useState([]);
  const [isTaxModalVisible, setIsTaxModalVisible] = useState(false);
  const [isDeliveryScheduleModalVisible, setIsDeliveryScheduleModalVisible] = useState(false);
  const [deliveryScheduleProducts, setDeliveryScheduleProducts] = useState([]);

  const fetchProductDetails = async () => {
    if (!orderBookEntry?.products?.length) return;
    if (mode === formModes.CREATE) {
      setDeliveryScheduleProducts(COBProducts || []);
      return;
    }
    else{
    try {
      const productsPromise = orderBookEntry.products.map(prod => getProductById(prod.product));
      const productsData = await Promise.all(productsPromise);
      setDeliveryScheduleProducts(productsData.map(response => response.data));
    } catch (error) {
      console.error('Error fetching product details:', error);
    }
  }
  };

  const handleManageDeliverySchedule = () => {
    fetchProductDetails();
    setIsDeliveryScheduleModalVisible(true);
  };

  const handleManageTax = () => {
    setIsTaxModalVisible(true);
  };

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form); // watch all values in form

  const saveOrderDetails = orderData => {
    if (mode === formModes.CREATE) {
      createSupplierOrderBook(orderData)
        .then(response => {
          console.log('Order api response:', response);
          message.success('Supplier Order Book created successfully');
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.log(error);
          message.error('Failed to create Supplier Order Book');
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE) {
      updateSupplierOrderBook(orderData, order.id)
        .then(response => {
          console.log('Order api response:', response);
          message.success('Supplier Order Book updated successfully');
          setSubmittingForm(false);
          hide(response.data);
        })
        .catch(error => {
          console.log(error);
          message.error('Failed to update Supplier Order Book');
          setSubmittingForm(false);
        });
    }
  };
  
  const submitFormHandler = orderBookEntry => {
    if (!Array.isArray(orderBookEntry.products) || orderBookEntry.products.length === 0) {
      message.error('At least one product is required!');
      setSubmittingForm(false);
      return; // Stop further execution
    }

    const missingIncoterms = orderBookEntry.products.some(product => !product.incoterms);

    const missingDocuments = orderBookEntry.products.some(product => {
      return SupplierOrderBookDocumentGroups.filter(doc => doc.required).some(doc => {
        // If the document type property doesn't exist at all, it's missing
        if (product?.documents && !(doc.name in product.documents)) {
          console.log(`${doc.name} property doesn't exist in documents:`, product.documents);
          return true;
        }

        const docArray = product?.documents ? product.documents[doc.name] : [];
        const isMissing = !Array.isArray(docArray) || docArray.length === 0;
        console.log(`Checking ${doc.name}:`, { docArray, isMissing });
        return isMissing;
      });
    });

    if (missingIncoterms && orderType !== 'SAMPLE') {
      setSubmittingForm(false);
      message.error('Incoterms is Required!');
      return;
    }

    if (missingDocuments && orderType !== 'SAMPLE') {
      setSubmittingForm(false);
      message.error('Required documents are missing!');
      return;
    }
    setSubmittingForm(true);
    const formattedData = JSON.parse(JSON.stringify(orderBookEntry));
    getSupplierOrderBookDataForApi(orderBookEntry)
      .then(results => {
        results.forEach((result, index) => {
          if (index === 0) {
            formattedData.supplier = result.data;
          } else {
            formattedData.products[index - 1].product = result.data;
          }
        });
        saveOrderDetails({
          ...formattedData,
          linkedCOBs: [orderBookId],
          poGenrationRequired: poRequirement === 'poRequired' ? true : false,
        });
      })
      .catch(error => {
        console.log('Error in getting data from server', error);
        setSubmittingForm(false);
      });
  };

  const validateAndSaveFormFields = () => {
    setSubmittingForm(true);
    let formObj = form;
    formObj
      .validateFields()
      .then(values => {
        let valuesCopy = JSON.parse(JSON.stringify(values));
        valuesCopy.products = (valuesCopy.products || []).filter(product =>
          Object.values(product).some(
            value => value !== '' && value !== undefined && value !== null
          )
        );

        const transformedValues = {
          ...valuesCopy,

          // Always attempt to format purchaseOrderDate if present
          purchaseOrderDate: valuesCopy.purchaseOrderDate
            ? dayjs(valuesCopy.purchaseOrderDate)
            : '',

          // Always map products if present
          products:
            valuesCopy.products?.map(prod => ({
              ...prod,
              shipmentDate: prod.shipmentDate ? dayjs(prod.shipmentDate) : '',
              deliveryDate: prod.deliveryDate ? dayjs(prod.deliveryDate) : '',
              packaging: getPackagingDetailFromId(prod.packaging, packagingList),
              numberOfPacking: prod.numberOfPacking,
            })) || [],

          // Always map deliverySchedule if present
          deliverySchedule: valuesCopy.deliverySchedule?.length
            ? valuesCopy.deliverySchedule.map(item => ({
                ...item,
                deliveryDate: item.deliveryDate ? dayjs(item.deliveryDate) : '',
              }))
            : [],

          // Always format these dates if present
          mrd: valuesCopy.mrd ? dayjs(valuesCopy.mrd) : '',
        };

        console.log('hello', transformedValues);

        // setOrderBookEntry({
        //   ...orderBookEntry,
        //   ...transformedValues,
        // });
        submitFormHandler({ ...orderBookEntry, ...transformedValues });
      })
      .catch(error => {
        console.log(error);
        message.error('Please fill all required fields.');
        setSubmittingForm(false);
      });
  };

  useEffect(() => {
    if (!packagingList || !packagingList.length) {
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  const getOptionList = key => {
    switch (key) {
      case 'supplierList':
        return supplierList;
      case 'currencyList':
        return currencyTypeList;
      case 'paymentTermsDate':
        return paymentTermsDate;
      case 'billingAddressList':
        return billingAddressList;
      case 'modeOfDeliveryList':
        return modeOfDeliveryList;
      case 'deliveyTermList':
        return deliveyTermList;
      default:
        return [];
    }
  };

  useEffect(() => {
    if (!supplierList || !supplierList.length) {
      // setLoading(true);
      getSupplierList()
        .then(res => {
          if (res.data && res.data.content) {
            setSupplierList(res.data.content);
            // setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          // setLoading(false);
        });
    }
  }, []);

  const renderField = field => {
    const options = getOptionList(field.optionListKey);
    const fieldStyle = field.style || {}; // directly use field style or empty object

    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={field?.rows} style={fieldStyle} />;
      case 'select':
        return (
          <Select showSearch optionFilterProp="label" style={fieldStyle}>
            {options.map(option => (
              <Select.Option
                key={option[field.optionMapping.key]}
                value={option[field.optionMapping.value]}
                label={option[field.optionMapping.label]}
              >
                {option[field.optionMapping.label]}
              </Select.Option>
            ))}
          </Select>
        );
      case 'date':
        return (
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={fieldStyle}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
          />
        );
      case 'radio':
        return (
          <Radio.Group buttonStyle="solid">
            {options.map(item => (
              <Radio
                key={item.key}
                value={item.value}
                style={{
                  display: 'block',
                  height: '30px',
                  lineHeight: '30px',
                  marginBottom: '0px',
                }}
              >
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      case 'text':
        return <Input className="w-52" style={fieldStyle} />;
      case 'number':
        return <Input className="w-52" style={fieldStyle} type="number" />;
      case 'switch':
        return <Switch className="w-52" style={fieldStyle} />;
      case 'file':
        return (
          <FileUpload
            category="Orders"
            meta={{ poId: 123, documentType: 'PurchaseOrder' }}
            maxFileLimit={1}
          />
        );
      default:
        return <Input className="w-52" style={fieldStyle} />;
    }
  };

  const checkShowIfFilledAndValueIn = (conditions, formValues) => {
    return Object.entries(conditions).every(([key, values]) => {
      const formValue = orderType;
      if (!formValue) return false;
      if (values.length === 0) return true;
      return values.includes(formValue);
    });
  };

  const addRow = () => {
    form.setFieldsValue({
      products: [...(form.getFieldValue('products') || []), {}],
    });
  };

  useEffect(() => {
    if (!productList || !productList.length) {
      // setLoading(true);
      getProductList()
        .then(res => {
          if (res.data && res.data.content) {
            setProductList(res.data.content);
            // setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          // setLoading(false);
        });
    }
  }, []);

  const getPaymentFormPrefilldValues = () => {
    const supplierState = orderBookEntry?.supplierData?.address?.state;
    if (supplierState && supplierState.length > 0) {
      let filteredTaxTypes = [];
      if (supplierState.toLowerCase() == 'maharashtra') {
        filteredTaxTypes = taxTypes.filter(taxType => {
          return taxType.label != 'IGST';
        });
      } else {
        filteredTaxTypes = taxTypes.filter(taxType => {
          return taxType.label == 'IGST';
        });
      }
      setTaxTypes(filteredTaxTypes);
    }
    return orderBookEntry
      ? {
          shippingAddress: orderBookEntry.shippingAddress,
          billingAddress: orderBookEntry.billingAddress,
          currencyType: orderBookEntry.currencyType,
          paymentTerms: { ...orderBookEntry?.paymentTerms },
          taxList: orderBookEntry?.taxList,
          modeOfDelivery: orderBookEntry?.modeOfDelivery,
          deliveryTerm: orderBookEntry?.deliveryTerm,
          deliveryLocation: orderBookEntry?.deliveryLocation,
          pickupLocation: orderBookEntry?.pickupLocation,
          deliverySchedule: orderBookEntry?.deliverySchedule?.map(item => ({
            ...item,
            deliveryDate: item?.deliveryDate ? dayjs(item.deliveryDate) : '',
          })),
          additionalCondition: orderBookEntry?.additionalCondition,
          termAndCondition: orderBookEntry?.termAndCondition,
          partialShipment: orderBookEntry?.partialShipment,
          transShipment: orderBookEntry?.transShipment,
          excludeDefaultTc: orderBookEntry?.excludeDefaultTc,
          mrd: orderBookEntry.mrd ? dayjs(orderBookEntry.mrd) : '',
          advPaymentDate: orderBookEntry?.advPaymentDate
            ? dayjs(orderBookEntry.advPaymentDate)
            : '',
          releasePaymentMadeOn: orderBookEntry?.releasePaymentMadeOn
            ? dayjs(orderBookEntry.releasePaymentMadeOn)
            : '',
        }
      : {};
  };

  const getBasicFormPrefilldValues = () =>
    orderBookEntry && orderBookEntry.purchaseOrderNumber
      ? {
          supplier: orderBookEntry?.supplier,
          purchaseOrderNumber: orderBookEntry?.purchaseOrderNumber,
          purchaseOrderDate: orderBookEntry?.purchaseOrderDate,
          purchaseOrderFile: orderBookEntry?.purchaseOrderFile,
          quotationNumber: orderBookEntry?.quotationNumber,
          supplierRefNumber: orderBookEntry?.supplierRefNumber,
          currencyType: orderBookEntry?.currencyType,
          materialSpecFile: orderBookEntry?.materialSpecFile,
        }
      : {};

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getProductFormPrefilldValues = () =>
    orderBookEntry && orderBookEntry?.products && packagingList
      ? {
          products: orderBookEntry.products.map(prod => ({
            ...prod,
            product: prod.product,
            uom: prod.uom,
            price: prod.price,
            quantity: prod.quantity,
            quantityPerUnit: prod.quantityPerUnit,
            units: prod?.units,
            margin: prod.margin,
            incoterms: prod.incoterms
              ? {
                  ...prod.incoterms,
                  data: {
                    ...prod.incoterms.data,
                  },
                }
              : null,
            deliveryDate: prod.deliveryDate,
            remarks: prod.remarks ? [...prod.remarks] : null,
            hsCode: prod.hsCode,
            productDescription: prod.productDescription,
            packaging: getPackagingFormValue(prod.packaging, packagingList),
            numberOfPacking: prod.numberOfPacking,
            documents: prod.documents ? prod.documents : null,
            otherPackagingDetails: prod.packaging?.otherPackagingDetails || prod.otherPackagingDetails || '',
          })),
        }
      : {};

  useEffect(() => {
    if (orderBookEntry && orderBookEntry.paymentTerms) {
      form.setFieldsValue(getPaymentFormPrefilldValues());
      form.setFieldsValue(getBasicFormPrefilldValues());
    }
  }, [orderBookEntry]);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      orderBookEntry &&
      orderBookEntry.products &&
      orderBookEntry.products.length &&
      packagingList &&
      packagingList.length
    ) {
      form.setFieldsValue(getProductFormPrefilldValues());
    }
  }, [productList]);

    // add a loader 
    if (submittingForm) {
      return <PageLoader />;
    }
  

  return (
    <>
      <div>
    
        <Form
          name="supplier_form"
          layout="vertical"
          scrollToFirstError
          size="large"
          form={form}
          initialValues={orderBookEntry}
          onFinish={validateAndSaveFormFields}
          // onValuesChange={() => saveformData()}
        >
          <div className={`flex ${orderType === 'SAMPLE' ? 'justify-between' : 'justify-end'}`}>
        {orderType === "SAMPLE" && <div>
            {/* Add the Radio.Group here */}
            <Radio.Group 
              value={poRequirement} 
              onChange={e => {
                setPoRequirement(e.target.value);
                form.validateFields(['materialSpecFile']);
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <Radio value="poRequired">PO Required</Radio>
                <Radio value="poNotRequired">PO Not Required</Radio>
              </div>
            </Radio.Group>
          </div>}
          <Button
            type="primary"
            size="large"
            loading={submittingForm}
            htmlType='submit'
          >
            {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
          </Button>
        </div>
           
          {SupplierOrderBookFormBasicInfo.map(section => (
            <div key={section.id} className="border-b border-gray-100 last:border-0 pt-6">
              <div className="flex flex-wrap gap-6 px-6 py-0">
                {section.fields.map(field => {
                  let shouldShow = true;

                  if (field.type === 'tax') {
                    return (
                      <div key={field.id}>
                        <Button 
                          type="dashed"
                          onClick={handleManageTax}
                          style={{ marginTop: '45px' }}
                        >
                          Manage Tax
                        </Button>

                        <Modal
                          title="Tax Configuration"
                          open={isTaxModalVisible}
                          onOk={() => setIsTaxModalVisible(false)}
                          onCancel={() => setIsTaxModalVisible(false)}
                          width="70%"
                          style={{ top: 20 }}
                          bodyStyle={{ 
                            maxHeight: '70vh',
                            overflowY: 'auto'
                          }}
                          footer={null}
                        >
                          <Row gutter={16}>
                            <Col span={24}>
                              <Form.List name="taxList" label="Tax Type">
                                {(fields, { add, remove }, { errors }) => (
                                  <div
                                    style={{
                                      display: 'flex',
                                      rowGap: 16,
                                      flexDirection: 'column',
                                    }}
                                  >
                                    {fields.map((field, index) => (
                                      <Card
                                        size="small"
                                        title={`Tax Type ${index + 1}`}
                                        key={field.key}
                                        extra={
                                          <CloseOutlined
                                            onClick={() => {
                                              remove(field.name);
                                            }}
                                          />
                                        }
                                      >
                                        <Row gutter={16}>
                                          <Col span={12}>
                                            <Form.Item
                                              name={[field.name, 'taxType']}
                                              label="Select Tax Type"
                                              rules={[{ required: true, message: 'Please select a tax type' }]}
                                            >
                                              <Select>
                                                {taxTypes.map(type => (
                                                  <Select.Option key={type.key} value={type.value} label={type.label}>
                                                    {type.label}
                                                  </Select.Option>
                                                ))}
                                              </Select>
                                            </Form.Item>
                                          </Col>
                                          <Col span={12}>
                                            <Form.Item
                                              name={[field.name, 'taxPercent']}
                                              label="Enter Tax Percent"
                                              rules={[
                                                {
                                                  required: true,
                                                },
                                              ]}
                                            >
                                              <InputNumber style={{ width: '100%' }} />
                                            </Form.Item>
                                          </Col>
                                        </Row>
                                      </Card>
                                    ))}
                                    {fields.length < taxTypeList.length && (
                                      <Button type="dashed" onClick={() => add()} block>
                                        + Add Tax
                                      </Button>
                                    )}
                                    <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                                  </div>
                                )}
                              </Form.List>
                            </Col>
                          </Row>
                        </Modal>
                      </div>
                    );
                  }

                  if (field.type === 'deliverySchedule') {
                    return (
                      <div key={field.id}>
                        <Button 
                          type="dashed"
                          onClick={handleManageDeliverySchedule}
                          style={{ marginTop: '45px' }}
                        >
                          Manage Delivery Schedule
                        </Button>

                        <Modal
                          title="Delivery Schedule"
                          open={isDeliveryScheduleModalVisible}
                          onOk={() => setIsDeliveryScheduleModalVisible(false)}
                          onCancel={() => setIsDeliveryScheduleModalVisible(false)}
                          width={'50%'} // Set width (adjust as needed)
                          style={{ top: 50, height: '50vh' }} // Adjust height & position
                          bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
                        >
                          <Row gutter={16}>
                            <Col span={24}>
                              <Form.List name="deliverySchedule" label="Delivery Schedule">
                                {(fields, { add, remove }, { errors }) => (
                                  <div
                                    style={{
                                      display: 'flex',
                                      rowGap: 16,
                                      flexDirection: 'column',
                                    }}
                                  >
                                    {fields.map((field, index) => (
                                      <Card
                                        size="small"
                                        title={`Schedule ${index + 1}`}
                                        key={field.key}
                                        extra={
                                          <CloseOutlined
                                            onClick={() => {
                                              remove(field.name);
                                            }}
                                          />
                                        }
                                      >
                                        <Row gutter={16}>
                                          <Col span={24}>
                                            <Form.Item
                                              name={[field.name, 'productName']}
                                              label="Select Product"
                                              rules={[{ required: true, message: 'Please select a product' }]}
                                            >
                                              <Select>
                                                {deliveryScheduleProducts.map(product => (
                                                  <Select.Option
                                                    key={product.id}
                                                    value={product.tradeName}
                                                    label={product.tradeName}
                                                  >
                                                    {product.tradeName}
                                                  </Select.Option>
                                                ))}
                                              </Select>
                                            </Form.Item>
                                          </Col>
                                          <Col span={12}>
                                            <Form.Item
                                              name={[field.name, 'quantity']}
                                              label="Enter Quantity"
                                              rules={[{ required: true }]}
                                            >
                                              <InputNumber style={{ width: '100%' }} />
                                            </Form.Item>
                                          </Col>
                                          <Col span={12}>
                                            <Form.Item
                                              name={[field.name, 'deliveryDate']}
                                              label="Select Date"
                                              rules={[{ required: true }]}
                                            >
                                              <DatePicker
                                                format={DateFormat}
                                                style={{ width: '100%' }}
                                                suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
                                              />
                                            </Form.Item>
                                          </Col>
                                          <Col span={24}>
                                            <Form.Item name={[field.name, 'remarks']} label="Remarks">
                                              <Input.TextArea />
                                            </Form.Item>
                                          </Col>
                                        </Row>
                                      </Card>
                                    ))}
                                    {fields.length < taxTypeList.length && (
                                      <Button type="dashed" onClick={() => add()} block>
                                        + Add Delivery Schedule
                                      </Button>
                                    )}
                                    <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                                  </div>
                                )}
                              </Form.List>
                            </Col>
                          </Row>
                        </Modal>

                        {/* Display selected delivery schedules */}
                        {/* <Form.List name="deliverySchedule">
                          {(fields) => (
                            <div style={{ marginTop: '10px' }}>
                              {fields.map((field, index) => {
                                const schedule = form.getFieldValue(['deliverySchedule', index]);
                                if (!schedule) return null;

                                return (
                                  <Card 
                                    size="small" 
                                    key={field.key} 
                                    style={{ marginBottom: '8px' }}
                                  >
                                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                      <span>{schedule.productName}</span>
                                      <span>Qty: {schedule.quantity}</span>
                                      <span>{schedule.deliveryDate?.format(DateFormat)}</span>
                                    </div>
                                  </Card>
                                );
                              })}
                            </div>
                          )}
                        </Form.List> */}
                      </div>
                    );
                  }

                  if (field.showIfFilledAndValueIn) {
                    shouldShow = checkShowIfFilledAndValueIn(
                      field.showIfFilledAndValueIn,
                      formValues
                    );
                  }

                  let fieldRules = field.rules || [];
                  let requiredProp = undefined;

                  // Special logic for Material SpecFile
                  if (field.id === 'materialSpecFile') {
                    if (orderType === 'CUSTOMER') {
                      // Always required for Customer Order
                      fieldRules = [{ required: true, message: 'Material SpecFile is required' }];
                      requiredProp = true;
                    } else if (orderType === 'SAMPLE') {
                      if (poRequirement === 'poRequired') {
                        fieldRules = [{ required: true, message: 'Material SpecFile is required' }];
                        requiredProp = true;
                      } else {
                        fieldRules = [];
                        requiredProp = false;
                      }
                    }
                  } else if (poRequirement === 'poNotRequired') {
                    // Your existing logic for other fields
                    const isMandatoryWithoutPO = ['supplier', 'mrd', 'shippingAddress'].includes(field.id);
                    if (!isMandatoryWithoutPO) {
                      fieldRules = fieldRules.map(rule => {
                        if (rule.required) {
                          const { required, ...ruleWithoutRequired } = rule;
                          return ruleWithoutRequired;
                        }
                        return rule;
                      }).filter(rule => Object.keys(rule).length > 0);
                    }
                  }

                  if (!shouldShow) return null;

                  return (
                    <Form.Item
                      key={field.id}
                      label={field.label}
                      name={field.id}
                      rules={fieldRules}
                      required={requiredProp}
                      style={{ width: '200px' }}
                      valuePropName={field.type === 'switch' ? 'checked' : 'value'}
                    >
                      {renderField(field)}
                    </Form.Item>
                  );
                })}
              </div>
            </div>
          ))}
          <Divider className={CSS.divider} />
          <div className="flex justify-between items-center px-6">
            <h2 className="text-xl font-semibold text-gray-800">
              Product Details{' '}
              <span className="text-sm text-gray-500">(at least 1 product is needed)</span>
            </h2>
            <Button onClick={addRow} style={{ marginTop: 10 }}>
              Add Row
            </Button>
          </div>
          <ProductTable
            form={form}
            packagingList={packagingList}
            productList={productList}
            initialValue={orderBookEntry}
            setInitialValue={setOrderBookEntry}
            orderType = {orderType}
            productsData={orderBook?.products}
            poRequirement={poRequirement}
          />
        </Form>
      </div>
    </>
  );
};

export default SupplierOrderBookForm;

SupplierOrderBookForm.propTypes = {
  mode: PropTypes.string.isRequired,
  order: PropTypes.object,
  hide: PropTypes.func.isRequired,
  orderBookId: PropTypes.string.isRequired,
  COBProducts: PropTypes.array,
  orderType: PropTypes.string.isRequired,
  orderBook: PropTypes.object,
  poGenrationRequired: PropTypes.bool,
};
