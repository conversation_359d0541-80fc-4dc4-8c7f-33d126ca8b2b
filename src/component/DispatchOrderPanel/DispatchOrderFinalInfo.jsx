import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Col, DatePicker, Form, Input, InputNumber, Row, Select, Button } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import {
  DateFormat,
  // countryList,
  formConfigName,
  formModes,
  paymentTermsDate,
} from '../../constants/formConstant';
import IncotermInput from '../IncotermInput/IncotermInput';
import { getFormConfigFromName } from '../../service/api/formConfig';
import PageLoader from '../Loaders/PageLoader';
import { getEmployeeList } from '../../service/api/employee';
import DocumentUpload from './DocumentUpload';

const DispatchOrderFinalInfo = props => {
  const { form, initialValue, formView } = props;

  const [documentList, setDocumentList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);

  const getFormPrefilldValues = () => {

    const prefilledValues =
      initialValue && initialValue.paymentTerms
        ? {
            paymentTerms: { ...initialValue.paymentTerms },
            labelFile: initialValue.labelFile,
            shipmentDate: initialValue.shipmentDate || '',
            expectedDeliveryDate: initialValue.expectedDeliveryDate || '',
            incoterms: initialValue.incoterms,
            remarks: initialValue.remarks,
            shipmentCreatedAtDate: initialValue.shipmentCreatedAtDate || '',
            invoiceSentOn: initialValue.invoiceSentOn || '',
            dispatchDate: initialValue.dispatchDate || '',
            customsReleasedOn: initialValue.customsReleasedOn || '',
            finalDocuments: Array.isArray(initialValue.finalDocuments) // ✅ Check if already an array
              ? initialValue.finalDocuments
              : Object.entries(initialValue.finalDocuments || {}).flatMap(([documentType, files]) =>
                  (Array.isArray(files) ? files : []).map(file => ({
                    documentType,
                    files: [file], // Convert back to array format
                  }))
                ),
          }
        : {};

    return prefilledValues;
  };

  useEffect(() => {
    if (!documentList || !documentList.length) {
      setLoading(true);
      getFormConfigFromName(formConfigName.documents)
        .then(res => {
          if (res.data) {
            setDocumentList(res.data.value);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);
  useEffect(() => {
    if (initialValue && initialValue.paymentTerms) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue, documentList]);

  useEffect(() => {
    if (formView === formModes.CREATE) setLoading(true);
    const fetchApiList = [getEmployeeList()];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setEmployeeList(responseList[0].data.content);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        if (formView === formModes.CREATE) setLoading(false);
      });
  }, []);

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      name="order_payment_info"
      layout="vertical"
      scrollToFirstError
      initialValues={formView === formModes.CREATE ? {} : initialValue}
      disabled={formView === formModes.READ}
      size="middle"
      form={form}
      preserve={false}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Shipment created at date" name={'shipmentCreatedAtDate'}>
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={'consignee'} label="Consignee Details">
            <Input.TextArea style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={'notifyParty'} label="Notify Party Details">
            <Input.TextArea style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <IncotermInput form={form} prefilledValue={initialValue?.incoterms} />
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={['paymentTerms', 'poPaymentTerms']} label="PO payment terms">
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditAmount']}
            label="Credit amount(in %)"
            rules={[
              {
                required: true,
                message: 'Credit amount can only be a valid number!',
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditorDays']}
            label="Credit days"
            rules={[
              {
                required: true,
                message: 'Credit days can only be a valid number!',
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'startDate']}
            label="Payment terms should be based on?"
            rules={[
              {
                required: true,
                message: 'Please input start date!',
              },
            ]}
          >
            <Select>
              {paymentTermsDate.map(date => (
                <Select.Option key={date.key} value={date.value}>
                  {date.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={'deliveryAddress'}
            label="Delivery Address"
            // rules={[
            //   {
            //     required: true,
            //     message: 'Please input start date!',
            //   },
            // ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={'invoiceSentBy'}
            label="Invoice Sent By"
            // rules={[
            //   {
            //     required: true,
            //     message: 'Please input start date!',
            //   },
            // ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={'invoiceSentOn'}
            label="Invoice Sent On"
            // rules={[
            //   {
            //     required: true,
            //     message: 'Please input start date!',
            //   },
            // ]}
          >
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="carrierName" label="Carrier Name">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="trackingNumber" label="Tracking Number">
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="trackingUrl" label="Tracking Url">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={'customsReleasedOn'} label="Customs Released On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={'customReleaseConfirmedBy'} label="Custom Release Confirmed By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={'dispatchDate'}
            label="Dispatch Date"
            // rules={[
            //   {
            //     required: true,
            //     message: 'Please input start date!',
            //   },
            // ]}
          >
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={'SealNo'} label="Seal Number">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.List label="Add Documents" name={['finalDocuments']}>
            {(subFields, { add, remove }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                  marginBottom: '24px',
                }}
              >
                <h2 className="text-xl font-semibold text-gray-800">
                  Documents{' '}
                  <span className="text-sm text-gray-500">
                    (Attach COA, costing document, and others as required)
                  </span>
                </h2>
                <Button type="dashed" onClick={() => add()} block>
                  + Add Documents
                </Button>
                {subFields.map((subField, index) => {
                  return (
                    <DocumentUpload key={index} index={index} remove={remove} subField={subField} />
                  );
                })}
              </div>
            )}
          </Form.List>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="igstAmt" label="IGST Amt">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="palletWt" label="Pallet Wt (Chemstack)">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="mstackPalletWt" label="Pallet Wt (Mstack)">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="marksAndContainers" label="Marks & Containers">
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      {/* <Row gutter={16}>
        <Col span={24}>
                {employeeList.length ? (
          <Form.Item name="l1Reviewers" label="L1 Reviewers" rules={[{required:true}]}>
                    <Select mode='multiple' showSearch>
                      {employeeList.map(emp => (
                        <Select.Option key={emp.id} value={`${emp.id}`}>
                          {`${emp.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
                {employeeList.length ? (
          <Form.Item name="l2Reviewers" label="L2 Reviewers" rules={[{required:true}]}>
                    <Select mode='multiple' showSearch>
                      {employeeList.map(emp => (
                        <Select.Option key={emp.id} value={`${emp.id}`}>
                          {`${emp.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
        </Col>
      </Row> */}
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="remarks" label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              savetimeStamp
              showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default DispatchOrderFinalInfo;

DispatchOrderFinalInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
};
