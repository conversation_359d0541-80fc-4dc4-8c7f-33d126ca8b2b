.title {
  margin-bottom: 16px;
  font-style: normal;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 18px;
  line-height: 1.4444444444444444;
}

.productAttributes {
  margin-bottom: 16px;
  padding-bottom: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 10px;
  border-bottom: 1px solid rgba(30, 30, 30, 0.20);
}

.collapseSection {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(30, 30, 30, 0.20);
}

.packagingGrid {
  padding-bottom: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 10px;
}

.attributeContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.remarksSection {
  margin-bottom: 16px;
  padding-bottom: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  border-bottom: 1px solid rgba(30, 30, 30, 0.20);
}

.docGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.collapseTitle {
  font-size: 18px;
  font-weight: 500;
  color: rgba(35, 86, 138, 0.80);
}

.label {
  color: rgba(35, 86, 138, 0.80);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.32px;
}

.value {
  color: #1E1E1E;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.36px;
}

.btnFlexStyle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.emptyDoc {
  margin: auto;
  width: fit-content;
}

.drawerHeader > div {
  flex-direction: row-reverse;
}

.drawerTitleBox {
  display: flex;
  flex-direction: column;
  gap: 10px
}
.drawerTitle{
  color: #1E1E1E;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
}

.drawerSubTitle {
  color: rgba(75, 75, 75, 0.80);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
}

.docHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.docTitle {
  color: rgba(35, 86, 138, 0.80);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  letter-spacing: -0.64px;
}

.formList {
  margin-bottom: 20px;
  height: 80vh;
  overflow-y: auto;
}