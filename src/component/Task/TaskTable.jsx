import React, { useEffect, useState } from 'react';
import { Table, Button, Modal, Input, DatePicker, Select, Tooltip, Form } from 'antd';
import { EditFilled, BellOutlined } from '@ant-design/icons';
import css from '../CustomerPanel/CustomerListView.module.css';
import { DateFormat, criticalPathStatus, formConfigName, formModes } from '../../constants/formConstant';
import TaskModal from './TaskModal';
import PropTypes from 'prop-types';
import { createTask, deleteTask, updateTask } from '../../service/api/activityApi';
import { isDateCloseToExpiry, isDateExpired } from '../../util/dateUtils';
import { formatTaskData, getInputFiledsFromList } from '../../util/formUtil';
import { getEmployeeList } from '../../service/api/employee';
import addIcon from '../../assets/icons/add_circle.svg';
import deleteIcon from '../../assets/icons/delete.svg'
import css2 from './TaskTable.module.css';
import dayjs from 'dayjs';
import { getFormConfigFromName } from '../../service/api/formConfig';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { useSelector } from 'react-redux';
import { getSimilarKeyCount } from '../../util/formatUtil';

const TaskTable = ({ entityId, tasks, setTaskData }) => {
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(null); // Use this state to manage edit mode
  const [taskToEdit, setTaskToEdit] = useState(null); // Store the task to edit
  const [confirmationModalVisible, setConfirmationModalVisible] = useState(false);
  const [deleteRecord, setDeleteRecord] = useState(null);
  const [employeeList, setEmployeeList] = useState([])
  const [taskCategories, setTaskCategories] = useState([])
  const [showCompleteStatusProofModal, setShowCompleteStatusProofModal] = useState(false);
  const [query, setQuery] = useState('');

  const user = useSelector(state => state.user);
  const [form] = Form.useForm();

  const columns = [
    {
      title: 'Stage',
      dataIndex: 'category',
      key: 'category',
      className: 'bg-base-white',
      fixed: 'left',
      width: 150,
      onCell: (record) => {
        if (record.count) {
          return { rowSpan: record.count };
        } else {
          return { rowSpan: 0 };
        }
      },
      render: (_, {category}) => {
        const catObj = taskCategories.find((cat) => cat.key === category)
        return <div className='font-semibold'>{catObj ? catObj.displayName : category}</div>
      }
    },
    {
      title: 'Activity',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 250,
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 150,
      render: (_, record) => (
        <DatePicker
          format={DateFormat}
          value={dayjs(record.dueDate)}
          variant='borderless'
          allowClear={false}
          onChange={(date) => onCriticalPathCellChange(record, date.toISOString(), 'dueDate')}
        />
      )
    },
    {
      title: 'Input Labels',
      dataIndex: 'inputLabel',
      key: 'inputLabel',
      width: 200,
      render: (_, record) => record?.taskConfig?.length ? (
        record.taskConfig.map((task) => task.label).join(',')
      ) : '-'
    },
    {
      title: 'Completion Date',
      dataIndex: 'displayDate',
      key: 'displayDate',
      width: 150,
      render: (_, record) => (
        <DatePicker
          format={DateFormat}
          value={record.displayDate ? dayjs(record.displayDate) : ''}
          variant='borderless'
          allowClear={false}
          disabled
        />
      )
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 200,
      render: (_, record) => (
        <Select
          defaultValue={record.assignedTo}
          style={{ width: '150px' }}
          onSelect={(value) => onCriticalPathCellChange(record, value, 'assignedTo')}
        >
          {employeeList.map((employee) => (
            <Select.Option key={employee.id} value={employee.id}>
              {employee.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (_, record) => (
        <Select
          defaultValue={record.status}
          style={{ width: '100px' }}
          value={record.status}
          // onSelect={(value) => onCriticalPathCellChange(record, value, 'status')}
          onSelect={(value) => onTaskStatusChangeHandler(record, value)}
        >
          {criticalPathStatus.map((status) => (
            <Select.Option key={status.key} value={status.value}>
              {status.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          onClick={() => showEditModal(record)}
          icon={<EditFilled />}
        />
      ),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (_, record) => record?.events?.length ? (
        <Tooltip title={record.description}>
          <BellOutlined style={{ marginLeft: '5px', fontSize: '24px', color: '#23568a' }} />
        </Tooltip>
      ) : (
        <Button
          type='text'
          onClick={() => openConfirmDeleteModal(record)}
          icon={<img src={deleteIcon} alt="delete" width={24} height={24}/>}
        />
      ),
    },
  ];

  useEffect(() => {
    setLoading(true);
    Promise.all([getEmployeeList(), getFormConfigFromName(formConfigName.taskCategories)]).then(
      (responseList) => {
        if(responseList?.[0]?.data?.content) {
          setEmployeeList(responseList?.[0]?.data?.content);
        }
        if (responseList?.[1]?.data?.value) {
          setTaskCategories(responseList?.[1].data.value);
        }
      }
    ).catch((err) => {
      console.log(err)
    }).finally(() => {
      setLoading(false);
    });
  }, []);

  const onSearchHandler = (searchText) => {
    setQuery(searchText);
  };
  // Function to show the modal for adding a new task
  const showAddModal = () => {
    setIsModalVisible(true);
    setEditMode(formModes.ADD); // Set edit mode to ADD
  };
  const hideCompleteStatusProofModal = () => {
    form.resetFields();
    setShowCompleteStatusProofModal(false);
  }
  // Function to show the modal for editing a task
  const showEditModal = record => {
    console.log(record);
    setIsModalVisible(true);
    setEditMode(formModes.UPDATE); // Set edit mode to UPDATE
    // pass record date as date
    setTaskToEdit(formatTaskData(record)); // Set the task to edit
  };
  const openConfirmDeleteModal = record => {
    // console.log(record);
    setDeleteRecord(record);
    setConfirmationModalVisible(true);
  };
  // Function to hide the modal
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditMode(null); // Reset the edit mode
    setTaskToEdit(null); // Reset the task to edit
  };
  const handleSubmit = (values, isEditMode, setLoader) => {
    // You can process the form data here based on isEditMode (ADD or UPDATE)
    console.log('Form data submitted:', values, 'EditMode:', isEditMode);
    if (isEditMode && values.id) {
      updateTask(values).then((res) => {
        if (res.data) {
          console.log("task updated");
          const taskNew = tasks.map((task) => {
            if (task.id === res.data.id) {
              return res.data;
            } else
              return task
          });
          if (setLoader) setLoader(false);
          setIsModalVisible(false); // Cancel the modal
          setTaskData(taskNew);
        }
      }).catch(error => {
        console.log(error);
        if (setLoader) setLoader(false);
        setIsModalVisible(false); // Cancel the modal
      });
      
      // console.log('Form data submitted:', values, 'EditMode:', isEditMode);
    } else {
      values.entityId = entityId;
      values.entityType = 'CUSTOMER_ORDER';
      createTask(values)
        .then(res => {
          if (res.data) {
            console.log('task created');
            if (setLoader) setLoader(false);
            setIsModalVisible(false); // Cancel the modal
          }
        })
        .catch(error => {
          console.log(error);
          if (setLoader) setLoader(false);
          setIsModalVisible(false); // Cancel the modal
        });
    }
  };
  const handleCancelConfirmationModal = () => {
    setConfirmationModalVisible(false);
    setDeleteRecord(null);
  };
  const handleDeleteConfirmation = () => {
    deleteTask(deleteRecord.id)
      .then(res => {
        if (res.status == 204) {
          const newTaskData = tasks.filter((task) => task.id !== deleteRecord.id);
          setTaskData(newTaskData);
          setDeleteRecord(null);
          setConfirmationModalVisible(false);
        }
      })
      .catch(error => {
        console.log(error);
      });
  };
  const getRowClassname = (record) => {
    if (record.status === criticalPathStatus[2].value) return css2.completed;
    else if (isDateExpired(record.dueDate)) return css2.deadlineCrossed;
    else if (isDateCloseToExpiry(record.dueDate)) return css2.deadlineClosing;
  };
  function onCriticalPathCellChange(record, value, key) {
    const taskData = {
      ...record,
      [key]: value,
    }
    handleSubmit(taskData, true);
  }
  function onTaskStatusChangeHandler(record, value) {
    if (record.status === value) {
      return;
    }
    if (value === 'COMPLETED') {
      setTaskToEdit(record);
      setShowCompleteStatusProofModal(true);  
    } else {
      onCriticalPathCellChange(record, value, 'status')
    }
  }
  const taskCompleteHandler = () => {
    setLoading(true);
    form.validateFields().then((values) => {
      let newTaskData = {
        ...taskToEdit,
        status: 'COMPLETED',
        displayDate: values.displayDate.toISOString(),
      };
      let newConfig = {};
      if (taskToEdit?.taskConfig?.length) {
        newConfig = taskToEdit.taskConfig.map((config) => ({
          ...config,
          value: values?.[config?.key],
        }));
        newTaskData = {
          ...newTaskData,
          taskConfig: newConfig,
        }
      }      
      handleSubmit(newTaskData, true);
      setLoading(false);
      hideCompleteStatusProofModal()
    })
    .catch(error => {
      console.log(error)
      setLoading(false);
    });
  }
  const getTableDataSource = () => {
    return getSimilarKeyCount(
      tasks.filter(
        task => task.name.toLowerCase().includes(query.toLowerCase())
      ).map(
        data => {return {...data, key: data.id }}
      ),
      'category',
    );
  }

  return (
    <div>
      <Modal
        title="Do you confirm to delete the task?"
        open={confirmationModalVisible}
        onOk={handleDeleteConfirmation}
        onCancel={() => handleCancelConfirmationModal()}
      />
      {isModalVisible ? (<TaskModal
        visible={isModalVisible}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        formMode={editMode}
        values={taskToEdit}
        employeeList={employeeList}
        taskCategories={taskCategories}
      />) : null}
      <div className={css.functionalBar} style={{ padding: '0 0 20px 0' }}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by Activity"
            variant='borderless'
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onChange={(e) => onSearchHandler(e.currentTarget.value)}
            onSearch={onSearchHandler}
          />
        </div>
        
        {hasPermission(userPermissionsList.createTask, user.permissions) ? (
        <Button
          onClick={showAddModal}
          type="primary"
          size="large"
          className={css.btnFlexStyle}
        >
          <img src={addIcon} />
          <span>Add Task</span>
        </Button>):null}
      </div>
      <Table
        bordered={true}
        columns={columns}
        dataSource={getTableDataSource()}
        pagination={false}
        loading={loading}
        rowClassName={(record) => getRowClassname(record)}
        scroll={{
          x: '100%',
          y: 600,
        }}
      />
      {taskToEdit && taskToEdit.id ? (
        <Modal
          open={showCompleteStatusProofModal}
          title="Task Details Required"
          width="50%"
          onCancel={() => hideCompleteStatusProofModal()}
          footer={[
            <Button
              key="cancel"
              onClick={() => hideCompleteStatusProofModal()}>
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={() => {
                taskCompleteHandler();
              }}
            >
              Submit
            </Button>,
          ]}
        >
          <div className='mb-3 py-3 border-l-0 border-r-0 border-t border-solid border-t-border-color border-b border-b-border-color'>
            <div className='flex gap-2'>
              <span className='font-medium'>Stage:</span>
              <span className='text-color-text-heading'>
                {taskCategories.find((cat) => cat.key === taskToEdit.category)?.displayName || taskToEdit?.category}
              </span>
            </div>
            <div className='flex gap-2'>
              <span className='font-medium'>Activity:</span>
              <span className='text-color-text-heading'>{taskToEdit.name}</span>
            </div>
            <div>{taskToEdit.description}</div>
          </div>
          <Form
            form={form}
            layout="vertical"
          >
            <Form.Item
              label="Display Date for Task"
              name="displayDate"
              rules={[{ required: true, message: 'Please enter a valid value', type: 'date' }]}
            >
              <DatePicker />
            </Form.Item>
            {taskToEdit && taskToEdit?.taskConfig?.length ? (
              getInputFiledsFromList(taskToEdit.taskConfig, true, null, null)
            ) : null}
            {/* create form as it is easy for validation and submition */}
            {/*  loop across task key which give proof detail to capture */}
            {/* create a function to be used in multiple places: inside task modal and dashboard */}
          </Form>        
        </Modal>
      ) : null}
    </div>
  );
};

TaskTable.propTypes = {
  tasks: PropTypes.array.isRequired,
  entityId:PropTypes.string.isRequired,
  setTaskData: PropTypes.func.isRequired,
};

export default TaskTable;
