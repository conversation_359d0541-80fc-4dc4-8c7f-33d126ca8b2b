import React from 'react';
import PropTypes from 'prop-types';
import { CloseOutlined, CalendarOutlined } from '@ant-design/icons';
import { Card, Col, DatePicker, Form, Input, InputNumber, Row } from 'antd';
import { DateFormat } from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';

const BatchCard = (props) => {

  const { index, field, remove} = props;
  return (
    <Card
      size="small"
      title={`Batch ${index + 1}`}
      key={field.key}
      extra={
      <CloseOutlined
        onClick={() => {
          remove(field.name);
        }}
      />}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'batchNumber']}
            label="Batch Number"
            rules={[{ required: true, message: 'Please enter batch number.' }]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'containerNumber']}
            label="Container Number"
            rules={[{ required: true, message: 'Please enter container number.' }]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'units']}
            label="Units"
            rules={[{ required: true, message: 'Please enter total units.' }]}
          >
            <InputNumber style={{ width: '100%' }}/>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={[field.name, 'mfgDate']}
            label="Manufacturing Date"
            rules={[{ required: true, message: 'Please enter valid date.' }]}
          >
            <DatePicker
              format={DateFormat}
              locale={enUS}
              showToday
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={[field.name, 'expDate']}
            label="Expiry Date"
            rules={[{ required: true, message: 'Please enter valid date.' }]}
          >
            <DatePicker
              format={DateFormat}
              locale={enUS}
              showToday
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )
}

export default BatchCard;

BatchCard.propTypes = {
  // form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  remove: PropTypes.func.isRequired,
  // initialValue: PropTypes.object.isRequired,
  // setInitialValue: PropTypes.func,
};