import { CloseCircleFilled, CloseOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { formConfigName, formModes } from '../../../constants/formConstant';
import RouteFactory from '../../../service/RouteFactory';
import { getFormConfigFromName } from '../../../service/api/formConfig';
import { createProduct } from '../../../service/api/productApi';
import MultipleTextFieldInput from '../../MultipleTextFieldInput/MultipleTextFieldInput';
import './ProductAddPage.module.css';
import css from './ProductAddPage.module.css';
import HeaderPanel from '../../headerPanel';
import SubCategoryDropdown from '../SubCategoryDropdown';
import PageLoader from '../../Loaders/PageLoader';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';
import { useNotificationContext } from '../../../provider/NotificationProvider';

const ProductAddPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [loading, isLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);

  const { api } = useNotificationContext();

  useEffect(() => {
    if (categoryList.length < 1) {
      getConfigs();
    }
  }, []);

  const goBack = () => {
    dispatch(setCollapseAsideBar(false));
    navigate(RouteFactory.buildProduct());
  };
  const getConfigs = async () => {
    try {
      const apiRes = await getFormConfigFromName(formConfigName.category);
      if (apiRes.data) {
        setCategoryList(apiRes.data.value || []);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const submitForm = async () => {
    isLoading(true);
    form
      .validateFields()
      .then(async values => {
        const apiRes = await createProduct(values);
        if (apiRes.status >= 200 && apiRes.status < 300) {
          navigate(RouteFactory.buildProduct());
        }
      })
      .catch(err => {
        isLoading(false);
        api.open({
          key: 'customerSaveError',
          message: 'Failed to add new product!!',
          description: (
            <>
              <div className="text-xs font-medium">
                {err?.response?.data?.message || err?.message}
              </div>
            </>
          ),
          duration: 5,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
        console.log(err);
      });
  };

  if (loading) {
    return <PageLoader />;
  }

  return (
    <>
      <HeaderPanel
        name="Product Directory"
        sideButtons={
          <Row>
            <Space>
              <Button type="primary" onClick={submitForm}>
                Add Product
              </Button>
              <Button type="default" htmlType="cancel" onClick={goBack}>
                Cancel
              </Button>
            </Space>
          </Row>
        }
      />
      <div className={css.formContainer}>
        <Form
          name="productForm"
          layout="vertical"
          scrollToFirstError
          initialValues={formModes.CREATE}
          size="large"
          form={form}
        >
          <Row justify="space-between">
            <Col span={12}>
              <Typography.Title level={2} style={{ color: '#23568A' }}>
                Add a Product
              </Typography.Title>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Name"
                name="tradeName"
                rules={[
                  {
                    required: true,
                    message: 'Please input product name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Technical Name"
                name="technicalName"
                rules={[
                  {
                    required: false,
                    message: 'Please input product technical name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="functions" label="Functions">
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[',']} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="family" label="Family">
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[',']} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="CAS Number"
                name="casNumber"
                rules={[
                  {
                    required: true,
                    message: 'Please input CAS Number!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Grade"
                name="grade"
                rules={[
                  {
                    required: true,
                    message: 'Please input grade!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <div className={css.categoriesList}>
            <Typography.Title level={4} style={{ color: '#23568A', fontWeight: '400' }}>
              Categories
            </Typography.Title>
            {categoryList.length ? (
              <Form.List
                label="Category"
                name="categories"
                rules={[{ required: true, message: 'Please select categories', type: 'array' }]}
              >
                {(fields, { add, remove }) => (
                  <>
                    {fields.map((field, index) => (
                      <Row gutter={16} key={index}>
                        <Col span={11}>
                          <Form.Item
                            key={field.key}
                            name={[field.name, 'category']}
                            label="Category"
                            rules={[
                              {
                                required: true,
                                message: 'Please select Category!',
                              },
                            ]}
                          >
                            <Select showSearch>
                              {categoryList.map((category, index) => (
                                <Select.Option key={index} value={category.name}>
                                  {category.name}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={11}>
                          <SubCategoryDropdown
                            form={form}
                            categoryList={categoryList}
                            field={field}
                          />
                        </Col>
                        <Col span={2}>
                          <Button
                            className={css.categoriesButton}
                            type="primary"
                            shape="circle"
                            icon={<CloseOutlined />}
                            onClick={() => {
                              remove(field.name);
                            }}
                          />
                        </Col>
                      </Row>
                    ))}
                    <Button onClick={() => add()} block>
                      + Add Category
                    </Button>
                  </>
                )}
              </Form.List>
            ) : null}
          </div>

          <Row>
            <Col span={24}>
              <Form.Item name="synonyms" label="Synonyms">
                <MultipleTextFieldInput mode={formModes.CREATE} placeholder="Enter synonyms" />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item name="features" label="Features">
                <MultipleTextFieldInput mode={formModes.CREATE} placeholder="Enter features" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default ProductAddPage;
