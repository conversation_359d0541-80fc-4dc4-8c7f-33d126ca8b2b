import React, { useState, useEffect} from 'react';
import PropTypes from 'prop-types';
import { Button, Form,Input, Select, DatePicker, Radio, Divider, message } from 'antd';
import dayjs from 'dayjs';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import {
  createSupplierDispatchOrder,
  updateSupplierDispatchOrder,
} from '../../service/api/supplierDispatchOrderApi';
import { getDispatchOrderDataForApi, getPackagingDetailFromId } from '../../util/formUtil';
import {
  SupplierDispatchOrderFormBasicInfo,
  DateFormat,
  orderForList,
  orderTypeList,
  paymentTermsDate,
} from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';
import { CalendarOutlined } from '@ant-design/icons';
import FileUpload from '../FileUpload';
import ProductTable from './ProductTable';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';
import ObjectUtil from '../../util/objectUtil';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import PageLoader from '../Loaders/PageLoader';

const SupplierDispatchOrderForm = props => {
  const { mode, dispatchOrder, orderBook, hide, customerorderbookId, poGenrationRequired } = props;

  const packagingList = useSelector(state => state.packagingList);
  const [dispatchOrderEntry, setDipatchOrderEntry] = useState(
    mode === formModes.UPDATE
      ? {
          ...dispatchOrder,
          productList: [...orderBook.products],
        }
      : {
          purchaseOrderNumber: orderBook.purchaseOrderNumber,
          purchaseOrderDate: dayjs(orderBook.purchaseOrderDate),
          suplier: orderBook.supplier,
          paymentTerms: { ...orderBook.paymentTerms },
          productList: [...orderBook.products],
          products: [
            ...Array(Math.max(1)).fill({}), // Add only the required number of empty objects
          ],
        }
  );

  const productList =
    dispatchOrderEntry && dispatchOrderEntry?.productList ? dispatchOrderEntry.productList : [];
  const [submittingForm, setSubmittingForm] = useState(false);
  const dispatch = useDispatch();

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form); // watch all values in form



  const saveOrderDetails = orderBook => {
    //private  List<String> linkedCustomerDispatchIds;

    const orderBookWithLinkedCustomerDispatchIds = {
      ...orderBook,
      linkedCustomerDispatchIds: Array.from(
        new Set(
          orderBook.products
            .flatMap(product =>
              product.linkedOrders?.map(order => order.orderId) || []
            )
        )
      ),
    };

    if (
      orderBookWithLinkedCustomerDispatchIds?.linkedCustomerDispatchIds &&
      Array.isArray(dispatchOrderEntry.cdoList)
    ) {
      const idToOrderIdMap = Object.fromEntries(
        dispatchOrderEntry.cdoList.map(item => [item.id, item.orderId])
      );

      orderBookWithLinkedCustomerDispatchIds.linkedCustomerDispatchIds =
        orderBookWithLinkedCustomerDispatchIds?.linkedCustomerDispatchIds?.map(
          id => idToOrderIdMap[id] || id
        );
    }
    
    if (mode === formModes.CREATE) {
      createSupplierDispatchOrder(orderBookWithLinkedCustomerDispatchIds)
        .then(response => {
          message.success('Supplier Dispatch Order created successfully');
          console.log("response",response);
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.log("error",error);
          message.error('Failed to create Supplier Dispatch Order');
          console.log("heere",error);
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE) {
      updateSupplierDispatchOrder(orderBookWithLinkedCustomerDispatchIds, orderBookWithLinkedCustomerDispatchIds?.id)
        .then(response => {
          message.success('Supplier Dispatch Order updated successfully');
          console.log("response",response);
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.log("error",error);
          message.error('Failed to update Supplier Dispatch Order');
          setSubmittingForm(false);
          console.log(error);
        });
    }
  };
  
  const submitFormHandler = dispatchOrderEntry => {
    if (!Array.isArray(dispatchOrderEntry.products) || dispatchOrderEntry.products.length === 0) {
      message.error('At least one product is required!');
      setSubmittingForm(false);
      return;
    }

    // Validate linkedOrders for each product
    const invalidProducts = dispatchOrderEntry.products.filter(product => {
      if (!Array.isArray(product.linkedOrders) || product.linkedOrders.length === 0) {
        return true;
      }
      
      // Check if any linkedOrder is missing orderId or quantity
      return product.linkedOrders.some(order => 
        !order.orderId || 
        !order.quantity || 
        order.quantity <= 0
      );
    });

    if (invalidProducts.length > 0) {
      setSubmittingForm(false);
      message.error({
        content: 'Each product must have at least one linked order with valid order ID and quantity',
        key: 'linkedOrders_validation',
        duration: 2,
      });
      return;
    }
    
    setSubmittingForm(true);
    const formattedData = JSON.parse(JSON.stringify(dispatchOrderEntry));
    getDispatchOrderDataForApi(dispatchOrderEntry, dispatchOrderEntry.productList)
      .then(results => {
        results.forEach((result, index) => {
          formattedData.products[index].product = result.data;
        });
        formattedData.purchaseOrderDate = dispatchOrderEntry.purchaseOrderDate
          ? dispatchOrderEntry.purchaseOrderDate.toISOString()
          : '';
        formattedData.deliveryDate = dispatchOrderEntry.deliveryDate
          ? dispatchOrderEntry.deliveryDate.toISOString()
          : '';
        if (formattedData.productList) delete formattedData.productList;
        if (formattedData.supplier?.products) delete formattedData.supplier.products;
        if (formattedData.cdoList) delete formattedData.cdoList;

        saveOrderDetails({
          ...formattedData,
          linkedSupplierOrderBookId: orderBook.id
        });
      })
      .catch(error => {
        console.log('Error in getting data from server', error);
        setSubmittingForm(false);
      });
  };

  const validateAndSaveFormFields = () => {
    setSubmittingForm(true);
    let formObj = form;
    formObj
      .validateFields()
      .then(values => {
        let valuesCopy = JSON.parse(JSON.stringify(values));
        valuesCopy.products = (valuesCopy.products || []).filter(product =>
          Object.values(product).some(
            value => value !== '' && value !== undefined && value !== null
          )
        );

        valuesCopy = {
          ...valuesCopy,
          products: [
            ...valuesCopy.products.map(prod => ({
              ...prod,
              packaging: getPackagingDetailFromId(prod.packaging, packagingList),
            })),
          ],
        };

        valuesCopy = {
          ...valuesCopy,
          shipmentDate: valuesCopy.shipmentDate ? dayjs(valuesCopy.shipmentDate) : '',
          deliveryDate: valuesCopy.deliveryDate ? dayjs(valuesCopy.deliveryDate) : '',
          bookingMadeOn: valuesCopy.bookingMadeOn ? dayjs(valuesCopy.bookingMadeOn) : null,
          qcApprovedOn: valuesCopy.qcApprovedOn ? dayjs(valuesCopy.qcApprovedOn) : null,
          loadingCompletedOn: valuesCopy.loadingCompletedOn
            ? dayjs(valuesCopy.loadingCompletedOn)
            : null,
          sampleReceivingDate: valuesCopy.sampleReceivingDate
            ? dayjs(valuesCopy.sampleReceivingDate)
            : null,
          dispatchToDestinationDate: valuesCopy.dispatchToDestinationDate
            ? dayjs(valuesCopy.dispatchToDestinationDate)
            : null,
          destinationRecievedDate: valuesCopy.destinationRecievedDate
            ? dayjs(valuesCopy.destinationRecievedDate)
            : null,
          dispatchFromDestinationOn: valuesCopy.dispatchFromDestinationOn
            ? dayjs(valuesCopy.dispatchFromDestinationOn)
            : null,
          supplierDispatchedOn: valuesCopy.supplierDispatchedOn
            ? dayjs(valuesCopy.supplierDispatchedOn)
            : null,
          etaForDestination: valuesCopy.etaForDestination
            ? dayjs(valuesCopy.etaForDestination)
            : null,
        };
        setDipatchOrderEntry({
          ...dispatchOrderEntry,
          ...valuesCopy,
        });
        submitFormHandler({
          ...dispatchOrderEntry,
          ...valuesCopy,
        });
      })
      .catch(error => {
        console.log(error);
        setSubmittingForm(false);
      });
  };

  const getOptionList = key => {
    switch (key) {
      case 'orderTypeList':
        return orderTypeList;
      case 'orderForList':
        return orderForList;
      case 'paymentTermsDate':
        return paymentTermsDate;
      default:
        return [];
    }
  };

  const renderField = field => {
    const options = getOptionList(field.optionListKey);
    const fieldStyle = field.style || {}; // directly use field style or empty object

    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={field?.rows} style={fieldStyle} disabled={field?.disable} />;
      case 'select':
        return (
          <Select showSearch optionFilterProp="label" style={fieldStyle} disabled={field?.disable}>
            {options.map(option => (
              <Select.Option
                key={option[field.optionMapping.key]}
                value={option[field.optionMapping.value]}
                label={option[field.optionMapping.label]}
              >
                {option[field.optionMapping.label]}
              </Select.Option>
            ))}
          </Select>
        );
      case 'date':
        return (
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={fieldStyle}
            disabled={field?.disable}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
          />
        );
      case 'radio':
        return (
          <Radio.Group buttonStyle="solid">
            {options.map(item => (
              <Radio
                key={item.key}
                value={item.value}
                style={{
                  display: 'block',
                  height: '30px',
                  lineHeight: '30px',
                  marginBottom: '0px',
                }}
              >
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      case 'text':
        return <Input className="w-52" style={fieldStyle} disabled={field?.disable} />;
      case 'number':
        return <Input className="w-52" style={fieldStyle} type="number" disabled={field?.disable} />;
      case 'file':
        return (
          <FileUpload
            category="Orders"
            meta={{ poId: 123, documentType: 'PurchaseOrder' }}
            maxFileLimit={1}
          />
        );
      default:
        return <Input className="w-52" style={fieldStyle} disabled={field?.disable} />;
    }
  };

  const checkShowIfFilledAndValueIn = (conditions, formValues) => {
    return Object.entries(conditions).every(([key, values]) => {
      const formValue = formValues?.[key];
      console.log('formValue', formValue);
      if (!formValue) return false;
      if (values.length === 0) return true;
      return values.includes(formValue);
    });
  };

  const addRow = () => {
    form.setFieldsValue({
      products: [...(form.getFieldValue('products') || []), {}],
    });
  };

  const validateForm = () => {
    const formValues = form.getFieldsValue();
    const validProducts = (formValues.products || []).filter(product =>
      Object.values(product).some(value => value !== '' && value !== undefined && value !== null)
    );
  };

  const saveformData = () => {
    if (mode === formModes.CREATE) {
      const formValues = form.getFieldsValue();
      const cleanedData = JSON.parse(JSON.stringify(formValues));
    } else {
      validateForm();
    }
  };

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    if (ObjectUtil.isEmptyObject(option)) {
      return option;
    }
    const listOptn = list.find(
      item => item.id === (option?.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getFormPrefilldValues = () => {
    const result = dispatchOrderEntry?.products
      ? {
          products: dispatchOrderEntry.products.map(prod => ({
            ...prod,
            product: prod.product,
            uom: prod.uom,
            price: prod.price,
            quantity: prod.quantity,
            units: prod?.units,
            hsCode: prod.hsCode,
            packaging: getPackagingFormValue(prod.packaging, packagingList),
            remarks: prod.remarks,
            label: prod.label,
            labelFile: prod.labelFile ? prod.labelFile : null,
            linkedOrderIds: prod.linkedOrderIds ? prod.linkedOrderIds : null,
          })),
        }
      : {};
    return result;
  };

  useEffect(() => {
    if (!packagingList || !packagingList.length) {
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      packagingList &&
      packagingList.length &&
      dispatchOrderEntry &&
      dispatchOrderEntry.products &&
      dispatchOrderEntry.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [dispatchOrderEntry.productList, dispatchOrderEntry.products, packagingList]); //TODO: verify this check


    // add a loader 
    if (submittingForm) {
      return <PageLoader />;
    }
  

  return (
    <>
      <div>
        {/* <div className="flex justify-end"> */}
          {/* <Button
            type="primary"
            size="large"
            loading={submittingForm}
            onClick={() => validateAndSaveFormFields()}
          >
            {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
          </Button> */}
        {/* </div> */}
        <Form
          name="supplier_form"
          layout="vertical"
          scrollToFirstError
          size="large"
          form={form}
          initialValues={dispatchOrderEntry}
          onValuesChange={() => saveformData()}
          onFinish={validateAndSaveFormFields}
        >
          <div className="flex justify-end">
          <Button
            type="primary"
            size="large"
            loading={submittingForm}
            htmlType='submit'
          >
            {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
          </Button>
        </div>
          {SupplierDispatchOrderFormBasicInfo.map(section => (
            <div key={section.id} className="border-b border-gray-100 last:border-0 pt-6">
              <div className="flex flex-wrap gap-6 px-6 py-0">
                {section.fields.map(field => {
                  let shouldShow = true;

                  if (field.showIfFilledAndValueIn) {
                    shouldShow = checkShowIfFilledAndValueIn(
                      field.showIfFilledAndValueIn,
                      formValues
                    );
                  }
                  let fieldRules = field.rules || [];

                  // If PO is not required, only make specific fields mandatory
                  if (poGenrationRequired === false || poGenrationRequired === 'false') {
                    // Check if this is a mandatory field when PO is not required
                    const isMandatoryWithoutPO = []?.includes(field.id);

                    // Remove required rule if not mandatory
                    if (!isMandatoryWithoutPO) {
                      fieldRules = fieldRules.filter(rule => !rule.required);
                    }
                  }
                  if (!shouldShow) return null;

                  return (
                    <Form.Item
                      key={field.id}
                      label={field.label}
                      name={field.id}
                      rules={fieldRules}
                    >
                      {renderField(field)}
                    </Form.Item>
                  );
                })}
              </div>
            </div>
          ))}
          <Divider className={CSS.divider} />
          <div className="flex justify-between items-center px-6">
            <h2 className="text-xl font-semibold text-gray-800">
              Product Details{' '}
              <span className="text-sm text-gray-500">(at least 1 product is needed)</span>
            </h2>
            <Button onClick={addRow} style={{ marginTop: 10 }}>
              Add Row
            </Button>
          </div>
          <ProductTable
            form={form}
            packagingList={packagingList}
            productList={productList}
            initialValue={dispatchOrderEntry}
            setInitialValue={setDipatchOrderEntry}
            customerorderbookId={customerorderbookId}
            poGenrationRequired={poGenrationRequired}
          />
        </Form>
      </div>
    </>
  );
};

export default SupplierDispatchOrderForm;

SupplierDispatchOrderForm.propTypes = {
  mode: PropTypes.string.isRequired,
  dispatchOrder: PropTypes.object,
  orderBook: PropTypes.object,
  hide: PropTypes.func.isRequired,
  customerorderbookId: PropTypes.string.isRequired,
  poGenrationRequired: PropTypes.bool,
};
