import React, { useEffect } from 'react'
import HeaderPanel from '../headerPanel'
import { useNavigate, useParams } from 'react-router';
import { useSelector } from 'react-redux';
import DocumentViewer from '../DocumentViewer/DocumentViewer';
import { convertToEnumKey } from '../../util/stringUtils';
import { Button } from 'antd';
import RouteFactory from '../../service/RouteFactory';

const DispatchDocumentReview = () => {

  const { dispatchId, type } = useParams();
  const reviewDocuments = useSelector(state => state.reviewDocuments);
  const navigate = useNavigate();

  useEffect(() => {
    if (!reviewDocuments || !reviewDocuments?.[convertToEnumKey(type)]) {
      navigate(-1);
    }
  }, [reviewDocuments])
  return (
    <>
      <HeaderPanel
        name="Dispatch Order Document Review"
        sideButtons={(
          <Button
            type="primary"
            size="large"
            onClick={() => {
              navigate(new RouteFactory().dashboard().dispatchOrder().setId(dispatchId).view().build());
            }}
          >
            Cancel
          </Button>
        )}
      />
      {reviewDocuments && reviewDocuments?.[convertToEnumKey(type)] ? (
        <DocumentViewer
          orderId={dispatchId}
          type={type}
          documentList={reviewDocuments[convertToEnumKey(type)]}
        />
      ) : null}
    </>
  )
}

export default DispatchDocumentReview