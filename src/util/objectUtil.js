export default class ObjectUtil {
  static isDefined(value) {
    return value !== null && value !== undefined && value !== 'undefined';
  }

  static isObject(value) {
    return this.isDefined(value) && typeof value === 'object' && !Array.isArray(value);
  }

  static isEmptyObject(value) {
    if (this.isObject(value)) {
      return Object.keys(value).length === 0;
    }
    return true;
  }
  static mergeWithoutNull( object1, object2) {
    const keys = [
      ...Object.keys(object1),
      ...Object.keys(object2),
    ];
    let mergedObj = {};
    keys.forEach((key) => {
      mergedObj = {
        ...mergedObj,
        [key]: object2?.[key] || object1?.[key],
      }
    });
    return mergedObj;
  }
}
