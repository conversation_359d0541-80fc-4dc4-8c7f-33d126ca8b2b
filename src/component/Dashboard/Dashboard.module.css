.dashboardContainer {
  padding: 20px 30px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #01279A;
}
.dashboardContainer > .title {
  font-size: 24px;
  font-weight: 600;
}
.dashboardContainer > .subTitle {
  margin-top: 5px;
  font-size: 16px;
  font-weight: 500;
}
.toggleSwitch {
  width: 60px;
  height: 32px;
  border-radius: 16px;
  background: rgba(35, 86, 138, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  transition: background 0.2s, border-color 0.2s;
  margin: 0 10px;
  border: 1px solid rgba(35, 86, 138, 0.3);
}
.toggleCircle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 4px;
  transition: left 0.2s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}
.toggleLeft .toggleCircle {
  left: 4px;
}
.toggleRight .toggleCircle {
  left: 30px;
}
.toggleLabel {
  border: 1px solid rgba(35, 86, 138, 0.3);
  border-radius: 10px;
  padding: 8px 14px;
  font-size: 16px;
  background: #fff;
  color: rgba(35, 86, 138, 0.6);
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.toggleLabelActive {
  border: 1px solid rgba(35, 86, 138, 0.9);
  border-radius: 10px;
  padding: 8px 14px;
  font-size: 16px;
  background: rgba(35, 86, 138, 0.1);
  color: rgba(35, 86, 138, 0.9);
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.toggleRowRight {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-end;
  width: 100%;
}