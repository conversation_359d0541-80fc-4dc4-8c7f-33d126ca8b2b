import React from 'react';
import { Form, Input, Modal } from 'antd';
import PropTypes from 'prop-types';
import { approveEnquiryQuotation, rejectEnquiryQuotation } from '../../../service/api/enquiryApi';
import { HttpStatusCode } from 'axios';

const ApproveOrRejectQuotationModal = props => {
  const { isRejectModal, quotationData, enquiryId, displayModal, onCancel, setLoading } = props;
  const [form] = Form.useForm();

  const validateQuotePrice = (value, materialCost, logisticCost) => {
    return value >= (materialCost + logisticCost) * 1.05
      ? Promise.resolve()
      : Promise.reject(
          `Quote price must be greater than or equal to ${Math.round((materialCost + logisticCost) * 1.05)}`
        );
  };

  const handleOk = async () => {
    try {
      // setLoading(true);
      const values = await form.validateFields();
      let response = isRejectModal
        ? await rejectEnquiryQuotation(enquiryId, values['rejectionMessage'])
        : await approveEnquiryQuotation(enquiryId, values['quotePrice']);

      if (response.status == HttpStatusCode.Ok) window.location.reload();
      // setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error occurred while submitting form ', error);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  const getQuotationForm = () => {
    if (isRejectModal) {
      return (
        <Form form={form} name="rejectQuotationForm">
          <Form.Item
            label="Rejection reason"
            name="rejectionMessage"
            rules={[{ required: true, message: 'Please provide reason to reject quotation ' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      );
    } else {
      return (
        <Form form={form} name="approveQuotationForm">
          <Form.Item label="Material cost">{quotationData.materialCost}</Form.Item>
          <Form.Item label="Logistic cost">{quotationData.logisticCost}</Form.Item>
          <Form.Item label="Minimum quotable price">{Math.round((quotationData.materialCost + quotationData.logisticCost) * 1.05)}</Form.Item>
          <Form.Item
            label="Quote price"
            name="quotePrice"
            rules={[
              { required: true, message: 'Please provide quote price' },
              {
                pattern: /^(?:\d+|\d*\.\d+)$/,
                message: 'Please enter a valid number with or without decimal',
              },
              {
                validator: (_, value) =>
                  validateQuotePrice(value, quotationData.materialCost, quotationData.logisticCost),
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <Modal
      open={displayModal}
      onOk={handleOk}
      onCancel={handleCancel}
      title={`Do you want to ${isRejectModal ? 'reject' : 'approve'} this quotation ?`}
    >
      {getQuotationForm()}
    </Modal>
  );
};

export default ApproveOrRejectQuotationModal;
ApproveOrRejectQuotationModal.propTypes = {
  displayModal: PropTypes.any,
  isRejectModal: PropTypes.bool.isRequired,
  quotationData: PropTypes.object.isRequired,
  enquiryId: PropTypes.string.isRequired,
  onCancel: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
};
