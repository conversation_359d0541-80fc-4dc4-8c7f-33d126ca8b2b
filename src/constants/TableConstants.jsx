import React from 'react';
import { Tag } from 'antd';
import { getDateFromTimeStamp } from '../util/dateUtils';
import { getEnumValueFromList, getTotalOrderValue } from '../util/formUtil';
import {
  enquiryPriorityList,
  enquiryStatusList,
  invoiceTypes,
  orderStatusList,
  productUOMList,
} from './formConstant';
// import FileUpload from '../component/FileUpload';

export const customerHeader = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Country', dataIndex: 'country', key: 'country' },
  { title: 'Type', dataIndex: 'type', key: 'type' },
  {
    title: 'Categories',
    dataIndex: 'categories',
    key: 'categories',
    render: (_, { categories }) =>
      categories && categories.length ? (
        <>
          {categories.map(category => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={category}
            >
              {category.toUpperCase()}
            </Tag>
          ))}
        </>
      ) : (
        '-'
      ),
  },
  { title: 'Size', dataIndex: 'size', key: 'size' },
  { title: 'Account Owner', dataIndex: 'accountOwner', key: 'accountOwner' },
];

export const orderBookHeader = [
  { title: 'PO Number', dataIndex: 'purchaseOrderNumber', key: 'purchaseOrderNumber' },
  { 
    title: 'Customer Name',
    dataIndex: 'customerName',
    key: 'customerName',
    render: (_ ,record) => record.inventoryId ? record?.inventoryName : record?.customerName
  },
  { title: 'Sourcing POC', dataIndex: 'sourcingPOC', key: 'sourcingPOC' },
  { title: 'Product', dataIndex: 'productName', key: 'productName',
    render: (_, {products} ) => products?.length ? (
      <>
        {products.map((product, index) => (
          <Tag
            style={{
              overflowWrap: 'break-word',
              maxWidth: '100%',
              display: 'block',
              whiteSpace: 'normal',
            }}
            key={index}
          >
            {product}
          </Tag>
        ))}
      </>
    ) : (
      '-'
    ),
  },
  {
    title: 'PO Date',
    dataIndex: 'purchaseOrderDate',
    key: 'purchaseOrderDate',
    render: (_, { purchaseOrderDate }) =>
      purchaseOrderDate ? getDateFromTimeStamp(purchaseOrderDate) : '-',
  },
];

export const supplierOrderBookHeader = [
  { title: 'PO Number', dataIndex: 'purchaseOrderNumber', key: 'purchaseOrderNumber' },
  { title: 'Supplier Name', dataIndex: 'supplierName', key: 'supplierName' },
  { title: 'Product', dataIndex: 'productName', key: 'productName' },
  { title: 'Total Order Value', dataIndex: 'totalOrderValue', key: 'totalOrderValue' },
  {
    title: 'Created',
    dataIndex: 'purchaseOrderDate',
    key: 'purchaseOrderDate',
    render: (_, { purchaseOrderDate }) =>
      purchaseOrderDate ? getDateFromTimeStamp(purchaseOrderDate) : '-',
  },
];

export const supplierOrderBookHeaderV2 = [
  {
    title: 'Date',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
  {
    title: 'SO#',
    dataIndex: 'SO#',
    key: 'SO#',
    render: (_, record) => (
      <div>
        <div>{record.supplier.name}</div>
        <div>{record.purchaseOrderNumber}</div>
      </div>
    ),
  },
  {
    title: 'Product(S)',
    dataIndex: 'productNames',
    key: 'productNames',
    width: 200,
    render: (_, { products }) => products && products.length ? (
      <>
        {products.map((productItem, index) => (
          <Tag
            style={{
              overflowWrap: 'break-word',
              maxWidth: '100%',
              display: 'block',
              whiteSpace: 'normal',
            }}
            key={index}
          >
            {productItem.product.tradeName}
          </Tag>
        ))}
      </>
    ) : (
      '-'
    ),
  },
  { title: 'Total Order Value($)', dataIndex: 'totalOrderValue', key: 'totalOrderValue',
    render:(_, record) => getTotalOrderValue(record)
  },
];

export const supplierDispatchColumns = [
  {
    title: 'Source',
    dataIndex: 'shippingAddress',
    key: 'shippingAddress',
    render: (text) => text || '-',
  },
  {
    title: 'Destination',
    dataIndex: 'destinationPartnerAddress',
    key: 'destinationPartnerAddress',
    render: (text) => text || '-',
  },
  {
    title: 'Dispatch Date',
    dataIndex: 'dispatchToDestinationDate',
    key: 'dispatchToDestinationDate',
    render: (text) => {
      if (text === 'N/A') return 'N/A';
      return text ? getDateFromTimeStamp(text) : '-';
    },
  },
  {
    title: 'Delivery Date',
    dataIndex: 'deliveryAtDestinationPartnerDate',
    key: 'deliveryAtDestinationPartnerDate',
    render: (text) => {
      if (text === 'N/A') return 'N/A';
      return text ? getDateFromTimeStamp(text) : '-';
    },
  },
];

export const dispatchOrderHeader = [
  // TODO: add OrderID and verify created date
  { title: 'Order Number', dataIndex: 'orderId', key: 'orderId' },
  { title: 'PO Number', dataIndex: 'purchaseOrderNumber', key: 'purchaseOrderNumber' },
  { title: 'Customer Name', dataIndex: 'customerName', key: 'customerName' },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: (_, { status }) => orderStatusList?.[status]?.label,
  },
  { title: 'Account Owner', dataIndex: 'accountOwner', key: 'accountOwner' },
  {
    title: 'Product(S)',
    dataIndex: 'productNames',
    key: 'productNames',
    render: (_, { productNames }) =>
      productNames && productNames.length ? (
        <>
          {productNames.map((product, index) => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={index}
            >
              {product.toUpperCase()}
            </Tag>
          ))}
        </>
      ) : (
        '-'
      ),
  },
  { title: 'Total Order Value', dataIndex: 'totalOrderValue', key: 'totalOrderValue' },
  {
    title: 'Created',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
];

export const dispatchOrderHeaderV2 = [
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: (_, { status }) => 
      <Tag
        style={{ 
          color: 'rgba(17, 94, 163, 1)',
          borderColor: 'rgba(17, 94, 163, 1)',
          borderRadius: '4px',
          backgroundColor: 'rgba(235, 243, 252, 1)'
        }}
      >
        {orderStatusList?.[status]?.label}
      </Tag>,
  },
  { title: 'CDO#', dataIndex: 'orderId', key: 'orderId' },
  {
    title: 'Created',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },  
  {
    title: 'Product(S)',
    dataIndex: 'productNames',
    key: 'productNames',
    render: (_, { products }) => products && products.length ? (
      <>
        {products.map((productItem, index) => (
          <Tag
            style={{
              overflowWrap: 'break-word',
              maxWidth: '100%',
              display: 'block',
              whiteSpace: 'normal',
            }}
            key={index}
          >
            {productItem.product.tradeName}
          </Tag>
        ))}
      </>
    ) : (
      '-'
    ),
  },
  { title: 'Total Order Value($)', dataIndex: 'totalOrderValue', key: 'totalOrderValue',
    render:(_, record) => getTotalOrderValue(record)
  },
];

export const invoiceHeader = [
  // TODO: add OrderID and verify created date
  { title: 'Invoice Number', dataIndex: 'invoiceNumber', key: 'invoiceNumber' },
  { title: 'Invoice Amount', dataIndex: 'invoiceAmount', key: 'invoiceAmount' },
  { title: 'Invoice Type', dataIndex: 'invoiceType', key: 'invoiceType',render: (_, { invoiceType }) =>{
    return invoiceTypes.find(invoice=>invoice.value==invoiceType).label
  } },
  {
    title: 'Created on',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
];

export const uploadInvoiceHeaders = [
  // TODO: add OrderID and verify created date
  { title: 'PO Number', dataIndex: 'secondaryId', key: 'secondaryId' },
  { title: 'Customer Name', dataIndex: 'customerName', key: 'customerName' },
  { title: 'Status', dataIndex: 'status', key: 'status' },

];

export const supplierDispatchOrderHeader = [
  // TODO: add OrderID and verify created date
  { title: 'Order Number', dataIndex: 'orderId', key: 'orderId' },
  { title: 'PO Number', dataIndex: 'purchaseOrderNumber', key: 'purchaseOrderNumber' },
  { title: 'Supplier Name', dataIndex: 'supplierName', key: 'supplierName' },
  {
    title: 'Product(S)',
    dataIndex: 'productNames',
    key: 'productNames',
    render: (_, { productNames }) =>
      productNames && productNames.length ? (
        <>
          {productNames.map(product => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={product}
            >
              {product.toUpperCase()}
            </Tag>
          ))}
        </>
      ) : (
        '-'
      ),
  },
  { title: 'Total Order Value', dataIndex: 'totalOrderValue', key: 'totalOrderValue' },
  {
    title: 'Created',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
];

export const supplierHeader = [
  { title: 'SupplierId', dataIndex: 'supplierId', key: 'supplierId' },
  { title: 'Supplier Name', dataIndex: 'name', key: 'name' },
  { title: 'Country', dataIndex: ['address', 'country'], key: 'country' },
  { title: 'Mobile', dataIndex: 'mobile', key: 'mobile' },
  {
    title: 'Products',
    dataIndex: 'products',
    key: 'products',
    render: (_, { products }) =>
      products && products.length ? (
        <>
          {products.map((product, index) => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={index}
            >
              {product.product?.tradeName}
            </Tag>
          ))}
        </>
      ) : null,
  },
];

export const ProductHeaders = [
  { title: 'Name', dataIndex: 'tradeName', key: 'name' },
  { title: 'Technical Name', dataIndex: 'technicalName', key: 'technicalName' },
  {
    title: 'Synonyms',
    dataIndex: 'synonyms',
    key: 'synonyms',
    render: (_, { synonyms }) =>
      synonyms && synonyms.length ? (
        <>
          {synonyms.map(synonym => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={synonym}
            >
              {synonym.toUpperCase()}
            </Tag>
          ))}
        </>
      ) : (
        '-'
      ),
  },
  { title: 'CAS Number', dataIndex: 'casNumber', key: 'casNumber' },
  {
    title: 'Categories',
    dataIndex: 'categories',
    key: 'categories',
    render: (_, { categories }) =>
      categories ? (
        <>
          {categories.map(category => (
            <Tag
              style={{
                overflowWrap: 'break-word',
                maxWidth: '100%',
                display: 'block',
                whiteSpace: 'normal',
              }}
              key={category}
            >
              {category.category.toUpperCase()}
            </Tag>
          ))}
        </>
      ) : (
        '-'
      ),
  },
];

export const productColumnHeaders = [
  {
    title: 'Product Name',
    dataIndex: 'product',
    key: 'product',
    fixed: 'left',
    width: 200,
    render: (_, { product }) => (product && product.tradeName ? product.tradeName : product),
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 150,
    render: (_, { status }) => (
      <div style={{ color: status ? orderStatusList[status].color : '#000', fontWeight: '600' }}>
        {status ? orderStatusList[status].label : '-'}
      </div>
    ),
  },
  {
    title: 'Unit of measurement(UOM)',
    dataIndex: 'uom',
    key: 'uom',
    width: 150,
    render: (_, { uom }) => getEnumValueFromList(uom, productUOMList),
  },
  {
    title: 'Price per unit',
    dataIndex: 'price',
    key: 'price',
    width: 130,
  },
  {
    title: 'Chemstack Price per unit',
    dataIndex: 'chemstackPrice',
    key: 'chemstackPrice',
    width: 130,
  },
  {
    title: 'Per Unit Kg value',
    dataIndex: 'perUnitKgValue',
    key: 'perUnitKgValue',
    width: 130,
  },
  {
    title: 'Batch Date Visibility',
    dataIndex: 'batchDateVisible',
    key: 'batchDateVisible',
    render: (_, { batchDateVisible }) => (batchDateVisible? "Yes" : "No"),
    width: 130,
  },
  {
    title: 'Quantity',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 120,
  },
  {
    title: 'Quantity per packaging type',
    dataIndex: 'quantityPerUnit',
    key: 'quantityPerUnit',
    width: 120,
  },
  {
    title: 'HS code',
    dataIndex: 'hsCode',
    key: 'hsCode',
    width: 100,
  },
  // {
  //   dataIndex:'districtOfOrigin',
  //   key: 'districtOfOrigin',
  //   title: 'District of origin',
  //   width: 100,
  // },  
  // {
  //   dataIndex:'stateOfOrigin',
  //   key: 'stateOfOrigin ',
  //   title: 'State of origin',
  //   width: 100,
  // },  
  // {
  //   dataIndex:'productNameAlias',
  //   key: 'productNameAlias ',
  //   title: 'Product Name Alias',
  //   width: 100,
  // },  
  // {
  //   title: 'Haz details (if applicable)',
  //   dataIndex: 'hazDetails',
  //   key: 'hazDetails',
  //   width: 100,
  // },
  {
    title: 'Packaging details',
    dataIndex: 'packaging',
    key: 'packaging',
    width: 300,
    render: (_, { packaging }) => (
      <>
        <div key={'packaging_type'}>
          <span style={{ fontWeight: '500' }}>Type:</span>
          <span>{packaging.type}</span>
        </div>
        <div key={'packaging_packsize'}>
          <span style={{ fontWeight: '500' }}>Pack Size:</span>
          <span>{packaging.packSize}</span>
        </div>
        <div key={'packaging_tareweight'}>
          <span style={{ fontWeight: '500' }}>Tare Weight:</span>
          <span>{packaging.tareWeight}</span>
        </div>
        <div key={'packaging_dimension'}>
          <span style={{ fontWeight: '500' }}>Dimension:</span>
          <span>{packaging.dimension}</span>
        </div>
      </>
    ),
  },
  {
    title: 'Total number of packaging units',
    dataIndex: 'units',
    key: 'units',
    width: 120,
  },
  // {
  //   title: 'Label Description',
  //   dataIndex: 'label',
  //   key: 'label',
  //   width: 200,
  // },
  // {
  //   title: 'Label File',
  //   dataIndex: 'labelFile',
  //   key: 'labelFile',
  //   width: 100,
  //   render: (_, { labelFile }) => (labelFile ? <FileUpload value={labelFile} disabled /> : '-'),
  // },
  // {
  //   title: 'Remarks',
  //   dataIndex: 'remarks',
  //   key: 'remarks',
  //   width: 100,
  //   render: (_, { remarks }) =>
  //     remarks ? remarks.map((data, index) => <div key={index}>{data.value || data}</div>) : '-',
  // },
];

export const enquiryHeaders = [
  { title: 'Id', dataIndex: 'enquiryId', key: 'enquiryId', width: 120, fixed: 'left' },
  {
    title: 'Status',
    dataIndex: 'enquiryStatus',
    key: 'enquiryStatus',
    width: 120,
    render: (_, { enquiryStatus }) => enquiryStatusList?.[enquiryStatus]?.label,
  },
  {
    title: 'Created',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 120,
    render: (_, { createdAt }) => getDateFromTimeStamp(createdAt),
  },
  { title: 'Product', dataIndex: 'productName', key: 'productName', width: 150 },
  { title: 'CAS', dataIndex: 'casNumber', key: 'casNumber', width: 120 },
  {
    title: 'Priority',
    dataIndex: 'enquiryPriority',
    key: 'enquiryPriority',
    width: 100,
    render: (_, { enquiryPriority }) => (
      <Tag color={enquiryPriorityList?.[enquiryPriority]?.tagColor}>
        {enquiryPriorityList?.[enquiryPriority]?.label}
      </Tag>
    ),
  },
];

export const inventoryProductHeader = [
  {
    title: 'Product',
    dataIndex: ['product', 'tradeName'],
    key: 'productName',
  },
  {
    title: 'Packaging',
    dataIndex: ['packaging', 'packagingName'],
    key: 'packagingName',
  },
  {
    title: 'Total Units',
    dataIndex: 'units',
    key: 'units',
  },
];

export const inventoryOutOrderHeader = [
  {
    title: 'Date',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
  {
    title: 'ILO#',
    dataIndex: 'orderId',
    key: 'orderId',
  },
  {
    title: 'Product(S)',
    dataIndex: 'productNames',
    key: 'productNames',
    render: (_, { products }) => products && products.length ? (
      <>
        {products.map((productItem, index) => (
          <Tag
            style={{
              overflowWrap: 'break-word',
              maxWidth: '100%',
              display: 'block',
              whiteSpace: 'normal',
            }}
            key={index}
          >
            {productItem?.product?.tradeName || productItem?.name}
          </Tag>
        ))}
      </>
    ) : (
      '-'
    ),
  },
];

export const allocatedInventoryHeader = [
  {
    title: 'Date',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (_, { createdAt }) => (createdAt ? getDateFromTimeStamp(createdAt) : '-'),
  },
  {
    title: 'Product',
    dataIndex: 'product',
    key: 'product',
    render: (_, { proudctDetail, packagingDetail }) => (
      <div>
        <div>{proudctDetail?.productName || '-'}</div>
        <div>{packagingDetail?.packagingName || '-'}</div>
      </div>
    ),
  },
  {
    title: 'Supplier Name(s)',
    dataIndex: 'supplier',
    key: 'supplier',
    render: (_, { allocatedUnitDetails }) => (
      <div>
        {allocatedUnitDetails?.map(item => (
          <div key={item?.supplierUID}>{item?.supplierName}</div>
        ))}
      </div>
    ),
  },
  {
    title: 'Supplier Order#',
    dataIndex: 'order',
    key: 'order',
    render: (_, { allocatedUnitDetails }) => (
      <div>
        {allocatedUnitDetails?.map(item => (
          <div key={item?.orderBookUID}>{item?.orderBookId}</div>
        ))}
      </div>
    ),
  },
  {
    title: 'Units Allocated',
    dataIndex: 'allocated',
    key: 'allocated',
    render: (_, { allocatedUnitDetails }) => (
      <div>
        {allocatedUnitDetails?.map(item => (
          <div key={item?.orderBookUID}>{item?.unitsAllocated}</div>
        ))}
      </div>
    ),
  },
]

export const employeeTableHeader = [
  {
    title: 'Employee No.',
    dataIndex: 'employeeId',
    key: 'employeeId',
    fixed: 'left',
    width: 150,
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Role',
    dataIndex: 'permissionGroups',
    key: 'permissionGroups',
    render: (_, {permissionGroups}) => permissionGroups?.toString()
  },
  {
    title: 'E-mail',
    dataIndex: 'email',
    key: 'email',
  }
]

export const userRoleTableHeader = [
  {
    title: 'Role Name',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    width: 200,
    align:'center',
  },
  {
    title: 'User Count',
    dataIndex: ["meta", "userCount"],
    key: 'count',
    align:'center',
    width: 100,
  },
  {
    title: 'Description',
    dataIndex: 'description',
    key: 'description',
    align:'center',
  },
];

export const packagingTableHeader = [
  {
    title: 'Packaging Type',
    dataIndex: 'type',
    key: 'type',
    fixed: 'left',
    width: 250,
    align:'center',
  },
  {
    title: 'Pack Size',
    dataIndex: 'pSize',
    key: 'pSize',
    align:'center',
    render:(_, { psize, puom }) => `${psize} ${getEnumValueFromList(puom, productUOMList)}`,
  },
  {
    title: 'Tare Weight',
    dataIndex: 'tWeight',
    key: 'tWeight',
    align:'center',
    render:(_, { tweight, tweightUom }) => `${tweight} ${getEnumValueFromList(tweightUom, productUOMList)}`,
  },
  {
    title: 'Dimension',
    dataIndex: 'dimension',
    key: 'dimension',
    align:'center',
  },
];

export const tablePageSize = 10;
export const orderBookTablePageSize = 10;
export const dispatchOrderTablePageSize = 10;
export const productTablePageSize = 10;
export const supplierTablePageSize = 10;
export const supplierOrderTablePageSize = 10;
export const supplierDispatchOrderTablePageSize = 10;
export const customerTablePageSize = 10;
export const invoiceTablePageSize = 10;

