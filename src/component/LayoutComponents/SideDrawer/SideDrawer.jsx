import { Layout } from 'antd';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { setSideDrawerStatus } from '../../../store/actions/collapseSideDrawer';
import { removeSideDrawerData } from '../../../store/actions/sideDrawerData';
import CustomerSideBar from '../../CustomerPanel/CustomerSideBar';
import DispatchOrderSideBar from '../../DispatchOrderPanel/DispatchOrderSideBar';
import OrderBookSideBar from '../../OrderBookPanel/OrderBookSideBar';
import ProductSideBar from '../../Products/ProductSidebar';
import SupplierDispatchOrderSideBar from '../../SupplierDispatchOrderPanel/SupplierDispatchOrderSideBar';
import SupplierOrderBookSideBar from '../../SupplierOrderBookPanel/SupplierOrderBookSideBar';
import SupplierSideBar from '../../SupplierPanel/SupplierSidebar';
import css from './SideDrawer.module.css';
import EnquirySideBar from '../../EnquiryPanel/EnquirySideBar';
import InvoiceSideBar from '../../InvoicePanel/InvoiceSideBar';
import InventorySideBar from '../../InventoryPanel/InventorySideBar';

const SideDrawer = () => {
  const collapseSideDrawer = useSelector(state => state.collapseSideDrawer);
  const sideDrawerData = useSelector(state => state.sideDrawerData);
  const dispatch = useDispatch();
  const { pathname } = useLocation();

  const hideSideDrawer = () => {
    dispatch(setSideDrawerStatus(true));
    // dispatch(removeSideDrawerData(null));
  };
  const getSideBarComponentFromLocation = () => {
    if (pathname.includes('customer'))
      return <CustomerSideBar customer={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('supplier-order-book'))
      return (
        <SupplierOrderBookSideBar
          order={sideDrawerData.sourceRecord}
          productId={sideDrawerData.sourceItemId}
          hide={hideSideDrawer}
        />
      );
    if (pathname.includes('order-book'))
      return (
        <OrderBookSideBar
          order={sideDrawerData.sourceRecord}
          hide={hideSideDrawer}
        />
      );
    if (pathname.includes('supplier-dispatch-order'))
      return <SupplierDispatchOrderSideBar order={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('dispatch-order'))
      return <DispatchOrderSideBar order={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('product'))
      return <ProductSideBar product={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('supplier'))
      return <SupplierSideBar supplier={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('enquiry'))
      return <EnquirySideBar enquiry={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('invoice'))
      return <InvoiceSideBar invoice={sideDrawerData} hide={hideSideDrawer} />;
    if (pathname.includes('inventory'))
      return <InventorySideBar inventoryData={sideDrawerData.sourceRecord} hide={hideSideDrawer} />
  };

  return sideDrawerData ? (
    <Layout.Sider
      className={css.sideDrawerContainer}
      width={sideDrawerData.isNewDisplay ? "70%" : "25%"}
      trigger={null}
      collapsible
      collapsed={collapseSideDrawer}
      collapsedWidth={0}
    >
      {sideDrawerData ? getSideBarComponentFromLocation() : null}
    </Layout.Sider>
  ) : null;
};

export default SideDrawer;
