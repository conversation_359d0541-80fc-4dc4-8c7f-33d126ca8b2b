import React, { useEffect, useState } from 'react';
import { Button, Space, Tabs, Dropdown, Form, message } from 'antd';
import { PlusOutlined, DownOutlined, EditOutlined } from '@ant-design/icons';
// import DispatchOrderBasicInfo from './DispatchOrderBasicInfo';
// import { formModes } from '../../constants/formConstant';
// import DispatchOrderProductInfo from './DispatchOrderProductInfo';
// import DispatchOrderFinalInfo from './DispatchOrderFinalInfo';
import TaskTable from '../Task/TaskTable';
import DocumentPanel from '../DocumentPanel/DocumentPanel';
import DispatchOrderReadView from './DispatchOrderReadView';
import HeaderPanel from '../headerPanel';
import { getCriticalPathTasks } from '../../service/api/activityApi';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import CriticalPathFormModal from '../Task/CriticalPathForm';
import { getDispatchOrderFromId, updateDispatchOrder as updateDispatchOrderAPI } from '../../service/api/dispatchOrderApi';
import PageLoader from '../Loaders/PageLoader';
import { formModes, orderStatusList } from '../../constants/formConstant';
import { userEntity, userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { getOrderDataFromId } from '../../service/api/orderBookApi';
import DDPCriticalPathFormModal from '../Task/DDPCriticalPathForm';
import DispatchBatchModal from './DispatchBatchModal';
import { getCustomerData } from '../../service/api/customerApi';
// import RouteFactory from '../../service/RouteFactory';
import DispatchOrderInputFields from './DispatchOrderInputFields';

const DispatchOrderReview = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { dispatchId, orderId } = useParams();
  const user = useSelector(state => state.user);
  const [loading, setLoading] = useState(false);
  const [dipatchOrderEntry, setDipatchOrderEntry] = useState({});
  const [orderbookData, setOrderBookData] = useState({});
  const [taskData, setTaskData] = useState([]);
  const [criticalPathModalVisible, setCriticalPahModalVisible] = useState(false);
  const [ddpCriticalPathModalVisible, setDdpCriticalPahModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);

  const [updateMode, setUpdateMode] = useState(false);
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });
  const [customerData, setCustomerData] = useState({});

  const showTask = !!taskData.length;
  const [form] = Form.useForm();

  const handleInputFieldsChange = (changedValues, allValues) => {
    console.log('Changed values:', changedValues);
    console.log('All values:', allValues);
    // Handle form changes here
  };

  const handleSubmit = values => {
    setLoading(true);
    const formattedValues = {
      ...dipatchOrderEntry,
      ...values,
    };

    updateDispatchOrderAPI(formattedValues, dipatchOrderEntry.id)
      .then(response => {
        console.log('Updated dispatch order:', response);
        if (response && response.data) {
          message.success('Dispatch order updated successfully');
          setDipatchOrderEntry(response.data);
        } else {
          throw new Error('Invalid response from server');
        }
      })
      .catch(error => {
        console.error('Error updating dispatch order:', error);
        message.error(error?.response?.data?.message || 'Failed to update dispatch order');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const panelItems = [
    {
      key: '1',
      label: 'View Details',
      children: (
        <DispatchOrderReadView
          formValues={dipatchOrderEntry}
          productList={dipatchOrderEntry.productList || dipatchOrderEntry.products}
          formView={formModes.READ}
        />
      ),
    },
    {
      key: '3',
      label: 'Documents',
      children: (
        <DocumentPanel
          order={dipatchOrderEntry}
          customerData={customerData}
          updateDispatchOrder={() => updateDispatchOrder()}
        />
      ),
    },
    {
      key: '4',
      label: 'Input Fields',
      children: (
        <DispatchOrderInputFields
          form={form}
          initialValues={dipatchOrderEntry}
          onValuesChange={handleInputFieldsChange}
          onSubmit={handleSubmit}
          loading={loading}
        />
      ),
    },
  ];
  if (showTask)
    panelItems.push({
      key: '2',
      label: 'Task Table',
      children: (
        <TaskTable tasks={taskData} entityId={dipatchOrderEntry?.id} setTaskData={setTaskData} />
      ),
    });

  const handleEditDispatchOrder = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.dispatchOrder,
        mode: formModes.UPDATE,
        orderBook: orderbookData,
        formData: dipatchOrderEntry,
      },
    });
  };

  const addMenuItems = [
    {
      label: 'Add Critical Path',
      key: 1,
      disabled: !(
        taskData.length === 0 &&
        dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value &&
        hasPermission(userPermissionsList.generateCriticalPath, user.permissions)
      ),
      onClick: () => {
        setUpdateMode(false);
        setCriticalPahModalVisible(true);
      },
    },
    {
      label: 'Add Ddp Critical Path',
      key: 2,
      disabled: !(
        taskData.length === 0 &&
        dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value &&
        hasPermission(userPermissionsList.generateCriticalPath, user.permissions)
      ),
      onClick: () => {
        setUpdateMode(false);
        setDdpCriticalPahModalVisible(true);
      },
    },
  ];

  const getCriticalPathUpdateButton = () => {
    const pathType = dipatchOrderEntry?.meta?.criticalPathType;
    if (pathType == 'ddpCriticalPath')
      return taskData.length !== 0 &&
        dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value &&
        hasPermission(userPermissionsList.updateCriticalPath, user.permissions) ? (
        <Button
          type="primary"
          onClick={() => {
            setUpdateMode(true);
            setDdpCriticalPahModalVisible(true);
          }}
          className="rounded-[4px]"
        >
          Update DDP Critical Path
        </Button>
      ) : null;
    else
      return taskData.length !== 0 &&
        dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value &&
        hasPermission(userPermissionsList.updateCriticalPath, user.permissions) ? (
        <Button
          type="primary"
          onClick={() => {
            setUpdateMode(true);
            setCriticalPahModalVisible(true);
          }}
          className="rounded-[4px]"
        >
          Update Critical Path
        </Button>
      ) : null;
  };
  const handleBatchDetails = () => {
    setBatchModalVisible(true);
  };

  const updateDispatchOrder = () => {
    if (dispatchId) {
      setLoading(true);
      getDispatchOrderFromId(dispatchId)
        .then(res => {
          setDipatchOrderEntry(res.data);
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  };

  useEffect(() => {
    dispatch(setSideDrawerStatus(true));
    if (dispatchId) {
      setLoading(true);
      Promise.all([
        getCriticalPathTasks(dispatchId),
        getDispatchOrderFromId(dispatchId),
        getOrderDataFromId(orderId),
      ])
        .then(res => {
          if (res[0]?.data) {
            // console.log(res.data)
            setTaskData(res[0].data);
          }
          if (res[1]?.data) {
            setDipatchOrderEntry(res[1].data);
          }
          if (res?.[2].data) {
            setOrderBookData(res[2].data);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [dispatchId]);

  useEffect(() => {
    setLoading(true);
    if (orderbookData?.customer?.id) {
      getCustomerData(orderbookData?.customer?.id)
        .then(res => {
          if (res) setCustomerData(res.data);
          else console.log('didnt foudn customer data ');
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  }, [orderbookData]);

  const handleSubmitCriticalPath = criticalPath => {
    setTaskData(criticalPath);
    if (dispatchId) {
      setLoading(true);
      getDispatchOrderFromId(dispatchId)
        .then(res => {
          if (res.data) {
            setDipatchOrderEntry(res.data);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  };

  return (
    <>
      <HeaderPanel
        name="Dispatch Order"
        sideButtons={
          <Space>
            {hasPermission(userPermissionsList.updateCustomerOrder, user.permissions) ? (
              <Button
                type="default"
                icon={<EditOutlined />}
                onClick={() => handleEditDispatchOrder()}
                className="rounded-[4px]"
              >
                Edit
              </Button>
            ) : null}
            {/* {taskData && taskData.length !== 0 ? (
              ''
            ) : (
              <Dropdown
                menu={{
                  items: addMenuItems,
                }}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined style={{ color: '#fff' }} />}
                  className="rounded-[4px]"
                >
                  <Space>
                    Add
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            )} */}
            {/* {getCriticalPathUpdateButton()} */}
            {dipatchOrderEntry?.meta?.hasILO ? (
              <Button
                type="default"
                icon={<PlusOutlined />}
                onClick={() => handleBatchDetails()}
                className="rounded-[4px]"
              >
                Batch Details
              </Button>
            ) : null}
            <Button
              type="primary"
              onClick={() => {
                // navigate(new RouteFactory().dashboard().orderBook().setId(orderId).view().build());
                navigate(-1);
              }}
              className="rounded-[4px]"
            >
              Cancel
            </Button>
          </Space>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <div style={{ margin: '2% 2% 5%' }}>
          <div className="mb-8">
            <div className="mb-2.5 text-xl font-semibold">
              {' '}
              {`Customer Name : ${dipatchOrderEntry?.customer?.name}`}{' '}
            </div>
            <div style={{ fontWeight: '500', color: 'rgb(97, 97, 97)' }}>
              {' '}
              {`Customer Dispatch Order: ${dipatchOrderEntry.orderId}`}{' '}
            </div>
          </div>
          <Tabs
            items={panelItems}
            defaultActiveKey={showTask ? '2' : '1'}
            type="card"
            destroyInactiveTabPane={true}
          />
        </div>
      )}
      {dipatchOrderEntry?.id ? (
        <>
          <CriticalPathFormModal
            products={dipatchOrderEntry?.products || []}
            visible={criticalPathModalVisible}
            onCancel={() => setCriticalPahModalVisible(false)}
            entityId={dipatchOrderEntry?.id}
            onSumbit={handleSubmitCriticalPath}
            updateMode={updateMode}
          />
          <DDPCriticalPathFormModal
            products={dipatchOrderEntry?.products || []}
            visible={ddpCriticalPathModalVisible}
            onCancel={() => setDdpCriticalPahModalVisible(false)}
            entityId={dipatchOrderEntry?.id}
            onSumbit={handleSubmitCriticalPath}
            updateMode={updateMode}
          />
        </>
      ) : null}
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={dispatchData => {
          setDipatchOrderEntry(dispatchData);
        }}
      />
      {dipatchOrderEntry?.id && batchModalVisible ? (
        <DispatchBatchModal
          visible={batchModalVisible}
          onCancel={() => setBatchModalVisible(false)}
          onSumbit={() => {}}
          orderData={dipatchOrderEntry}
        />
      ) : null}
    </>
  );
};

export default DispatchOrderReview;
