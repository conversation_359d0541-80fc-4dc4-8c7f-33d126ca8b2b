import { Button, Col, Form, InputNumber, Modal, Row, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { getEnumValueFromList } from '../../util/formUtil';
import { productUOMList } from '../../constants/formConstant';
import { getSupplierList } from '../../service/api/supplierService';
import PageLoader from '../Loaders/PageLoader';
import IncotermInput from '../IncotermInput/IncotermInput';
import DocumentSelection from './DocumentSelection';
import { createQuotationForEnquiry } from '../../service/api/enquiryApi';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';

const EnquiryQuotationModal = props => {
  const { open, onAdd, onCancel, enquiry } = props;

  const [loading, setLoading] = useState(false);
  const [supplierList, setSupplierList] = useState(false);
  const [productList, setProductList] = useState([]);
  const [supplierProductData, setSupplierProductData] = useState(null);

  const [form] = Form.useForm();
  const supplierIdWatch = Form.useWatch('supplierId', form);
  const productIdWatch = Form.useWatch('productId', form);

  const onAddHandler = () => {
    form.validateFields().then(values => {
      setLoading(true);
      const packaging = supplierProductData.packaging.find(
        pkg => `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}` === values.packaging
      );
      if (!packaging) {
        setLoading(false);
        return;
      }
      createQuotationForEnquiry(enquiry.id, { ...values, packaging })
        .then(res => {
          if (onAdd) onAdd(res.data);
          setLoading(false);
          form.resetFields();
          onCancel();
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    });
  };
  const cancelHandler = () => {
    form.resetFields();
    onCancel();
  };

  useEffect(() => {
    if (!supplierList || !supplierList.length) {
      setLoading(true);
      getSupplierList()
        .then(res => {
          if (res.data && res.data.content) {
            setSupplierList(res.data.content);
          }
          setLoading(false);
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);
  useEffect(() => {
    if (supplierIdWatch) {
      const supplier = supplierList.find(item => item.id === supplierIdWatch);
      if (supplier && supplier?.products?.length) {
        setProductList(supplier.products);
      }
    }
  }, [supplierIdWatch]);

  useEffect(() => {
    if (productIdWatch) {
      const product = productList.find(item => item.product.id === productIdWatch);
      if (product) {
        setSupplierProductData(product);
      }
    }
  }, [productIdWatch]);

  return (
    <Modal
      className="relative"
      open={open}
      title="Add Quotation"
      okText="Apply"
      cancelText="Reset"
      width="80%"
      onCancel={cancelHandler}
      footer={[
        <Button key="reset" onClick={cancelHandler}>
          Cancel
        </Button>,
        <Button
          key="apply"
          type="primary"
          loading={loading}
          onClick={() => {
            onAddHandler();
          }}
        >
          Add
        </Button>,
      ]}
    >
      {loading ? (
        <PageLoader style={{ position: 'absolute', width: '100%', height: '80vh', opacity: '1' }} />
      ) : (
        <>
          <div className="p-4 flex justify-between rounded-[10px] border border-solid border-dark-gray-200 bg-dark-gray-100">
            <div className="flex flex-col gap-1">
              <div className="text-text-black text-sm">Product</div>
              <div className="text-text-black text-lg">{enquiry?.productName}</div>
            </div>
            <div className="flex flex-col gap-1">
              <div className="text-text-black text-sm">CAS</div>
              <div className="text-text-black text-lg">{enquiry?.casNumber || '-'}</div>
            </div>
            <div className="flex flex-col gap-1">
              <div className="text-text-black text-sm">Quantity</div>
              <div className="text-text-black text-lg">
                {`${enquiry?.quantity} ${
                  getEnumValueFromList(enquiry?.unitOfMeasure, productUOMList) || ''
                }`}
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <div className="text-text-black text-sm">UOM</div>
              <div className="text-text-black text-lg">{enquiry?.unitOfMeasure}</div>
            </div>
          </div>
          <Form
            name="quotation_form"
            layout="vertical"
            scrollToFirstError={true}
            preserve={false}
            size="large"
            form={form}
            validateTrigger="onBlur"
            className="mt-8 h-[500px] overflow-y-auto"
          >
            <Row gutter={16} className="w-full">
              <Col span={12}>
                {supplierList.length ? (
                  <Form.Item
                    label="Supplier Name"
                    name="supplierId"
                    rules={[
                      { required: true, message: 'Please select Supplier' },
                      // () => ({
                      //   validator(_, value) {
                      //     const supplier = supplierList.find(
                      //       item => item.id === value
                      //     );
                      //     if (supplier && supplier?.products?.length) {
                      //       const product = supplier.products.find(
                      //         item => item?.product?.tradeName === enquiry?.productName
                      //       );
                      //       if (!product) {
                      //         return Promise.reject(
                      //           new Error(`Above supplier does not serve this product!`)
                      //         );
                      //       } else {
                      //         setSupplierProductData(product);
                      //         return Promise.resolve();
                      //       }
                      //     }
                      //     return Promise.reject(new Error('Unable to fetch supplier or its products!'));
                      //   },
                      // }),
                    ]}
                  >
                    <Select showSearch optionFilterProp="label">
                      {supplierList.map(supplier => (
                        <Select.Option key={supplier.id} value={supplier.id} label={supplier.name}>
                          {supplier.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
              <Col span={12}>
                {productList.length ? (
                  <Form.Item
                    label="Product Name"
                    name="productId"
                    rules={[{ required: true, message: 'Please select a product' }]}
                  >
                    <Select showSearch optionFilterProp="label">
                      {productList.map(product => (
                        <Select.Option
                          key={product.product.id}
                          value={product.product.id}
                          label={product.product.tradeName}
                        >
                          {product.product.tradeName}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
            </Row>
            <Row gutter={16} className="w-full">
              <Col span={23}>
              <Form.Item
                name='uom'
                label="Unit of measurement(UOM)"
                rules={[{ required: true, message: 'Please select a Uom' }]}
              >
                <Select>
                  {productUOMList.map(uom => (
                    <Select.Option key={uom.key} value={uom.value}>
                      {uom.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className="w-full">
              <Col span={12}>
                <Form.Item
                  name="materialCost"
                  label="Material cost ($/unit)"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter material cost!',
                    },
                  ]}
                >
                  <InputNumber addonBefore="$" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="logisticCost"
                  label="Logistic Cost ($/unit)"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter logistic cost!',
                    },
                  ]}
                >
                  <InputNumber addonBefore="$" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className="w-full">
              <Col span={12}>
                <Form.Item
                  name="leadTime"
                  label="Lead Time"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter lead time!',
                    },
                  ]}
                >
                  <InputNumber addonAfter="days" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="transitTime"
                  label="Transit Time"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter transit time!',
                    },
                  ]}
                >
                  <InputNumber addonAfter="days" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className="w-full">
              <Col span={23}>
                <IncotermInput form={form} />
              </Col>
            </Row>
            {supplierProductData ? (
              <>
                <Row gutter={16}>
                  <Col span={24}>
                    <PackagingSelect
                      packagingList={supplierProductData.packaging.map(pkg => ({
                        ...pkg,
                        id: `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
                      }))}
                      disableCustomPackaging
                    />
                  </Col>
                </Row>
                <Row gutter={16} className="w-full">
                  <Col span={24}>
                    <Form.Item
                      label="Documents"
                      name="documents"
                      rules={[
                        {
                          required: true,
                          message: 'Please choose document!',
                        },
                      ]}
                    >
                      <DocumentSelection productData={supplierProductData} />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            ) : null}
          </Form>
        </>
      )}
    </Modal>
  );
};

export default EnquiryQuotationModal;

EnquiryQuotationModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onAdd: PropTypes.func,
  onCancel: PropTypes.func.isRequired,
  enquiry: PropTypes.object.isRequired,
};
