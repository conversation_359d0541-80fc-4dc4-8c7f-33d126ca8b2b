import React from 'react';
import { Form, Input, Modal } from 'antd';
import PropTypes from 'prop-types';
import { negotiateEnquiryQuotation } from '../../../service/api/enquiryApi';
import { HttpStatusCode } from 'axios';

const NegotiateEnquiryModal = props => {
  const {
    enquiryId,
    displayModal,
    onCancel,
    // setLoading
  } = props;
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      //   setLoading(true);
      const values = await form.validateFields();
      console.log('values ', values['negotiationRemark'], enquiryId);
      let response = await negotiateEnquiryQuotation(enquiryId, values['negotiationRemark']);
      if (response.status == HttpStatusCode.Ok) window.location.reload();
      //   setLoading(false);
    } catch (error) {
      //   setLoading(false);
      console.log('error occurred while submitting form ', error);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  const getEnquiryForm = () => {
    return (
      <Form form={form} name="negotiateEnquiryForm">
        <Form.Item
          label="Negotiate reason"
          name="negotiationRemark"
          rules={[{ required: true, message: 'Please provide reason to negotiate enquiry ' }]}
        >
          <Input />
        </Form.Item>
      </Form>
    );
  };

  return (
    <Modal
      open={displayModal}
      onOk={handleOk}
      onCancel={handleCancel}
      title={`Do you want to negotiate this enquiry ?`}
    >
      {getEnquiryForm()}
    </Modal>
  );
};

export default NegotiateEnquiryModal;
NegotiateEnquiryModal.propTypes = {
  displayModal: PropTypes.any,
  enquiryId: PropTypes.string,
  onCancel: PropTypes.func,
  setLoading: PropTypes.func,
};
