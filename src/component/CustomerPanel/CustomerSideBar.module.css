.customerSider {
  padding: 0 12px 16px;
}

.customerSider > .titleBar {
  padding: 16px 0 ;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
  background-color: #fff;
}

.actionBtnBox {
  margin-bottom: 18px;
  padding-bottom: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  position: sticky;
  top: 27px;
  z-index: 99;
  background-color: #fff;
}
.actionBtnBox a {
  width: inherit;
}
.actionBtnBox button {
  white-space: break-spaces;
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customerSider .label {
  margin-bottom: 8px;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%;
  color: rgba(35, 86, 138, 0.7);
}
.customerSider .value {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%;
}