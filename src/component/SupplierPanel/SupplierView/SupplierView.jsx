import { Button, Collapse } from 'antd';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import SupplierBasicDescriptionView from './SupplierBasicDetailsView';
import SupplierProductListView from './SupplierProductListView';
import HeaderPanel from '../../headerPanel';
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';
import RouteFactory from '../../../service/RouteFactory';
import { getSupplierData } from '../../../service/api/supplierService';
import PageLoader from '../../Loaders/PageLoader';

const SupplierView = props => {
  const { initialValue, formView } = props;

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { supplierId } = useParams()

  const [loading, setLoading] = useState(false);
  const [supplierData, setSupplierData] = useState(initialValue || {});

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };

  useEffect(() => {
    if (supplierId && !initialValue && !formView) {
      setLoading(true)
      getSupplierData(supplierId).then((res) => {
        if (res?.data?.id) {
          setSupplierData((res.data));
          setLoading(false);
        }
      }).catch(error => {
      console.log(error);
      setLoading(false);
    });
    }
  }, [supplierId])

  return (
    <>
      {!formView && !initialValue ? (
        <HeaderPanel
          name="Supplier"
          sideButtons={
            <Button
              type="primary"
              size="large"
              onClick={() => {
                collapseAsideBarHandler(false);
                navigate(new RouteFactory().dashboard().supplier().build())
              }}
            >
              Cancel
            </Button>
          }
        />
      ) : null}
      {loading
        ? <PageLoader/>
        : <Collapse            
            defaultActiveKey={['1', '2']}
            style={!formView && !initialValue ? { margin: '5%' } : null}
            items={[
              {
                key: '1',
                label: 'Supplier Basic Details ',
                children: <SupplierBasicDescriptionView supplierData={supplierData} />,
              },
              {
                key: '2',
                label: 'Supplier Products ',
                children: <SupplierProductListView productList={supplierData?.products || []} />,
              },
            ]}
          />
      }
    </>
  );
};

export default SupplierView;
SupplierView.propTypes = {
  initialValue: PropTypes.object,
  formView: PropTypes.string,
};
