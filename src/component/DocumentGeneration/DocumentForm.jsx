import { <PERSON><PERSON>, <PERSON> } from 'antd';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { enumKeyToTitle } from '../../util/formatUtil';
// import { getInputFiledsFromList } from '../../util/formUtil';

const DocumentForm = props => {
  const { docLabel, formData, hidePanel, generateDocument } = props;

  const [loading, setLoading] = useState(false);

  const getFormItemValue = item => {
    if (item.inputType === 'text') {
      return <div className="p-2.5 rounded text-sm bg-base-white">{item.value}</div>;
    } else if (item.inputType === 'list') {
      return item?.value?.map((valueItem, index) => (
        <Card size="small" title={`${item.label} ${index + 1}`} key={index}>
          {Object.keys(valueItem).map(key => (
            <div key={key}>
              <div className="text-gray-600 text-xs font-medium">{enumKeyToTitle(key)}</div>
              <div className="p-2.5 rounded text-sm bg-base-white">{valueItem[key]}</div>
            </div>
          ))}
        </Card>
      ));
    } else return <div>-</div>;
  };

  return (
    <div className="w-[30%] relative">
      {/* <Form
        layout="vertical"
        form={form}
        onValuesChange={(changedValue, allValues) => onFormDataChange(changedValue, allValues)}
        className='p-5 pb-14 h-full overflow-auto bg-gray-50 border-r-solid border-r--theme-border'
        disabled
        size='small'
        requiredMark={false}
      >
        {formData && formData.length ? getInputFiledsFromList(formData, true, null, null, true) : null}
      </Form> */}
      <div className="p-5 pb-14 h-full overflow-auto bg-gray-50 border-r-solid border-r--theme-border">
        <div className="font-bold mb-3">{docLabel}</div>
        <div className="flex flex-col gap-2.5">
          {Array.isArray(formData) &&
            formData.length > 0 &&
            formData?.map(item => (
              <div key={item.key} className="flex flex-col gap-1">
                <div className="text-gray-600 text-xs font-medium">{item.label}</div>
                {getFormItemValue(item)}
              </div>
            ))}
        </div>
      </div>
      <div className="absolute bottom-0 p-2.5 w-full flex items-center justify-between bg-gray-50 shadow-top-shadow-box">
        <Button
          className="bg-base-white text-black border-solid border-black rounded-md"
          type="default"
          onClick={() => hidePanel()}
        >
          Cancel
        </Button>
        <Button
          loading={loading}
          className="rounded-md"
          type="primary"
          onClick={() => generateDocument(setLoading)}
          disabled={!formData?.length}
        >
          Generate
        </Button>
      </div>
    </div>
  );
};

export default DocumentForm;

DocumentForm.propTypes = {
  docLabel: PropTypes.string.isRequired,
  formData: PropTypes.array.isRequired,
  hidePanel: PropTypes.func.isRequired,
  generateDocument: PropTypes.func.isRequired,
};
