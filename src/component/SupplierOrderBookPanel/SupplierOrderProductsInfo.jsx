import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Form, Row } from 'antd';
import PropTypes from 'prop-types';
import { getProductList } from '../../service/api/productApi';
import { useDispatch, useSelector } from 'react-redux';
import { addPackaging } from '../../store/actions/packagingList';
import PageLoader from '../Loaders/PageLoader';
import SupplierOrderBookProduct from './SupplierOrderBookProduct';

const SupplierOrderProductsInfo = props => {
  const { form, initialValue, mode, setInitialValue, COBProducts } = props;

  const [productList, setProductList] = useState(COBProducts || []);
  const [loading, setLoading] = useState(false);

  const packagingList = useSelector(state => state.packagingList);
  const dispatch = useDispatch();

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getFormPrefilldValues = () =>
    initialValue && initialValue?.products && packagingList
      ? {
          products: initialValue.products.map(prod => ({
            ...prod,
            product: prod.product,
            uom: prod.uom,
            price: prod.price,
            quantity: prod.quantity,
            units: prod?.units,
            margin: prod.margin,
            incoterms: prod.incoterms
              ? {
                  ...prod.incoterms,
                  data: {
                    ...prod.incoterms.data,
                  },
                }
              : {},
            deliveryDate: prod.deliveryDate,
            remarks: prod.remarks ? [...prod.remarks] : [],
            hsCode: prod.hsCode,
            productDescription:prod.productDescription,
            packaging:getPackagingFormValue(prod.packaging,packagingList),
            numberOfPacking: prod.numberOfPacking,
            documents: Array.isArray(prod.documents) // ✅ Check if already converted
            ? prod.documents
            : Object.entries(prod.documents || {}).flatMap(([documentType, files]) =>
                (Array.isArray(files) ? files : []).map(file => ({
                  documentType,
                  files: [file], // Convert back to array format
                }))
              ),
          })),
        }
      : {};

  useEffect(() => {
    if (!productList || !productList.length) {
      setLoading(true);
      getProductList()
        .then(res => {
          if (res.data && res.data.content) {
            setProductList(res.data.content);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      initialValue &&
      initialValue.products &&
      initialValue.products.length &&
      packagingList &&
      packagingList.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [productList]);


  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      name="order_product_info"
      layout="vertical"
      scrollToFirstError
      size="middle"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.List
            name="products"
            rules={[
              {
                required: true,
                message: 'At least 1 product details is required!',
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                }}
              >
                {fields.map((field, index) => {
                  return (
                    <SupplierOrderBookProduct
                      key={index}
                      form={form}
                      field={field}
                      index={index}
                      mode={mode}
                      remove={remove}
                      productList={productList}
                      packagingList={packagingList}
                      setInitialValue={data => {
                        setInitialValue(prevState => {
                          return {
                            ...prevState,
                            productList: [...(prevState.productList || []), data],
                          };
                        });
                      }}
                    />
                  );
                })}
                <Button type="dashed" onClick={() => add()} block>
                  + Add Product
                </Button>
                <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
    </Form>
  );
};

export default SupplierOrderProductsInfo;

SupplierOrderProductsInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  mode: PropTypes.string.isRequired,
  setInitialValue: PropTypes.func.isRequired,
  COBProducts: PropTypes.array,
};
