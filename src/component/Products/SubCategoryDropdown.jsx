import { Form, Select } from 'antd';
import PropTypes from 'prop-types';
import React from 'react';

export default function SubCategoryDropdown(props) {
  const { field, form, categoryList } = props;

  const category = Form.useWatch(['categories', field.name, 'category'], form);

  return (
    <Form.Item key={field.key} name={[field.name, 'subCategory']} label="Sub Category">
      <Select showSearch disabled={!category}>
        {categoryList
          .find(e => e.name === category)
          ?.subCategory?.map((subCat, index) => (
            <Select.Option key={index} value={subCat.name}>
              {subCat.name}
            </Select.Option>
          ))}
      </Select>
    </Form.Item>
  );
}

SubCategoryDropdown.propTypes = {
  field: PropTypes.any.isRequired,
  form: PropTypes.any.isRequired,
  categoryList: PropTypes.array.isRequired,
};
