import React, { useEffect, useState } from 'react';
import HeaderPanel from '../headerPanel';
import { Button, Descriptions, Tabs } from 'antd';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import PageLoader from '../Loaders/PageLoader';
import FileUpload from '../FileUpload';
import {
  paymentTermsDate,
  productIncoTermsList,
  productUOMList,
  incotermsDataList,
  formModes,
} from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getSupplierOrderBookDataFromId } from '../../service/api/supplierOrderBookApi';
// import RouteFactory from '../../service/RouteFactory';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { userEntity, userPermissionsList } from '../../constants/UserTabsConstants';
import SupplierDispatchOrderTabs from '../SupplierDispatchOrderPanel/SupplierDispatchOrderTabs';
import { hasPermission } from '../../util/userUtils';
import { getOrderDataFromId } from '../../service/api/orderBookApi';
// import dayjs from 'dayjs';

const SupplierOrderReview = () => {
  const dispatch = useDispatch();
  const { orderId, supplierOrderId} = useParams();
  const location = useLocation();

  // Parse the query parameters
  const searchParams = new URLSearchParams(location.search);
  const poGenrationRequired = searchParams.get('poGenrationRequired') 
  const navigate = useNavigate();
  const user = useSelector(state => state.user);

  const [loading, setLoading] = useState(false);
  const [orderBookEntry, setOrderBookEntry] = useState({});
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });

  function getProductFields(productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Price per unit',
        children: productData?.price,
      },
      {
        key: '4',
        label: 'Delivery date',
        children: getDateFromTimeStamp(orderBookEntry?.purchaseOrderDate),
      },
      {
        key: '5',
        label: 'HS code',
        children: productData?.hsCode,
      },
      {
        key: '6',
        label: 'Label',
        children: productData?.label,
      },
      {
        key: '7',
        label: 'Total number of packaging units',
        children: productData?.units,
      },
      {
        key: '8',
        label: 'Product Description',
        children: productData?.productDescription,
      },
      {
        key: '9',
        label: 'Remarks',
        span: 3,
        // children: (
        //   <>
        //     {productData?.remarks?.length
        //       ? productData.remarks.map((remark, index) => <div key={index}>{remark?.value}</div>)
        //       : null}
        //   </>
        // ),
      },
    ];
  }
  function getProductPackagingFields(productData) {
    return [
      {
        key: '1',
        label: 'Type',
        children: productData?.packaging?.type,
      },
      {
        key: '2',
        label: 'Pack Size',
        children: productData?.packaging?.packSize,
      },
      {
        key: '3',
        label: 'Tare Weight',
        children: productData?.packaging?.tareWeight,
      },
      {
        key: '4',
        label: 'Dimension',
        children: productData?.packaging?.dimension,
      },
    ];
  }
  function getProductIncotermFields(productData) {
    return [
      {
        key: '1',
        label: 'Type',
        children: getEnumValueFromList(productData?.incoterms?.type, productIncoTermsList),
      },
      {
        key: '2',
        label: 'Destination Country:',
        children: productData?.incoterms?.country,
      },
      ...(productData?.incoterms?.data && Object.keys(productData.incoterms.data).length > 0
        ? Object.keys(productData.incoterms.data).map((key, index) => ({
            key: `${index + 3}`,
            label: camelCaseToTitle(key),
            children: getLabelForShipmentMethodOrDefault(productData.incoterms?.data?.[key]),
          }))
        : []),
    ];
  }
  const getLabelForShipmentMethodOrDefault = key => {
    const label = getEnumValueFromList(key, incotermsDataList.FOB[0].child);
    return label ? label : key;
  };
  const handleAddSupplierDispatchOrder = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.supplierDispatchOrder,
        mode: formModes.CREATE,
        orderBook: orderBookEntry,
        customerorderbookId: orderId,
      },
    });
  };


  const handleEditSupplierOrderbook = async () => {
   
    const response = await getOrderDataFromId(orderId);
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.supplierOrderBook,
        mode: formModes.UPDATE,
        formData: orderBookEntry,
        orderBookProducts: response?.data?.products,
        orderBookId: orderId,
        orderType: response?.data?.orderType,
        poGenrationRequired 
      },
    });
  };

  const orderFieldItems = [
    {
      key: '1',
      label: 'Supplier Name',
      children: orderBookEntry?.supplier?.name,
    },
    {
      key: '2',
      label: 'PO number',
      children: orderBookEntry?.purchaseOrderNumber,
    },
    {
      key: '3',
      label: 'PO date',
      children: getDateFromTimeStamp(orderBookEntry?.purchaseOrderDate),
    },
    {
      key: '4',
      label: 'PO file',
      children: <FileUpload value={orderBookEntry?.purchaseOrderFile} disabled />,
    },
    {
      key: '5',
      label: 'Currency Type',
      children: orderBookEntry?.currencyType,
    },
    {
      key: '6',
      label: 'Material Spec file',
      children: <FileUpload value={orderBookEntry?.materialSpecFile} disabled />,
    },
    ...(orderBookEntry?.mrd ? [
      {
        key: '7',
        label: 'MRD',
        children: getDateFromTimeStamp(orderBookEntry?.mrd),
      }
    ] : []),
  ];
  const shipmentFieldItems = [
    {
      key: '1',
      label: 'Billing Address',
      children: orderBookEntry?.billingAddress,
    },
    {
      key: '2',
      label: 'Supplier Invoice Currency',
      children: orderBookEntry?.currencyType,
    },
    {
      key: '3',
      label: 'Shipping Address',
      children: orderBookEntry?.shippingAddress,
    },
    {
      key: '4',
      label: 'Gst percent',
      children: orderBookEntry?.gstPercent,
    },
    {
      key: '5',
      label: 'Partial Shipment',
      children: orderBookEntry?.partialShipment ? 'ALLOWED' : 'NOT ALLOWED',
    },
    {
      key: '6',
      label: 'Trans Shipment',
      children: orderBookEntry?.transShipment ? 'ALLOWED' : 'NOT ALLOWED',
    },
    {
      key: '7',
      label: 'Default T&C Disabled ',
      children: orderBookEntry?.excludeDefaultTc ? 'Yes' : 'No',
    },
    {
      key: '8',
      label: 'Delivery Location ',
      children: orderBookEntry?.deliveryLocation,
    },
    {
      key: '9',
      label: 'Pickup Location ',
      children: orderBookEntry?.pickupLocation,
    },
  ];
  const paymentFieldItems = [
    {
      key: '1',
      label: 'Credit amount(%)',
      children: orderBookEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '2',
      label: 'Credit days',
      children: orderBookEntry?.paymentTerms?.creditorDays,
    },
    {
      key: '4',
      label: 'Advance(%)',
      children: orderBookEntry?.paymentTerms?.advanceAmount,
    },
    {
      key: '3',
      label: 'Payment Term Based on',
      children: getEnumValueFromList(orderBookEntry?.paymentTerms?.poPaymentTerms, paymentTermsDate),
    }
    // {
    //   key: '4',
    //   label: 'Remarks',
    //   children: (
    //     <>
    //       {orderBookEntry?.remarks?.length
    //         ? orderBookEntry.remarks.map((remark, index) => <div key={index}>{remark?.value}</div>)
    //         : null}
    //     </>
    //   ),
    // },
  ];
  const tabPanels = [
    {
      key: '1',
      label: 'View Details',
      children: (
        <div className="flex flex-col gap-5">
          <Descriptions
            title="Supplier Order"
            items={orderFieldItems}
            column={2}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
            style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
          />
          <Descriptions
            title="Shipment Details"
            items={shipmentFieldItems}
            column={2}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
            style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
          />
          <Descriptions
            title="Payment Details"
            items={paymentFieldItems}
            column={2}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
            style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
          />
          <div style={{ marginTop: '40px', fontSize: '18px', fontWeight: '600' }}>
            Product Detail
          </div>
          <Tabs
            items={
              orderBookEntry?.products?.length
                ? orderBookEntry.products.map((product, index) => ({
                    key: index,
                    label: product?.product?.tradeName,
                    children: (
                      <div className="flex flex-col gap-3">
                        <Descriptions
                          title="Product Details"
                          items={getProductFields(product)}
                          column={3}
                          labelStyle={{ fontWeight: '700', color: '#23568A' }}
                          style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
                        />
                        <Descriptions
                          title="Incoterm Details"
                          items={getProductIncotermFields(product)}
                          column={3}
                          labelStyle={{ fontWeight: '700', color: '#23568A' }}
                          style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
                        />
                        <Descriptions
                          title="Packaging Details"
                          items={getProductPackagingFields(product)}
                          column={3}
                          labelStyle={{ fontWeight: '700', color: '#23568A' }}
                          style={{ borderBottom: '1px solid rgba(35, 86, 138, 0.2)' }}
                        />
                      </div>
                    ),
                  }))
                : []
            }
            defaultActiveKey={'1'}
            type="line"
            destroyInactiveTabPane={true}
          />
        </div>
      ),
    },
  ];
  if (hasPermission(userPermissionsList.viewSupplierOrder, user.permissions)) {
    tabPanels.push({
      key: '2',
      label: 'Supplier DOs',
      children: (
        <SupplierDispatchOrderTabs orderbookData={orderBookEntry} customerorderbookId={orderId} />
      ),
    });
  }

  useEffect(() => {
    dispatch(setSideDrawerStatus(true));
    if (supplierOrderId) {
      setLoading(true);
      Promise.all([
        getSupplierOrderBookDataFromId(supplierOrderId),
        getOrderDataFromId(orderId)
      ])
        .then(([supplierOrderRes, orderRes]) => {
          if (supplierOrderRes?.data?.id) {
            setOrderBookEntry(supplierOrderRes.data);
          }
          if (orderRes?.data?.orderType) {
            setOrderBookEntry(prev => ({
              ...prev,
              orderType: orderRes.data.orderType,
              COBProducts: orderRes.data?.products?.map((item) => item.product)
            }));
          }
          setLoading(false);
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [supplierOrderId, orderId]);

  return (
    <>
      <HeaderPanel
        name="Supplier order book"
        sideButtons={
          <div className="flex items-center gap-4">
            {hasPermission(userPermissionsList.updatesupplierOrderBook, user.permissions) ? (
              <Button
                type="default"
                icon={<EditOutlined />}
                onClick={() => handleEditSupplierOrderbook()}
                className="rounded-[4px]"
              >
                Edit Supplier Order
              </Button>
            ) : null}
            {hasPermission(userPermissionsList.createSupplierOrder, user.permissions) ? (
              <Button
                type="primary"
                icon={<PlusOutlined style={{ color: '#fff' }} />}
                onClick={() => handleAddSupplierDispatchOrder()}
                className="rounded-[4px]"
              >
                Add Supplier DO
              </Button>
            ) : null}
            <Button
              type="primary"
              onClick={() => {
                // navigate(new RouteFactory().dashboard().orderBook().setId(orderId).view().build());
                navigate(-1);
              }}
              className="rounded-[4px]"
            >
              Cancel
            </Button>
          </div>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <div style={{ margin: '2%' }}>
          <Tabs
            items={tabPanels}
            defaultActiveKey={'1'}
            type="card"
            destroyInactiveTabPane={true}
          />
        </div>
      )}
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={data => {
          if (overlayData.drawerData.entity === userEntity.supplierOrderBook) {
            setOrderBookEntry(data);
          }
        }}
      />
    </>
  );
};

export default SupplierOrderReview;
