.functionalBar {
  padding: 32px 32px 24px;
  display: flex;
  width: 100%;
}

.seacrhBar {
  width: -webkit-fill-available;
  /* margin-right: 12px; */
}

.searchInput {
  background: rgba(30, 30, 30, 0.05);
  border-radius: 12px;
}

.searchInput span,
.searchInput span input {
  background: transparent;
}

.btnFlexStyle {
  margin-left: 12px;
  display: flex;
  align-items: center;
  padding: 12px;
}

.tableContainer {
  margin: 0 32px 32px;
}

.tableContainer table {
  border-radius: 12px;
  border: 1px solid rgba(35, 86, 138, 0.20);
}

:root .tableContainer table>thead>tr>th {
  background: rgba(35, 86, 138, 0.05);
}