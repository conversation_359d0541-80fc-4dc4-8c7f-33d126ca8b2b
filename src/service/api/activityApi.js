import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

export const getCriticalPathTasks = id => {
  return axiosInstance.get(`${apiEndpoints.criticalPathTasks}/activities/${id}`);
};

export const createTask = task => {
  return axiosInstance.post(apiEndpoints.task, task);
};

export const generateCriticalPath = (entityType,entityId,criticalPathType, payload) => {
  return axiosInstance.post(`${apiEndpoints.criticalPathTasks}/submitCriticalPathForm/${entityType}/${entityId}/${criticalPathType}`, payload);
};

export const updateCriticalPath = (entityType,entityId,criticalPathType, payload) => {
  return axiosInstance.put(`${apiEndpoints.criticalPathTasks}/submitCriticalPathForm/${entityType}/${entityId}/${criticalPathType}`, payload);
};

export const updateTask = task => {
  return axiosInstance.put(`${apiEndpoints.task}/${task.id}`, task);
};

export const deleteTask = id => {
  return axiosInstance.delete(apiEndpoints.task+"/"+id);
};