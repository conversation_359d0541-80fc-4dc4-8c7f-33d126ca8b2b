import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  Modal,
  InputNumber,
  Row,
  Col,
  Card,
  Alert,
  message,
} from 'antd';
import { formModes } from '../../constants/formConstant';
import PropTypes from 'prop-types';
import css from './ProductTable.module.css';
import dayjs from 'dayjs';
import ObjectUtil from '../../util/objectUtil';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { validateSupplierProductOrderQuantity } from '../../service/api/supplierDispatchOrderApi';
import { getDispatchOrderForOrderbook } from '../../service/api/dispatchOrderApi';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';
import { DeleteOutlined } from '@ant-design/icons';

const ProductRow = ({
  form,
  formView,
  packagingList,
  productList,
  index,
  name,
  initialValue,
  productUOMList,
  setInitialValue,
  customerorderbookId,
  poGenrationRequired,
  onDeleteRow
}) => {
  const [focusedField, setFocusedField] = useState(null);
  const [selectedField, setSelectedField] = useState(null); // Store index & modal type
  const [customerDispatchOrders, setCustomerDispatchOrders] = useState([]);
  const { api } = useNotificationContext();
  const [validationErrors, setValidationErrors] = useState([]);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);

  const packagingId = Form.useWatch(['products', name, 'packaging'], form);
  const packagingObj = packagingList.find(pkg => pkg.id === packagingId);
  const isOtherPackaging = packagingObj && packagingObj.type === 'Others';

  const isRowFilled = row => {
    return Object.values(row).some(value => {
      if (value == null) return false; // null or undefined
      if (typeof value === 'string' && value.trim() === '') return false; // empty or whitespace string
      if (Array.isArray(value) && value.length === 0) return false; // empty array
      if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
        return false; // empty object
      return true; // if none of the above, it's filled
    });
  };

  const validateProductQuantity = async (poNumber, productId, productQuantity) => {
    const response = await validateSupplierProductOrderQuantity(
      poNumber,
      productId,
      productQuantity,
      initialValue?.id,
      initialValue?.customer?.id
    );
    const isValid = response.data.isValid;
    const availableQuantity = response.data.availableQuantity;
    if (availableQuantity == 0) return Promise.reject('No more quantity left for usage');
    return isValid
      ? Promise.resolve()
      : Promise.reject(`Quantity cannot exceed more then ${availableQuantity}`);
  };

  const formIdWatch = Form.useWatch(['products', name, 'product'], form);
  const quantityWatch = Form.useWatch(['products', name, 'quantity'], form);
  const linkedOrdersWatch = Form.useWatch(['products', name, 'linkedOrders'], form);


  useEffect(() => {
    if (
      formView !== formModes.READ &&
      formIdWatch &&
      (!initialValue?.products?.length ||
        !initialValue?.products[index] ||
        initialValue?.products[index]?.id !== formIdWatch)
    ) {
      let newInitialValue = JSON.parse(JSON.stringify(initialValue));
      if (!initialValue?.products?.length) initialValue.products = [];
      let newValue = {};
      const [productInfo] = productList.filter(prod => prod.id === formIdWatch);
      if (productInfo && productInfo.uom) {
        newValue = {
          ...productInfo,
          uom: productInfo.uom,
          quantity: productInfo.quantity,
          units: productInfo.units,
          product: productInfo.id,
          packaging: productInfo.packaging,
          price: productInfo.price,
          hsCode: productInfo.hsCode,
          linkedOrderIds: productInfo.linkedOrderIds,
          label: productInfo.label,
          otherPackagingDetails: (productInfo.packaging && productInfo.packaging.otherPackagingDetails) || productInfo.otherPackagingDetails || '',
        };
        if (!newInitialValue?.products?.length) newInitialValue.products = [];
        newInitialValue.products[index] = newValue;
        newInitialValue = {
          ...newInitialValue,
          incoterms: productInfo.incoterms,
          purchaseOrderDate: newInitialValue.purchaseOrderDate
            ? dayjs(newInitialValue.purchaseOrderDate)
            : '',
          shipmentDate: productInfo.shipmentDate ? dayjs(productInfo.shipmentDate) : '',
          deliveryDate: productInfo.deliveryDate ? dayjs(productInfo.deliveryDate) : '',
          products: [
            ...newInitialValue.products.map((item, idx) => {
              const newProductFormField = form.getFieldValue(['products', idx]);
              return ObjectUtil.mergeWithoutNull(item, newProductFormField);
            }),
          ],
        };

        form.setFieldsValue({
          products: {
            [index]: {
              otherPackagingDetails: (productInfo.packaging && productInfo.packaging.otherPackagingDetails) || productInfo.otherPackagingDetails || '',
            },
          },
        });
      }
      setInitialValue(newInitialValue);
    }
  }, [formIdWatch]);

  const openModal = (index, type) => {
    setSelectedField({ index, type }); // Set both index and modal type
  };

  const closeModal = () => {
    setSelectedField(null); // Close modal
  };

  const getProductRelatedCustomerDispatchOrders = (customerDispatchOrdersList, productId) => {
    const productIdToCheck = productList.find(product => product.id === productId)?.product?.id;
    return customerDispatchOrdersList.filter(order => {
      const matchingProduct = order.products.find(
        product => product.product.id === productIdToCheck
      );
      if (matchingProduct) {
        order.productQty = matchingProduct.quantity;
        return true;
      }
      return false;
    });
  };

  const calculateTotalQuantity = linkedOrders => {
    return linkedOrders.reduce((totalQuantity, linkedOrder) => {
      if (linkedOrder && linkedOrder.quantity != null) {
        return totalQuantity + linkedOrder.quantity;
      }
      return totalQuantity;
    }, 0);
  };

  const hasDuplicateLinkedOrders = array => {
    const orderIdSet = new Set();
    let hasDuplicates = false;

    array.forEach(obj => {
      const orderId = obj && obj.orderId;

      if (orderId !== undefined) {
        if (orderIdSet.has(orderId)) {
          // Duplicate found
          hasDuplicates = true;
        } else {
          orderIdSet.add(orderId);
        }
      }
    });

    return hasDuplicates;
  };

  useEffect(() => {
    // setLoading(true);
    getDispatchOrderForOrderbook(customerorderbookId)
      .then(response => {
        // TODO: proper check for dispatchOrderList
        if (!response?.data?.length) {
          api.error({
            messgae: 'Error',
            description:
              'Can not create supplier dispatch order as no customer dispatch order exist for linking!!. Please add customer dispatch order first.',
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            ),
          });
        }
        setCustomerDispatchOrders(response.data);
        setInitialValue(prevState => {
          return {
            ...prevState,
            cdoList: response.data,
          };
        });
        // setLoading(false);
      })
      .catch(error => {
        // setLoading(false);
        const errMsg = error?.response?.data?.message;
        api.error({
          messgae: 'Error',
          description: errMsg,
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          ),
        });
      });
  }, []);

  useEffect(() => {
    if (!Array.isArray(linkedOrdersWatch) || linkedOrdersWatch.length === 0) return;

    let errors = [];

    // Check for duplicates
    const hasOrderIds = linkedOrdersWatch.some(item => item && item.orderId);
    if (hasOrderIds && hasDuplicateLinkedOrders(linkedOrdersWatch)) {
      errors.push('linked dispatch orders cannot have duplicate order id');
    }

    // Check quantities
    const hasQuantities = linkedOrdersWatch.some(item => item && item.quantity);
    if (hasQuantities && quantityWatch) {
      const totalLinkedOrderQty = calculateTotalQuantity(linkedOrdersWatch);
      if (totalLinkedOrderQty !== quantityWatch) {
        errors.push(
          'Total qty of linked dispatch orders qty must always be equal to supplier dispatch order qty!'
        );
      }
    }

    // Store errors in state
    setValidationErrors(errors);

    // Set form errors
    form.setFields([
      {
        name: ['products', name, 'linkedOrders'],
        errors: errors.length > 0 ? errors : [],
      },
    ]);
  }, [linkedOrdersWatch, quantityWatch]);

  // --- Added logic for prefilling quantity---
  


useEffect(() => {
  console.log("linkedOrdersWatch", linkedOrdersWatch)
  if (!Array.isArray(linkedOrdersWatch) || linkedOrdersWatch.length === 0) return;

  // Check all linked orders and prefill quantities if they're empty or zero
  const updatedLinkedOrders = linkedOrdersWatch.map(linkedOrder => {
    const currentQty = linkedOrder?.quantity;
    
    // Only prefill if quantity is empty or zero and orderId exists
    if ((!currentQty) && linkedOrder?.orderId) {
      // Find the selected dispatch order to get its actual quantity
      const selectedOrder = getProductRelatedCustomerDispatchOrders(
        customerDispatchOrders,
        formIdWatch
      ).find(order => order.orderId === linkedOrder?.id);
      
      // Get the quantity from the selected product within the dispatch order
      if (selectedOrder && selectedOrder.products) {
        const ProductInfo = productList.find(product => product.id === formIdWatch);
        const selectedProduct = selectedOrder.products.find(product => 
          product.product.id === ProductInfo.product.id
        );
        
        if (selectedProduct && selectedProduct.quantity) {
          return { ...linkedOrder, quantity: selectedProduct.quantity };
        }
      }
    }
    return linkedOrder;
  });
  
  // Only update if there were changes
  const hasChanges = updatedLinkedOrders.some((order, index) => {
    const originalOrder = linkedOrdersWatch[index];
    const originalQuantity = originalOrder?.quantity;
    const newQuantity = order?.quantity;
    return originalQuantity !== newQuantity;
  });
  
  if (hasChanges) {
    form.setFieldValue(['products', name, 'linkedOrders'], updatedLinkedOrders);
  }
}, [linkedOrdersWatch, form, name, customerDispatchOrders, formIdWatch]);


  // --- End of new logic ---

  const handleModalClose = () => {
    if (validationErrors.length > 0) {
      // If there are validation errors, clear the linked orders
      form.setFieldValue(['products', name, 'linkedOrders'], []);
      message.error('Linked orders were not saved due to validation errors');
    }
    closeModal();
  };

  return (
    <tr>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        {productList.length ? (
          <Form.Item name={[name, 'product']}>
            <Select
              showSearch
              allowClear
              optionFilterProp="label"
              style={{
                width: focusedField === `${index}-product` ? '350px' : '150px',
              }}
              onFocus={() => setFocusedField(`${index}-product`)}
              onBlur={() => setFocusedField(null)}
              disabled={formView === formModes.UPDATE}
            >
              {productList.map(product => (
                <Select.Option
                  key={product.id}
                  value={product.id}
                  label={product.product.tradeName}
                >
                  {product.product.tradeName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'uom']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Select
            disabled={formView === formModes.UPDATE}
            style={{
              width: focusedField === `${index}-uom` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-uom`)}
            onBlur={() => setFocusedField(null)}
          >
            {productUOMList.map(uom => (
              <Select.Option key={uom.key} value={uom.value}>
                {uom.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantity']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              async validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                const [prod] = initialValue.products.filter(item => item.product === formIdWatch);
                const poNumber = initialValue.purchaseOrderNumber;

                // 1. If row is filled and value is empty, reject
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }

                // 2. If value is less than or equal to 0, reject
                if (value && value <= 0) {
                  return Promise.reject(
                    new Error('Quantity can never be less than or equal to zero')
                  );
                }

                // 3. If product exists and quantity validation needed, do async validation
                if (prod && prod.quantity && value) {
                  return await validateProductQuantity(poNumber, formIdWatch, value);
                }

                // 4. If value is empty and row is empty, pass (since it's not required when row is empty)
                return Promise.resolve();
              },
            }),
          ]}
        >
          <InputNumber
            style={{
              width: focusedField === `${index}-quantity` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantity`)}
            onBlur={() => setFocusedField(null)}
            // type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'price']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                if (poGenrationRequired && value && value <= 0) {
                  return Promise.reject(new Error('Price can never be less than or equal to zero'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-price` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-price`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantityPerUnit']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-quantityPerUnit` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantityPerUnit`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) => {
            const linkedOrders = getFieldValue(['products', name, 'linkedOrders']) || [];
            const hasLinkedOrders = linkedOrders.length > 0;

            return (
              <Button
                type="primary"
                onClick={() => openModal(index, 'documents')}
                style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
              >
                {hasLinkedOrders ? `View (${linkedOrders.length})` : 'Add'}
              </Button>
            );
          }}
        </Form.Item>

        {/* Modal for Address Input */}
        <Modal
          title="Add Documents"
          open={selectedField?.index === index && selectedField?.type === 'documents'}
          onCancel={handleModalClose}
          onOk={handleModalClose}
          destroyOnClose
          width={'80%'} // Set width (adjust as needed)
          style={{ top: 20, height: '80vh' }} // Adjust height & position
          bodyStyle={{ height: '70vh', overflowY: 'auto' }} // Make content scrollable
          footer={[
            <Button key="cancel" onClick={handleModalClose}>
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                if (validationErrors.length === 0) {
                  closeModal();
                } else {
                  message.error('Please fix validation errors before saving');
                }
              }}
            >
              Save
            </Button>,
          ]}
        >
          <Form.List
            name={[name, 'linkedOrders']}
            rules={[
              {
                required: true,
                message: 'Please add linked dispatch orders!',
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => {
              return (
                <>
                  {fields.map(({ key, name: linkedOrderIndex }) => (
                    <Card
                      size="small"
                      style={{ margin: '20px' }}
                      key={key}
                      title={`Linked Order ${linkedOrderIndex + 1}`}
                      extra={<CloseOutlined onClick={() => remove(linkedOrderIndex)} />}
                    >
                      <Row gutter={16}>
                        <Col span={24}>
                          <Form.Item
                            name={[linkedOrderIndex, 'orderId']}
                            rules={[
                              {
                                required: true,
                                message: 'Please select linked Orders!',
                              },
                            ]}
                            label="Linked Order"
                          >
                            <Select showSearch optionFilterProp="label" onChange={(value) => {
                              // Set the id field at the same level as orderId within the linkedOrders array
                              // 'name' is the product index, 'linkedOrderIndex' is the linkedOrders index
                              // Find the selected order to get the orderId (label)
                              const selectedOrder = getProductRelatedCustomerDispatchOrders(
                                customerDispatchOrders,
                                formIdWatch
                              ).find(order => order.id === value);
                              
                              if (selectedOrder) {
                                form.setFieldValue(['products', name, 'linkedOrders', linkedOrderIndex, 'id'], selectedOrder.orderId);
                              }
                            }}>
                              {getProductRelatedCustomerDispatchOrders(
                                customerDispatchOrders,
                                formIdWatch
                              ).map(order => (
                                <Select.Option
                                  key={order.id}
                                  value={order.id}
                                  label={order.orderId}
                                >
                                  {order.orderId + ' - PO: ' + order.purchaseOrderNumber}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={24}>
                          <Form.Item
                            name={[linkedOrderIndex, 'quantity']}
                            rules={[
                              {
                                required: true,
                                message: 'Please enter quantity!',
                              },
                            ]}
                            label="Quantity"
                          >
                            <InputNumber />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Card>
                  ))}
                  <Form.Item>
                    <Button
                      block
                      style={{ marginTop: '20px' }}
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      Link Dispatch Orders
                    </Button>
                    <div
                      style={{
                        marginTop: '8px',
                        color: '#1890ff',
                        backgroundColor: '#e6f7ff',
                        padding: '8px',
                        borderRadius: '4px',
                        fontSize: '14px',
                      }}
                    >
                      Note: Total quantity of linked dispatch orders must be equal to the supplier
                      dispatch order quantity ({quantityWatch || 0})
                    </div>
                  </Form.Item>
                  <div style={{ color: 'red' }}>
                    <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                  </div>
                </>
              );
            }}
          </Form.List>
          {/* {validationErrors.length > 0 && (
            <Alert
              message="Validation Errors"
              description={
                <ul>
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              }
              type="error"
              showIcon
              style={{ marginTop: 16 }}
            />
          )} */}
        </Modal>
      </td>

      {packagingList.length > 0 && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
          <Form.Item
            name={[name, 'packaging']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              // mode="multiple"
              className={css.selectBox}
              style={{
                width: focusedField === `${index}-packaging` ? '350px' : '150px',
              }}
              optionFilterProp="label"
              showSearch
              onFocus={() => setFocusedField(`${index}-packaging`)}
              onBlur={() => setFocusedField(null)}
            >
              {packagingList.map((packageType, index) => (
                <Select.Option key={index} value={packageType.id} label={packageType.type}>
                  <div className={css.CustomSelectOptn}>
                    <div className={css.optnTitle}>{packageType.type}</div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Pack size:</div>
                      <div className={css.optnValue}>{packageType.packSize}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Tare weight:</div>
                      <div className={css.optnValue}>{packageType.tareWeight}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Dimension:</div>
                      <div className={css.optnValue}>{packageType.dimension}</div>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </td>
      )}
      
      <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
        {isOtherPackaging && (
          <span style={{ color: 'red', marginRight: 2 }}>*</span>
        )}
        <Form.Item
          name={[name, 'otherPackagingDetails']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (isOtherPackaging && !value) {
                  return Promise.reject(new Error('Other Packaging Details is required!'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-otherPackagingDetails` ? '350px' : '150px',
              borderColor: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? 'red' : undefined,
              background: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? '#fff0f0' : undefined,
            }}
            onFocus={() => setFocusedField(`${index}-otherPackagingDetails`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'units']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);

                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }

                if (value && value <= 0) {
                  return Promise.reject(new Error('Units can never be less than or equal to zero'));
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-units` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-units`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'remarks']} style={{ marginBottom: 0, display: 'block' }}>
          <Input
            style={{
              width: focusedField === `${index}-remarks` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-remarks`)}
            onBlur={() => setFocusedField(null)}
            // type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', textAlign: 'center', verticalAlign: 'middle', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', margin: '4px 0' }}>
          <Button
            type="text"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => setDeleteConfirmVisible(true)}
            style={{ 
              color: '#ff4d4f',
              border: '1px solid #ff4d4f',
              borderRadius: '4px',
              width: '32px',
              height: '28px',
              padding: '0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title="Delete this product row"
          />
        </div>

        {/* Confirmation Modal */}
        <Modal
          title="Confirm Delete"
          open={deleteConfirmVisible}
          onOk={() => {
            onDeleteRow();
            setDeleteConfirmVisible(false);
          }}
          onCancel={() => setDeleteConfirmVisible(false)}
          okText="Delete"
          cancelText="Cancel"
          okButtonProps={{ danger: true }}
        >
          <p>Are you sure you want to delete this product row?</p>
          <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
            This action cannot be undone.
          </p>
        </Modal>
      </td>
    </tr>
  );
};

ProductRow.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
  initialValue: PropTypes.object,
  productUOMList: PropTypes.array.isRequired,
  setInitialValue: PropTypes.func.isRequired,
  customerorderbookId: PropTypes.string.isRequired,
  poGenrationRequired: PropTypes.bool,
  onDeleteRow: PropTypes.func.isRequired,
};

export default ProductRow;
