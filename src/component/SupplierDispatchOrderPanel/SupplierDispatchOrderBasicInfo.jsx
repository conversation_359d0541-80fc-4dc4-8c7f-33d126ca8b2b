import { CalendarOutlined } from '@ant-design/icons';
import { Col, DatePicker, Form, Row, Select } from 'antd';
import enUS from 'antd/es/calendar/locale/en_US';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { DateFormat, formModes } from '../../constants/formConstant';
import { getSupplierOrderBookList } from '../../service/api/supplierOrderBookApi';
import css from '../CustomerPanel/CustomerForm.module.css';
import PageLoader from '../Loaders/PageLoader';
import { getFormModeFromPathV2 } from '../../util/route';
import { useLocation } from 'react-router-dom';

const SupplierDispatchOrderBasicInfo = props => {
  const { form, initialValue, 
    // formView, 
    setInitialValue } = props;

  const [purchaseOrderList, setPurchaseOrderList] = useState([]);
  const [loading, setLoading] = useState(false);
  const poNum = Form.useWatch('purchaseOrderNumber', form);
  const { pathname } = useLocation();
  const currentFormMode=getFormModeFromPathV2(pathname)

  const getFormPrefilldValues = () =>
    initialValue && initialValue.purchaseOrderNumber
      ? {
          purchaseOrderNumber: initialValue?.purchaseOrderNumber,
          purchaseOrderDate: initialValue?.purchaseOrderDate,
        }
      : {};

  useEffect(() => {
    if (!purchaseOrderList || !purchaseOrderList.length) {
      setLoading(true);
      //api call to get order list TODO: format api params
      const pageNumber = 0;
      const filters = {};
      const searchkey = '';
      getSupplierOrderBookList(null,pageNumber, filters, searchkey)
        .then(res => {
          if (res.data && res.data.content) {
            setPurchaseOrderList(res.data.content);
          }
          setLoading(false);
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);

  useEffect(() => {
    if (initialValue && initialValue.purchaseOrderNumber) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);

  useEffect(() => {
    //TODO: modify check
    if (
      currentFormMode === formModes.CREATE &&
      poNum &&
      !initialValue.purchaseOrderDate &&
      !initialValue.purchaseOrderNumber
    ) {
      const [purchaseOrder] = purchaseOrderList.filter(
        order => order.purchaseOrderNumber === poNum
      );
      let newValue = {};
      if (purchaseOrder && purchaseOrder.purchaseOrderNumber) {
        newValue = {
          purchaseOrderNumber: purchaseOrder.purchaseOrderNumber,
          purchaseOrderDate: dayjs(purchaseOrder.purchaseOrderDate),
          supplier: purchaseOrder.supplier,
          paymentTerms: { ...purchaseOrder.paymentTerms },
          productList: [...purchaseOrder.products],
        };
      }
      setInitialValue(newValue);
    } else if (currentFormMode.mode !== formModes.CREATE && poNum && !initialValue.productList) {
      const [purchaseOrder] = purchaseOrderList.filter(
        order => order.purchaseOrderNumber === poNum
      );
      purchaseOrder && purchaseOrder.purchaseOrderNumber
        ? setInitialValue({
            ...initialValue,
            productList: [...purchaseOrder.products],
          })
        : null;
    }
  }, [poNum]);

  if (loading) {
    return <PageLoader style={{ width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      className={css.formContainer}
      name="dispatch_order_basic_info"
      layout="vertical"
      scrollToFirstError
      initialValues={getFormPrefilldValues()}
      preserve={false}
      disabled={currentFormMode === formModes.READ}
      size="large"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          {purchaseOrderList.length ? (
            <Form.Item
              label="PO number"
              name="purchaseOrderNumber"
              rules={[{ required: true, message: 'Please select Po number' }]}
            >
              <Select showSearch>
                {purchaseOrderList.map(order => (
                  <Select.Option key={order.id} value={order.purchaseOrderNumber}>
                    {order.purchaseOrderNumber}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      {poNum ? (
        <Row>
          <Col span={24}>
            <Form.Item
              label="PO date"
              name="purchaseOrderDate"
              rules={[
                {
                  required: true,
                  message: 'Please input PO date!',
                },
              ]}
            >
              <DatePicker
                format={DateFormat}
                disabled
                locale={enUS}
                showToday
                style={{ width: '100%' }}
                suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
              />
            </Form.Item>
          </Col>
        </Row>
      ) : null}
    </Form>
  );
};

export default SupplierDispatchOrderBasicInfo;

SupplierDispatchOrderBasicInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func,
  formView: PropTypes.object.isRequired,
};
