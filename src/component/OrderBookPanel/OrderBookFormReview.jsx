import React from 'react';
import PropTypes from 'prop-types';
import { Collapse, Descriptions } from 'antd';
import FileUpload from '../FileUpload';
import {
  paymentTermsDate,
  productIncoTermsList,
  productUOMList,
  incotermsDataList,
} from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const OrderBookFormReview = props => {
  const { orderBookEntry } = props;

  const getLabelForShipmentMethodOrDefault = key => {
    const label = getEnumValueFromList(key, incotermsDataList.FOB[0].child);
    return label ? label : key;
  };

  const orderFieldItems = [
    {
      key: '1',
      label: 'Customer Name',
      children: orderBookEntry?.customer?.name || orderBookEntry?.customerData?.name,
    },
    {
      key: '2',
      label: 'PO number',
      children: orderBookEntry?.purchaseOrderNumber,
    },
    {
      key: '3',
      label: 'PO date',
      children: getDateFromTimeStamp(orderBookEntry?.purchaseOrderDate),
    },
    {
      key: '4',
      label: 'PO file',
      children: <FileUpload value={orderBookEntry?.purchaseOrderFile} disabled />,
    },
  ];
  const InventoryFieldItems = [
    {
      key: '1',
      label: 'Inventory Name',
      children: orderBookEntry?.inventory?.name || orderBookEntry?.inventoryData?.name,
    },
  ];
  const paymentFieldItems = [
    {
      key: '5',
      label: 'Billing address',
      children: orderBookEntry?.billTo,
    },
    {
      key: 'poPaymentTerms',
      label: 'PO Payment Terms',
      children: orderBookEntry?.paymentTerms?.poPaymentTerms,
    },
    {
      key: '6',
      label: 'Credit amount(%)',
      children: orderBookEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '7',
      label: 'Credit days',
      children: orderBookEntry?.paymentTerms?.creditorDays,
    },
    {
      key: '8',
      label: 'Payment Term Based on',
      children: getEnumValueFromList(orderBookEntry?.paymentTerms?.startDate, paymentTermsDate),
    },
    {
      key: '9',
      label: 'margin Percentage',
      children:orderBookEntry?.marginPercentage,
    },
    {
      key: '10',
      label: 'margin ApprovedBy',
      dataIndex: 'marginApprovedBy',
      width: 150,
      children: orderBookEntry?.marginApprovedBy,
    },
    {
      key: '11',
      label: 'margin ApprovedOn',
      dataIndex: 'marginApprovedOn',
      width: 150,
      children: getDateFromTimeStamp(orderBookEntry?.expectedDeliveryDate),
    },
    {
      key: '12',
      label: 'Remarks',
      children: (
        <>
          {orderBookEntry?.remarks
            ? <div>orderBookEntry?.remarks</div>
            : null}
        </>
      ),
    },
  ];

  const orderProductsList = orderBookEntry?.products?.length
    ? orderBookEntry.products.map((product, index) => ({
        key: index,
        label: product?.product?.tradeName || orderBookEntry?.productList[index]?.tradeName,
        children: (
          <Descriptions
            layout="vertical"
            column={2}
            items={getProductFields(product)}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
          />
        ),
      }))
    : [];

  function getProductFields(productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Price per unit',
        children: productData?.price,
      },
      {
        key: '4',
        label: 'Incoterms',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>
                {getEnumValueFromList(productData?.incoterms?.type, productIncoTermsList)}
              </span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Destination Country: </span>
              <span>{productData?.incoterms?.country}</span>
            </div>
            {productData?.incoterms?.data && Object.keys(productData.incoterms.data).length > 0
              ? Object.keys(productData.incoterms.data).map(key => (
                  <div key={key}>
                    <span style={{ fontWeight: '600' }}>{camelCaseToTitle(key)}:</span>
                    <span>
                      {getLabelForShipmentMethodOrDefault(productData.incoterms?.data?.[key])}
                    </span>
                  </div>
                ))
              : ''}
          </div>
        ),
      },
      {
        key: '5',
        label: 'Packaging',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>{productData?.packaging?.type}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Pack Size:</span>
              <span>{productData?.packaging?.packSize}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Tare Weight:</span>
              <span>{productData?.packaging?.tareWeight}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Dimension:</span>
              <span>{productData?.packaging?.dimension}</span>
            </div>
          </div>
        ),
      },
      {
        key: '7',
        label: 'Expected Delivery date',
        children: getDateFromTimeStamp(productData?.expectedDeliveryDate),
      },
      {
        key: '11',
        label: 'Remarks',
        span: 3,
        children: (
          <>
            {productData?.remarks
              ?  <div>{productData?.remarks}</div>
              : null}
          </>
        ),
      },
      {
        key: '12',
        label: 'Customer product code',
        children: productData?.customerProductCode,
      },
    ];
  }

  return (
    <div className="flex flex-col gap-5">
      <Descriptions
        title={<div className="text-lg font-semibold">Order Details</div>}
        items={orderBookEntry?.inventoryId ? InventoryFieldItems : orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout="vertical"
      />
      <Descriptions
        title={<div className="text-lg font-semibold">Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout="vertical"
      />
      <div className="text-lg font-semibold">Product Detail</div>
      <Collapse style={{ marginTop: '20px' }} items={orderProductsList} defaultActiveKey={['0']} />
    </div>
  );
};

export default OrderBookFormReview;

OrderBookFormReview.propTypes = {
  orderBookEntry: PropTypes.object.isRequired,
};
