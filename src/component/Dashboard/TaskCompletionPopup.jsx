import {
  Button,
  Modal,
  Row,
  Input,
  Select,
  InputNumber,
  DatePicker,
  Alert,
  Form,
  Divider,
  Col,
  message,
} from 'antd';
import { CheckOutlined, CloseOutlined, CalendarOutlined } from '@ant-design/icons';
import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { getOrderByPOId } from '../../service/api/orderBookApi';
import { updateTasks, updateTaskData, getTaskbyId } from '../../service/api/taskService';
import { DateFormat } from '../../constants/formConstant';
import OrderLink from './OrderLink';
import enUS from 'antd/es/calendar/locale/en_US';
import css from '../DispatchOrderPanel/ProductTable.module.css';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';

import dayjs from 'dayjs';
import FileUpload from '../FileUpload';
import TaskViewData from './TaskViewData';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import IncotermInput from './IncotermInput';
import FormulaField from '../Common/FormulaField';
import PageLoader from '../Loaders/PageLoader';
import currencyRates from '../../util/currency.json';
import { useNavigate } from 'react-router-dom';
import { getSupplierOrderBookList } from '../../service/api/supplierOrderBookApi';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import useFormTimeTracker from '../PostHog/useFormTimeTracker';
import useClickTracker from '../PostHog/useClickTracker';
import { getSupplierList } from '../../service/api/supplierService';

const { Option } = Select;
const { TextArea } = Input;

// Supplier Dropdown Component
const SupplierDropdown = ({ placeholder, style, disabled, value, onChange }) => {
  const [supplierList, setSupplierList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    getSupplierList()
      .then(res => {
        if (res.data && res.data.content) {
          setSupplierList(res.data.content);
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <Select
      placeholder={placeholder}
      style={style}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      value={value}
      onChange={onChange}
    >
      {supplierList.map(supplier => (
        <Select.Option key={supplier.id} value={supplier.name} label={supplier.name}>
          {supplier.name}
        </Select.Option>
      ))}
    </Select>
  );
};

SupplierDropdown.propTypes = {
  placeholder: PropTypes.string,
  style: PropTypes.object,
  disabled: PropTypes.bool,
  value: PropTypes.any,
  onChange: PropTypes.func,
};

const ORDER_TYPE_MAPPING = {
  PURCHASE_ORDER: 'order-book',
  CUSTOMER_DISPATCH_ORDER: 'customer-order',
  SUPPLIER_ORDER_BOOK: 'supplier-order-book',
  SUPPLIER_DISPATCH_ORDER: 'supplier-order',
};

const ORDER_KEY_MAPPING = {
  PURCHASE_ORDER: 'orderBookId',
  CUSTOMER_DISPATCH_ORDER: 'orderId',
  SUPPLIER_ORDER_BOOK: 'orderBookId',
  SUPPLIER_DISPATCH_ORDER: 'orderId',
};

const CATEGORY_MAP = {
  PURCHASE_ORDER: 'Purchase Order',
  CUSTOMER_DISPATCH_ORDER: 'Customer Dispatch Order',
  SUPPLIER_ORDER_BOOK: 'Supplier Order',
  SUPPLIER_DISPATCH_ORDER: 'Supplier Dispatch Order',
};

const TaskCompletionPopup = ({ taskData, taskUpdated }) => {
  const packagingList = useSelector(state => state.packagingList);
  const navigate = useNavigate();
  const [modalOpen, setModalOpen] = useState(false);
  const [validationInputs, setValidationInputs] = useState({});
  const [apiData, setApiData] = useState(null);
  const [orderMissing, setOrderMissing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [record, setRecord] = useState(taskData);
  const [isDisabled, setIsDisabled] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [showRemarksForm, setShowRemarksForm] = useState(false); // To show/hide form
  const [remarks, setRemarks] = useState(); // { taskId: "remark text" }
  const [invoiceData, setInvoiceData] = useState(); 
  const RequiredTaskIds = record?.prerequisiteTasks; // Example IDs
  const [disableSupplierRFQEdit, setDisableSupplierRFQEdit] = useState(false);
  const isSupplierRFQEdit = record?.name === "Supplier RFQ" && record?.status === "COMPLETED";

  const groupedReceivedRemarks = record?.remarksReceived?.reduce(
    (acc, { taskId, taskName, remark }) => {
      if (!acc[taskId]) {
        acc[taskId] = {
          taskName,
          remarks: [],
        };
      }
      acc[taskId].remarks.push(remark);
      return acc;
    },
    {}
  );
  const user = useSelector(state => state.user);
  const taskCompletionFormOpened = useFormTimeTracker('Task Completion');
  const { trackClick } = useClickTracker();


  const groupedGivenRemarks = record?.remarksGiven?.reduce((acc, { taskId, taskName, remark }) => {
    if (!acc[taskId]) {
      acc[taskId] = {
        taskName,
        remarks: [],
      };
    }
    acc[taskId].remarks.push(remark);
    return acc;
  }, {});

  const toggleExpand = () => setExpanded(prev => !prev);
const dispatch = useDispatch();

    useEffect(() => {
      if ((!packagingList || !packagingList.length) && modalOpen) {
        // setLoading(true);
        getAllPackagingDetails()
          .then(res => {
            if (res.data) {
              const pkgList = res.data.map(pkg => ({
                ...pkg,
                id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
              }));
              dispatch(setPackagingList(pkgList));
              // setLoading(false);
            }
          })
          .catch(error => {
            console.log(error);
            // setLoading(false);
          });
      }
    }, [modalOpen]);
  
  

  const handleRemarkIdChange = value => {
    setRemarks({ ...remarks, taskId: value });
  };

  const handleRemarkButtonClick = () => {
    if (showRemarksForm) {
      // Form is open, now submit data
      handleSubmitRemarks();
    } else {
      // Form is closed, open form
      setShowRemarksForm(true);
    }
  };

  const getFieldPath = path => {
    if (!path) return [];
    return path.split('.');
  };

  const getCurrencyName = code => {
    const currencyNames = {
      USD: 'Dollar',
      EUR: 'Euro',
      INR: 'Rupee',
      JPY: 'Yen',
      AED: 'Dirham',
      CNY: 'Yuan',
    };
    return currencyNames[code] || code;
  };

  const handleSubmitRemarks = async () => {
    if (!remarks?.taskId || !remarks?.remark) {
      return;
    }

    setLoading(true);
    try {
      // Call API to submit remarks
      const response = await getTaskbyId(remarks.taskId);
      const updatePayload = response?.data;

      // // Convert remarks object to array
      const remarksReceivedArray = [
        { taskId: record?.id, remark: remarks.remark, taskName: record?.name },
      ];

      // Merge existing remarks with new ones safely
      const updatedReceivedRemarks = [
        ...(updatePayload?.remarksReceived || []),
        ...remarksReceivedArray,
      ];

      // Assume you have current status in 'currentTaskStatus' variable
      let finalStatus =
        updatePayload?.status === 'COMPLETED' ? 'IN_PROGRESS' : updatePayload?.status;

      // Final payload to update
      const receivedtaskData = await updateTasks({
        ...updatePayload,
        remarksReceived: updatedReceivedRemarks,
        status: finalStatus, // conditionally set status
      });

      // // Convert remarks object to array
      const remarksGivenArray = [
        { taskId: remarks?.taskId, remark: remarks.remark, taskName: receivedtaskData?.data?.name },
      ];

      // Merge existing remarks with new ones safely
      const updatedGivenRemarks = [...(record?.remarksGiven || []), ...remarksGivenArray];

      const recorddata = await updateTasks({ ...record, remarksGiven: updatedGivenRemarks });
      // Keep the taskId if it was pre-filled (single required task)
      setRemarks(RequiredTaskIds?.length === 1 ? { taskId: RequiredTaskIds[0]?.taskId } : {});
      setShowRemarksForm(false);
      setRecord(recorddata.data);
      taskUpdated();
    } catch (error) {
      console.error('Failed to submit remarks:', error);
      if (error?.response?.status === 409) {
        message.error('Data has been modified recently. Please refresh the page and try again.');
      } else {
        message.error('Failed to submit remarks. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle remark input
  const handleRemarkChange = text => {
    if (!remarks?.taskId) return; // Avoid setting without task selected
    console.log('Remark text:', remarks, text);
    setRemarks({ ...remarks, remark: text });
  };

  useEffect(() => {
    if (modalOpen) {
      fetchInitialData();
    }
  }, [modalOpen]);

  useEffect(() => {
    setRecord(taskData);

    const isCompleted = taskData?.status === 'COMPLETED';
    const isAdmin = user?.permissionsGroups?.includes('TASK_ADMIN') || user?.permissionsGroups?.includes('ADMIN');

    // Disable only if completed and NOT admin
    const isDisabled = isCompleted && !isAdmin;
    setIsDisabled(isDisabled);
    if(disableSupplierRFQEdit){
      setIsDisabled(true);
    }
  }, [taskData, user]);

  useEffect(() => {
    if (RequiredTaskIds?.length === 1) {
      setRemarks(prev => ({
        ...prev,
        taskId: RequiredTaskIds[0]?.taskId
      }));
    }
  }, [RequiredTaskIds]);

  const convertDatesToDayjs = (data, taskConfig) => {
    const finalData = JSON.parse(JSON.stringify(data)); // Deep clone to avoid mutation

    const getValueFromPath = (obj, path) => {
      return path?.split('.').reduce((acc, part) => {
        if (part.endsWith('[]')) {
          const arrayKey = part.slice(0, -2);
          return acc?.[arrayKey] || [];
        } else {
          return acc?.[part];
        }
      }, obj);
    };

    const setValueByPath = (obj, path, value) => {
      const parts = path?.split('.');
      let current = obj;

      parts.forEach((part, index) => {
        const isArray = part.endsWith('[]');
        const key = isArray ? part.slice(0, -2) : part;

        if (index === parts.length - 1) {
          current[key] = value;
        } else {
          if (!current[key]) current[key] = isArray ? [] : {};
          current = current[key];
        }
      });
    };

    taskConfig?.forEach(({ inputType, path }) => {
      if (inputType === 'date') {
        const value = getValueFromPath(data, path);
        if (value && typeof value === 'string') {
          const dayjsDate = dayjs(value); // Convert to dayjs
          setValueByPath(finalData, path, dayjsDate);
        }
      }
    });

    return finalData;
  };

  const fetchInitialData = async () => {
    setLoading(true);
    try {
      if(!record?.entityId){
        setLoading(false);
        return;
      }
      const response = await fetchOrderBookFromId(record?.category);
      if (!response) {
        setLoading(false);
        return;
      }

      setApiData(response);

      const formattedForForm = convertDatesToDayjs(response, record?.taskConfig);
      
      // Check if task is completed and auto-fill values
      if (record?.status === 'COMPLETED') {
        // For completed tasks, get values from the response
        const testingRequired = response?.testingRequired ?? formattedForForm?.products?.[0]?.testingRequired;
        const dispatchWithResults = response?.dispatchWithResults ?? formattedForForm?.products?.[0]?.dispatchWithResults;
        

        // Set values in form
        const formValues = {
          testingRequired: testingRequired,
          dispatchWithResults: testingRequired ? dispatchWithResults : null,
          packaging: formattedForForm?.products[0]?.packaging?.id,
          otherPackagingDetails: formattedForForm?.products[0]?.packaging?.type === 'Others' ? 
            formattedForForm?.products[0]?.packaging?.otherPackagingDetails : null
        };

        form.setFieldsValue(formValues);
      } else {
        // For non-completed tasks, set only what is necessary
        const testingRequired = formattedForForm?.testingRequired ?? formattedForForm?.products?.[0]?.testingRequired; 
        
        form.setFieldValue('packaging', formattedForForm?.products[0]?.packaging?.id);
        form.setFieldValue('testingRequired', testingRequired);
        if (testingRequired) {
          const dispatchWithResults = formattedForForm?.dispatchWithResults ?? formattedForForm?.products?.[0]?.dispatchWithResults;
          form.setFieldValue('dispatchWithResults', dispatchWithResults);
        }
      }

      setValidationInputs({
        ...formattedForForm,
        testingRequired: formattedForForm?.testingRequired ?? formattedForForm?.products?.[0]?.testingRequired
      });

    // Only run this effect when record changes and conditions are met
    const shouldFetchSupplierOrderBook = record?.name === "Supplier RFQ" && 
                                        record?.status === "COMPLETED" 
 
    if (shouldFetchSupplierOrderBook) {
      const fetchSupplierOrderBook = async () => {
        try {
          const response = await getOrderByPOId(
            record?.orderId, 
            ORDER_TYPE_MAPPING['SUPPLIER_ORDER_BOOK'], 
            "linkedCOBId"
          );
          if (response?.data?.content?.length > 0) {
            setDisableSupplierRFQEdit(true);
          }
        } catch (error) {
          console.error('Error fetching supplier order book:', error);
        }
      };

      fetchSupplierOrderBook();
    }
    } catch (error) {
      console.error('Error fetching initial data:', error);
      setLoading(false);
    }

    setLoading(false);
  };

  // Add a useEffect to handle changes in record status
  useEffect(() => {
    if (modalOpen && record?.status === 'COMPLETED' && apiData) {
      // Re-initialize form data when task is completed
      const testingRequired = apiData?.testingRequired ?? apiData?.products?.[0]?.testingRequired;
      const dispatchWithResults = apiData?.dispatchWithResults ?? apiData?.products?.[0]?.dispatchWithResults;


      const formValues = {
        testingRequired: testingRequired,
        dispatchWithResults: testingRequired ? dispatchWithResults : null,
        packaging: apiData?.products[0]?.packaging?.id,
        otherPackagingDetails: apiData?.products[0]?.packaging?.type === 'Others' ? 
          apiData?.products[0]?.packaging?.otherPackagingDetails : null
      };

      form.setFieldsValue(formValues);
    }
  }, [record?.status, modalOpen, apiData]);

  // Add useEffect to watch form values
  useEffect(() => {
    if (modalOpen) {
      const formValues = form.getFieldsValue();
    }
  }, [modalOpen, form]);

  const fetchOrderBookFromId = async type => {
    try {
      if (!ORDER_TYPE_MAPPING[type]) return null;

      if (!record?.entityId) {
        // const orderResponse = await getOrderByPOId(
        //   record?.entityId,
        //   ORDER_TYPE_MAPPING['PURCHASE_ORDER'],
        //   ORDER_KEY_MAPPING['PURCHASE_ORDER']
        // );
        setOrderMissing(true);
        return null;
      }
      const getOrderBookListFromConfig = async (pageSize, pageNumber, filters, searchkey) => {
        setLoading(true);
        getSupplierOrderBookList(pageSize, pageNumber, filters, searchkey)
          .then(response => {
            // TODO: proper check for orderBookList
            console.log("this is response ",response);
            setInvoiceData(response?.data?.content);
          })
          .catch(error => {
            console.log(error);
            setLoading(false);
          });
      };

      if(record?.name ==="Invoice Upload"){
        const response =  await getOrderBookListFromConfig(100, 0, {
          linkedCOBId: [record?.entityId],
        }, '');
      }

      const response = await getOrderByPOId(
        record?.entityId,
        ORDER_TYPE_MAPPING[type],
        ORDER_KEY_MAPPING[type]
      );
      if (response?.data?.content?.length > 0) {
        setOrderMissing(false);
        return response.data.content[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching order details:', error);
    }
    return null;
  };

  const formatDateFieldsInPayload = (validationInputs, taskConfig) => {
    const finalPayload = JSON.parse(JSON.stringify(validationInputs)); // Deep clone to avoid mutation

    const getValueFromPath = (obj, path) => {
      return path?.split('.').reduce((acc, part) => {
        if (part.endsWith('[]')) {
          const arrayKey = part.slice(0, -2);
          return acc?.[arrayKey] || [];
        } else {
          return acc?.[part];
        }
      }, obj);
    };

    const setValueByPath = (obj, path, value) => {
      const parts = path?.split('.');
      let current = obj;

      parts.forEach((part, index) => {
        const isArray = part.endsWith('[]');
        const key = isArray ? part.slice(0, -2) : part;

        if (index === parts.length - 1) {
          current[key] = value;
        } else {
          if (!current[key]) current[key] = isArray ? [] : {};
          current = current[key];
        }
      });
    };

    taskConfig?.forEach(({ inputType, path }) => {
      if (inputType === 'date') {
        const value = getValueFromPath(validationInputs, path);
        if (value) {
          const isoDate = dayjs(value).toISOString(); // Convert to ISO string
          setValueByPath(finalPayload, path, isoDate);
        }
      }
    });

    return finalPayload;
  };

  // Then modify the onInputChange function
  const onInputChange = (allValues, changedValues = {}) => {

      // Check if any of the logistics component fields have changed
  const hasFirstMileChanged = changedValues?.supplierRfq?.firstMileLogistics !== undefined;
  const hasSeaFreightChanged = changedValues?.supplierRfq?.seaFreight !== undefined;
  const hasLastMileChanged = changedValues?.supplierRfq?.lastMileLogistics !== undefined;
  const hasDestinationChargesChanged = changedValues?.supplierRfq?.destinationCharges !== undefined;
  
  // If any logistics field changed, update the total
  if (hasFirstMileChanged || hasSeaFreightChanged || hasLastMileChanged || hasDestinationChargesChanged) {
    const totalLogisticsCost = calculateLogisticsCostSum();
    
    // Update the form field
    form.setFieldsValue({
      supplierRfq: {
        logisticsCost: {
          value: totalLogisticsCost
        }
      }
    });
    
    // Make sure to update allValues with the new total for state consistency
    if (!allValues.supplierRfq) allValues.supplierRfq = {};
    if (!allValues.supplierRfq.logisticsCost) allValues.supplierRfq.logisticsCost = {};
    allValues.supplierRfq.logisticsCost.value = totalLogisticsCost;
  }

  // Check if any currency field has changed
  let currencyChanged = false;
  let changedPaths = [];

  // Function to recursively check for currency changes in nested objects
  const checkForCurrencyChanges = (obj, path = []) => {
    if (!obj || typeof obj !== 'object') return;
    
    Object.keys(obj).forEach(key => {
      const currentPath = [...path, key];
      
      if (key === 'currency') {
        currencyChanged = true;
        changedPaths.push(path.join('.')); // Path to the parent of currency
      } else if (typeof obj[key] === 'object') {
        checkForCurrencyChanges(obj[key], currentPath);
      }
    });
  };

  // Check for currency changes in the changed values
  checkForCurrencyChanges(changedValues);
  // If a currency has changed, update conversion rates
  if (currencyChanged) {
    record?.taskConfig?.forEach(config => {
      if (config.inputType === 'formulaInput' && config.group?.find(item => item.key === 'conversionRate')) {
        const path = config.path;
        const formPath = getFieldPath(path);
        
        // Get the current currencies
        let fromCurrency = form.getFieldValue([...formPath, 'currency']);
        let toCurrency = form.getFieldValue(['supplierRfq', 'salesOrderValue', 'currency']);

        // If both currencies are present and different, update the conversion rate        
        if (fromCurrency && toCurrency) {
     
          // Check if we have direct conversion rate
          if (currencyRates[fromCurrency]) {
            const directRate = currencyRates[fromCurrency][`${fromCurrency}${toCurrency}`] || 1.00000;
              const rateString = directRate.toFixed(5);
  
            // For calculations, we can use either the original number or the parsed one
            // Both will work correctly in calculations
            const rate = parseFloat(rateString);
            // Update the form field
            form.setFieldValue([...formPath, 'conversionRate'], rate);
            
            // Also update the allValues object to include this change
            const pathParts = path.split('.');
            let current = allValues;
            
            // Navigate to the correct nested location
            for (let i = 0; i < pathParts.length - 1; i++) {
              if (!current[pathParts[i]]) {
                current[pathParts[i]] = {};
              }
              current = current[pathParts[i]];
            }
            
            // Set the conversion rate
            const lastPart = pathParts[pathParts.length - 1];
            if (!current[lastPart]) {
              current[lastPart] = {};
            }
            current[lastPart].conversionRate = rate;
            
            console.log(`Updated conversion rate for ${path} to ${rate}`);
          }
        }
      }
    });
  }

    const mergedDocuments = {
      ...(validationInputs.documents || {}), // Existing top-level documents
      ...(allValues.documents || {}), // New/updated top-level documents
    };

    const mergedFinalDocuments = {
      ...(validationInputs.finaDocuments || {}), // Existing top-level documents
      ...(allValues.finaDocuments || {}), // New/updated top-level documents
    };

    const mergedProducts = (validationInputs.products || []).map((product, index) => {
      const formProduct = (allValues.products && allValues.products[index]) || {};

      return {
        ...product,
        ...formProduct, // Shallow merge of product data like 'name'
        documents: {
          ...(product.documents || {}), // Existing product-level documents
          ...(formProduct.documents || {}), // New/updated product-level documents
        },
      };
    });

    // Finally merge at the top level
    const finalValues = {
      ...validationInputs, // Spread all initial values
      ...allValues, // Spread all form updated values
      products: mergedProducts, // Merged products with documents
      documents: mergedDocuments, // Merged top-level documents
      finaDocuments: mergedFinalDocuments,
    };

    // Set final merged state
    setValidationInputs(finalValues);
  };

  const handleCompleteTask = async () => {
    try {
      // Set loading state based on whether validation is needed
      if (!record?.dependencyResolved) {
        // For "Move to In Progress", set loading immediately (no validation needed)
        setLoading(true);
      } else {
        // For "Complete Task", validate first
        try {
          // Run form validation BEFORE setting loading state
           await form.validateFields(); // Will throw if validation fails

          // Only set loading state AFTER validation passes
          setLoading(true);
        } catch (validationError) {
          console.error('Validation failed:', validationError);
          return; // Stop further processing if validation fails
        }
      }
      if (!apiData) {
        setLoading(false);
        return;
      }

      const finalPayload = formatDateFieldsInPayload(validationInputs, record?.taskConfig);

      //update the stale values.
      if (finalPayload && finalPayload.supplierRfq) {
         finalPayload.supplierRfq.targetContributionMargin = form.getFieldValue(['supplierRfq','targetContributionMargin'])
      }

      if (finalPayload && (record?.name === 'Sample Request No (SR No.) generation' || record?.name === 'Sample Request generation')) {
        // Get the testing flag from the form
        const testingRequired = form.getFieldValue('testingRequired');
        const dispatchWithResults = testingRequired ? form.getFieldValue('dispatchWithResults') : null;
        
        // Set the flags at the top level
        finalPayload.testingRequired = testingRequired;
        finalPayload.dispatchWithResults = dispatchWithResults;
        
        // Set flags for each product
        finalPayload.products.forEach((product) => {
          product.testingRequired = testingRequired;
          product.dispatchWithResults = dispatchWithResults;
          
          // Handle packaging
          const packagingObj = packagingList.find(pkg => pkg.id === finalPayload?.packaging);
          if (packagingObj) {
            product.packaging = packagingObj;
            
            // If packaging type is 'Others', add otherPackagingDetails
            if (packagingObj.type === 'Others') {
              product.packaging.otherPackagingDetails = form.getFieldValue(['otherPackagingDetails']) || null;
            }
          }
        });
      }

      if(record?.name === "Margin Approval"){
        finalPayload.marginApprovedBy = user?.username
        finalPayload.marginApprovedOn = new Date().toISOString();
        finalPayload.marginPercentage = finalPayload?.supplierRfq?.targetContributionMargin
      }
      
      await updateTaskData(ORDER_TYPE_MAPPING[record?.category], apiData.id, finalPayload);
      let taskRecordDueDate = record?.dueDate;
      if (record.taskConfig && Array.isArray(record.taskConfig)) {
        const isMatch = record.taskConfig.some(obj => obj.key === record.dueDateLogic);

        if (isMatch) {
          const response = await getTaskbyId(record?.id);
          setRecord(response.data);
          taskRecordDueDate = response.data?.dueDate;
        }
      }
      if (!record?.dependencyResolved) {
        const updatedTask = {
          ...record,
          dueDate: taskRecordDueDate,
          status: 'IN_PROGRESS',
        };
        await updateTasks(updatedTask);
      } else {
        // We've already validated the form at the beginning, so we can proceed
        const updatedTask = {
          ...record,
          status: 'COMPLETED',
          dueDate: taskRecordDueDate,
          assignedTo: record.assignedTo || user.entityId, // Assign current user if not assigned
        };

        // Update task as COMPLETED
        await updateTasks(updatedTask);
      }
      // taskupdated()
      taskUpdated();
      setModalOpen(false);
      setLoading(false);
    } catch (error) {
      console.error(error);
      if (error?.response?.status === 409) {
        message.error('Data has been modified recently. Please refresh the page and try again.');
      } else {
        message.error('Failed to complete task. Please try again.');
      }
      setLoading(false);
    }
  };

  const handleSaveData = async () => {
    setLoading(true);
    try {
      if (!apiData) return;

      const finalPayload = formatDateFieldsInPayload(validationInputs, record?.taskConfig);
      if (finalPayload && (record?.name === 'Sample Request No (SR No.) generation' || record?.name === 'Sample Request generation')) {
        finalPayload.products.forEach((product) => {
          product.testingRequired = finalPayload?.testingRequired;
          product.packaging= packagingList.find(pkg => pkg.id === finalPayload?.packaging);
        });
      }
      await updateTaskData(ORDER_TYPE_MAPPING[record?.category], apiData.id, finalPayload);
      setModalOpen(false);
      setLoading(false);
    } catch (error) {
      console.error(error);
      if (error?.response?.status === 409) {
        message.error('Data has been modified recently. Please refresh the page and try again.');
      } else {
        message.error('Failed to save data. Please try again.');
      }
      setLoading(false);
    }
  };

  const calculateLogisticsCostSum = () => {
 
    const firstMileLogistics = form.getFieldValue(['supplierRfq', 'firstMileLogistics']) || 0;
    const seaFreight = form.getFieldValue(['supplierRfq', 'seaFreight']) || 0;
    const lastMileLogistics = form.getFieldValue(['supplierRfq', 'lastMileLogistics']) || 0;
    const destinationCharges = form.getFieldValue(['supplierRfq', 'destinationCharges']) || 0;
    
    return firstMileLogistics + seaFreight + lastMileLogistics + destinationCharges;
  };

  let buttonName = record?.onHold ? 'On Hold' : 'Action';
  let disable = false;
  if (record?.name === 'Margin Approval' && !record?.dependencyResolved) {
    buttonName = 'RFQ Pending';
    disable = true;
  }

  const isRowFilled = row => {
    return Object.values(row).some(value => value && value.toString().trim() !== '');
  };

  const packagingId = Form.useWatch(['packaging'], form);
  
  const packagingObj = packagingList.find(pkg => pkg.id === packagingId);

  const isOtherPackaging = packagingObj && packagingObj.type === 'Others';


  if (loading) {
    return <PageLoader />;
  }
  // const [focusedField, setFocusedField] = useState(null);



  const handleUploadInvoices = () => {
    try {
      navigate('/invoice', { 
        state: { 
          customerOrder: apiData || null,
          supplierOrder: invoiceData || null,
          from: 'invoice-upload'
        }
      });
      trackClick('button_clicked', { uploaded_via : "Task Completion", name : "Invoice Upload" });
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const trackFormCompletion = async (submitted, action, validate = true) => {
  try {
    if (validate) {
      const fieldErrors = form.getFieldsError();
      const hasErrors = fieldErrors.some(field => field.errors.length > 0);

      if (hasErrors) {
        console.log('Validation failed — event not sent');
        return;
      }
    }

    taskCompletionFormOpened.endTracking(submitted, record?.orderType, {
      tak_name: record?.name,
      group: record?.group,
      action,
    });
  } catch (e) {
    console.log(e);
  }
};


  return (
    <>
      <Button
        disabled={record?.onHold || disable}
        onClick={() => {
          taskCompletionFormOpened.startTracking();
          setModalOpen(true);
        }}
      >
        {' '}
        {buttonName}
      </Button>

      <Modal
        title="Task Completion Checklist"
        open={modalOpen}
        onCancel={() => {
          taskCompletionFormOpened.endTracking(false, record?.orderType, {});
          setModalOpen(false);
        }}
        footer={null}
        destroyOnClose
        width="70%"
        style={{ top: '10%' }}
        bodyStyle={{ maxHeight: '60vh', overflowY: 'auto' }}
      >
        <div className="p-4">
          {record?.orderId && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '550' }}>
                This is a {CATEGORY_MAP[record?.category]} Task. Click here to view complete order
                details:
              </h4>
              <OrderLink
                orderId={record?.orderId}
                poNumber={record?.secondaryId}
                setLoading={setLoading}
              />
            </div>
          )}
          {!record?.entityId || orderMissing ? (
            <Alert
              message={`${CATEGORY_MAP[record?.category]} Not Created Yet`}
              description={`Please create the ${
                CATEGORY_MAP[record?.category]
              } first before proceeding.`}
              type="error"
              style={{
                marginTop: '12px',
                backgroundColor: '#fff1f0', // Light red background
                border: '1px solid #ffa39e', // Light red border
                color: '#cf1322', // Text color for better visibility
              }}
            />
          ) : (
            <>
              <Row className="flex items-center space-x-2">
                <h4 className="text-sm font-medium text-gray-700">Task Name:</h4>
                <h4 className="font-semibold text-blue-900">{record?.name}</h4>
              </Row>
              <div>
                {RequiredTaskIds?.length > 0 && (
                  <>
                    <div
                      onClick={toggleExpand}
                      className="flex items-center gap-2 cursor-pointer font-semibold text-blue-900"
                    >
                      {expanded ? <DownOutlined /> : <RightOutlined />}
                      <span>{expanded ? 'Hide Details' : 'Show Details'}</span>
                    </div>
                  </>
                )}
                {expanded && (
                  <div className="p-2 border rounded-md mt-2 bg-gray-50">
                    <TaskViewData record={record} formData={validationInputs} />
                  </div>
                )}
                {/* Remarks Section */}
                <div className="mt-4 border border-gray-200 rounded-lg bg-gray-50">
                  {groupedReceivedRemarks && (
                    <div className="p-4">
                      <h4 className="font-semibold mb-4 text-lg">Remarks Received on Tasks</h4>
                      {Object.entries(groupedReceivedRemarks).map(
                        ([taskId, { remarks, taskName }]) => (
                          <div key={taskId} className="mb-3">
                            <div className="font-medium text-gray-700 mb-1">
                              Task Name: {taskName}
                            </div>
                            <div className="bg-white p-2 border rounded text-gray-600">
                              <b>Remarks:</b>
                              <ul className="list-disc pl-5">
                                {remarks?.map((remark, i) => (
                                  <li key={i}>{remark}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )
                      )}

                      <Divider />
                    </div>
                  )}

                  {groupedGivenRemarks && (
                    <div className="p-4">
                      <h4 className="font-semibold mb-4 text-lg">
                        Remarks Given on Required Tasks
                      </h4>

                      {Object.entries(groupedGivenRemarks).map(
                        ([taskId, { remarks, taskName }]) => (
                          <div key={taskId} className="mb-3">
                            <div className="font-medium text-gray-700 mb-1">
                              Task Name: {taskName}
                            </div>
                            <div className="bg-white p-2 border rounded text-gray-600">
                              <b>Remarks:</b>
                              <ul className="list-disc pl-5">
                                {remarks?.map((remark, i) => (
                                  <li key={i}>{remark}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )
                      )}

                      <Divider />
                    </div>
                  )}

                  {showRemarksForm && (
                    <div className="space-y-4 p-4">
                      {/* Task Selection Dropdown */}
                      <div className="flex items-center gap-2">
                        <label className="w-32 font-medium text-gray-700">Select Task:</label>
                        <Select
                          value={RequiredTaskIds?.length === 1 ? RequiredTaskIds[0]?.taskId : remarks?.taskId}
                          onChange={handleRemarkIdChange}
                          className="flex-1"
                          placeholder="Select Task"
                          disabled={RequiredTaskIds?.length === 1}
                        >
                          {RequiredTaskIds?.map(task => (
                            <Option value={task?.taskId} key={task?.taskId} label={task?.taskName}>
                              {task?.taskName}
                            </Option>
                          ))}
                        </Select>
                      </div>

                      {/* Remark Textarea */}
                      <div className="flex items-center gap-2">
                        <label className="w-32 font-medium text-gray-700">Remark:</label>
                        <TextArea
                          rows={3}
                          value={remarks?.remark}
                          onChange={e => handleRemarkChange(e.target.value)}
                          placeholder="Enter remark"
                          className="flex-1 resize-none"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <Form
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  onInputChange(allValues,changedValues);
                }}
                initialValues={validationInputs}
              >
                <div className="space-y-4 mt-4">
                  {(() => {
                    // Add supplier name field to Supplier RFQ task configuration
                    let taskConfig = record?.taskConfig || [];
                    if (record?.name === 'Supplier RFQ') {
                      const supplierNameField = {
                        key: 'supplierName',
                        label: 'Supplier Name',
                        inputType: 'supplierSelect',
                        path: 'supplierRfq.supplierName',
                        mandatory: true
                      };

                      // Check if supplier name field already exists
                      const hasSupplierName = taskConfig.some(field => field.key === 'supplierName');
                      if (!hasSupplierName) {
                        // Add supplier name field at the beginning, before payment terms
                        const paymentTermsIndex = taskConfig.findIndex(field =>
                          field.path && field.path.includes('paymentTerms')
                        );
                        if (paymentTermsIndex !== -1) {
                          taskConfig = [
                            ...taskConfig.slice(0, paymentTermsIndex),
                            supplierNameField,
                            ...taskConfig.slice(paymentTermsIndex)
                          ];
                        } else {
                          // If no payment terms found, add at the beginning
                          taskConfig = [supplierNameField, ...taskConfig];
                        }
                      }
                    }
                    return taskConfig;
                  })().map(
                    ({
                      key,
                      label,
                      inputType,
                      group,
                      path,
                      options,
                      mandatory,
                      formula,
                      dependencies,
                    }) => (
                      <div
                        key={key}
                        className="flex flex-col p-3 rounded-md hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <label className={`text-sm font-medium text-black`}>
                            {label}
                            {mandatory && (
                              <span style={{ color: '#ff4d4f', marginLeft: '4px' }}>*</span>
                            )}
                          </label>
                        </div>
                        <div className="mt-2">
                          {inputType === 'text' && (
                            <Form.Item
                              name={path?.split('.')}
                              validateTrigger={[]}
                              rules={
                                mandatory
                                  ? [{ required: true, message: 'This field is required' }]
                                  : []
                              }
                            >
                              <Input placeholder="Enter text" disabled={isDisabled} />
                            </Form.Item>
                          )}

                          {inputType === 'number' && (
                            <Form.Item
                              name={path?.split('.')}
                              validateTrigger={[]}
                              rules={
                                mandatory
                                  ? [{ required: true, message: 'This field is required' }]
                                  : []
                              }
                            >
                              <InputNumber
                                placeholder="Enter number"
                                style={{ width: '100%' }}
                                disabled={isDisabled}
                              />
                            </Form.Item>
                          )}

                          {inputType === 'select' && (
                            <>
                              <Form.Item
                                name={path?.split('.')}
                                validateTrigger={[]}
                                style={{ width: '509.69px' }}
                                rules={
                                  mandatory && !isDisabled
                                    ? [{ required: true, message: `Please select ${label}` }]
                                    : []
                                }
                              >
                                <Select
                                  placeholder={`Select ${label.toLowerCase()}`}
                                  style={{ width: '509.69px', height: '32px' }}
                                  options={options?.map(option => ({
                                    label: option.label,
                                    value: option.value,
                                    key: option.key,
                                  }))}
                                  disabled={isDisabled}
                                  onChange={(value) => {
                                    // If this is the testing required field
                                    if (key === 'testingRequired') {
                                      // Update all products with the same testing flag
                                      const products = form.getFieldValue('products') || [];
                                      products.forEach((product, index) => {
                                        form.setFieldValue(['products', index, 'testingRequired'], value);
                                      });
                                      
                                      // Clear dispatch with results if testing is set to NO
                                      if (!value) {
                                        form.setFieldValue('dispatchWithResults', null);
                                        products.forEach((product, index) => {
                                          form.setFieldValue(['products', index, 'dispatchWithResults'], null);
                                        });
                                      }
                                    }
                                  }}
                                />
                              </Form.Item>

                              {/* Add Dispatch With Results when Testing Required is true */}
                              {key === 'testingRequired' && form.getFieldValue(path?.split('.')) === true && (
                                <div className="mt-4" style={{ marginLeft: '-12px', width: '509.69px' }}>
                                  <div className="flex flex-col p-3 rounded-md hover:bg-gray-100 transition-colors">
                                    <div className="flex items-center space-x-3">
                                      <label className="text-sm font-medium text-black">
                                        Dispatch With Results
                                        <span style={{ color: '#ff4d4f', marginLeft: '4px' }}>*</span>
                                      </label>
                                    </div>
                                    <div className="mt-2">
                                      <Form.Item
                                        name="dispatchWithResults"
                                        validateTrigger={[]}
                                        style={{ marginBottom: 0 }}
                                        rules={[
                                          { 
                                            required: !isDisabled, 
                                            message: 'Please select dispatch with results option' 
                                          }
                                        ]}
                                      >
                                        <Select
                                          placeholder="Select dispatch with results"
                                          style={{ width: '509.69px', height: '32px' }}
                                          disabled={isDisabled}
                                          options={[
                                            { label: 'Yes', value: true },
                                            { label: 'No', value: false }
                                          ]}
                                          onChange={(value) => {
                                            // Update dispatch with results for all products
                                            const products = form.getFieldValue('products') || [];
                                            products.forEach((product, index) => {
                                              form.setFieldValue(['products', index, 'dispatchWithResults'], value);
                                            });
                                          }}
                                        />
                                      </Form.Item>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </>
                          )}

                          {inputType === 'supplierSelect' && (
                            <Form.Item
                              name={path?.split('.')}
                              validateTrigger={[]}
                              style={{ width: '509.69px' }}
                              rules={
                                mandatory && !isDisabled
                                  ? [{ required: true, message: `Please select ${label}` }]
                                  : []
                              }
                            >
                              <SupplierDropdown
                                placeholder={`Select ${label.toLowerCase()}`}
                                style={{ width: '509.69px', height: '32px' }}
                                disabled={isDisabled}
                              />
                            </Form.Item>
                          )}

                          {inputType === 'date' && (
                            <Form.Item
                              name={path?.split('.')}
                              validateTrigger={[]}
                              rules={
                                mandatory
                                  ? [{ required: true, message: 'This field is required' }]
                                  : []
                              }
                            >
                              <DatePicker
                                format={DateFormat}
                                locale={enUS}
                                style={{ width: '100%' }}
                                suffixIcon={
                                  <CalendarOutlined
                                    style={{ color: '#000' }}
                                    disabled={isDisabled}
                                  />
                                }
                              />
                            </Form.Item>
                          )}
                          {inputType === 'file' && (
                            <Form.Item
                              name={path?.split('.')}
                              rules={
                                mandatory
                                  ? [{ required: true, message: 'Please upload at least one file' }]
                                  : []
                              }
                              style={{ marginBottom: '0px' }}
                            >
                              <FileUpload
                                category="Orders"
                                meta={{ documentType: 'label', poId: 123 }}
                                deletedisabled={isDisabled}
                                disabled={isDisabled}
                                isSingle={true}
                              />
                            </Form.Item>
                          )}

                          {inputType === 'document' && (
                            <>
                              {group?.map((docType, index) => (
                                <Form.Item
                                  key={index}
                                  label={docType?.label || docType?.name}
                                  name={
                                    path === 'products[].documents'
                                      ? ['products', 0, 'documents', docType?.name]
                                      : [path, docType?.name]
                                  }
                                  rules={
                                    docType?.mandatory
                                      ? [
                                          {
                                            required: true,
                                            message: `Please upload files for ${
                                              docType?.label || docType?.name
                                            }`,
                                          },
                                        ]
                                      : []
                                  }
                                >
                                  <FileUpload
                                    category="Orders"
                                    meta={{ documentType: docType, poId: 123 }}
                                    deleteDisabled={isDisabled}
                                    disabled={isDisabled}
                                    accept='application/pdf, image/*, .doc, .docx'
                                    multiple={true}
                                  />
                                </Form.Item>
                              ))}
                            </>
                          )}

                          {inputType === 'incoterms' && (
                            <IncotermInput form={form} name={'supplierRfq'} />
                          )}

                          {inputType === 'custom' && (
                            <div>
                              <label className="text-sm text-gray-500">{label}</label>
                              <FormulaField
                                form={form}
                                path={path}
                                formula={formula}
                                dependencies={dependencies}
                                disabled={isDisabled}
                              />
                            </div>
                          )}

                          {inputType === 'packagingType' && (
                            <div className="flex items-start gap-4">
                              <div className="w-1/2">
                                <Form.Item
                                  name={['packaging']}
                                  style={{ marginBottom: 0 }}
                                  rules={[
                                    { required: mandatory, message: 'Please select packaging type' }
                                  ]}
                                >
                                  <Select
                                    className={`${css.selectBox}`}
                                    optionFilterProp="label"
                                    showSearch
                                    allowClear
                                    placeholder="Select packaging type"
                                    disabled={isDisabled}
                                    onChange={(value) => {
                                      const selectedPackage = packagingList.find(pkg => pkg.id === value);
                                      if (selectedPackage?.type === 'Others') {
                                        form.setFieldsValue({
                                          [path ? [...getFieldPath(path), 'otherPackagingDetails'].join('.') : 'otherPackagingDetails']: ''
                                        });
                                      }
                                    }}
                                  >
                                    {packagingList?.map((packageType, index) => (
                                      <Select.Option key={packageType.id} value={packageType.id} label={packageType.type}>
                                        <div className={css.CustomSelectOptn}>
                                          <div className={css.optnTitle}>{packageType.type}</div>
                                          <div className={css.optnDetails}>
                                            <div className={css.optnLabel}>Pack size:</div>
                                            <div className={css.optnValue}>{packageType.packSize}</div>
                                          </div>
                                          <div className={css.optnDetails}>
                                            <div className={css.optnLabel}>Tare weight:</div>
                                            <div className={css.optnValue}>{packageType.tareWeight}</div>
                                          </div>
                                          <div className={css.optnDetails}>
                                            <div className={css.optnLabel}>Dimension:</div>
                                            <div className={css.optnValue}>{packageType.dimension}</div>
                                          </div>
                                        </div>
                                      </Select.Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                              </div>

                              {isOtherPackaging && (
                                <div className="w-1/2">
                                  <Form.Item
                                    name={ ['otherPackagingDetails']}
                                    required={isOtherPackaging}
                                    rules={[
                                      {
                                        required: isOtherPackaging,
                                        message: 'Please specify other packaging details'
                                      }
                                    ]}
                                    style={{ marginBottom: 0 }}
                                  >
                                    <Input
                                      placeholder="Enter other packaging details"
                                      className="w-full"
                                      disabled={isDisabled}
                                    />
                                  </Form.Item>
                                </div>
                              )}
                            </div>
                          )}
           

                          {inputType === 'formulaInput' && (
                            <Row gutter={16}>
                              <Col span={8}>
                                <Form.Item
                                  name={path ? [...getFieldPath(path), 'value'] : ['value']}
                                  validateTrigger={[]}
                                  rules={
                                    mandatory
                                      ? [{ required: true, message: 'value is required' }]
                                      : []
                                  }
                                  initialValue={
                                    path?.includes('logisticsCost')
                                      ? calculateLogisticsCostSum()
                                      : undefined
                                  }
                                >
                                  <InputNumber
                                    placeholder="Enter cost"
                                    style={{ width: '100%' }}
                                    disabled={isDisabled || path?.includes('logisticsCost')} // Also disable the field if it's logisticsCost
                                  />
                                </Form.Item>
                              </Col>
                              {/* Rest of the columns remain unchanged */}
                              <Col span={8}>
                                <Form.Item
                                  name={path ? [...getFieldPath(path), 'currency'] : ['currency']}
                                  validateTrigger={[]}
                                  rules={
                                    mandatory
                                      ? [{ required: true, message: 'Currency is required' }]
                                      : []
                                  }
                                  help={(() => {
                                    if (path.includes('salesOrderValue')) {
                                      return 'Please Ensure same currency as in customer PO';
                                    }

                                    return '';
                                  })()}
                                >
                                  <Select
                                    placeholder="Select currency"
                                    style={{ width: '100%' }}
                                    disabled={isDisabled}
                                    options={[
                                      { value: 'USD', label: 'USD' },
                                      { value: 'EUR', label: 'EUR' },
                                      { value: 'INR', label: 'INR' },
                                      { value: 'JPY', label: 'JPY' },
                                      { value: 'AED', label: 'AED' },
                                      { value: 'CNY', label: 'CNY' },
                                    ]}
                                  />
                                </Form.Item>
                              </Col>
                              {group?.find(item => item.key === 'conversionRate') && (
                                <Col span={8}>
                                  <Form.Item
                                    name={
                                      path
                                        ? [...getFieldPath(path), 'conversionRate']
                                        : ['conversionRate']
                                    }
                                    validateTrigger={[]}
                                    rules={[
                                      {
                                      required: mandatory,
                                      message: 'Conversion factor is required'
                                     },
                                   ({ getFieldValue }) => ({
                                    validator(_, value) {
  
                                      if (value === undefined || value === null) {
                                        return Promise.reject(new Error('Conversion factor is required!'));
                                      }
                                     
                                      if (value <= 0) {
                                        return Promise.reject(new Error(`Conversion factor should be greater than 0!`));
                                      }
                                     
                                      return Promise.resolve();
                                    },
                                  }),                             
                                  
                                    ]}
                                    help={(() => {
                                      const fromCurrency = form.getFieldValue([
                                        ...getFieldPath(path),
                                        'currency',
                                      ]);
                                      const toCurrency = form.getFieldValue([
                                        'supplierRfq',
                                        'salesOrderValue',
                                        'currency',
                                      ]);
                                      const conversionFactor = form.getFieldValue([
                                        ...getFieldPath(path),
                                        'conversionRate',
                                      ]);

                                      if (fromCurrency && toCurrency) {
                                        return `Please enter the conversion rate from ${getCurrencyName(
                                          fromCurrency
                                        )} to ${getCurrencyName(toCurrency)}`;
                                      }
                                      return '';
                                    })()}
                                    dependencies={[
                                      [...getFieldPath(path), 'currency'],
                                      ['supplierRfq', 'salesOrderValue', 'currency'],
                                    ]}
                                  >
                                    <InputNumber
                                      placeholder="Enter conversion factor"
                                      style={{ width: '100%' }}
                                      disabled={isDisabled}
                                    />
                                  </Form.Item>
                                </Col>
                              )}
                            </Row>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </Form>
            </>
          )}
        </div>
        {!isDisabled && record?.entityId && (
          <div className="border-t p-4 flex justify-end gap-6">
            <Button
              onClick={() => {
                trackFormCompletion(false, "Cancel", false);
                setModalOpen(false);
              }}
              icon={<CloseOutlined />}
            >
              Cancel
            </Button>
            {RequiredTaskIds?.length > 0 && (
              <Button
                onClick={async () => {
                  await handleRemarkButtonClick();
                  trackFormCompletion(false, "Redo Task", false);
                }}
                type="primary"
                loading={loading}
              >
                {showRemarksForm ? 'Sumbit Redo' : 'Redo'}
              </Button>
            )}
            {record?.name === 'Invoice Upload' && (
              <Button
                onClick={handleUploadInvoices}
                type="primary"
                loading={loading}
                disabled={!hasPermission(userPermissionsList.uploadInvoice, user.permissions)}
              >
                Upload Invoices
              </Button>
            )}
            {record?.name !== "Invoice Upload" && !disableSupplierRFQEdit && <Button type="primary" icon={<CheckOutlined />} onClick={handleSaveData}>
              Save as Draft
            </Button>}
            {record?.name !== "Invoice Upload" && !disableSupplierRFQEdit && ( <Button type="primary" icon={<CheckOutlined />} onClick={handleCompleteTask}>
              {record?.dependencyResolved ? 'Complete Task' : 'Move To In Progress'}
            </Button>)}
          </div>
        )}
      </Modal>
    </>
  );
};

TaskCompletionPopup.propTypes = {
  taskData: PropTypes.object.isRequired,
  taskUpdated: PropTypes.func.isRequired,
};

export default TaskCompletionPopup;
