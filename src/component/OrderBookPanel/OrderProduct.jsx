import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Button,
  Divider,
} from 'antd';
import { DateFormat, formModes, productUOMList } from '../../constants/formConstant';
import { CloseOutlined, CalendarOutlined } from '@ant-design/icons';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import IncotermInput from '../IncotermInput/IncotermInput';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';
import DocumentUpload from './DocumentUpload';

const OrderProduct = props => {
  const {
    form,
    field,
    index,
    formView,
    remove,
    productList,
    packagingList,
    initialValue,
    setInitialValue,
    updateDefaultIncoterms,
  } = props;

  const formProductIdWatch = Form.useWatch(['products', field.name, 'product'], form);

  useEffect(() => {
    if (formProductIdWatch) {
      const productObj = productList.find(prod => prod.id === formProductIdWatch);
      if (productObj && productObj.tradeName) setInitialValue(productObj);
    }
  }, [formProductIdWatch]);

  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={
        <CloseOutlined
          onClick={() => {
            remove(field.name);
          }}
        />
      }
    >
      <Row gutter={16}>
        <Col span={24}>
          {productList.length ? (
            <Form.Item
              name={[field.name, 'product']}
              label="Product Name"
              rules={[
                {
                  required: true,
                  message: 'Please select product!',
                },
              ]}
            >
              <Select showSearch optionFilterProp="label" disabled={formView === formModes.UPDATE}>
                {productList.map(product => (
                  <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                    {product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'uom']}
            label="Unit of measurement(UOM)"
            rules={[
              {
                required: true,
                message: 'Please select UOM!',
              },
            ]}
          >
            <Select disabled={formView === formModes.UPDATE}>
              {productUOMList.map(uom => (
                <Select.Option key={uom.key} value={uom.value}>
                  {uom.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Customer Product code" name={[field.name, 'customerProductCode']}>
            <Input style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="HS Code"
            // rules={[
            //   {
            //     required: true,
            //     message: 'Please input hscode!',
            //   },
            // ]}
            name={[field.name, 'hsCode']}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Price per unit"
            name={[field.name, 'price']}
            rules={
              initialValue?.inventoryId
                ? []
                : [
                    {
                      required: true,
                      message: 'Please input price!',
                      type: 'number',
                      validator(_, value) {
                        if (value === undefined || value === null) return Promise.resolve();
                        if (value < 0) {
                          return Promise.reject(new Error('Price cannot be less then zero'));
                        }
                        return Promise.resolve();
                      }
                    },
                  ]
            }
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Quantity"
            name={[field.name, 'quantity']}
            rules={[
              {
                required: true,
                message: 'Please select quantity!',
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} disabled={formView === formModes.UPDATE} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Units"
            name={[field.name, 'units']}
            rules={[
              {
                required: true,
                message: 'Please enter units!',
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} disabled={formView === formModes.UPDATE} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <IncotermInput field={field} form={form} parentKey="products" />
        </Col>
        {index == 0 ? (
          <Col span={24}>
            <Checkbox
              onClick={e => {
                updateDefaultIncoterms(e);
              }}
            >
              Set above incoterm as default
            </Checkbox>
          </Col>
        ) : null}
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Expected Delivery date" name={[field.name, 'expectedDeliveryDate']}>
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <PackagingSelect
            packagingList={packagingList}
            field={field}
            disableCustomPackaging
            disabled={formView === formModes.UPDATE}
          />
        </Col>
      </Row>
      <Divider className={CSS.divider} />
      <Row gutter={16}>
        <Col span={24}>
          <Form.List label="Add Documents" name={[field.name, 'documents']}>
            {(subFields, { add, remove }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                  marginBottom: '24px',
                }}
              >
                <h2 className="text-xl font-semibold text-gray-800">
                  Documents{' '}
                  <span className="text-sm text-gray-500">
                    (Attach COA, costing document, and others as required)
                  </span>
                </h2>
                <Button type="dashed" onClick={() => add()} block>
                  + Add Documents
                </Button>
                {subFields.map((subField, index) => {
                  return (
                    <DocumentUpload
                      key={index}
                      field={field}
                      index={index}
                      remove={remove}
                      subField={subField}
                      productCertificatesType={['select']}
                    />
                  );
                })}
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name,'marginPercentage']} label="margin Percentage">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name,'marginApprovedBy']} label="margin Approved By">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name,'marginApprovedOn']} label="margin Approved On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name={[field.name, 'remarks']} label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              savetimeStamp
              showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );
};

export default OrderProduct;

OrderProduct.propTypes = {
  form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  formView: PropTypes.string.isRequired,
  remove: PropTypes.func.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  initialValue: PropTypes.object,
  setInitialValue: PropTypes.func,
  updateDefaultIncoterms: PropTypes.func,
};
