import { Col, Form, Input, InputNumber, Row, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { countryList } from '../../constants/formConstant';
import css from '../CustomerPanel/CustomerForm.module.css';
import MobileInput from '../MobileInput';

const SupplierBasicInfo = props => {
  const { form, initialValue } = props;

  useEffect(() => {
    if (initialValue) {
      form.setFieldsValue(initialValue);
    }
  }, [initialValue]);

  return (
    <Form
      className={css.formContainer}
      name="order_basic_info"
      layout="vertical"
      scrollToFirstError
      initialValues={initialValue}
      preserve={false}
      size="large"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Supplier Name"
            name="name"
            rules={[{ required: true, message: 'Supplier name required!' }]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Corporate Address"
            name={['address', 'street']}
            rules={[
              {
                required: true,
                message: 'Address required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="Country"
            name={['address', 'country']}
            rules={[
              {
                required: true,
                message: 'Country required!',
              },
            ]}
          >
            <Select showSearch optionLabelProp="value">
              {countryList.map((country, index) => (
                <Select.Option key={index} value={country.name} label={country.name}>
                  {`${country.flag} ${country.name}`}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="State"
            name={['address', 'state']}
            rules={[
              {
                required: true,
                message: 'State required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            label="City"
            name={['address', 'city']}
            rules={[
              {
                required: true,
                message: 'City required!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Postal Code" name={['address', 'postalCode']}>
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="Email Id" name="email">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="GSTIN" name="gstin">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="PAN" name="pan">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <MobileInput isRequired={true} />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Revenue " name="revenue">
            <InputNumber
              min="0.00"
              addonBefore="$"
              addonAfter="million"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>      
    </Form>
  );
};

export default SupplierBasicInfo;

SupplierBasicInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
};
