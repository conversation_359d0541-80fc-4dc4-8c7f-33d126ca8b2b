import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Card,
  Col,
  Form,
  DatePicker,
  Input,
  InputNumber,
  Row,
  Select,
  // Checkbox,
  Switch,
} from 'antd';
// import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import {
  DateFormat,
  billingAddressList,
  deliveyTermList,
  modeOfDeliveryList,
  paymentTermsDate,
  taxTypeList,
} from '../../constants/formConstant';
import { CloseOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import TextArea from 'antd/es/input/TextArea';

const SupplierOrderPaymentInfo = props => {
  const { form, initialValue, mode } = props;
  const [taxTypes, setTaxTypes] = useState([...taxTypeList]);

  const deliverTermWatch = Form.useWatch('deliveryTerm', form);
  const getFormPrefilldValues = () => {
    const supplierState = initialValue?.supplierData?.address?.state;
    if (supplierState && supplierState.length > 0) {
      let filteredTaxTypes = [];
      if (supplierState.toLowerCase() == 'maharashtra') {
        filteredTaxTypes = taxTypes.filter(taxType => {
          return taxType.label != 'IGST';
        });
      } else {
        filteredTaxTypes = taxTypes.filter(taxType => {
          return taxType.label == 'IGST';
        });
      }
      setTaxTypes(filteredTaxTypes);
    }
    return initialValue
      ? {
          shippingAddress: initialValue.shippingAddress,
          billingAddress: initialValue.billingAddress,
          currencyType: initialValue.currencyType,
          paymentTerms: { ...initialValue?.paymentTerms },
          taxList: initialValue?.taxList,
          modeOfDelivery: initialValue?.modeOfDelivery,
          deliveryTerm: initialValue?.deliveryTerm,
          deliveryLocation: initialValue?.deliveryLocation,
          pickupLocation: initialValue?.pickupLocation,
          deliverySchedule: initialValue?.deliverySchedule?.map(item => ({
            ...item,
            deliveryDate: item?.deliveryDate ? dayjs(item.deliveryDate) : '',
          })),
          additionalCondition: initialValue?.additionalCondition,
          termAndCondition: initialValue?.termAndCondition,
          partialShipment: initialValue?.partialShipment,
          transShipment: initialValue?.transShipment,
          excludeDefaultTc: initialValue?.excludeDefaultTc,
          mrd: initialValue.mrd ? dayjs(initialValue.mrd) : '',
          advPaymentDate: initialValue?.advPaymentDate ? dayjs(initialValue.advPaymentDate) : '',
          releasePaymentMadeOn: initialValue?.releasePaymentMadeOn ? dayjs(initialValue.releasePaymentMadeOn) : '',
        }
      : {};
  };

  useEffect(() => {
    if (initialValue && initialValue.paymentTerms) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);

  return (
    <Form name="order_payment_info" layout="vertical" scrollToFirstError size="middle" form={form}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditAmount']}
            label="Credit amount(in %)"
            rules={[
              {
                required: true,
                message: 'Credit amount can only be a valid number!',
                type: 'number',
              },
              () => ({
                validator(_, value) {
                  if (value !== undefined && value > 100) {
                    return Promise.reject(new Error(`credit can't be greater then 100!`));
                  } else if (value !== undefined && value <= 100) return Promise.resolve();

                  return Promise.reject(new Error('Unable to validate!'));
                },
              }),
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'startDate']}
            label="Payment terms should be based on?"
            rules={[
              {
                required: true,
                message: 'Please input start date!',
              },
            ]}
          >
            <Select>
              {paymentTermsDate.map(date => (
                <Select.Option key={date.key} value={date.value}>
                  {date.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditorDays']}
            label="Credit days"
            rules={[
              {
                required: true,
                message: 'Credit days can only be a valid number!',
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.List name="taxList" label="Tax Type">
            {(fields, { add, remove }, { errors }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                }}
              >
                {fields.map((field, index) => {
                  return (
                    <Card
                      size="small"
                      title={`Tax Type ${index + 1}`}
                      key={field.key}
                      extra={
                        <CloseOutlined
                          onClick={() => {
                            remove(field.name);
                          }}
                        />
                      }
                    >
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name={[field.name, 'taxType']}
                            label="Select Tax Type"
                            rules={[{ required: true, message: 'Please select a tax type' }]}
                          >
                            <Select>
                              {taxTypes.map(type => (
                                <Select.Option key={type.key} value={type.value} label={type.label}>
                                  {type.label}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name={[field.name, 'taxPercent']}
                            label="Enter Tax Percent"
                            rules={[
                              {
                                required: true,
                              },
                            ]}
                          >
                            <InputNumber />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Card>
                  );
                })}
                {fields.length < taxTypeList.length ? (
                  <Button type="dashed" onClick={() => add()} block>
                    + Add Tax
                  </Button>
                ) : null}
                <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
      {/* <Row gutter={16}>
        <Col span={24}>
          <Form.Item required name={'gstPercent'} label="Gst percent">
            <Select>
              {gstList.map((gst,index)=>{
                return (
                  <Select.Option key={index} value={gst.value} label={gst.label}>
                    {gst.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
      </Row> */}
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item required name="shippingAddress" label="Shipping address">
            <Input.TextArea />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="billingAddress" required label="billing address">
            <Select>
              {billingAddressList.map((billingAddress, index) => {
                return (
                  <Select.Option
                    key={index}
                    value={billingAddress.value}
                    label={billingAddress.label}
                  >
                    {billingAddress.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item required name={'modeOfDelivery'} label="Mode of Delivery">
            <Select>
              {modeOfDeliveryList.map((mode, index) => {
                return (
                  <Select.Option key={index} value={mode.value} label={mode.label}>
                    {mode.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item required name={'deliveryTerm'} label="Delivery Term">
            <Select>
              {deliveyTermList.map((term, index) => {
                return (
                  <Select.Option key={index} value={term.value} label={term.label}>
                    {term.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Col>
        {deliverTermWatch && deliverTermWatch.length ? (
          <Col span={12}>
            <Form.Item required name={'deliveryLocation'} label="Delivery Location">
              <Input />
            </Form.Item>
          </Col>
        ) : null}
      </Row>
      {deliverTermWatch && deliverTermWatch.length ? (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item required name={'pickupLocation'} label="Pickup Location">
              <Input />
            </Form.Item>
          </Col>
        </Row>
      ) : null}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name={'partialShipment'} label="Partial Shipment">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name={'transShipment'} label="Trans Shipment">
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name={'excludeDefaultTc'} label="Disable default t&c">
            <Switch />
          </Form.Item>
        </Col>
      </Row>
      {initialValue?.productList?.length ? (
        <Row gutter={16}>
          <Col span={24}>
            <Form.List name="deliverySchedule" label="Delivery Schedule">
              {(fields, { add, remove }, { errors }) => (
                <div
                  style={{
                    display: 'flex',
                    rowGap: 16,
                    flexDirection: 'column',
                  }}
                >
                  {fields.map((field, index) => {
                    return (
                      <Card
                        size="small"
                        title={`Schedule ${index + 1}`}
                        key={field.key}
                        extra={
                          <CloseOutlined
                            onClick={() => {
                              remove(field.name);
                            }}
                          />
                        }
                      >
                        <Row gutter={16}>
                          <Col span={24}>
                            <Form.Item
                              name={[field.name, 'productName']}
                              label="Select Product"
                              rules={[{ required: true, message: 'Please select a product' }]}
                            >
                              <Select>
                                {initialValue.productList.map(product => (
                                  <Select.Option
                                    key={product.id}
                                    value={product.tradeName}
                                    label={product.tradeName}
                                  >
                                    {product.tradeName}
                                  </Select.Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name={[field.name, 'quantity']}
                              label="Enter Quantity"
                              rules={[
                                {
                                  required: true,
                                },
                              ]}
                            >
                              <InputNumber />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name={[field.name, 'deliveryDate']}
                              label="Select Date"
                              rules={[
                                {
                                  required: true,
                                },
                              ]}
                            >
                              <DatePicker
                                format={DateFormat}
                                style={{ width: '100%' }}
                                suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={24}>
                            <Form.Item name={[field.name, 'remarks']} label="Remarks">
                              <Input.TextArea />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    );
                  })}
                  {fields.length < taxTypeList.length ? (
                    <Button type="dashed" onClick={() => add()} block>
                      + Add Delivery Schedule
                    </Button>
                  ) : null}
                  <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                </div>
              )}
            </Form.List>
          </Col>
        </Row>
      ) : null}
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="additionalCondition" label="Additional Condition">
            <TextArea type="TextArea" mode={mode} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="mrd" label="MRD">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="advPaymentAmount" label="advance Payment Amount">
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="advPaymentMadeBy" label="advance Payment MadeBy">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="advPaymentDate" label="advance Payment Date">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="releasePaymentMadeBy" label="release Payment Made By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="releasePaymentMadeOn" label="release Payment Made On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SupplierOrderPaymentInfo;

SupplierOrderPaymentInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  mode: PropTypes.string.isRequired,
};
