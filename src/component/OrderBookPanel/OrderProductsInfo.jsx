import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Form, Row } from 'antd';
// import { formConfigName } from '../../constants/formConstant';
import { getProductList } from '../../service/api/productApi';
// import { getFormConfigFromName } from '../../service/api/formConfig';
import OrderProduct from './OrderProduct';
import { useDispatch, useSelector } from 'react-redux';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';
import PageLoader from '../Loaders/PageLoader';
import dayjs from 'dayjs';
import { getAllPackagingDetails } from '../../service/api/packagingApi';

const OrderProductsInfo = props => {
  const { form, initialValue, formView, setInitialValue } = props;

  const [productList, setProductList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [defaultIncoterms, setDefaultIncoterms] = useState({});

  const packagingList = useSelector(state => state.packagingList);
  const dispatch = useDispatch();

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };
  const updateDefaultIncoterms = e => {
    if (e?.target?.checked) {
      const formData = form.getFieldsValue();
      const products = formData?.products;
      if (products && products.length > 0) {
        setDefaultIncoterms(products[0]?.incoterms);
      }
    } else {
      setDefaultIncoterms({});
    }
  };
  const setDefaultIncotermForProduct = index => {
    if (index != 0) {
      const { products } = form.getFieldsValue('products');
      let updatedProductsData = [...products];
      // only update if incoterm is not present
      if (updatedProductsData && !updatedProductsData[index].incoterms) {
        // set the product incoterm
        updatedProductsData[index].incoterms = { ...defaultIncoterms };
        form.setFieldsValue({ products: [...updatedProductsData] });
      }
    }
  };

  const getFormPrefilldValues = () => {
    if (!initialValue || !initialValue?.products) return {};

    console.log("hereee",initialValue)

    return {
      products: initialValue.products.map(prod => ({
        ...prod,
        product: prod.product,
        uom: prod.uom,
        price: prod.price,
        quantity: prod.quantity,
        units: prod.units,
        margin: prod.margin,
        packaging: getPackagingFormValue(prod.packaging, packagingList),
        otherPackagingDetails: prod.packaging?.otherPackagingDetails || '',
        // otherPackagingDetails: prod.otherPackagingDetails || (prod.packaging && prod.packaging.otherPackagingDetails) || '',
        incoterms: prod.incoterms
          ? {
              ...prod.incoterms,
              data: {
                ...prod.incoterms.data,
              },
            }
          : {},
        expectedDeliveryDate: prod.expectedDeliveryDate ? dayjs(prod.expectedDeliveryDate) : '',
        marginApprovedOn: prod.marginApprovedOn ? dayjs(prod.marginApprovedOn) : '',
        remarks: prod.remarks ? [...prod.remarks] : [],
        documents: Array.isArray(prod.documents) // ✅ Check if already converted
          ? prod.documents
          : Object.entries(prod.documents || {}).flatMap(([documentType, files]) =>
              (Array.isArray(files) ? files : []).map(file => ({
                documentType,
                files: [file], // Convert back to array format
              }))
            ),
      })),
    };
  };

  useEffect(() => {
    setLoading(true);
    Promise.all([getProductList(), getAllPackagingDetails()])
      .then(responseList => {
        if (responseList[1].data) {
          const pkgList = responseList[1].data.map(pkg => ({
            ...pkg,
            id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
          }));
          dispatch(setPackagingList(pkgList));
        }
        if (responseList[0]?.data && responseList[0].data?.content) {
          setProductList(responseList[0].data.content);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      packagingList &&
      packagingList.length &&
      initialValue &&
      initialValue.products &&
      initialValue.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [productList]);

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Form name="order_product_info" layout="vertical" scrollToFirstError size="middle" form={form}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.List
            name="products"
            rules={[
              {
                required: true,
                message: 'At least 1 product details is required!',
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                }}
              >
                {fields.map((field, index) => {
                  return (
                    <OrderProduct
                      key={index}
                      form={form}
                      field={field}
                      index={index}
                      formView={formView}
                      remove={remove}
                      productList={productList}
                      packagingList={packagingList}
                      initialValue={initialValue}
                      setInitialValue={data => {
                        setDefaultIncotermForProduct(index);
                        setInitialValue(prevState => {
                          return {
                            ...prevState,
                            productList: [...(prevState.productList || []), data],
                          };
                        });
                      }}
                      updateDefaultIncoterms={updateDefaultIncoterms}
                    />
                  );
                })}
                <Button type="dashed" onClick={() => add()} block>
                  + Add Product
                </Button>
                <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
    </Form>
  );
};

export default OrderProductsInfo;

OrderProductsInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
  setInitialValue: PropTypes.func,
};
