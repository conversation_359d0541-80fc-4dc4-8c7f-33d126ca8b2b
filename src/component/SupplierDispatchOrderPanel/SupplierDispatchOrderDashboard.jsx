import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setFormView } from '../../store/actions/formView';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import SupplierDispatchOrderListView from './SupplierDispatchOrderListView';
import SupplierDispatchOrderForm from './SupplierDispatchOrderForm';

const SupplierDispatchOrderDashboard = () => {
  const dispatch = useDispatch();
  const formView = useSelector(state => state.formView);

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const setDispatchOrderFormView = status => {
    dispatch(setFormView(status));
  };

  return (
    <>
      {formView.show ? (
        <SupplierDispatchOrderForm
          collapseAsideBarHandler={collapseAsideBarHandler}
          setDispatchOrderFormView={setDispatchOrderFormView}
        />
      ) : (
        <SupplierDispatchOrderListView
          collapseAsideBarHandler={collapseAsideBarHandler}
          collapseSideDrawerHandler={collapseSideDrawerHandler}
          setDispatchOrderFormView={setDispatchOrderFormView}
        />
      )}
    </>
  );
};

export default SupplierDispatchOrderDashboard;
