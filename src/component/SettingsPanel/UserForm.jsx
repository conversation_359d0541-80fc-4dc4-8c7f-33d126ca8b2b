/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import PageLoader from '../Loaders/PageLoader';
import { formModes } from '../../constants/formConstant';
import { addEmployeeUser, updateEmployeeUser } from '../../service/api/employee';
import { getAllUserRoles } from '../../service/api/UserRolesApi';

const UserForm = (props) => {

  const { mode, user, hide } = props;

  const [loading, setLoading] = useState(false);
  const [userRoles, setUserRoles] = useState([]);

  const [form] = Form.useForm();
  const passwordWatch = Form.useWatch("password", form);

  const saveEmployeeDetails = (employeeData) => {
    setLoading(true);
    if (mode === formModes.CREATE) {
      addEmployeeUser(employeeData).then(() => {
        setLoading(false);
        hide();
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
    } else if (mode === formModes.UPDATE) {
      updateEmployeeUser(employeeData, user?.id)
        .then(() => {
          setLoading(false);
          hide();
          // attach btn loader
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }
  const validateFormandSubmit = () => {
    form.validateFields().then((values) => {
      saveEmployeeDetails(values);
    })
    .catch(error => console.log(error));
  }

  useEffect(() => {
    if (!userRoles?.length) {
      setLoading(true);
      getAllUserRoles().then((res) => {
        if (res.data) {
          setUserRoles(res.data);
          setLoading(false);
        }
      }).catch((err) => {
        console.log(err);
        setLoading(false);
      })
    }
  }, [])

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
      <Form
        name="customer_form"
        layout="vertical"
        scrollToFirstError
        initialValues={mode === formModes.CREATE ? {} : user}
        preserve={false}
        size="middle"
        form={form}
        validateTrigger="onBlur"
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="User Name" name="name" rules={[{ required: true }]}>
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="email" label="Email" rules={[{ type: 'email' }, { required: true }]}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
        {mode === formModes.CREATE ? 
        <>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="password" label="Password" rules={[{ required: true }]}>
                <Input.Password placeholder="Input password" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="rePassword"
                label="Confirm Password"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (value !== passwordWatch ) {
                        return Promise.reject('Password do not match');
                      }                    
                      return Promise.resolve();
                    },
                  },
                ]}
                
              >
                <Input.Password placeholder="Confirm password" />
              </Form.Item>
            </Col>
          </Row>
        </>
        : null}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Select user roles"
              name="permissionGroups"
              rules={[{ required: true, message: 'Please select a role' }]}
            >
              <Select mode="multiple" showSearch allowClear>
                {userRoles.map(role => (
                  <Select.Option key={role.id} value={role.name}>
                    {role.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div className='absolute left-0 bottom-0 w-full p-2 flex justify-between bg-base-white z-10 box-border'>
        <Button
          type="primary"
          size="large"
          onClick={() => validateFormandSubmit()}
          loading={loading}
        >
          {mode === formModes.CREATE ? 'Add User' : 'Save Changes'}
        </Button>
      </div>
    </>
  )
}

export default UserForm;

UserForm.propTypes = {
  mode: PropTypes.string.isRequired,
  user: PropTypes.object,
  hide: PropTypes.func.isRequired,
}