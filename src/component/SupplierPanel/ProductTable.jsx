import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Select, Modal, Cascader } from 'antd';
import { countryList, formModes } from '../../constants/formConstant';
import PropTypes from 'prop-types';
import SupplierCertificateUpload from './SupplierCertificateUpload';
import SupplierDocumentUpload from './SupplierDocumentUpload';
import { getFormConfigFromName } from '../../service/api/formConfig';
import { formConfigName } from '../../constants/formConstant';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import css from './ProductTable.module.css';
import { CloseOutlined } from '@ant-design/icons';

const ProductTable = ({ form, formView, packagingList, productList }) => {
  const [focusedField, setFocusedField] = useState(null);
  const [productCertificatesType, setProductCertificatesType] = useState([]);
  const [productDocumentsType, setProductDocumentsType] = useState([]);
  const [hazLevel, setHazLevel] = useState([]);
  const [errors, setErrors] = useState({});
  const [selectedField, setSelectedField] = useState(null); // Store index & modal type

  const [headers] = useState([
    { label: 'Product Name', required: true },
    { label: 'Plant Address', required: false },
    { label: 'Packaging', required: true },
    { label: 'Country', required: true },
    { label: 'State', required: false },
    { label: 'City', required: false },
    { label: 'Postal Code', required: false },
    { label: 'Capacity available (in metric tonnes)', required: false },
    { label: 'Total capacity (in metric tonnes)', required: false },
    { label: 'Lead Time', required: false },
    { label: 'Typical Order Size', required: false },
    { label: 'HS Code', required: false },
    { label: 'Hazardous', required: false },
    { label: 'Hazardous Level', required: false },
    { label: 'Documents', required: false },
    { label: 'Remarks', required: false },
  ]);

  const openModal = (index, type) => {
    setSelectedField({ index, type }); // Set both index and modal type
  };

  const closeModal = () => {
    setSelectedField(null); // Close modal
  };

  const hazardousValues = Form.useWatch('products', form) || [];

  useEffect(() => {
    const fetchListApi = [
      getFormConfigFromName(formConfigName.productCertificates),
      getFormConfigFromName(formConfigName.productDocuments),
      getFormConfigFromName(formConfigName.hazardousLevel),
    ];
    Promise.all(fetchListApi)
      .then(res => {
        if (res?.[0]?.data?.value) {
          setProductCertificatesType(res?.[0].data.value);
        }
        if (res?.[1]?.data?.value) {
          console.log("hello",res?.[1].data.value)
          setProductDocumentsType(res?.[1].data.value);
        }
        if (res?.[2]?.data?.value) {
          setHazLevel(res?.[2].data.value);
        }
      })
      .catch(error => {
        console.log(error);
      });
  }, []);

  const isRowFilled = row => {
    return Object.values(row).some(value => value && value.toString().trim() !== '');
  };

  const scrollRef = useRef(null);
  const [indicatorWidth, setIndicatorWidth] = useState(5); // Initial width
  const [indicatorLeft, setIndicatorLeft] = useState(0); // Initial position
  useEffect(() => {
    if (!scrollRef.current) return;

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      const visibleRatio = clientWidth / scrollWidth; // How much of the table is visible
      const newWidth = visibleRatio * 100; // Convert to percentage
      const newLeft = (scrollLeft / scrollWidth) * 100; // Position of indicator

      setIndicatorWidth(Math.max(newWidth, 10)); // Ensure it's always visible
      setIndicatorLeft(newLeft);
    };

    const scrollDiv = scrollRef.current;
    scrollDiv?.addEventListener('scroll', handleScroll);
    handleScroll(); // Run once initially

    return () => scrollDiv?.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div style={{ position: 'relative', padding: '16px', margin: '0px 0px 12px 0px' }}>
      <div
        style={{
          position: 'absolute',
          bottom: '4px',
          width: '95%',
          height: '6px',
          background: '#e0e0e0',
          borderRadius: '2px',
          overflow: 'hidden',
          margin: '0px 8px',
          left: '50%', // Move to center
          transform: 'translateX(-50%)', // Shift back by half of its width
        }}
      >
        <div
          style={{
            width: `${indicatorWidth}%`,
            left: `${indicatorLeft}%`,
            height: '100%',
            background: 'rgba(35, 86, 138, 0.7)',
            position: 'absolute',
            transition: 'left 0.3s ease-out, width 0.3s ease-out', // Smoother easing
            borderRadius: '2px',
          }}
        ></div>
        {/* Scroll text with subtle bounce animation */}
      </div>
      <div
        style={{
          position: 'absolute',
          right: '16px', // Align text to the right end
          top: '0px', // Move slightly above the bar
          fontSize: '16px',
          color: 'rgba(35, 86, 138, 0.7)',
          padding: '4px',
          fontWeight: 'bold',
          animation: 'bounceText 1.5s infinite alternate ease-in-out', // Bouncing effect
          cursor: 'pointer',
        }}
        onClick={() => {
          const scrollContainer = document.getElementById('scrollableTable');
          if (scrollContainer) {
            scrollContainer.scrollTo({
              left: scrollContainer.scrollWidth, // Scroll to the end
              behavior: 'smooth', // Smooth scrolling effect
            });
          }
        }}
      >
        Scroll →
      </div>

      {/* Keyframes for bounce animation */}
      <style>
        {`
      @keyframes bounceText {
        0% { transform: translateY(0); }
        100% { transform: translateY(-3px); } /* Moves text slightly up */
      }
    `}
      </style>
      <div className={css.ScrollContainer} ref={scrollRef} id="scrollableTable">
        <table
          border="1"
          style={{ width: '100%', borderCollapse: 'collapse' }}
          className={css.productTable}
        >
          <thead>
            <tr>
              {headers.map((header, index) => (
                <th
                  key={index}
                  style={{
                    width: '150px',
                    fontSize: 16,
                    fontWeight: '500',
                    color: 'rgba(35, 86, 138, 0.7)',
                    padding: '0px 6px',
                    textAlign: 'center',
                  }}
                >
                  {header.required ? (
                    <>
                      <span style={{ color: 'red' }}>*</span> {header.label}
                    </>
                  ) : (
                    header.label
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <Form.List name="products">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name }, index) => {
                    const isHazardous = hazardousValues?.[name]?.hazardous;

                    return (
                      <tr key={key}>
                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {/* Small cross icon button for removing */}
                            <Button
                              type="text"
                              icon={<CloseOutlined style={{ color: 'red' }} />}
                              onClick={() => remove(name)}
                              size='small'
                            />

                            {productList && productList.length ? (
                              <Form.Item
                                name={[name, 'product']}
                                style={{ marginBottom: 0, display: 'block' }}
                                rules={[
                                  ({ getFieldValue }) => ({
                                    validator(_, value) {
                                      const rowValues = getFieldValue(['products', name]);
                                      if (isRowFilled(rowValues) && !value) {
                                        return Promise.reject();
                                      }
                                      return Promise.resolve();
                                    },
                                  }),
                                ]}
                              >
                                <Select
                                  style={{
                                    width: focusedField === `${index}-product` ? '350px' : '150px',
                                  }}
                                  showSearch
                                  optionFilterProp="label"
                                  onFocus={() => setFocusedField(`${index}-product`)}
                                  onBlur={() => setFocusedField(null)}
                                >
                                  {productList.map(product => (
                                    <Select.Option
                                      key={product.id}
                                      value={product.id}
                                      label={product.tradeName}
                                    >
                                      {product.tradeName}
                                    </Select.Option>
                                  ))}
                                </Select>
                              </Form.Item>
                            ) : null}
                          </div>
                        </td>
                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'address', 'street']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-street` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-street`)}
                              onBlur={() => setFocusedField(null)}
                            />
                          </Form.Item>
                        </td>
                        {packagingList.length > 0 && (
                          <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
                            <Form.Item
                              name={[name, 'packaging']}
                              style={{ marginBottom: 0, display: 'block' }}
                            >
                              <Select
                                mode="multiple"
                                className={css.selectBox}
                                style={{
                                  width: focusedField === `${index}-packaging` ? '350px' : '150px',
                                }}
                                optionFilterProp="label"
                                onFocus={() => setFocusedField(`${index}-packaging`)}
                                onBlur={() => setFocusedField(null)}
                              >
                                {packagingList.map((packageType, index) => (
                                  <Select.Option
                                    key={index}
                                    value={packageType.id}
                                    label={packageType.type}
                                  >
                                    <div className={css.CustomSelectOptn}>
                                      <div className={css.optnTitle}>{packageType.type}</div>
                                      <div className={css.optnDetails}>
                                        <div className={css.optnLabel}>Pack size:</div>
                                        <div className={css.optnValue}>{packageType.packSize}</div>
                                      </div>
                                      <div className={css.optnDetails}>
                                        <div className={css.optnLabel}>Tare weight:</div>
                                        <div className={css.optnValue}>
                                          {packageType.tareWeight}
                                        </div>
                                      </div>
                                      <div className={css.optnDetails}>
                                        <div className={css.optnLabel}>Dimension:</div>
                                        <div className={css.optnValue}>{packageType.dimension}</div>
                                      </div>
                                    </div>
                                  </Select.Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </td>
                        )}
                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'address', 'country']}
                            style={{ marginBottom: 0, display: 'block' }}
                            rules={[
                              ({ getFieldValue }) => ({
                                validator(_, value) {
                                  const rowValues = getFieldValue(['products', name]);
                                  if (isRowFilled(rowValues) && !value) {
                                    setErrors(prevErrors => ({
                                      ...prevErrors,
                                      [name]: { ...prevErrors[name], country: 'Country required!' },
                                    }));
                                    return Promise.reject();
                                  }
                                  setErrors(prevErrors => ({
                                    ...prevErrors,
                                    [name]: { ...prevErrors[name], country: null },
                                  }));
                                  return Promise.resolve();
                                },
                              }),
                            ]}
                          >
                            <Select
                              style={{
                                width: focusedField === `${index}-country` ? '350px' : '150px',
                              }}
                              showSearch
                              optionFilterProp="label"
                              onFocus={() => setFocusedField(`${index}-country`)}
                              onBlur={() => setFocusedField(null)}
                            >
                              {countryList.map((country, index) => (
                                <Select.Option
                                  key={index}
                                  value={country.name}
                                  label={country.name}
                                >
                                  {`${country.flag} ${country.name}`}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                          {/* Show error only for this row */}
                          {errors[name]?.country && (
                            <p style={{ color: 'red', margin: 0 }}>{errors[name].country}</p>
                          )}
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'address', 'state']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-state` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-state`)}
                              onBlur={() => setFocusedField(null)}
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'address', 'city']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-city` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-city`)}
                              onBlur={() => setFocusedField(null)}
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'address', 'postalCode']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-postalCode` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-postalCode`)}
                              onBlur={() => setFocusedField(null)}
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'capacityAvailable']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width:
                                  focusedField === `${index}-capacityAvailable` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-capacityAvailable`)}
                              onBlur={() => setFocusedField(null)}
                              type="number"
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'totalCapacity']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width:
                                  focusedField === `${index}-totalCapacity` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-totalCapacity`)}
                              onBlur={() => setFocusedField(null)}
                              type="number"
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'leadTime']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-leadTime` ? '150px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-leadTime`)}
                              onBlur={() => setFocusedField(null)}
                              type="number"
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'typicalOrderSize']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width:
                                  focusedField === `${index}-typicalOrderSize` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-typicalOrderSize`)}
                              onBlur={() => setFocusedField(null)}
                              type="number"
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'hsCode']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Input
                              style={{
                                width: focusedField === `${index}-hsCode` ? '350px' : '150px',
                              }}
                              onFocus={() => setFocusedField(`${index}-hsCode`)}
                              onBlur={() => setFocusedField(null)}
                            />
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'hazardous']}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Select>
                              <Select.Option value={true}>Yes</Select.Option>
                              <Select.Option value={false}>No</Select.Option>
                            </Select>
                          </Form.Item>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          <Form.Item
                            name={[name, 'hazardousLevel']}
                            onFocus={() => setFocusedField(`${index}-hazardousLevel`)}
                            onBlur={() => setFocusedField(null)}
                            style={{ marginBottom: 0, display: 'block' }}
                          >
                            <Cascader
                              options={hazLevel}
                              placement="bottomLeft"
                              style={{
                                width:
                                  focusedField === `${index}-hazardousLevel` ? '350px' : '150px',
                              }}
                            />
                          </Form.Item>
                        </td>

                        <td
                          style={{
                            padding: '4px',
                            textAlign: 'center',
                            verticalAlign: 'top',
                            height: '100%',
                          }}
                        >
                          {/* Button to Open Overlay */}
                          <Button
                            type="primary"
                            onClick={() => openModal(index, 'documents')}
                            style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
                          >
                            Add
                          </Button>

                          {/* Modal for Address Input */}
                          <Modal
                            title="Add Documents"
                            open={
                              selectedField?.index === index && selectedField?.type === 'documents'
                            }
                            onCancel={closeModal}
                            onOk={closeModal}
                            destroyOnClose
                            width={'80%'} // Set width (adjust as needed)
                            style={{ top: 20, height: '80vh' }} // Adjust height & position
                            bodyStyle={{ height: '70vh', overflowY: 'auto' }} // Make content scrollable
                          >
                            <Form.List name={[name, 'certificateDocuments']}>
                              {(subFields, { add, remove }) => (
                                <div
                                  style={{
                                    display: 'flex',
                                    rowGap: 16,
                                    flexDirection: 'column',
                                    marginBottom: '24px',
                                  }}
                                >
                                  <Button type="dashed" onClick={() => add()} block>
                                    + Add Certificates
                                  </Button>
                                  {subFields.map((subField, index) => {
                                    return (
                                      <SupplierCertificateUpload
                                        key={index}
                                        field={name}
                                        index={index}
                                        remove={remove}
                                        subField={subField}
                                        productCertificatesType={productCertificatesType}
                                      />
                                    );
                                  })}
                                </div>
                              )}
                            </Form.List>
                            <Form.List name={[name, 'documents']}>
                              {(subFields, { add, remove }) => (
                                <div
                                  style={{
                                    display: 'flex',
                                    rowGap: 16,
                                    flexDirection: 'column',
                                  }}
                                >
                                  <Button type="dashed" onClick={() => add()} block>
                                    + Add Document
                                  </Button>
                                  {subFields.map((subField, index) => {
                                    return (
                                      <SupplierDocumentUpload
                                        key={index}
                                        field={name}
                                        index={index}
                                        remove={remove}
                                        subField={subField}
                                        productDocumentsType={productDocumentsType}
                                      />
                                    );
                                  })}
                                </div>
                              )}
                            </Form.List>
                          </Modal>
                        </td>

                        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
                          {/* Button to Open Overlay */}
                          {/* Button to Open Overlay */}
                          <Button
                            type="primary"
                            onClick={() => openModal(index, 'remarks')}
                            style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
                          >
                            Add
                          </Button>

                          {/* Modal for Address Input */}
                          <Modal
                            title="Add Remarks"
                            open={
                              selectedField?.index === index && selectedField?.type === 'remarks'
                            }
                            onCancel={closeModal}
                            onOk={closeModal}
                            destroyOnClose
                            width={'80%'} // Set width (adjust as needed)
                            style={{ top: 20, height: '80vh' }} // Adjust height & position
                            bodyStyle={{ height: '70vh', overflowY: 'auto' }} // Make content scrollable
                          >
                            <Form.Item
                              name={[name, 'exportApprovedRemarks']}
                              style={{ marginBottom: '16px' }}
                            >
                              <MultipleTextFieldInput
                                type="TextArea"
                                mode={formView}
                                placeholder="Enter Export approved remarks"
                                savetimeStamp={false}
                                showDelete={formView === formModes.CREATE}
                              />
                            </Form.Item>
                            <Form.Item name={[name, 'dutyRemarks']}>
                              <MultipleTextFieldInput
                                type="TextArea"
                                mode={formView}
                                placeholder="Enter duty remarks"
                                savetimeStamp={false}
                                showDelete={formView === formModes.CREATE}
                              />
                            </Form.Item>
                          </Modal>
                        </td>
                      </tr>
                    );
                  })}
                </>
              )}
            </Form.List>
          </tbody>
        </table>
      </div>
    </div>
  );
};

ProductTable.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
};

export default ProductTable;
