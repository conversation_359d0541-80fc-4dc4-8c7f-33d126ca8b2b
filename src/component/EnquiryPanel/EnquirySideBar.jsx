import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import RouteFactory from '../../service/RouteFactory';
import { Link } from 'react-router-dom';
import { Button, Space ,message} from 'antd';
import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import add from '../../assets/icons/add_circle_theme.svg';
import approve from '../../assets/icons/approve_theme_color.svg';
import reject from '../../assets/icons/block_theme_red.svg';
import cycle from '../../assets/icons/cycle_theme_color.svg';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import {
  enquiryStatusList,
  productIncoTermsList,
  productUOMList,
} from '../../constants/formConstant';
import { camelCaseToTitle } from '../../util/stringUtils';
import EnquiryQuotationModal from './EnquiryQuotationModal';
import {
  approveCustomerEnquiry,
  closeEnquiry,
  deleteEnquiry,
  // negotiateEnquiryQuotation,
} from '../../service/api/enquiryApi';
import ApproveOrRejectQuotationModal from './ApproveOrRejectModals/ApproveOrRejectQuotationModal';
import ApproveOrRejectEnquiryModal from './ApproveOrRejectModals/ApproveOrRejectEnquiryModal';
import NegotiateEnquiryModal from './NegotiateModal/NegotiateEnquiryModal';

const EnquirySideBar = props => {
  const { enquiry, hide } = props;

  const [showQuotationModal, setQuotationModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState({
    show: false,
    approval: null,
    type: '',
    action: null,
  });

  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const [messageApi, contextHolder] = message.useMessage();

  const openForm = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };
  const hideSideDrawer = () => {
    hide();
  };
  const handleEnquiryApproval = actionValue => {
    if (enquiry?.id) {
      setLoading(true);
      approveCustomerEnquiry(enquiry?.id, actionValue)
        .then(res => {
          if (res.data) {
            window.location.reload();
          }
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  };

  const handleEnquiryClosure = actionValue => {
    if (enquiry?.id) {
      setLoading(true);
      closeEnquiry(enquiry?.id, actionValue)
        .then(res => {
          if (res.data) {
            window.location.reload();
          }
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  };

  // const handleQuotationNegotiation = () => {
  //   if (enquiry?.id) {
  //     setLoading(true);
  //     negotiateEnquiryQuotation(enquiry?.id)
  //       .then(res => {
  //         if (res.data) {
  //           window.location.reload();
  //         }
  //         setLoading(false);
  //       })
  //       .catch(err => {
  //         console.log(err);
  //         setLoading(false);
  //       });
  //   }
  // };

  const getConfirmationModal = confirmationModal => {
    if (confirmationModal) {
      if (confirmationModal.type == 'enquiryNegotiate') {
        return (
          <NegotiateEnquiryModal
            enquiryId={enquiry?.id}
            displayModal={confirmationModal.show}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      } else if (
        confirmationModal.type == 'quotationApproval' ||
        confirmationModal.type == 'quotationRejection'
      ) {
        return (
          <ApproveOrRejectQuotationModal
            displayModal={confirmationModal.show}
            quotationData={enquiry?.quotation}
            isRejectModal={confirmationModal.type == 'quotationRejection'}
            enquiryId={enquiry?.id}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      } else {
        return (
          <ApproveOrRejectEnquiryModal
            enquiryId={enquiry?.id}
            isRejectModal={!confirmationModal.approval}
            displayModal={confirmationModal.show}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      }
    }
  };

  const openConfirmationModal = modalData => {
    if (modalData.type && modalData.approval !== null) {
      setConfirmationModal({
        ...modalData,
        show: true,
      });
    }
  };

  const handleDelete = id => {
    deleteEnquiry(id)
      .then(() => {
        console.log('sucessfully deleted');
        window.location.replace(new RouteFactory().dashboard().enquiry().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        messageApi.error(errMsg);
      });
  };

  return enquiry ? (
    <>
      <div className={css.customerSider}>
        {contextHolder}
        <div className={css.titleBar}>
          <div className={css.title}>{enquiry.enquiryId}</div>
          <div className={css.closeBtn} onClick={hideSideDrawer}>
            <img src={close} alt="close" />
          </div>
        </div>
        <div className={css.actionBtnBox}>
          {hasPermission(userPermissionsList.viewEnquiry, user.permissions) ? (
            <Link
              to={new RouteFactory().dashboard().enquiry().setId(enquiry.id).view().build()}
              target="_blank"
            >
              <Button
                onClick={() => openForm()}
                icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
              >
                View
              </Button>
            </Link>
          ) : null}
          {hasPermission(userPermissionsList.updateEnquiry, user.permissions) ? (
            <Link to={new RouteFactory().dashboard().enquiry().setId(enquiry.id).edit().build()}>
              <Button
                onClick={() => openForm()}
                type="primary"
                icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
              >
                Edit
              </Button>
            </Link>
          ) : null}
          {hasPermission(userPermissionsList.deleteEnquiry, user.permissions) ? (
            <Button onClick={() => handleDelete(enquiry.id)}>Delete</Button>
          ) : null}
          {hasPermission(userPermissionsList.approveEnquiry, user.permissions) &&
          enquiry.enquiryStatus === enquiryStatusList.INITIATED.value ? (
            <>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: true,
                    type: 'enquiry',
                    action: () => handleEnquiryApproval(true),
                  });
                }}
                type="default"
                className="bg-white"
                loading={loading}
                icon={<img src={approve} alt="approve" width={20} height={20} />}
              >
                Approve Enquiry
              </Button>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: true,
                    type: 'enquiry',
                    action: () => handleEnquiryApproval(false),
                  });
                }}
                type="default"
                danger
                className="bg-white"
                icon={<img src={reject} alt="reject" width={20} height={20} />}
              >
                Reject Enquiry
              </Button>
            </>
          ) : null}
          {hasPermission(userPermissionsList.createQuotation, user.permissions) &&
          (enquiry.enquiryStatus === enquiryStatusList.ASSIGNED.value ||
            enquiry.enquiryStatus === enquiryStatusList.NEGOTIATE.value) ? (
            <Button
              className="col-span-2"
              onClick={() => setQuotationModal(true)}
              type="default"
              icon={<img src={add} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Add Quotation
            </Button>
          ) : null}
          {hasPermission(userPermissionsList.approveQuotation, user.permissions) &&
          enquiry.enquiryStatus === enquiryStatusList.QUOTED.value ? (
            <>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: true,
                    type: 'quotationApproval',
                    action: null,
                  });
                }}
                type="default"
                className="bg-white"
                loading={loading}
                icon={<img src={approve} alt="approve" width={20} height={20} />}
              >
                Approve Quote
              </Button>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: false,
                    type: 'quotationRejection',
                    action: null,
                  });
                }}
                type="default"
                danger
                className="bg-white"
                icon={<img src={reject} alt="reject" width={20} height={20} />}
              >
                Reject Quote
              </Button>
            </>
          ) : null}
          {hasPermission(userPermissionsList.closeEnquiry, user.permissions) &&
          enquiry.enquiryStatus === enquiryStatusList.QUOTATION_APPROVED.value ? (
            <>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: true,
                    type: 'enquiry',
                    action: () => handleEnquiryClosure(true),
                  });
                }}
                type="default"
                className="bg-white"
                icon={<img src={approve} alt="approve" width={20} height={20} />}
              >
                Approve Enquiry
              </Button>
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: false,
                    type: 'enquiry',
                    action: () => handleEnquiryClosure(false),
                  });
                }}
                type="default"
                danger
                className="bg-white"
                icon={<img src={reject} alt="reject" width={20} height={20} />}
              >
                Reject Enquiry
              </Button>
            </>
          ) : null}
          {hasPermission(userPermissionsList.negotiateEnquiry, user.permissions) &&
          enquiry.enquiryStatus === enquiryStatusList.QUOTATION_APPROVED.value ? (
            <Button
              onClick={() => {
                openConfirmationModal({
                  approval: false,
                  type: 'enquiryNegotiate',
                });
              }}
              type="default"
              className="col-span-2"
              loading={loading}
              icon={<img src={cycle} alt="reject" width={20} height={20} />}
            >
              Negotiate
            </Button>
          ) : null}
        </div>
        <Space direction="vertical" size="large" className="w-full">
          <Space.Compact block direction="vertical" size="medium">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Customer Name
            </div>
            <div className={css.value}>{enquiry?.customer?.name}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Product
            </div>
            <div className={css.value}>{enquiry?.productName}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              CAS Number
            </div>
            <div className={css.value}>{enquiry?.casNumber}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Grade
            </div>
            <div className={css.value}>{enquiry?.grade}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Quantity
            </div>
            <div className={css.value}>{`${enquiry?.quantity} ${
              getEnumValueFromList(enquiry?.unitOfMeasure, productUOMList) || ''
            }`}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Application
            </div>
            <div className={css.value}>{enquiry?.application}</div>
          </Space.Compact>
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Incotems
            </div>
            <div>
              <span style={{ fontWeight: '500' }}>Type:</span>
              <span>{getEnumValueFromList(enquiry?.incoterms?.type, productIncoTermsList)}</span>
            </div>
            <div>
              <span style={{ fontWeight: '500' }}>Country:</span>
              <span>{enquiry?.incoterms?.country}</span>
            </div>
            {enquiry?.incoterms?.data && Object.keys(enquiry.incoterms.data).length > 0
              ? Object.keys(enquiry.incoterms.data).map(key => (
                  <div key={key}>
                    <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                    <span>{enquiry.incoterms.data[key]}</span>
                  </div>
                ))
              : ''}
          </Space.Compact>
        </Space>
        {showQuotationModal ? (
          <EnquiryQuotationModal
            open={showQuotationModal}
            enquiry={enquiry}
            onCancel={() => setQuotationModal(false)}
          />
        ) : null}
      </div>
      {getConfirmationModal(confirmationModal)}
    </>
  ) : null;
};

export default EnquirySideBar;

EnquirySideBar.propTypes = {
  enquiry: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
};
