import React, { useState } from 'react';
import { Form, Input, Button, Select, Modal, DatePicker } from 'antd';
import { DateFormat, formModes } from '../../constants/formConstant';
import PropTypes from 'prop-types';
import css from './ProductTable.module.css';
import { productUOMList } from '../../constants/formConstant';
import FileUpload from '../FileUpload';
import IncotermInput from '../IncotermInput/IncotermInput';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';

const ProductRow = ({ form, formView, packagingList, productList, index, name, isOtherPackaging }) => {
  const orderType = Form.useWatch('orderType', form);
  const [focusedField, setFocusedField] = useState(null);
  const [selectedField, setSelectedField] = useState(null); // Store index & modal type

  const OrderBookDocumentGroups = [
    { name: 'COA', required: false },
    { name: 'TDS', required: true },
    { name: 'SDS', required: false },
    { name: 'M<PERSON>', required: false },
    { name: 'Other', required: false },
  ];

  const openModal = (index, type) => {
    setSelectedField({ index, type }); // Set both index and modal type
  };

  const closeModal = () => {
    setSelectedField(null); // Close modal
  };

  const incotermsValue = Form.useWatch(['products', name, 'incoterms'], form);
  const documentsValue = Form.useWatch(['products', name, 'documents'], form);
  const testingFlag = Form.useWatch(['products', name, 'testingRequired'], form);
  const remarksValue = Form.useWatch(['products', name, 'deliveryInstructions'], form);

  const isRowFilled = row => {
    return Object.values(row).some(value => value && value.toString().trim() !== '');
  };

  return (
    <tr>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        {productList && productList.length ? (
          <Form.Item
            name={[name, 'product']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              style={{
                width: focusedField === `${index}-product` ? '350px' : '150px',
              }}
              showSearch
              allowClear
              optionFilterProp="label"
              onFocus={() => setFocusedField(`${index}-product`)}
              onBlur={() => setFocusedField(null)}
            >
              {productList.map(product => (
                <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                  {product.tradeName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'uom']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Select
            disabled={formView === formModes.UPDATE}
            style={{
              width: focusedField === `${index}-street` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-street`)}
            onBlur={() => setFocusedField(null)}
          >
            {productUOMList.map(uom => (
              <Select.Option key={uom.key} value={uom.value}>
                {uom.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantity']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-quantity` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantity`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'price']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (orderType != 'CUSTOMER_ORDER') {
                  return Promise.resolve();
                }
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                if (value && value <= 0) {
                  return Promise.reject(new Error('Price can never be less than or equal to zero'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-price` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-price`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'customerProductCode']}
          style={{ marginBottom: 0, display: 'block' }}
        >
          <Input
            style={{
              width: focusedField === `${index}-customerProductCode` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-customerProductCode`)}
            onBlur={() => setFocusedField(null)}
            // rules={[
            //   ({ getFieldValue }) => ({
            //     validator(_, value) {
            //       const rowValues = getFieldValue(['products', name]);

            //       if (isRowFilled(rowValues) && !value) {
            //         return Promise.reject();
            //       }
            //       return Promise.resolve();
            //     },
            //   }),
            // ]}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'hsCode']} style={{ marginBottom: 0, display: 'block' }}>
          <Input
            style={{
              width: focusedField === `${index}-hsCode` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-hsCode`)}
            onBlur={() => setFocusedField(null)}
            // rules={[
            //   ({ getFieldValue }) => ({
            //     validator(_, value) {
            //       const rowValues = getFieldValue(['products', name]);

            //       if (isRowFilled(rowValues) && !value) {
            //         return Promise.reject();
            //       }
            //       return Promise.resolve();
            //     },
            //   }),
            // ]}
          />
        </Form.Item>
      </td>

      {orderType === 'CUSTOMER_ORDER' && (
        <>
          <td
            style={{
              padding: '4px',
              textAlign: 'center',
              verticalAlign: 'top',
              height: '100%',
            }}
          >
            {/* Button to Open Overlay */}
            <Button
              type="primary"
              onClick={() => openModal(index, 'incoterms')}
              style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
            >
              {incotermsValue ? 'View' : 'Add'}
            </Button>

            {/* Modal for Address Input */}
            <Modal
              title="Add Incoterms"
              open={selectedField?.index === index && selectedField?.type === 'incoterms'}
              onCancel={closeModal}
              onOk={closeModal}
              destroyOnClose
              width={'50%'} // Set width (adjust as needed)
              style={{ top: 20, height: '50vh' }} // Adjust height & position
              bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
            >
              <IncotermInput name={name} form={form} parentKey="products" hideLabel={true} />
            </Modal>
          </td>
        </>
      )}

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'expectedDeliveryDate']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (orderType != 'CUSTOMER_ORDER') {
                  return Promise.resolve();
                }

                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <DatePicker
            format={DateFormat} // You can customize format here
            style={{
              width: focusedField === `${index}-expectedDeliveryDate` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-expectedDeliveryDate`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      {/* <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'countryOfOrigin']} style={{ marginBottom: 0, display: 'block' }}>
          <Input
            style={{
              width: focusedField === `${index}-countryOfOrigin` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-countryOfOrigin`)}
            onBlur={() => setFocusedField(null)}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);

                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          />
        </Form.Item>
      </td> */}

      {packagingList.length > 0 && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
          <Form.Item
            name={[name, 'packaging']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              { required: true, message: 'Packaging Details is required' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject(new Error('Packaging Details is required'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              showSearch
              optionFilterProp="label"
              className={css.selectBox}
              style={{
                width: focusedField === `${index}-packaging` ? '350px' : '150px',
              }}
              onFocus={() => setFocusedField(`${index}-packaging`)}
              onBlur={() => setFocusedField(null)}
            >
              {packagingList.map((packageType, index) => (
                <Select.Option key={index} value={packageType.id} label={packageType.type}>
                  <div className={css.CustomSelectOptn}>
                    <div className={css.optnTitle}>{packageType.type}</div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Pack size:</div>
                      <div className={css.optnValue}>{packageType.packSize}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Tare weight:</div>
                      <div className={css.optnValue}>{packageType.tareWeight}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Dimension:</div>
                      <div className={css.optnValue}>{packageType.dimension}</div>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </td>
      )}

      {packagingList.length > 0 && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
          {(() => {
            return (
              <Form.Item
                name={[name, 'otherPackagingDetails']}
                style={{ marginBottom: 0, display: 'block' }}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (isOtherPackaging && !value) {
                        return Promise.reject(new Error('Other Packaging Details is required!'));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input
                  style={{
                    width: focusedField === `${index}-otherPackagingDetails` ? '350px' : '150px',
                    borderColor: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? 'red' : undefined,
                    background: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? '#fff0f0' : undefined,
                  }}
                  onFocus={() => setFocusedField(`${index}-otherPackagingDetails`)}
                  onBlur={() => setFocusedField(null)}
                />
              </Form.Item>
            );
          })()}
        </td>
      )}

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'units']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);

                if (orderType != 'CUSTOMER_ORDER') {
                  return Promise.resolve();
                }

                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-units` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-units`)}
            onBlur={() => setFocusedField(null)}
            type="number"
          />
        </Form.Item>
      </td>

      {/* Mode of Export field follows */}

      {orderType === 'CUSTOMER_ORDER' && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
          <Form.Item
            name={[name, 'modeOfExport']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              style={{
                width: focusedField === `${index}-modeOfExport` ? '350px' : '150px',
              }}
              onFocus={() => setFocusedField(`${index}-modeOfExport`)}
              onBlur={() => setFocusedField(null)}
            >
              <Select.Option value="AIR">Air</Select.Option>
              <Select.Option value="OCEAN">Ocean</Select.Option>
              <Select.Option value="OTHERS">Others</Select.Option>
            </Select>
          </Form.Item>
        </td>
      )}

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Button
          type="primary"
          onClick={() => openModal(index, 'testingFlag')}
          style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
        >
          {form.getFieldValue(['products', name, 'testingRequired']) !== undefined ? 'View' : 'Add'}
        </Button>

        {/* Modal for Address Input */}
        <Modal
          title="Add Testing Flag"
          open={selectedField?.index === index && selectedField?.type === 'testingFlag'}
          onCancel={closeModal}
          onOk={closeModal}
          footer={null}
          destroyOnClose
          width={'50%'}
          style={{ top: 20, height: '50vh' }} // Adjust height & position
          bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
        >
          <Form.Item
            name={[name, 'testingRequired']}
            label="Testing Required"
            rules={[{ required: true, message: 'Please select if testing is required' }]}
          >
            <Select
              onChange={value => {
                if (value === false) {
                  form.setFieldValue([name, 'dispatchWithResults'], null);
                }
              }}
            >
              <Select.Option value={true}>Yes</Select.Option>
              <Select.Option value={false}>No</Select.Option>
            </Select>
          </Form.Item>
          {testingFlag && (
            <Form.Item
              name={[name, 'dispatchWithResults']}
              label="Dispatch With Results"
              rules={[{ required: true, message: 'Please select dispatch type' }]}
            >
              <Select>
                <Select.Option value={true}>Yes</Select.Option>
                <Select.Option value={false}>No</Select.Option>
              </Select>
            </Form.Item>
          )}
        </Modal>
      </td>

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Button
          type="primary"
          onClick={() => openModal(index, 'documents')}
          style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
        >
          {Object.values(documentsValue || {}).some(docs => docs && docs.length > 0)
            ? 'View'
            : 'Add'}
        </Button>

        {/* Modal for Address Input */}
        <Modal
          title="Add Documents"
          open={selectedField?.index === index && selectedField?.type === 'documents'}
          destroyOnClose // Keep the modal in DOM after closing
          onCancel={closeModal}
          onOk={closeModal}
          footer={null}
          width={'50%'} // Set width (adjust as needed)
          style={{ top: 20, height: '50vh' }} // Adjust height & position
          bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
        >
          {OrderBookDocumentGroups.map((docType, index) => (
            <Form.Item
              key={index}
              label={docType.name}
              name={[name, 'documents', docType.name]}
              {...(docType.required
                ? {
                    rules: [
                      {
                        required: true,
                        message: `Please upload files for ${docType.name}`,
                      },
                    ],
                  }
                : {})}
            >
              <FileUpload
                category="Orders"
                meta={{ documentType: docType.name, poId: 123 }}
                // deleteDisabled={isDisabled}
                // disabled={isDisabled}
              />
            </Form.Item>
          ))}
        </Modal>
      </td>

      <td
        style={{
          padding: '4px',
          textAlign: 'center',
          verticalAlign: 'top',
          height: '100%',
        }}
      >
        {/* Button to Open Overlay */}
        <Button
          type="primary"
          onClick={() => openModal(index, 'remarks')}
          style={{ backgroundColor: '#d3e5ff', color: '#004085', border: 'none' }}
        >
          {remarksValue?.length > 0 ? 'View' : 'Add'}
        </Button>

        {/* Modal for Address Input */}
        <Modal
          title="Add Special delivery instructions"
          open={selectedField?.index === index && selectedField?.type === 'remarks'}
          onCancel={closeModal}
          onOk={closeModal}
          footer={null}
          destroyOnClose
          width={'50%'} // Set width (adjust as needed)
          style={{ top: 20, height: '50vh' }} // Adjust height & position
          bodyStyle={{ height: '50vh', overflowY: 'auto' }} // Make content scrollable
        >
          <Form.Item name={[name, 'deliveryInstructions']} label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              savetimeStamp
              // showDelete={formView === formModes.CREATE}
            />
          </Form.Item>
        </Modal>
      </td>
    </tr>
  );
};

ProductRow.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
  isOtherPackaging: PropTypes.bool,
};

export default ProductRow;
