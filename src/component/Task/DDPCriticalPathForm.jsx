import React, { useEffect, useState } from 'react';
import { Modal, Form, DatePicker, Button } from 'antd';
import PropTypes from 'prop-types';
import { generateCriticalPath, updateCriticalPath } from '../../service/api/activityApi';
import { DateFormat } from '../../constants/formConstant';



const DDPCriticalPathFormModal = ({ visible, onCancel, entityId, onSumbit, updateMode }) => {
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const handleFormSubmit = () => {
    // console.log(form.getFieldValue());
    form.validateFields().then(values => {
      setLoading(true);
      if (updateMode) {
        updateCriticalPath('CUSTOMER_ORDER', entityId, 'ddpCriticalPath', values)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        generateCriticalPath('CUSTOMER_ORDER', entityId, 'ddpCriticalPath', values)
          .then(res => {
            if (res.data) {
              // console.log("generated")
              onSumbit(res.data);
              setLoading(false);
              cancelModal();
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    });
  };
  const cancelModal = () => {
    if (loading) return;
    form.resetFields();
    onCancel();
  };


  const initialValue = {
    deliveryDate: ''
  };

  useEffect(() => {
    form.setFieldValue(initialValue);
  }, []);

  return (
    <Modal
      open={visible}
      title={updateMode ? 'Update DDP Critical Path' : 'Generate DDP Critical Path'}
      onCancel={cancelModal}
      footer={[
        <Button key="cancel" onClick={cancelModal}>
          Cancel
        </Button>,
        <Button loading={loading} key="submit" type="primary" onClick={handleFormSubmit}>
          Submit
        </Button>,
      ]}
      styles={{
        body: {
          maxHeight: '50vh',
          overflow: 'scroll',
        },
      }}
    >
      <Form preserve={false} form={form}>
        <Form.Item label="Delivery Date" name="deliveryDate" rules={[{ required: true }]}>
          <DatePicker format={DateFormat} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

DDPCriticalPathFormModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  entityId: PropTypes.string.isRequired,
  onSumbit: PropTypes.func.isRequired,
  updateMode: PropTypes.bool.isRequired,
};

export default DDPCriticalPathFormModal;
