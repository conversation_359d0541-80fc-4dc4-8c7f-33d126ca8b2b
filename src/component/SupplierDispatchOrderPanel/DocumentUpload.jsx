import React from 'react';
import PropTypes from 'prop-types';
import { CloseOutlined } from '@ant-design/icons';
import { Form, Row, Col, Select } from 'antd';
import FileUpload from '../FileUpload';
export default function DocumentUpload(props) {
  const { subField, remove } = props;

  const productDocumentsType = [
    { value: 'MOA', label: 'MOA' },
    { value: 'labCOA', label: 'LabCOA' },
    { value: 'recieptFiles', label: 'recieptFiles' },
    { value: 'cargoDocuments', label: 'cargoDocuments' },
    { value: 'shippingBill', label: 'shippingBill' },
    {value:'productPackagingPhotos', label:'productPackaging Photos'},
    { value: 'SDS', label: 'SDS' },
    { value: 'TDS', label: 'TDS' },
    { value: 'Invoice', label: 'Invoice' },
    { value: 'DGD', label: 'DGD' },
    { value: 'COO', label: 'COO' },
  ];

  return (
    <Row gutter={16}>
      <Col span={16}>
        <Form.Item
          label={`Document - ${subField.name + 1}`}
          name={[subField.name, 'documentType']}
          // rules={[{ required: true, message: 'Please select a certificate type' }]}
        >
          <Select options={productDocumentsType} />
        </Form.Item>
      </Col>
      <Col span={4}>
        <Form.Item
          label="Upload Certificate"
          name={[subField.name, 'files']}
          rules={[{ required: true, message: 'Please upload at least one file' }]}
        >
          <FileUpload category="Orders" meta={{ documentType: 'label', poId: 123 }} />
        </Form.Item>
      </Col>
      <Col span={2}>
        <CloseOutlined span={2} onClick={() => remove(subField.name)} />
      </Col>
    </Row>
  );
}

DocumentUpload.propTypes = {
  //   form: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  field: PropTypes.object.isRequired,
  subField: PropTypes.object.isRequired,
  //   formView: PropTypes.object.isRequired,
  remove: PropTypes.func.isRequired,
  productCertificatesType: PropTypes.array.isRequired,
};
