import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { getSupplierDispatchOrderList } from '../../service/api/supplierDispatchOrderApi';
import PageLoader from '../Loaders/PageLoader';
import { Tabs } from 'antd';
import SupplierDispatchOrderReadView from './SupplierDispatchOrderReadView';

const SupplierDispatchOrderTabs = (props) => {

  const { orderbookData, customerorderbookId } = props;

  const [supplierDispatchOrderList, setSupplierDispatchOrderList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (orderbookData.purchaseOrderNumber) {
      setLoading(true);
      getSupplierDispatchOrderList(null, null, null, orderbookData.purchaseOrderNumber)
      .then(response => {
        setSupplierDispatchOrderList(response.data.content);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
    }
  }, [orderbookData.purchaseOrderNumber]);

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Tabs
      items={supplierDispatchOrderList.map((dispatchOrder, index) => ({
        key: `${index+1}`,
        label: dispatchOrder.orderId,
        children:(
          <SupplierDispatchOrderReadView
            customerorderbookId={customerorderbookId}
            orderbookData={orderbookData}
            dispatchOrder={dispatchOrder}
            setDispatchOrder={(data) => setSupplierDispatchOrderList(
              supplierDispatchOrderList.map(item => item.id === data.id ? data : item)
            )}
            orderType={orderbookData.orderType}
          />
        ),
      }))}
      defaultActiveKey={'1'}
      type='line'
      destroyInactiveTabPane={true}
    />
  )
}

export default SupplierDispatchOrderTabs;

SupplierDispatchOrderTabs.propTypes = {
  orderbookData: PropTypes.object.isRequired,
  customerorderbookId: PropTypes.string.isRequired,
}