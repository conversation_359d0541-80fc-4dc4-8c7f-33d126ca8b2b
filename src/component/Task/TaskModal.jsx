import React, {useEffect, useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, Button } from 'antd';
import PropTypes from 'prop-types';
import { DateFormat, criticalPathStatus, formModes } from '../../constants/formConstant';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import { getInputFiledsFromList } from '../../util/formUtil';
import { convertTimestampsToDayjs } from '../../util/dateUtils';
import dayjs from 'dayjs';

const TaskModal = (props) => {

  const { visible, onSubmit, onCancel, formMode ,values, employeeList, taskCategories} = props;

  const [submitLoader, setSubmitLoader] = useState(false);

  const [form] = Form.useForm();
  // Use the 'taskToEdit' prop to determine if we're in edit mode
  const isEditMode = formMode === formModes.UPDATE;
  const statusWatch = Form.useWatch('status', form);

  // Initialize form values with the task details if in edit mode
  useEffect(() => {
    if (isEditMode) {
      let taskValues = {}
      let formFiledValues = {
        ...values,
      }
      if (values?.taskConfig?.length) {
        values.taskConfig.forEach((config) => {
          if (config?.value && Array.isArray(config?.value)) {
            taskValues[config.key] = config.value.map((val) => {
              let newVal = {...val};
              convertTimestampsToDayjs(newVal);
              return newVal;
            })
          } else {
            if (typeof config.value === 'string' && config.key.toLowerCase().includes('date')) {
              // If the value is a string that can be parsed as a date, convert it to a Day.js object
              taskValues[config.key] = dayjs(config.value);
            } else
              taskValues[config.key] = config.value;
          }            
        })
        formFiledValues = {
          ...formFiledValues,
          ...taskValues,
        }
      }
      form.setFieldsValue(formFiledValues);
    } else {
      form.resetFields();
    }
  }, [isEditMode, values, form]);

  const handleFormSubmit = () => {
    form.validateFields().then((formValues) => {
      setSubmitLoader(true);
      let newConfig = { ...formValues };
      // convertDayjsToTimestamps(newConfig)
      const taskData = {
        ...values,
        ...newConfig,        
      }
      if (values?.taskConfig?.length) {
        newConfig = values.taskConfig.map((config) => ({
          ...config,
          value: newConfig?.[config?.key],
        }));
        taskData.taskConfig = newConfig;
      } 
      onSubmit(taskData, isEditMode, setSubmitLoader);
      form.resetFields();
    });
  };

  return (
    <Modal
      open={visible}
      title={isEditMode ? 'Edit Task' : 'Add Task'}
      onCancel={onCancel}
      confirmLoading={submitLoader}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button loading={submitLoader} key="submit" type="primary" onClick={handleFormSubmit}>
          {isEditMode ? 'Update' : 'Submit'}
        </Button>,
      ]}
      styles={{
        body:{
        maxHeight:'50vh',
        overflow: 'scroll',
      }}}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label="Task Name"
          name="name"
          rules={[
            {
              required: true,
              message: 'Please enter the task name',
            },
          ]}
        >
          <Input disabled={isEditMode} />
        </Form.Item>
        <Form.Item label="Description" name="description">
          <Input.TextArea disabled={isEditMode} />
        </Form.Item>
        {taskCategories.length ? (
          <Form.Item
            label="Category"
            name="category"
            rules={[
              {
                required: true,
                message: 'Please select a category',
              },
            ]}
          >
            <Select showSearch optionFilterProp="label" disabled={isEditMode}>
              {taskCategories.map(cat => (
                <Select.Option key={cat.key} value={cat.key} label={cat.displayName}>
                  {cat.displayName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        {employeeList.length ? (
          <Form.Item
            name="assignedTo"
            label="Assigned To"
            rules={[
              {
                required: true,
                message: 'Please select an assignee',
              },
            ]}
          >
            <Select showSearch optionFilterProp="label">
              {employeeList.map(emp => (
                <Select.Option key={emp.id} value={emp.id} label={emp.name || emp.id}>
                  {emp.name || emp.id}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label="Due Date"
          name="dueDate" 
          rules={[
            {
              required: true,
              message: 'Please select a due date',
            },
          ]}
        >
          <DatePicker format={DateFormat}/>
        </Form.Item>
        <Form.Item name="remarks" label="Remarks(Press enter to add)">
          <MultipleTextFieldInput
            type="TextArea"
            mode={formMode}
            placeholder="Enter a remark"
            savetimeStamp
            showDelete={formMode === formModes.CREATE}
          />
        </Form.Item>
        <Form.Item
          label="Status"
          name="status"
          rules={[
            {
              required: true,
              message: 'Please select an status',
            },
          ]}
        >
          <Select>
            {criticalPathStatus.map((status) => (
              <Select.Option key={status.key} value={status.value}>
                {status.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isEditMode && statusWatch === 'COMPLETED' ? (
          <>
            <Form.Item
              label="Display Date for Task"
              name="displayDate"
              rules={[{ required: true, message: 'Please enter a valid value', type: 'date' }]}
            >
              <DatePicker format={DateFormat}/>
            </Form.Item>
            {values?.taskConfig?.length ? getInputFiledsFromList(values.taskConfig, true, null, null) : null}
          </>
        ) : null}
      </Form>
    </Modal>
  );
};

TaskModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  formMode: PropTypes.string, // Task details for editing mode
  values: PropTypes.object, // Task details for editing mode
  employeeList: PropTypes.array.isRequired,
  taskCategories: PropTypes.array.isRequired,
};

export default TaskModal;
