import React, { useEffect, useState } from 'react'
import css from '../CustomerPanel/CustomerListView.module.css';
import PropTypes from 'prop-types';
import { assignEnquiryToEmployee, getEnquiryList } from '../../service/api/enquiryApi';
import HeaderPanel from '../headerPanel';
import { Button, Input, Select, Table } from 'antd';
import { Link } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import addIcon from '../../assets/icons/add_circle.svg';
import { enquiryHeaders, tablePageSize } from '../../constants/TableConstants';
import { getEmployeeList } from '../../service/api/employee';
import { useSelector } from 'react-redux';
import { getEmployeeNameFromId, hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';

const EnquiryListView = (props) => {
  const { collapseAsideBarHandler, collapseSideDrawerHandler, sideDrawerDataHandler } = props;

  const [enquiryList, setEnquiryList] = useState({});
  const [loading, setLoading] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);
  // const [showFiltersModal, setShowFiltersModal] = useState(false);
  // const [showClear, setShowClear] = useState(false);

  const user = useSelector((state) => state.user);

  const enquiryAssignActionHeader = [
    {
      title: 'Action',
      dataIndex: 'assignAction',
      key: 'assignAction',
      width: 200,
      fixed: 'right',
      render: (_, { id }) =>  (
        <Select
          showSearch
          onClick={(e) => e.stopPropagation()}
          onChange={(value) => assignEnquiry(id, value)}
          style={{ width: '100%' }}
          placeholder="Assign To"
        >
          {employeeList.map(emp => (
            <Select.Option
              key={emp.id}
              value={emp.id}
            >
              {emp.name}
            </Select.Option>
          ))}
        </Select>
      ),
    }
  ];
  const enquiryAssignedHeader = [
    {
      title: 'AssignedTo',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 150,
      render: (_, { assignedTo }) => getEmployeeNameFromId(assignedTo, employeeList)
    }
  ];

  function assignEnquiry (enquiryId, employeeId) {
    if (enquiryId && employeeId) {
      setLoading(true);
      assignEnquiryToEmployee(enquiryId, employeeId).then((res) => {
        if (res.data) {
          // update enquiry
          const newList = enquiryList.content.map((item) => {
            if (item.id === res.data.id) return res.data;
            return item;
          });
          setEnquiryList({
            ...enquiryList,
            content: newList,
          });
        }
        setLoading(false);
      }).catch((err) => {
        console.log(err);
        setLoading(false);
      })
    }
  }
  const getEnquiryFromConfig = (pageNumber, filters, searchkey) => {
    setLoading(true);
    getEnquiryList(pageNumber, filters, searchkey)
      .then(response => {
        setEnquiryList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    sideDrawerDataHandler(record);
    collapseSideDrawerHandler(false);
  };
  const onClickAddEnquiry= () => {
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };
  const getEnquiryFromSearch = searchText => {
    getEnquiryFromConfig(null, null, searchText);
  };
  const getTableHeader = () => {
    const newHeader = [
      ...enquiryHeaders,
      ...enquiryAssignedHeader,
    ];
    if (hasPermission(userPermissionsList.assignEnquiry, user.permissions)) {
      return [
        ...newHeader,
        ...enquiryAssignActionHeader,
      ];
    }
    return newHeader;
  }

  useEffect(() => {
    const pageNumber = 0;
    const filters = {};
    const searchkey = '';
    getEnquiryFromConfig(pageNumber, filters, searchkey);
    getEmployeeList().then(res => {
      if (res.data && res.data.content) {
        setEmployeeList(res.data.content);
      }
    })
    .catch(error => {
      console.log(error);
    });
  }, []);

  return (
    <>
      <HeaderPanel name="Enquiry Directory" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by Enquiry Id"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={getEnquiryFromSearch}
            bordered={false}
            enterButton
          />
        </div>
        {/* <Button size="large" className={css.btnFlexStyle} onClick={() => setShowFiltersModal(true)}>
          <img src={filterIcon} />
          <span>Filter</span>
        </Button>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              getCustomerListFromConfig();
              setShowClear(false);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null} */}
        <Link to={new RouteFactory().dashboard().enquiry().add().build()}>
          <Button
            onClick={onClickAddEnquiry}
            type="primary"
            size="large"
            className={css.btnFlexStyle}
          >
            <img src={addIcon} />
            <span>Add Enquiry</span>
          </Button>
        </Link>
      </div>
      <Table
        bordered={false}
        columns={getTableHeader()} // add dynamic action column
        dataSource={enquiryList?.content?.length ? enquiryList?.content.map((item) => ({ ...item, key: item.id})) : []}
        className={css.tableContainer}
        pagination={{
          showSizeChanger:false,
          current: enquiryList?.pageable?.pageNumber + 1,
          pageSize: tablePageSize,
          total: enquiryList?.totalElements,
          onChange: (page) => {
            getEnquiryFromConfig(page - 1, {}, '');
          }
        }}
        loading={loading}
        scroll={{
          x: '100%',
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = enquiryList.content.filter(
                enquiry => record.id === enquiry.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
    </>
  )
}

export default EnquiryListView;

EnquiryListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
  sideDrawerDataHandler: PropTypes.func.isRequired,
};