import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig';

export const getTasks = payload => {
  return axiosInstance.post(apiEndpoints.tasksEndPoint, payload);
};

export const updateTasks = payload => {
  return axiosInstance.put(apiEndpoints.updateTasks(payload?.id), payload);
};

export const getTaskbyId = id => {
  return axiosInstance.get(apiEndpoints.updateTasks(id));
};

export const updateTaskData = (entity, id, payload) => {
  return axiosInstance.put(apiEndpoints.putEndpoint(entity, id), payload);
};
