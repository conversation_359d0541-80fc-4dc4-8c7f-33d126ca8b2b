import React from 'react';
import { Form, Input, Modal } from 'antd';
import PropTypes from 'prop-types';
import { closeEnquiry } from '../../../service/api/enquiryApi';
import { HttpStatusCode } from 'axios';

const ApproveOrRejectEnquiryModal = props => {
  const { isRejectModal, enquiryId, displayModal, onCancel, setLoading } = props;
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      console.log(values);
      let response = isRejectModal
        ? await closeEnquiry(enquiryId, false, values['rejectionMessage'])
        : await closeEnquiry(enquiryId, true, '');

      if (response.status == HttpStatusCode.Ok) window.location.reload();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error occurred while submitting form ', error);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  const getEnquiryForm = () => {
    if (isRejectModal) {
      return (
        <Form form={form} name="rejectEnquiryForm">
          <Form.Item
            label="Rejection reason"
            name="rejectionMessage"
            rules={[{ required: true, message: 'Please provide reason to reject enquiry ' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      );
    } else {
      return '';
    }
  };

  return (
    <Modal
      open={displayModal}
      onOk={handleOk}
      onCancel={handleCancel}
      title={`Do you want to ${isRejectModal ? 'reject' : 'approve'} this enquiry ?`}
    >
      {getEnquiryForm()}
    </Modal>
  );
};

export default ApproveOrRejectEnquiryModal;
ApproveOrRejectEnquiryModal.propTypes = {
  displayModal: PropTypes.any,
  isRejectModal: PropTypes.bool.isRequired,
  enquiryId: PropTypes.string.isRequired,
  onCancel: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
};
