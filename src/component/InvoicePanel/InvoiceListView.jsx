import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import css from '../CustomerPanel/CustomerListView.module.css';
import { Badge, Button, Form, Input, Table } from 'antd';
import filterIcon from '../../assets/icons/filter_icon.svg';
import addIcon from '../../assets/icons/add_circle.svg';
import { useDispatch, useSelector } from 'react-redux';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import FilterModal from '../FilterModal/FilterModal';
import { invoiceHeader, invoiceTablePageSize } from '../../constants/TableConstants';
import HeaderPanel from '../headerPanel';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';
import { generateReport, getInvoiceList } from '../../service/api/invoiceApi';
import ReportModal from './FilterModal/ReportModal';

const InvoiceListView = props => {
  const { collapseAsideBarHandler, collapseSideDrawerHandler } = props;
  const user = useSelector(state => state.user);

  const [invoiceListReponse, setInvoiceListResponse] = useState({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [disableReportModal, setDisableReportModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  const [reportModalForm] = Form.useForm();

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const getInvoiceListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getInvoiceList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        if (response.data) {
          setInvoiceListResponse(response.data);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    collapseSideDrawerHandler(false);
    // collapseAsideBarHandler(true);
  };
  const onClickAddDispatchOrder = () => {
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };

  // const getDispatchOrderFromSearch = searchText => {
  //   getInvoiceListFromConfig(null,null, null, searchText);
  // };

  useEffect(() => {
    //api call to get order list TODO: format api params
    const pageNumber = 0;
    const { filters, searchkey } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getInvoiceListFromConfig(null, pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Invoice" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by Invoice Number"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={
              value => {
                updateParamsAndNavigate(navigate, location, { searchkey: value });
              }
              // getDispatchOrderFromSearch
            }
            bordered={false}
            enterButton
          />
        </div>
        <Badge color="#23568A" count={filterCount}>
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => setShowFiltersModal(true)}
          >
            <img src={filterIcon} />
            <span>Filter</span>
          </Button>
        </Badge>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              updateParamsAndNavigate(navigate, location, {});
              // getInvoiceListFromConfig();
              setShowClear(false);
              setFilterCount(0);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createCustomerOrder, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().invoice().add().build()}>
            <Button
              onClick={onClickAddDispatchOrder}
              type="primary"
              size="large"
              className={css.btnFlexStyle}
            >
              <img src={addIcon} />
              <span>Add Invoice</span>
            </Button>
          </Link>
        ) : null}
        {hasPermission(userPermissionsList.generateReport, user.permissions) ? (
          <Button
            onClick={() => setShowReportModal(true)}
            type="primary"
            size="large"
            className={css.btnFlexStyle}
          >
            <span>Generate Report</span>
          </Button>
        ) : null}
      </div>
      <Table
        columns={invoiceHeader}
        dataSource={invoiceListReponse?.content?.map((invoice, index) => {
          return {
            ...invoice,
            key: index,
          };
        })}
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          current: invoiceListReponse?.pageable?.pageNumber + 1,
          pageSize: invoiceTablePageSize,
          total: invoiceListReponse?.totalElements,
          onChange: page => {
            const { filters, searchkey } = getQeryParamsFromURL();
            getInvoiceListFromConfig(invoiceTablePageSize, page - 1, filters, searchkey);
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = invoiceListReponse.content.filter(
                invoice => record.id === invoice.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
      <FilterModal
        view="orderbook"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          // getInvoiceListFromConfig(null,null, values, null);
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
      <ReportModal
        disabled={disableReportModal}
        open={showReportModal}
        form={reportModalForm}
        onCancel={() => setShowReportModal(false)}
        onSubmit={async () => {
          const values = await reportModalForm.validateFields();
          setDisableReportModal(true);
          const response = await generateReport(values);
          reportModalForm.resetFields()
          console.log('respose ', response);
          setDisableReportModal(false);
          setShowReportModal(false);
        }}
      />
    </>
  );
};

export default InvoiceListView;

InvoiceListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};
