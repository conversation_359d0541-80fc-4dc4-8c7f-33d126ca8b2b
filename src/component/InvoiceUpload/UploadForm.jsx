import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Input, Col, Form, Row, DatePicker } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';
import FileUpload from '../../component/FileUpload';
import { getCustomerList } from '../../service/api/customerApi';
import { getSupplierList } from '../../service/api/supplierService';
import { createInvoiceV2 } from '../../service/api/invoiceApi';
import { getTasks } from '../../service/api/taskService';
import { getDispatchOrderList } from '../../service/api/dispatchOrderApi';
import { getSupplierByName } from '../../service/api/supplierOrderBookApi';
import { calculateMarginData } from '../../service/api/invoiceApi';
import PropTypes from 'prop-types';
import currencyRates from '../../util/currency.json';
import { message } from 'antd';
import useClickTracker from '../PostHog/useClickTracker';

const { TextArea } = Input;

const costDescriptions = [
  'Supplier Invoice',
  'First mile logistics',
  'Port charges',
  'Sea Freight',
  'Warehouse',
  'Last mile logistics',
  'Destination Charges',
  'Duty Applicable',
  'Others',
];

const deviationReasons = [
  'No Deviation Observed',
  'Cost Price changed from Supplier',
  'Did not estimate this cost earlier',
  'Estimated the cost but ended up being more',
  'Extra cost due to penalty',
  'Market price increased',
  'Scope of work got changed',
  'Others',
];

const fieldsToClear = [
  'invoiceNumber',
  'invoiceDate',
  'invoiceAmount',
  'currency',
  'dueDate',
  'taxAmount',
  'invoiceQty',
  'reasonForDeviation',
  'remarks',
  'invoiceFile',
  'conversionRate',
];

const currencies = ['USD', 'EUR', 'JPY', 'INR', 'CNY', 'AED'];

const UploadForm = ({
  setShowMarginAnalysis,
  setRfqData,
  supplierOrder,
  customerOrder,
  invoice,
  setInvoice,
  setMarginData,
  masterCurrency,
  marginData,
  setPOData,
  POData,
  setLoading,
}) => {
  const [form] = Form.useForm();
  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: '',
    invoiceDate: '',
    currency: '',
    invoiceAmount: '',
    taxAmount: '',
    invoiceQty: '',
    dueDate: '',
    costDescription: '',
    reasonForDeviation: '',
    remarks: '',
    file: null,
    supplierName: '',
    customerName: '',
    supplierPO: '',
    chemicalName: '',
    customerPO: '',
    dispatchOrder: '',
    supplierCustomerName: '',
    customCostDescription: '',
    conversionRate: 1.00000, // Add this

  });
  const [customerNames, setcustomerNames] = useState([]);
  const [supplierCustomerNames, setSupplierCustomerNames] = useState([]);
  const [supplierNames, setsupplierNames] = useState([]);
  const [activityData, setActivityData] = useState();
  const [customerDispatchOrders, setCustomerDispatchOrders] = useState();
  const [supplierPos, setSupplierPos] = useState();
  const [supplierPOData, setSupplierPOData] = useState();
  const [chemicalNames, setChemicalNames] = useState();
  const [ids, setIds] = useState();
  const [calculatedValues, setCalculatedValues] = useState({
    variance: 0,
    deviation: 0,
  });
  const prevCostDescriptionRef = useRef();
  const { trackClick } = useClickTracker();

  useEffect(() => {
    prevCostDescriptionRef.current = invoiceData.costDescription;
  }, []);

  // clearing stale values in case of deviation and variance calculation.
  useEffect(() => {
    const currentCostDescription = invoiceData.costDescription;

    if (
      (currentCostDescription === 'Supplier Invoice' ||
        (prevCostDescriptionRef.current &&
          prevCostDescriptionRef.current === 'Supplier Invoice')) &&
      !customerOrder
    ) {
      setShowMarginAnalysis(false);
      clearFormFields();
      setMarginData({});
      clearSpecificFields(['customerPO', 'dispatchOrder', 'supplierPO', 'chemicalName']);
    }

    // Update the previous value reference
    prevCostDescriptionRef.current = currentCostDescription;
  }, [invoiceData.costDescription]);

  // Handle supplier name or customer name selection for Supplier Invoice
  useEffect(() => {
    if (invoiceData.costDescription === 'Supplier Invoice') {
      const selectedName = invoiceData.supplierName || '';
      const customerName = invoiceData.supplierCustomerName || '';
      clearSpecificFields(['supplierPO', 'chemicalName']);

      if (selectedName || customerName) {
        setLoading(true);
        getSupplierByName(selectedName, customerName) // Replace 'Lakeland' with actual parameter
          .then(response => {
            let newResponse = response?.data?.map(supplier => supplier.orderBookId);
            setSupplierPOData(response?.data);
            setSupplierPos(newResponse);
          })
          .catch(error => {
            console.error('Error fetching supplier by name:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
     if(!customerOrder){ setShowMarginAnalysis(false);
      setMarginData({});}
      clearFormFields();
    }
  }, [invoiceData.supplierName, invoiceData.supplierCustomerName]);

  // Add the method
  const clearSpecificFields = fieldsToReset => {
    const formValues = {};
    const stateValues = {};

    fieldsToReset.forEach(field => {
      formValues[field] = undefined;
      stateValues[field] = '';
    });

    form.setFieldsValue(formValues);

    setInvoiceData(prev => ({
      ...prev,
      ...stateValues,
    }));
  };

  //resetting the values
  useEffect(() => {
    clearSpecificFields(['customerPO', 'dispatchOrder']);
  }, [invoiceData.customerName]);

  // Handle customer name selection for other categories
  useEffect(() => {
    if (invoiceData.costDescription && invoiceData.costDescription !== 'Supplier Invoice') {
      if (invoiceData.customerName) {
        setShowMarginAnalysis(false);
        clearFormFields();
        setMarginData({});
        setLoading(true);
        const payload = {
          size: 100,
          number: 0,
          filters: {
            status: ['TODO', 'IN_PROGRESS'],
            name: ['Invoice Upload'],
          },
          search: {
            customerName: invoiceData.customerName,
          },
        };

        getTasks(payload)
          .then(response => {
            const newData = response?.data?.content?.map(task => task.secondaryId);
            setActivityData(newData);
            setIds(
              response?.data?.content?.map(task => {
                return {
                  customerOrderBookId: task.orderId,
                  customerPurchcaseOrderId: task.secondaryId,
                };
              })
            );
          })
          .catch(error => {
            console.error('Error fetching tasks:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [invoiceData.customerName]);

  // Handle supplier PO selection
  useEffect(() => {
    if (invoiceData.supplierPO) {
      // Find the matching supplier PO from supplierPOData
      const matchingSupplierPO = supplierPOData?.find(
        po => po.orderBookId === invoiceData.supplierPO
      );
      
      if (!matchingSupplierPO) {
        return; // Return empty array if no match found
      }
      clearSpecificFields(['chemicalName']);
      // Extract chemical names from products
      const chemicalNames =
        matchingSupplierPO.products?.map(product => product.product?.tradeName) || [];

      setPOData({
        customerPurchcaseOrderId: matchingSupplierPO?.matchedActivities[0]?.secondaryId,
        customerOrderBookId: matchingSupplierPO?.matchedActivities[0]?.orderId,
      });
      // Filter out any undefined/null values
      setMarginData(null);
      setChemicalNames(chemicalNames.filter(name => name));
      getDispatchOrderListFromConfig(
        100,
        0,
        {
          purchaseOrderNumber: [matchingSupplierPO?.matchedActivities[0]?.secondaryId], //matchingSupplierPO?.matchedActivities[0]?.secondaryId
        },
        {},
        matchingSupplierPO?.matchedActivities[0]?.secondaryId
      );
      // supplierPOData?.filter
    }
  }, [invoiceData.supplierPO]);

  // Handle customer PO selection
  useEffect(() => {
    if (invoiceData.customerPO || customerOrder) {
      if (customerOrder) {
        setPOData({
          customerPurchcaseOrderId: customerOrder?.purchaseOrderNumber,
          customerOrderBookId: customerOrder?.orderBookId,
        });
      } else {
        setPOData({
          customerPurchcaseOrderId: invoiceData.customerPO,
          customerOrderBookId: ids?.find(
            id => id.customerPurchcaseOrderId === invoiceData.customerPO
          )?.customerOrderBookId,
        });
      }
      setMarginData(null);
      clearSpecificFields(['dispatchOrder']);
      getDispatchOrderListFromConfig(
        100,
        0,
        {
          purchaseOrderNumber: [customerOrder?.purchaseOrderNumber || invoiceData?.customerPO], //customerOrder.purchaseOrderNumber || invoiceData.customerPO
        },
        {},
        customerOrder?.purchaseOrderNumber || invoiceData?.customerPO
      );
    }
  }, [invoiceData.customerPO, customerOrder]);

  // Handle dispatch orderList
  const getDispatchOrderListFromConfig = (
    pageSize,
    pageNumber,
    filters,
    searchkey,
    purchaseOrderNumber
  ) => {
    setLoading(true);
    getDispatchOrderList(pageSize, pageNumber, filters, '')
      .then(response => {
        let newData = response?.data?.content?.map(order => order.orderId);
        setCustomerDispatchOrders(newData);
        setShowMarginAnalysis(true);
        let rfqData = response?.data?.content;
        setRfqData(rfqData);
        calculateMarginData(purchaseOrderNumber).then(res => {
          if (res.data) {
            let invoiceData = res.data;
            setMarginData(invoiceData);
          }
        });
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    try {
      let initialFormValues = {};
      let initialInvoiceData = {};
      if (supplierOrder && supplierOrder.length > 0) {
        let prefillData = {
          supplierPO: supplierOrder.map(supplier => supplier.orderBookId),
          chemicalNames: supplierOrder.map(supplier =>
            supplier.products.map(product => product.product.tradeName)
          ),
          supplierNames: supplierOrder.map(supplier => supplier.supplier.name),
        };
        // Get unique supplier names for the dropdown
        setsupplierNames([...new Set(prefillData?.supplierNames)]);
        const selectedSupplierName = prefillData.supplierNames[0];
        // Filter ALL POs for the selected supplier (not just first one)
        const selectedSupplierPOs = supplierOrder
          .filter(supplier => supplier.supplier.name === selectedSupplierName)
          .map(supplier => supplier.orderBookId);

        setSupplierPos(selectedSupplierPOs);

        setSupplierPos(selectedSupplierPOs);

        // Also get all chemicals for this supplier from all their POs
        const selectedSupplierChemicals = supplierOrder
          .filter(supplier => supplier.supplier.name === selectedSupplierName)
          .flatMap(supplier => supplier.products.map(product => product.product.tradeName));

        setChemicalNames(selectedSupplierChemicals);

        // setSupplierPos(prefillData?.supplierPO);
        // setChemicalNames(prefillData?.chemicalNames?.flat());
        setsupplierNames(prefillData?.supplierNames);

        initialFormValues.supplierPO = prefillData.supplierPO[0];
        initialFormValues.chemicalName = prefillData.chemicalNames[0][0];
        initialFormValues.supplierName = prefillData.supplierNames[0];

        initialInvoiceData = { ...initialInvoiceData, ...initialFormValues };
      }

      if (customerOrder) {
        let prefillData = {
          customerName: customerOrder.customer.name,
          customerPO: customerOrder.purchaseOrderNumber,
        };
        setcustomerNames([prefillData?.customerName]);
        setSupplierCustomerNames([prefillData?.customerName]);
        setActivityData([prefillData?.customerPO]);
        initialFormValues.customerName = prefillData.customerName;
        initialFormValues.customerPO = prefillData.customerPO;
        initialFormValues.supplierCustomerName = prefillData.customerName;
        initialInvoiceData = { ...initialInvoiceData, ...initialFormValues };
      }

      if (!customerOrder) {
        getSupplierList().then(response => {
          if (response.data && response.data.content) {
            let supplierNames = response.data.content.map(supplier => supplier.name);
            setsupplierNames(supplierNames);
          }
        });
        getCustomerList()
          .then(response => {
            if (response.data && response.data.content) {
              let customerNames = response.data.content.map(customer => customer.name);
              setcustomerNames(customerNames);
              setSupplierCustomerNames(customerNames);
            }
          })
          .catch(error => {
            console.log(error);
          });
      }

      // Set form values and state in one go
      if (Object.keys(initialFormValues).length > 0) {
        form.setFieldsValue(initialFormValues);
        setInvoiceData(prevData => ({ ...prevData, ...initialInvoiceData }));
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  }, [form, customerOrder]);

  const calculateTotal = () => {
    const amount = parseFloat(invoiceData.invoiceAmount) || 0;
    const tax = parseFloat(invoiceData.taxAmount) || 0;
    return (amount + tax).toFixed(2);
  };

  const handleSubmit = values => {
    if (
      !(values.supplierName || values.supplierCustomerName) &&
      values.costDescription === 'Supplier Invoice'
    ) {
      message.error('Either Select Supplier or Customer Name to proceed!!');
      return;
    }
    setLoading(true);
    const costType =
      values.costDescription === 'Others' ? values.customCostDescription : values.costDescription;
    let payload = {};
    payload.customerPurchcaseOrderId = POData?.customerPurchcaseOrderId;
    payload.customerName =
      values.costDescription === 'Supplier Invoice'
        ? values.supplierCustomerName
        : values.customerName;
    payload.customerOrderBookId = POData?.customerOrderBookId;

    const output = {
      ...(values.costDescription === 'Supplier Invoice'
        ? {
            // For Supplier Invoice - send first 2 fields
            supplierName: values.supplierName,
            supplierOrderId: values.supplierPO,
            chemicalName: values.chemicalName,
          }
        : {
            // For other cost descriptions - send next 2 fields
            customerDispatchOrderId: values.dispatchOrder,
          }),
      invoiceType: costType,
      invoiceNumber: values.invoiceNumber,
      invoiceDate: values.invoiceDate,
      invoiceAmountWithoutTax: values.invoiceAmount,
      allocatedAmount: values.allocatedAmount,
      currencyType: values.currency,
      dueDate: values.dueDate || '',
      taxAmount: values.taxAmount || '',
      invoiceQuantity: values.invoiceQty || '',
      totalAmount: String(
        (parseFloat(values.invoiceAmount || 0) + parseFloat(values.taxAmount || 0)).toFixed(2)
      ),
      reasonforDeviation: values.reasonForDeviation || '',
      remarks: values.remarks || '',
      invoiceFile: values?.invoiceFile === '' ? null : values?.invoiceFile,
      conversionRate: values.conversionRate,
      // not in values, use static or provide dynamically
    };
    if (!payload.invoices) {
      payload.invoices = [];
    }
    payload.invoices.push(output);

    createInvoiceV2(payload)
      .then(response => {
        console.log('Invoice uploaded successfully:', response.data);
        calculateMarginData(POData?.customerPurchcaseOrderId).then(res => {
          if (res.data) {
            let invoiceData = res.data;
            setMarginData(invoiceData);
          }
        });
        delete payload.invoices;
        payload.invoice_type = costType;
        payload.action_name = 'Uploading Invoice';
        trackClick('button_clicked', payload);
        clearFormFields();

        message.success('Invoice uploaded successfully!'); 
      })
      .catch(error => {
        console.error('Error uploading invoice:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.currency) {
      let parentCurrency = masterCurrency;
      console.log("currency", masterCurrency);

      if (parentCurrency) {
        const directRate =
          currencyRates[changedValues.currency][`${changedValues.currency}${parentCurrency}`] ||
          1.0;
        const rateString = directRate.toFixed(5);
        const rate = parseFloat(rateString);
        // Set the conversion rate in the form
        form.setFieldsValue({ conversionRate: rate });
        
        // Update state with both currency and conversion rate
        setInvoiceData(prevData => ({
          ...prevData,
          currency: changedValues.currency,
          conversionRate: rate,
        }));
      } else {
        // Handle other field changes normally
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
        }));
      }
    }
    // Handle cost description change
    else if (changedValues.costDescription) {
      // If changing away from "Others", clear the custom description
      if (changedValues.costDescription !== 'Others') {
        form.setFieldsValue({ customCostDescription: undefined });
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
          customCostDescription: '',
        }));
      } else {
        setInvoiceData(prevData => ({
          ...prevData,
          ...changedValues,
        }));
      }
    } else {
      // Handle other field changes
      setInvoiceData(prevData => ({
        ...prevData,
        ...changedValues,
      }));
    }
  };

  const isSupplierInvoice = invoiceData.costDescription === 'Supplier Invoice';
  const isOtherInvoiceType =
    invoiceData.costDescription && invoiceData.costDescription !== 'Supplier Invoice';
  const watchedSupplierName = Form.useWatch('supplierName', form);
  const watchedSupplierCustomerName = Form.useWatch('supplierCustomerName', form);
  const watchedSupplierPO = Form.useWatch('supplierPO', form);
  const watchedCustomerName = Form.useWatch('customerName', form);
  const watchedCustomerPO = Form.useWatch('customerPO', form);

  // Add useEffect to recalculate when values change
  useEffect(() => {
    let invoiceAmount = calculateTotal();
    const conversionRate = form.getFieldValue('conversionRate');
    const costDescription = form.getFieldValue('costDescription');

    if (invoiceAmount && conversionRate && costDescription && marginData) {
      const calculated = calculateVarianceAndDeviation(
        costDescription,
        invoiceAmount,
        conversionRate,
        marginData
      );
      setCalculatedValues(calculated);
    }
  }, [
    form.getFieldValue('invoiceAmount'),
    form.getFieldValue('conversionRate'),
    form.getFieldValue('costDescription'),
    form.getFieldValue('taxAmount'),
    marginData,
  ]);

  const calculateVarianceAndDeviation = (
    costDescription,
    invoiceAmount,
    conversionRate,
    marginData
  ) => {
    if (!marginData || !costDescription || !invoiceAmount || !conversionRate) {
      return { variance: 0, deviation: 0 };
    }

    const COST_TYPE_MAPPING = {
      'Supplier Invoice': 'Supplier Invoice',
      'First mile logistics': 'First mile logistics',
      'Sea Freight': 'Sea Freight',
      'Last mile logistics': 'Last mile logistics',
      'Destination Charges': 'Destination Charges',
      'Duty Applicable': 'Duty Applicable',
    };

    const mappedCostType = COST_TYPE_MAPPING[costDescription];
    const costData = marginData.find(item => item.costType === mappedCostType);
    const rfqValue = costData?.rfqValue || 0;
    const convertedInvoiceAmount = parseFloat(invoiceAmount) * parseFloat(conversionRate);
    // Get existing invoice value (already converted) if any
    const existingInvoiceValue = costData?.invoiceValue || 0;
    const newTotalInvoiceValue = existingInvoiceValue + convertedInvoiceAmount;
    // Calculate variance (simple subtraction after conversion)
    const variance = rfqValue - newTotalInvoiceValue;
    // Calculate deviation: (rfq - newValue) / rfqValue * 100
    const deviation = rfqValue !== 0 ? ((rfqValue - newTotalInvoiceValue) / rfqValue) * 100 : 100;

    return {
      variance: parseFloat(variance.toFixed(2)),
      deviation: parseFloat(deviation.toFixed(2)),
    };
  };

  const clearFormFields = () => {
    const clearedValues = {};
    fieldsToClear.forEach(field => {
      clearedValues[field] = field.includes('Date') ? undefined : '';
    });

    form.setFieldsValue(clearedValues);

    setInvoiceData(prevData => ({
      ...prevData,
      ...clearedValues,
      file: null,
    }));

    setCalculatedValues({
      variance: 0,
      deviation: 0,
    });
  };

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <FileTextOutlined className="h-6 w-6" />
          Upload Invoice
        </div>
      }
      className="w-full max-w-4xl mx-auto"
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit} onValuesChange={onValuesChange}>
        {/* File Upload section */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg text-center mb-4">
          <Row justify="center" style={{ marginBottom: 0 }}>
            <Col span={4}>
              <Form.Item
                name="invoiceFile"
                rules={[{ required: true, message: 'Please upload Invoice File' }]}
                style={{
                  marginTop: 16,
                  marginBottom: 0,
                  '.ant-form-item-explain-error': {
                    whiteSpace: 'nowrap',
                  },
                }}
              >
                <FileUpload
                  category="Orders"
                  meta={{ poId: 123, documentType: 'PurchaseOrder' }}
                  isSingle={true}
                />
              </Form.Item>
            </Col>
          </Row>
          <p className="text-sm text-gray-500 ">
            {invoiceData.file ? invoiceData.file.name : 'Upload PDF, JPEG, or PNG files'}
          </p>
        </div>

        {/* Cost Description */}
        <Form.Item
          label="Cost Description"
          name="costDescription"
          rules={[{ required: true, message: 'Please select cost description' }]}
        >
          <Select placeholder="Select cost type">
            {costDescriptions.map((desc, idx) => (
              <Select.Option key={idx} value={desc}>
                {desc}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Custom Cost Description - Show only when "Others" is selected */}
        {invoiceData.costDescription === 'Others' && (
          <Form.Item
            label="Cost Description Name"
            name="customCostDescription"
            rules={[
              {
                required: true,
                message: 'Please enter cost description name',
              },
            ]}
          >
            <Input
              placeholder="Enter custom cost description name"
              onChange={e =>
                setInvoiceData(prev => ({
                  ...prev,
                  customCostDescription: e.target.value,
                }))
              }
            />
          </Form.Item>
        )}

        {/* Supplier Invoice Mapping Fields */}
        {isSupplierInvoice && (
          <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-lg font-medium text-blue-900">Supplier Invoice Mapping</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item label="Supplier Name" name="supplierName">
                <Select
                  placeholder="Search and select supplier"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierNames.map((supplier, idx) => (
                    <Select.Option key={idx} value={supplier}>
                      {supplier}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label="Customer Name" name="supplierCustomerName">
                <Select
                  placeholder="Search and select customer"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierCustomerNames?.map((customer, idx) => (
                    <Select.Option key={idx} value={customer}>
                      {customer}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            {(watchedSupplierName ||
              watchedSupplierCustomerName ||
              (supplierOrder && supplierOrder.length > 0)) && (
              <Form.Item
                label="Supplier PO"
                name="supplierPO"
                rules={[{ required: true, message: 'Please select Supplier PO' }]}
              >
                <Select
                  placeholder="Search and select Supplier PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {supplierPos?.map((po, idx) => (
                    <Select.Option key={idx} value={po}>
                      {po}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {(watchedSupplierPO || (supplierOrder && supplierOrder.length > 0)) && (
              <Form.Item label="Chemical Name" name="chemicalName">
                <Select
                  placeholder="Select chemical from Supplier PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {chemicalNames?.map((chemical, idx) => (
                    <Select.Option key={idx} value={chemical}>
                      {chemical}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            <p className="text-sm text-blue-700">
              Note: Select either Supplier Name or Customer Name to proceed with Supplier PO
              selection.
            </p>
          </div>
        )}

        {/* Customer Order Mapping Fields for Other Invoice Types */}
        {isOtherInvoiceType && (
          <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="text-lg font-medium text-green-900">Customer Order Mapping</h3>

            <Form.Item label="Customer Name" name="customerName">
              <Select
                placeholder="Search and select customer"
                showSearch
                optionFilterProp="children"
                allowClear={customerOrder ? false : true}
              >
                {customerNames?.map((customer, idx) => (
                  <Select.Option key={idx} value={customer}>
                    {customer}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            {(watchedCustomerName || customerOrder) && (
              <Form.Item
                label="Customer PO"
                name="customerPO"
                rules={[{ required: true, message: 'Please select Customer PO' }]}
                initialValue={customerOrder ? activityData[0] : undefined}
              >
                <Select
                  placeholder="Search and select Customer PO"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {activityData?.map((po, idx) => (
                    <Select.Option key={idx} value={po}>
                      {po}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {(watchedCustomerPO || customerOrder) && (
              <Form.Item
                label="Dispatch Order"
                name="dispatchOrder"
                initialValue={
                  customerOrder && customerDispatchOrders?.length === 1
                    ? customerDispatchOrders[0]
                    : undefined
                }
              >
                <Select
                  placeholder="Select dispatch order (auto-filled if single)"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                >
                  {customerDispatchOrders?.map((order, idx) => (
                    <Select.Option key={idx} value={order}>
                      {order}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            <p className="text-sm text-green-700">
              Note: Customer PO will be auto-filled when accessed through customer order task.
              Dispatch order will auto-fill if only one exists.
            </p>
          </div>
        )}

        {/* Invoice Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            label="Invoice Number"
            name="invoiceNumber"
            rules={[{ required: true, message: 'Please enter invoice number' }]}
          >
            <Input placeholder="Enter invoice number" />
          </Form.Item>

          <Form.Item
            label="Invoice Date"
            name="invoiceDate"
            rules={[{ required: true, message: 'Please select invoice date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="Currency"
            name="currency"
            rules={[{ required: true, message: 'Please Select Currency' }]}
          >
            <Select placeholder="Select currency">
              {currencies?.map(curr => (
                <Select.Option key={curr} value={curr}>
                  {curr}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Add the Conversion Rate field here */}
          <Form.Item
            label="Conversion Rate"
            name="conversionRate"
            rules={[{ required: true, message: 'Please enter conversion rate' }]}
          >
            <Input type="number" step="0.00001" placeholder="0.00000" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="Due Date"
            name="dueDate"
            rules={[{ required: true, message: 'Please select due date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="Invoice Amount (without tax)"
            name="invoiceAmount"
            rules={[
              { required: true, message: 'Please enter invoice amount' },
              {
                validator: (_, value) => {
                  if (value && parseFloat(value) < 0.1) {
                    return Promise.reject(new Error('Invoice amount must be at least 0.1'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input type="number" step="0.01" placeholder="0.00" min="0.1" />
          </Form.Item>

          <Form.Item
            label="Allocated Amount"
            name="allocatedAmount"
            rules={[
              { required: true, message: 'Please enter allocated amount' },
              {
                validator: (_, value) => {
                  if (value && parseFloat(value) < 0) {
                    return Promise.reject(new Error('Allocated amount cannot be negative.'));
                  }
                  if (value && parseFloat(value) > (invoiceData.invoiceAmount + invoiceData.taxAmount)) {
                    return Promise.reject(new Error('Allocated amount cannot be greater than invoice amount + tax amount.'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input type="number" step="0.01" placeholder="0.00" />
          </Form.Item>

          <Form.Item
            label="Tax Amount"
            name="taxAmount"
            rules={[
              { required: false, message: 'Please enter invoice amount' },
              {
                validator: (_, value) => {
                  if (value && parseFloat(value) < 0) {
                    return Promise.reject(new Error('Tax Amount cannot be negative.'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input type="number" step="0.01" placeholder="0.00" />
          </Form.Item>

          <Form.Item label="Invoice Quantity" name="invoiceQty">
            <Input type="number" placeholder="Enter quantity" />
          </Form.Item>

          <Form.Item label="Total Amount">
            <Input
              value={calculateTotal()}
              readOnly
              style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
            />
          </Form.Item>
          {
            <div className="flex justify-between gap-2">
              <Form.Item label="Variance">
                <Input
                  value={calculatedValues?.variance}
                  readOnly
                  style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
                />
              </Form.Item>
              <Form.Item label="Deviation (%)">
                <Input
                  value={calculatedValues?.deviation}
                  readOnly
                  style={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}
                />
              </Form.Item>
            </div>
          }
        </div>

        {/* Reason for Deviation */}
        <Form.Item
          label="Reason for Deviation"
          name="reasonForDeviation"
          rules={[{ required: true, message: 'Please select reason for deviation' }]}
        >
          <Select placeholder="Select reason for deviation">
            {deviationReasons.map(reason => (
              <Select.Option key={reason} value={reason}>
                {reason}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Remarks */}
        <Form.Item
          label="Remarks"
          name="remarks"
          rules={[{ required: true, message: 'Please add remarks' }]}
        >
          <TextArea
            rows={4}
            placeholder="Enter any additional remarks (Mandatory, fill NA if not applicable)"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
            Upload Invoice
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

UploadForm.propTypes = {
  setShowMarginAnalysis: PropTypes.func.isRequired,
  setRfqData: PropTypes.func.isRequired,
  supplierOrder: PropTypes.array,
  customerOrder: PropTypes.object,
  invoice: PropTypes.object,
  setInvoice: PropTypes.func.isRequired,
  setMarginData: PropTypes.func.isRequired,
  masterCurrency: PropTypes.string,
  marginData: PropTypes.arrayOf(PropTypes.object).isRequired,
  POData: PropTypes.object.isRequired,
  setPOData: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
};

export default UploadForm;
