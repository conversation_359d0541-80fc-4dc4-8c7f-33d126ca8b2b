import dayjs from "dayjs";

const months = [
  "Jan", "Feb", "Mar", "Apr",
  "May", "Jun", "Jul", "Aug",
  "Sep", "Oct", "Nov", "Dec"
];

export const isDateExpired = (date = '') => {
  return new Date(date).getTime() < new Date().getTime();
};
// TODO: implement custom format for date: DD/MM/YYYY
export const getDateFromTimeStamp = (timestamp) => {
  if (!timestamp) return null;
  const date = new Date(timestamp);
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
}

export const isDateCloseToExpiry = (date = '', buffer = 1) => {
  const daysInMiliseconds = 24 * 60 * 60 * 1000;
  const dueDate = new Date(date);
  const currentDate = new Date();
  const timeDiff = Math.round(Math.abs(currentDate - dueDate) / daysInMiliseconds)
  return timeDiff <= buffer;
}

export const  convertTimestampsToDayjs = (obj) => {
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];

      if (typeof value === 'object') {
        // If the value is an object, recursively check it
        convertTimestampsToDayjs(value);
      } else if (typeof value === 'string' && key?.toLowerCase()?.includes('date')) {
        // If the value is a string that can be parsed as a date, convert it to a Day.js object
        obj[key] = dayjs(value);
      }
    }
  }
}

export const convertDayjsToTimestamps = (obj) => {
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];

      if (typeof value === 'object' && dayjs.isDayjs(value)) {
        // If the value is a Day.js object, convert it to a timestamp string
        obj[key] = value.toISOString();
      } else if (typeof value === 'object') {
        // If the value is an object, recursively check it
        convertDayjsToTimestamps(value);
      }
    }
  }
}
