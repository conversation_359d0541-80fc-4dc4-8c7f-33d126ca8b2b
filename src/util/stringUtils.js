export const convertToEnumKey = (inputString) => {
  // Replace spaces with underscores and convert to uppercase
  const formattedString = inputString ? inputString.replace(/ /g, '_').toUpperCase() : '';
  return formattedString;
}

export const camelCaseToTitle = (camelCase) => {
  // Use a regular expression to insert spaces before uppercase letters
  const titleCase = camelCase.replace(/([A-Z])/g, ' $1');
  
  // Capitalize the first letter and trim any leading/trailing spaces
  return titleCase.charAt(0).toUpperCase() + titleCase.slice(1).trim();
}

export const isStringEmpty = (string) => !string || !string.trim();

export const enumKeyToTitle = (input) => {
  // Check if the input string is not empty
  if (input.length === 0) {
    return '';
  }

  // Split the input string into words using underscores as delimiters
  const words = input.split('_');

  // Capitalize the first letter of each word and convert the rest to lowercase
  const formattedWords = words.map((word) =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  );

  // Join the formatted words with spaces to create the final string
  const formattedString = formattedWords.join(' ');

  return formattedString;
}