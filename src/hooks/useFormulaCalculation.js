import { useEffect, useState } from 'react';
import { Form } from 'antd';

export const useFormulaCalculation = (form, formula, dependencies) => {
  const [result, setResult] = useState(0);
  
  // Watch all dependency paths
  const watchedValues = dependencies.map(dep => 
    Form.useWatch(dep.path, form)
  );

  // Use Form.getFieldValue as a backup
  const getFieldValues = () => {
    return dependencies.map(dep => form.getFieldValue(dep.path));
  };

 
  useEffect(() => {
    const calculateFormula = () => {
      try {
        // Create a mapping of variables to their values
        const variables = {};
        const currentValues = getFieldValues();
        
        dependencies.forEach((dep, index) => {
          // Use either watched value or directly get field value
          const value = watchedValues[index] ?? currentValues[index];
          variables[dep.variable] = parseFloat(value) || 0;
        });

        // Replace variables in formula with their values
        console.log("formula", formula);
        console.log("variables", variables);
        let calculationString = formula;
        Object.entries(variables).forEach(([variable, value]) => {
          calculationString = calculationString.replace(
            new RegExp(variable, 'g'), 
            value.toString()
          );
        });

        // Evaluate the formula
        console.log("calculationString", calculationString);
        const calculated = eval(calculationString);
        setResult(isNaN(calculated) ? 0 : Number(calculated.toFixed(2)));
      } catch (error) {
        console.error('Error calculating formula:', error);
        setResult(0);
      }
    };

    calculateFormula();
  }, [watchedValues, formula, dependencies, form]);

  return result;
};
