import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import {
  approveCustomerEnquiry,
  closeEnquiry,
  getEnquiryData,
  // negotiateEnquiryQuotation,
} from '../../service/api/enquiryApi';
import RouteFactory from '../../service/RouteFactory';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import {
  enquiryPriorityList,
  enquiryStatusList,
  productIncoTermsList,
  productUOMList,
  supplierQuotationStatusList,
} from '../../constants/formConstant';
import { camelCaseToTitle } from '../../util/stringUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import HeaderPanel from '../headerPanel';
import { Button, Collapse, Descriptions, Tag } from 'antd';
import PageLoader from '../Loaders/PageLoader';
import FileUpload from '../FileUpload';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import add from '../../assets/icons/add_circle_theme.svg';
import approve from '../../assets/icons/approve_theme_color.svg';
import reject from '../../assets/icons/block_theme_red.svg';
import cycle from '../../assets/icons/cycle_theme_color.svg';
import EnquiryQuotationModal from './EnquiryQuotationModal';
import ApproveOrRejectEnquiryModal from './ApproveOrRejectModals/ApproveOrRejectEnquiryModal';
import ApproveOrRejectQuotationModal from './ApproveOrRejectModals/ApproveOrRejectQuotationModal';
import NegotiateEnquiryModal from './NegotiateModal/NegotiateEnquiryModal';

const EnquiryReview = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { enquiryId } = useParams();
  const user = useSelector(state => state.user);

  const [loading, setLoading] = useState(false);
  const [enquiryData, setEnquiryData] = useState({});
  const [showQuotationModal, setQuotationModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState({
    show: false,
    approval: null,
    type: '',
    action: null,
  });

  const fieldItems = [
    {
      key: '1',
      label: 'EnquiryId',
      children: enquiryData.enquiryId,
    },
    {
      key: '2',
      label: 'Status',
      children: enquiryStatusList[enquiryData?.enquiryStatus]?.label,
    },
    {
      key: '3',
      label: 'Created',
      children: getDateFromTimeStamp(enquiryData.createdAt),
    },
    {
      key: '4',
      label: 'Customer',
      children: enquiryData?.customer?.name,
    },
    {
      key: '5',
      label: 'Product',
      children: enquiryData.productName,
    },
    {
      key: '6',
      label: 'CAS ',
      children: enquiryData.casNumber || '-',
    },
    {
      key: '7',
      label: 'Grade',
      children: enquiryData.grade,
    },
    {
      key: '8',
      label: 'Quantity',
      children: enquiryData.quantity,
    },
    {
      key: '9',
      label: 'UOM',
      children: getEnumValueFromList(enquiryData.unitOfMeasure, productUOMList),
    },
    {
      key: '10',
      label: 'Application',
      children: enquiryData.application,
    },
    {
      key: '11',
      label: 'Priority',
      children: (
        <Tag color={enquiryPriorityList?.[enquiryData.enquiryPriority]?.tagColor}>
          {enquiryPriorityList?.[enquiryData.enquiryPriority]?.label}
        </Tag>
      ),
    },
    {
      key: '12',
      label: 'Category',
      children: enquiryData.categories?.map((category, index) => <Tag key={index}>{category}</Tag>),
    },
    {
      key: '13',
      label: 'Incoterm',
      children: (
        <>
          <div>
            <span style={{ fontWeight: '500' }}>Type:</span>
            <span>{getEnumValueFromList(enquiryData?.incoterms?.type, productIncoTermsList)}</span>
          </div>
          <div>
            <span style={{ fontWeight: '500' }}>Country:</span>
            <span>{enquiryData?.incoterms?.country}</span>
          </div>
          {enquiryData?.incoterms?.data && Object.keys(enquiryData.incoterms.data).length > 0
            ? Object.keys(enquiryData.incoterms.data).map(key => (
                <div key={key}>
                  <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                  <span>{enquiryData.incoterms.data[key]}</span>
                </div>
              ))
            : ''}
        </>
      ),
    },
    {
      key: '14',
      label: 'Rejection remark',
      children: enquiryData.rejectionRemark ? enquiryData.rejectionRemark : '',
    },
    {
      key: '15',
      label: 'Negotiation remark',
      children: enquiryData.negotiationRemark ? enquiryData.negotiationRemark : '',
    },
  ];

  const handleEnquiryApproval = actionValue => {
    if (enquiryData?.id) {
      setLoading(true);
      approveCustomerEnquiry(enquiryData?.id, actionValue)
        .then(res => {
          if (res.data) {
            window.location.reload();
          }
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  };

  const handleEnquiryClosure = actionValue => {
    if (enquiryData?.id) {
      setLoading(true);
      closeEnquiry(enquiryData?.id, actionValue)
        .then(res => {
          if (res.data) {
            window.location.reload();
          }
          setLoading(false);
        })
        .catch(err => {
          console.log(err);
          setLoading(false);
        });
    }
  };

  // const handleQuotationNegotiation = negotiateReason => {
  //   if (enquiryData?.id) {
  //     setLoading(true);
  //     negotiateEnquiryQuotation(enquiryData?.id, negotiateReason)
  //       .then(res => {
  //         if (res.data) {
  //           window.location.reload();
  //         }
  //         setLoading(false);
  //       })
  //       .catch(err => {
  //         console.log(err);
  //         setLoading(false);
  //       });
  //   }
  // };

  const openConfirmationModal = modalData => {
    if (modalData.type && modalData.approval !== null) {
      setConfirmationModal({
        ...modalData,
        show: true,
      });
    }
  };
  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const goToDashBoard = () => {
    collapseSideDrawerHandler(true);
    collapseAsideBarHandler(false);
    navigate(new RouteFactory().dashboard().enquiry().build());
  };
  const getQuotationField = quotation => [
    {
      key: '1',
      label: 'Supplier Name',
      children: quotation.supplier?.name,
    },
    {
      key: '2',
      label: 'Product',
      children: quotation.productName,
    },
    {
      key: '3',
      label: 'Status',
      children: supplierQuotationStatusList?.[quotation.supplierQuotationStatus]?.label,
    },
    {
      key: '4',
      label: 'Rejction reason',
      children: quotation.rejectionReason ? quotation.rejectionReason : '',
    },
    {
      key: '5',
      label: 'Uom',
      children: getEnumValueFromList(quotation?.uom, productUOMList),
    },
    {
      key: '6',
      label: 'Logistic Cost($/unit)',
      children: quotation.logisticCost,
    },
    {
      key: '7',
      label: 'Material Cost($/unit)',
      children: quotation.materialCost,
    },
    {
      key: '8',
      label: 'Quoted Price($/unit)',
      children: quotation.quotedPrice,
    },
    {
      key: '9',
      label: 'Minimum quotable price ($/unit) ',
      children:
        quotation.materialCost && quotation.logisticCost
          ? Math.round((quotation.materialCost + quotation.logisticCost) * 1.05)
          : '',
    },
    {
      key: '10',
      label: 'Lead Time',
      children: quotation.leadTime,
    },
    {
      key: '11',
      label: 'Transit Time',
      children: quotation.transitTime,
    },
    {
      key: '12',
      label: 'Supplier Incoterm',
      span: 6,
      children: (
        <div className="flex items-center gap-20">
          <div>
            <div style={{ fontWeight: '500' }}>Type</div>
            <div>{getEnumValueFromList(quotation?.incoterms?.type, productIncoTermsList)}</div>
          </div>
          <div>
            <div style={{ fontWeight: '500' }}>Country</div>
            <div>{quotation?.incoterms?.country}</div>
          </div>
          {quotation?.incoterms?.data && Object.keys(quotation.incoterms.data).length > 0
            ? Object.keys(quotation.incoterms.data).map(key => (
                <div key={key}>
                  <div style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}</div>
                  <div>{quotation.incoterms.data[key]}</div>
                </div>
              ))
            : ''}
        </div>
      ),
    },
    {
      key: '13',
      label: 'Packaging',
      span: 6,
      children: (
        <div className="flex gap-20">
          <div>
            <div style={{ fontWeight: '500' }}>Type</div>
            <div>{quotation?.packaging?.type}</div>
          </div>
          <div>
            <div style={{ fontWeight: '500' }}>Pack size</div>
            <div>{quotation?.packaging?.packSize}</div>
          </div>
          <div>
            <div style={{ fontWeight: '500' }}>Tare size</div>
            <div>{quotation?.packaging?.tareWeight}</div>
          </div>
          <div>
            <div style={{ fontWeight: '500' }}>Dimension</div>
            <div>{quotation?.packaging?.dimension}</div>
          </div>
        </div>
      ),
    },
    {
      key: '14',
      label: 'Documents',
      span: 12,
      children: quotation.documents.map((doc, index) => (
        <div key={index} className="mb-2">
          <div className="font-medium">{doc.documentType}</div>
          <FileUpload value={doc?.files} disabled deleteDisabled />
        </div>
      )),
    },
  ];
  const getQuotationList = () => {
    let list = [];
    if (enquiryData?.quotation) {
      list.push(enquiryData.quotation);
    }
    if (enquiryData?.quotationHistory?.length) {
      list = [...list, ...enquiryData.quotationHistory];
    }
    return list;
  };

  useEffect(() => {
    if (enquiryId) {
      setLoading(true);
      getEnquiryData(enquiryId)
        .then(res => {
          if (res?.data?.id) {
            setEnquiryData(res.data);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [enquiryId]);

  const getConfirmationModal = confirmationModal => {
    if (confirmationModal) {
      if (confirmationModal.type == 'enquiryNegotiate') {
        return (
          <NegotiateEnquiryModal
            enquiryId={enquiryData?.id}
            displayModal={confirmationModal.show}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      } else if (
        confirmationModal.type == 'quotationApproval' ||
        confirmationModal.type == 'quotationRejection'
      ) {
        return (
          <ApproveOrRejectQuotationModal
            displayModal={confirmationModal.show}
            quotationData={enquiryData?.quotation}
            isRejectModal={confirmationModal.type == 'quotationRejection'}
            enquiryId={enquiryData?.id}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      } else {
        return (
          <ApproveOrRejectEnquiryModal
            enquiryId={enquiryData?.id}
            isRejectModal={!confirmationModal.approval}
            displayModal={confirmationModal.show}
            onCancel={() =>
              setConfirmationModal({
                show: false,
                approval: null,
                type: '',
              })
            }
            setLoading={setLoading}
          />
        );
      }
    }
  };

  return (
    <>
      <HeaderPanel
        name="Enquiry Directory"
        sideButtons={
          <Button type="primary" size="large" onClick={goToDashBoard}>
            Cancel
          </Button>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <>
          <Descriptions
            style={{ margin: '5%' }}
            title={`Enquiry Details`}
            layout="vertical"
            bordered
            items={fieldItems}
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
          />
          <div className="mx-[5%] flex flex-wrap items-center gap-2">
            {hasPermission(userPermissionsList.approveEnquiry, user.permissions) &&
            enquiryData.enquiryStatus === enquiryStatusList.INITIATED.value ? (
              <>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: true,
                      type: 'enquiry',
                      action: () => handleEnquiryApproval(true),
                    });
                  }}
                  type="default"
                  className="bg-white flex items-center"
                  loading={loading}
                  icon={<img src={approve} alt="approve" width={20} height={20} />}
                >
                  Approve Enquiry
                </Button>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: true,
                      type: 'enquiry',
                      action: () => handleEnquiryApproval(false),
                    });
                  }}
                  type="default"
                  danger
                  className="bg-white flex items-center"
                  icon={<img src={reject} alt="reject" width={20} height={20} />}
                >
                  Reject Enquiry
                </Button>
              </>
            ) : null}
            {hasPermission(userPermissionsList.createQuotation, user.permissions) &&
            (enquiryData.enquiryStatus === enquiryStatusList.ASSIGNED.value ||
              enquiryData.enquiryStatus === enquiryStatusList.NEGOTIATE.value) ? (
              <Button
                className="col-span-2 flex items-center"
                onClick={() => setQuotationModal(true)}
                type="default"
                icon={<img src={add} style={{ height: '100%', objectFit: 'contain' }} />}
              >
                Add Quotation
              </Button>
            ) : null}
            {hasPermission(userPermissionsList.approveQuotation, user.permissions) &&
            enquiryData.enquiryStatus === enquiryStatusList.QUOTED.value ? (
              <>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: true,
                      type: 'quotationApproval',
                      action: null,
                    });
                  }}
                  type="default"
                  className="bg-white flex items-center"
                  loading={loading}
                  icon={<img src={approve} alt="approve" width={20} height={20} />}
                >
                  Approve Quote
                </Button>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: true,
                      type: 'quotationRejection',
                      action: null,
                    });
                  }}
                  type="default"
                  danger
                  className="bg-white flex items-center"
                  icon={<img src={reject} alt="reject" width={20} height={20} />}
                >
                  Reject Quote
                </Button>
              </>
            ) : null}
            {hasPermission(userPermissionsList.closeEnquiry, user.permissions) &&
            enquiryData.enquiryStatus === enquiryStatusList.QUOTATION_APPROVED.value ? (
              <>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: true,
                      type: 'enquiry',
                      action: () => handleEnquiryClosure(true),
                    });
                  }}
                  type="default"
                  className="bg-white flex items-center"
                  icon={<img src={approve} alt="approve" width={20} height={20} />}
                >
                  Approve Enquiry
                </Button>
                <Button
                  onClick={() => {
                    openConfirmationModal({
                      approval: false,
                      type: 'enquiry',
                      action: () => handleEnquiryClosure(false),
                    });
                  }}
                  type="default"
                  danger
                  className="bg-white flex items-center"
                  icon={<img src={reject} alt="reject" width={20} height={20} />}
                >
                  Reject Enquiry
                </Button>
              </>
            ) : null}
            {hasPermission(userPermissionsList.negotiateEnquiry, user.permissions) &&
            enquiryData.enquiryStatus === enquiryStatusList.QUOTATION_APPROVED.value ? (
              <Button
                onClick={() => {
                  openConfirmationModal({
                    approval: false,
                    type: 'enquiryNegotiate',
                    action: () => handleEnquiryClosure(false),
                  });
                }}
                type="default"
                className="col-span-2 flex items-center"
                loading={loading}
                icon={<img src={cycle} alt="reject" width={20} height={20} />}
              >
                Negotiate
              </Button>
            ) : null}
          </div>
          <Collapse
            defaultActiveKey={['0']}
            style={{ margin: '5%' }}
            items={getQuotationList().map((item, index) =>
              item
                ? {
                    key: index,
                    label: `Quotation ${index + 1}`,
                    children: (
                      <Descriptions
                        layout="vertical"
                        bordered
                        colon={false}
                        items={getQuotationField(item)}
                        labelStyle={{ fontWeight: '700', color: '#23568A' }}
                      />
                    ),
                  }
                : null
            )}
          />
          {showQuotationModal ? (
            <EnquiryQuotationModal
              open={showQuotationModal}
              enquiry={enquiryData}
              onCancel={() => setQuotationModal(false)}
            />
          ) : null}
          {getConfirmationModal(confirmationModal)}
        </>
      )}
    </>
  );
};

export default EnquiryReview;
