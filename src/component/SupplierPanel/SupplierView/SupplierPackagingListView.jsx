import { Descriptions } from 'antd';
import React from 'react';
import PropTypes from 'prop-types';
import SupplierPackagingView from './SupplierPackagingView';

const SupplierPackagingListView = props => {
  const { packageList } = props;

  const getPackagingItems = packageList => {
    return packageList
      ? packageList.map((packageData, index) => {
          return {
            key: index,
            label: `Package ${index + 1}`,
            span: { md: 12, xl: 1 },
            children: <SupplierPackagingView key={index} packagingData={packageData} />,
          };
        })
      : null;
  };

  return <Descriptions colon={false} layout="vertical" items={getPackagingItems(packageList)} labelStyle={{ fontWeight: '700', color: '#23568A' }}/>;
};

export default SupplierPackagingListView;

SupplierPackagingListView.propTypes = {
  packageList: PropTypes.array,
};
