import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types';
import HeaderPanel from '../headerPanel'
import { Button, Select, Table } from 'antd';
import { getInventoryList, getProductsFromInventory } from '../../service/api/inventoryApi';
import { inventoryProductHeader } from '../../constants/TableConstants';
import PageLoader from '../Loaders/PageLoader';
import { Link, useNavigate, useParams } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { useDispatch, useSelector } from 'react-redux';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import { getDateFromTimeStamp } from '../../util/dateUtils';

const InventoryListView = (props) => {

  const { collapseSideDrawerHandler } = props;

  const [pageLoader, setPageLoader] = useState(false);
  const [tableLoader, setTableLoader] = useState(false);
  const [inventoryList, setInventoryList] = useState([]);
  // const [inventorySelected, setInventorySelected] = useState(null);
  const [inventoryProducts, setInventoryProducts] = useState([]);

  const collapseSideDrawer = useSelector(state => state.collapseSideDrawer);
  const { productId, inventoryId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const tableActions = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    render: (_, record) => (
      // hasPermission(userPermissionsList.viewOrderBook, user.permissions) ? (
        <Link to={new RouteFactory().dashboard().inventory().setId(record.inventoryId).setId(record.id).view().build()}>
          <Button type='text'>
            View
          </Button>
        </Link>
      // ) : null
    )
  };

  const getInventoryListFormConfig = (pageSize, pageNumber, filters, searchkey) => {
    setPageLoader(true);
    getInventoryList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        // TODO: proper check for orderBookList
        setInventoryList(response.data);
        setPageLoader(false);
      })
      .catch(error => {
        console.log(error);
        setPageLoader(false);
      });
  };
  const getInventoryProductsFormConfig = (pageSize, pageNumber, filters, inventoryId, productName) => {
    setTableLoader(true)
    getProductsFromInventory(pageSize, pageNumber, filters, inventoryId, productName)
      .then(response => {
        // TODO: proper check for orderBookList
        setInventoryProducts(response.data);
        setTableLoader(false);
      })
      .catch(error => {
        console.log(error);
        setTableLoader(false);
      });
  };
  const handleInventorySelection = (value) => {
    // setInventorySelected(value);
    getInventoryProductsFormConfig(null, null, null, value, null)
    collapseSideDrawerHandler(true);
    navigate(new RouteFactory().dashboard().inventory().build());
  }

  useEffect(() => {
    if (!inventoryList?.content?.length) {
      getInventoryListFormConfig(null, null, null, null);
    }
  }, [])
  useEffect(() => {
    if (productId && inventoryList?.content?.length) {
      const [inventoryRecord] = inventoryList.content.filter(
        inventory => inventoryId === inventory.inventoryId
      );
      const [productRecord] = inventoryProducts.content.filter(
        product => productId === product.id
      );
      if (inventoryRecord && productRecord) {
        dispatch(setSideDrawerData({
          isNewDisplay: true,
          sourceRecord: {
            inventory: inventoryRecord,
            inventoryProduct: productRecord,
          },
        }));
        collapseSideDrawerHandler(false);
      } else {
        navigate(new RouteFactory().dashboard().inventory().build());
      }
    } else {
      navigate(new RouteFactory().dashboard().inventory().build());
    }
    
  }, [productId, inventoryList?.content?.length > 0])

  return (
    <>
      {pageLoader ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} /> : null}
      <HeaderPanel
        name="Inventory"
      />
      <div className={!collapseSideDrawer ? 'py-4 px-1 flex flex-col gap-2.5' : 'pt-8 pb-6 px-8 flex gap-2 justify-between'}>
        {inventoryList?.content?.length ? (
          <Select
            className={!collapseSideDrawer ? 'w-full' : 'w-2/5'}
            placeholder="Select a Inventory to view products"
            onSelect={(value) => handleInventorySelection(value)}
          >
            {inventoryList.content.map((customer) => (
              <Select.Option key={customer.id} value={customer.id}>
                {customer.name}
              </Select.Option>
            ))}
          </Select>
        ) : null}
        {/* <div className={!collapseSideDrawer ? 'w-full' : 'w-2/5'}>
          <Input.Search
            className='w-full bg-gray-100 rounded-xl'
            placeholder="Search by product"
            allowClear
            onSearch={value => {
              getInventoryProductsFormConfig(null, null, null, inventorySelected, value);
            }}
            variant="borderless"
            enterButton
          />
        </div> */}
      </div>
      {collapseSideDrawer ? (
        <Table
          className='px-8 pb-8'
          columns={[...inventoryProductHeader, tableActions]}
          dataSource={inventoryProducts?.content?.map(data => ({...data, key: data.id }))}
          pagination={false}
          loading={tableLoader}
          scroll={{
            x: '100%',
            y: 550,
          }}
        />
      ): (
      <>
        <div className='overflow-auto h-[80vh]'>
          {inventoryProducts?.content?.map((item) => (
            <Link
              key={item.id}
              to={new RouteFactory().dashboard().inventory().setId(item.inventoryId).setId(item.id).view().build()}
              style={{ color: 'unset' }}
            >
              <div
                className='p-4 flex justify-between hover:bg-neutral-50 cursor-pointer'
              >
                <div>
                  <div>{item?.product?.tradeName}, {item?.packaging?.packagingName}</div>
                  {/* <div>{item.packagingName}</div> */}
                </div>
                <div>{getDateFromTimeStamp(item.createdAt)}</div>
              </div>
            </Link>
          ))}
        </div>
        {/* <Pagination
          style={{ margin: '15px 0' }}
          showSizeChanger={false}
          current={orderBookList?.pageable?.pageNumber + 1}
          pageSize={orderBookTablePageSize}
          total={orderBookList?.totalElements}
          onChange={(page) => {
            updateParamsAndNavigate(navigate, location, { page }); 
          }}
        /> */}
      </>
      )}
    </>
  )
}

export default InventoryListView;

InventoryListView.propTypes = {
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};