import React from 'react';
import { CalendarOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import PropTypes from 'prop-types'; // Import PropTypes at top
import { downloadFile } from '../../service/api/storageService';
import MarginApprovalTable from './MarginApprovalTable';
import QCApprovalTable from './QCApprovalTable';

const TaskViewData = ({ record = {}, formData = {} }) => {

  const DocumentsData = [
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: [
        'SDS',
        'TDS',
        'COA',
        'invoice',
        'DGD',
        'COO',
        'productPackagingPhotos',
        'supplierInvoice',
        'labCOA',
      ],
    },
    {
      patch: true,
      path: 'sentForTestingOn',
      inputType: 'date',
      label: 'Sent For Testing On',
      mandatory: true,
      key: 'sentForTestingOn',
    },
    {
      patch: true,
      path: 'sentForTestingBy',
      inputType: 'text',
      label: 'Sent For Testing By',
      mandatory: true,
      key: 'sentForTestingBy',
    },
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'labMoa',
    },
    {
      patch: true,
      path: 'labCoaReceivedBy',
      inputType: 'text',
      label: 'Lab Coa Received By',
      mandatory: true,
      key: 'labCoaReceivedBy',
    },
    {
      patch: true,
      path: 'testingDate',
      inputType: 'text',
      label: 'Testing Happened On',
      mandatory: true,
      key: 'testingDate',
    },
    {
      patch: true,
      path: 'packagingDoneBy',
      inputType: 'text',
      label: 'Packaging Done By',
      mandatory: true,
      key: 'packagingDoneBy',
    },
    {
      patch: true,
      path: 'batchNumber',
      inputType: 'text',
      label: 'Batch Number',
      mandatory: true,
      key: 'batchNumber',
    },
  ];

  const SampleDocumentsData = [
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'incoTerm',
    },
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: [
        'labCOA',
        'SUPPLIER_COA',
        'SUPPLIER_TDS',
        'SUPPLIER_SDS',
        'MSTACK_COA',
        'MSTACK_TDS',
        'MSTACK_SDS',
        'relabelledPhotos',
        'bookingDocuments',
      ],
    },
  ];

  const DestinationApprovalData = [
    {
      patch: true,
      path: 'labMoa',
      inputType: 'text',
      label: 'Lab Moa',
      mandatory: true,
      key: 'incoTerm',
    },
    {
      path: 'documents', // Specific groups only
      inputType: 'document',
      label: 'Documents',
      key: 'documents',
      group: ['destinationRelablledPhotos','labCOA'],
    },
  ];

  const data =
    record.orderType === 'SAMPLE'
      ? record.taskId === '11'
        ? SampleDocumentsData
        : DestinationApprovalData
      : record.taskId === '4'
      ? []
      : DocumentsData;
  const onDownload = async file => {
    try {
      // setLoading(file.uid);
      const apiRes = await downloadFile(file.fileId);
      window.open(apiRes.data.url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    }
  };

  const renderValue = (inputType, value, group = []) => {
    
    if (!value || (typeof value === 'object' && Object.keys(value).length === 0)) return 'N/A';

    switch (inputType) {
      case 'text':
      case 'number':
      case 'supplierSelect':
        return <span className="font-semibold text-gray-800">{value}</span>;

      case 'date':
        return (
          <span className="flex items-center gap-2 font-semibold text-gray-800">
            <CalendarOutlined />
            {dayjs(value).format('DD-MM-YYYY')}
          </span>
        );

      case 'file': {
        const files = Array.isArray(value)
          ? value
          : value && typeof value === 'object'
          ? [value]
          : [];
        return (
          <div className="flex flex-col gap-2">
            {files.length > 0
              ? files.map((file, index) => (
                  <div key={file.fileId || index} className="flex gap-2 flex-wrap items-center">
                    <div>
                      <div>{file.name || 'Unnamed File'}</div>
                    </div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))
              : 'No Files'}
          </div>
        );
      }

      case 'document': {
        // CASE when group is provided - show only specific groups
        const groupsToShow = group.length > 0 ? group : Object.keys(value);

        return groupsToShow
          .filter(docType => Array.isArray(value[docType]) && value[docType].length > 0) // Only keep non-empty groups
          .map((docType, idx) => {
            const files = value[docType];
            return (
              <div key={idx} className="flex flex-col gap-2 mb-3">
                <h4 className="font-medium text-gray-600">{docType}</h4>
                {files.map((file, index) => (
                  <div
                    key={file.fileId || index}
                    className="flex gap-2 flex-wrap items-center bg-gray-100 p-2 rounded-md"
                  >
                    <div>{file.name || `File ${index + 1}`}</div>
                    <span style={{ display: 'flex' }}>
                      <DownloadOutlined
                        onClick={() => onDownload(file)}
                        style={{ marginLeft: '15px', cursor: 'pointer' }}
                      />
                    </span>
                  </div>
                ))}
              </div>
            );
          });
      }

      case 'incoterms':
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Type:</span>
              <span className="font-semibold text-gray-800">{value.type}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Country:</span>
              <span className="font-semibold text-gray-800">{value.country}</span>
            </div>
            {value.data && Object.entries(value.data).map(([key, val]) => (
              <div key={key} className="flex items-center gap-2">
                <span className="text-gray-600">{key}:</span>
                <span className="font-semibold text-gray-800">{val}</span>
              </div>
            ))}
          </div>
        );

      case 'formulaInput':
        return (
          <div className="space-y-2">
            {value && (
              <>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Value:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.value || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Currency:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.currency || 'N/A'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Conversion Rate:</span>
                  <span className="font-semibold text-gray-800">
                    {value?.conversionRate || 'N/A'}
                  </span>
                </div>
              </>
            )}
          </div>
        );

      default:
        return <span>{value}</span>;
    }
  };

  return (
    <>
      {record.taskId === '4' && record.orderType !== 'SAMPLE' ? (
        <div>
          <MarginApprovalTable
            formData={formData}
            onDownload={onDownload}
            record={record}
          />
        </div>
      ) : record.orderType !== 'SAMPLE' ? (
        <div>
          <QCApprovalTable
            formData={formData}
            onDownload={onDownload}
            record={record}
          />
        </div>
      ) : (
        <div className="space-y-4 mt-4">
          {data.map(({ key, label, inputType, group, path }) => {
            const fieldPath = path?.split('.') || [];
            let value = formData;
            fieldPath.forEach(p => {
              value = value?.[p];
            });
            return (
              <div
                key={key}
                className="flex flex-col p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <h4 className="text-sm font-semibold text-gray-700 mb-2">{label}</h4>
                {renderValue(inputType, value, group)}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

TaskViewData.propTypes = {
  record: PropTypes.object.isRequired,
  formData: PropTypes.object.isRequired, // You can shape it more if you want, but object is fine for now
};

export default TaskViewData;
