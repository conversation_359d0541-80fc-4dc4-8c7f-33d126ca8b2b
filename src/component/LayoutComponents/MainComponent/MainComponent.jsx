import React from 'react';
import PropTypes from 'prop-types';
// import css from './MainComponent.module.css';
import { Layout } from 'antd';

const MainComponent = ({ children }) => {
  // fucntion to add props to component passed as children
  //
  // const renderChildrenWithProps = () => {
  //   return React.Children.map(children, (child) => {
  //     return React.cloneElement(child, {
  //       collapseAsideBar: collapseAsideBar,
  //       setCollapseAsideBar: setCollapseAsideBar,
  //     });
  //   });
  // };

  return (
    <Layout.Content
      style={{
        overflow: 'initial',
        minHeight: '100vh',
        position: 'relative',
      }}
    >
      {children}
    </Layout.Content>
  );
};

export default MainComponent;

MainComponent.propTypes = {
  children: PropTypes.element.isRequired,
};
