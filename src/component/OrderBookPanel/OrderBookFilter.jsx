import { Form, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { getEmployeeList } from '../../service/api/employee';
import { getCustomerList } from '../../service/api/customerApi';
import { getProductList } from '../../service/api/productApi';

const OrderBookFilter = props => {
  const { form } = props;

  // TODO: move it to store
  const [accountOwnerList, setAccountOwnerList] = useState([]);
  const [customerList, setCustomerList] = useState([]);
  const [productList, setProductList] = useState([]);

  useEffect(() => {
    if (!accountOwnerList || !accountOwnerList.length) {
      getEmployeeList()
        .then(res => {
          if (res.data && res.data.content) {
            setAccountOwnerList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    if (!customerList || !customerList.length) {
      getCustomerList()
        .then(res => {
          if (res.data && res.data.content) {
            setCustomerList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    if (!productList || !productList.length) {
      getProductList()
        .then(res => {
          if (res.data && res.data.content) {
            setProductList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  return (
    <Form
      form={form}
      layout="vertical"
      // className={css.filterForm}
    >
      {accountOwnerList.length ? (
        <Form.Item name="customer.accountOwner" label="Account Owner">
          <Select mode="multiple" showSearch>
            {accountOwnerList.map(emp => (
              <Select.Option key={emp.id} value={emp.name}>
                {emp.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      {customerList.length ? (
        <Form.Item name="customer.id" label="Customer Name">
          <Select mode="multiple" showSearch optionFilterProp='label'>
            {customerList.map(customer => (
              <Select.Option key={customer.id} value={customer.id} label={customer.name}>
                {customer.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      {productList.length ? (
        <Form.Item name="products.product.id" label="Product Name">
          <Select mode="multiple" showSearch optionFilterProp='label'>
            {productList.map(product => (
              <Select.Option key={product.id} value={product.id} label={product.tradeName}>
                {product.tradeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
    </Form>
  );
};

export default OrderBookFilter;

OrderBookFilter.propTypes = {
  form: PropTypes.object.isRequired,
};
