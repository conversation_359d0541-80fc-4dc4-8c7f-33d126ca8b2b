import React from 'react'
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import EnquiryListView from './EnquiryListView';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';

const EnquiryDashboard = () => {

  const dispatch = useDispatch();

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const sideDrawerDataHandler = data => {
    dispatch(setSideDrawerData(data));
  };

  return (
    <EnquiryListView
      collapseAsideBarHandler={collapseAsideBarHandler}
      collapseSideDrawerHandler={collapseSideDrawerHandler}
      sideDrawerDataHandler={sideDrawerDataHandler}
    />
  )
}

export default EnquiryDashboard