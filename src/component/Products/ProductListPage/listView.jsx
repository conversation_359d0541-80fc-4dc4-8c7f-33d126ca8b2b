import { Button, Input, Table } from 'antd';
import PropTypes from 'prop-types';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import addIcon from '../../../assets/icons/add_circle.svg';
import { ProductHeaders, productTablePageSize } from '../../../constants/TableConstants';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';
import { setSideDrawerData } from '../../../store/actions/sideDrawerData';
import { setSideDrawerStatus } from '../../../store/actions/collapseSideDrawer';
import css from './ProductListView.module.css';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import RouteFactory from '../../../service/RouteFactory';
import { userPermissionsList } from '../../../constants/UserTabsConstants';
import { hasPermission } from '../../../util/userUtils';
import { updateParamsAndNavigate } from '../../../util/route';

const ProductList = ({ productList, getProductHandler, loading }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const user = useSelector(state => state.user);

  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    dispatch(setSideDrawerStatus(false));
  };

  const onAddProduct = () => {
    dispatch(setSideDrawerStatus(true));
    dispatch(setCollapseAsideBar(true));
  }
  // const getProductFromSearch = searchText => {
  //   getProductHandler(null, null,null, searchText);
  // };

  return (
    <>
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by name"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={(val)=>{
              updateParamsAndNavigate(navigate,location,{searchkey:val})
            }}
            bordered={false}
            enterButton
          />
        </div>
        {hasPermission(userPermissionsList.createProduct, user.permissions) ? (
        <Link to={new RouteFactory().dashboard().product().add().build()}>
          <Button onClick={onAddProduct} type="primary" size="large" className={css.btnFlexStyle}>
            <img src={addIcon} />
            <span>Add Product</span>
          </Button>
        </Link>):null}
      </div>
      <Table
        bordered={false}
        loading={loading}
        columns={ProductHeaders}
        dataSource={productList?.content ? productList.content.map((data) => ({ ...data, key: data.id})) : []}
        className={css.tableContainer}
        pagination={{
          showSizeChanger:false,
          current: productList?.pageable?.pageNumber + 1,
          pageSize: productTablePageSize,
          total: productList?.totalElements,
          onChange: page => {
            getProductHandler(productTablePageSize,page - 1, {}, '');
          },
        }}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = productList.content.filter(
                customer => record.id === customer.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
    </>
  );
};

export default ProductList;

ProductList.propTypes = {
  productList: PropTypes.object.isRequired,
  getProductHandler: PropTypes.func.isRequired,
  loading: PropTypes.bool,
};
