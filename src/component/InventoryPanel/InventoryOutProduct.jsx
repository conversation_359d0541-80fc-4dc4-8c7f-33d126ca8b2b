import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { <PERSON>ton, Card, Col, Form, InputNumber, Row, Select } from 'antd';
import { CloseOutlined, CloseCircleFilled } from '@ant-design/icons';
import { getVirtualBatchDetail } from '../../service/api/batchApi';
import { useSelector } from 'react-redux';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';
import ObjectUtil from '../../util/objectUtil';
import { productUOMList } from '../../constants/formConstant';
import InventoryOutBatch from './InventoryOutBatch';
import { useNotificationContext } from '../../provider/NotificationProvider';

const InventoryOutProduct = (props) => {

  const {
    form, field, index, remove, initialValue, setLoading
  } = props;

  const [batchData, setBatchData] = useState([]);

  const { api } = useNotificationContext();
  const packagingList = useSelector(state => state.packagingList);
  const productIdWatch = Form.useWatch(['products', field.name, 'productId'], form);

  useEffect(() => {
    if (productIdWatch && initialValue?.dispatchProducts?.length) {
      const product = initialValue?.dispatchProducts.find((item) => item.product.id === productIdWatch);
      if (product && product?.packaging?.id) {
        const prodData = form.getFieldValue('products');
        prodData[index].productData = product?.product;
        prodData[index].packagingId = product?.packaging?.id;
        prodData[index].packagingData = product?.packaging;
        prodData[index].quantity = product?.quantity;
        prodData[index].uom = product?.uom;
        form.setFieldsValue({
          products: [
            ...prodData,
          ],
        });
        setLoading(true);
        getVirtualBatchDetail(productIdWatch, product?.packaging?.id, initialValue.inventoryId, initialValue?.customerDispatchOrderId).then((res) => {
          if (!res?.data?.length) {
            api.error({
              messgae: 'Error',
              description: 'Can not create Inventory link order as no supplier order exist for linking batch units!!.',
              icon: (
                <CloseCircleFilled
                  style={{
                    color: '#dc2626',
                  }}
                />
              )
            })
          }
          if (res.data) {
            setBatchData(res.data);
            setLoading(false);
          }
        }).catch((err) => {
          console.log(err);
          setLoading(false);
        })
      }
    }
  }, [productIdWatch]);

  useEffect(() => {
    if (initialValue.inventoryId && initialValue?.products?.[index]?.productId) {
      const prodData = form.getFieldValue('products');
      prodData[index] = ObjectUtil.mergeWithoutNull(prodData[index], initialValue?.products?.[index])
      form.setFieldsValue({
        products: prodData,
      });
    }
  }, [initialValue, batchData]);


  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={<CloseOutlined
        onClick={() => {
          remove(field.name);
        }}
      />}
    >
      <Row gutter={16}>
        <Col span={24}>
          {initialValue.dispatchProducts.length ? (
            <Form.Item name={[field.name, 'productId']} label="Product Name">
              <Select showSearch optionFilterProp="label">
                {initialValue.dispatchProducts.map(product => (
                  <Select.Option
                    key={product.id}
                    value={product.product.id}
                    label={product.product.tradeName}
                  >
                    {product.product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <PackagingSelect
            disabled
            packagingList={packagingList}
            field={field}
            fieldKey="packagingId"
            disableCustomPackaging
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Quantity"
            name={[field.name, 'quantity']}
            rules={[
              {
                required: true,
                message: 'Please input quantity!',
              },
            ]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>          
        </Col>
        <Col span={12}>
          <Form.Item
            name={[field.name, 'uom']}
            rules={[
              {
                required: true,
                message: 'Please input unit of measurement!',
              },
            ]}
            label="Unit of measurement(UOM)"
          >
            <Select>
              {productUOMList.map(uom => (
                <Select.Option key={uom.key} value={uom.value}>
                  {uom.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'units']}
            rules={[
              {
                required: true,
                message: 'Please input units',
              },
            ]}
            label="Units"
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.List
            name={[field.name, "productBatches"]}
            rules={[
              {
                required: true,
                message: 'At least 1 product details is required!',
              },
            ]}
          >
            {(fields, { add: addBatch, remove: removeBatch }, { errors }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                }}
              >
                {fields.map((batchField, batchIndex) => {
                  return (
                    <InventoryOutBatch
                      key={`product_${batchField.key}`}
                      form={form}
                      field={batchField}
                      parentField={field}
                      index={batchIndex}
                      remove={removeBatch}
                      bacthData={batchData}
                    />
                  );
                })}
                {fields.length < initialValue?.dispatchProducts?.length ? (
                  <Button type="dashed" onClick={() => addBatch()} block>
                    + Map Supplier Order
                  </Button>
                ) : null}
                <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
    </Card>
  )
}

export default InventoryOutProduct;

InventoryOutProduct.propTypes = {
  form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  remove: PropTypes.func.isRequired,
  initialValue: PropTypes.object.isRequired,
  setLoading: PropTypes.func.isRequired,
};