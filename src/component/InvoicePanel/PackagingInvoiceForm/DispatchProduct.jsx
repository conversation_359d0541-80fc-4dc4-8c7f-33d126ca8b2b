import React, { useEffect, useState } from 'react';
import { Form, Select, Col, InputNumber, Input } from 'antd';
import PropTypes from 'prop-types';
import {
  formModes,
  // productUOMList
} from '../../../constants/formConstant';
import { roundToTwoDecimalPlaces } from '../../../util/formatUtil';

const DispatchProduct = ({ index, productList, form, formView }) => {
  const [prevProductId, setPrevProductId] = useState(undefined);
  const productId = Form.useWatch([`items`, index, 'productId'], form);
  const quantity = Form.useWatch([`items`, index, 'quantity'], form);
  const cpPerUnit = Form.useWatch([`items`, index, 'cpPerUnit'], form);
  const currencyType = Form.useWatch(['currencyType'], form);
  const taxInPercent = Form.useWatch([`items`, index, 'taxInPercent'], form);

  useEffect(() => {
    console.log(formModes, formView);
    console.log('product list ', productList);
    if (prevProductId != productId && prevProductId != undefined) {
      // reset uom volue and cost per mt
      form.setFieldValue(['items', index, 'uom'], undefined);
      form.setFieldValue(['items', index, 'quantity'], undefined);
      form.setFieldValue(['items', index, 'cpPerUnit'], undefined);
      form.setFieldValue(['items', index, 'description'], undefined);
      form.setFieldValue(['items', index, 'taxInPercent'], undefined);
    }
    setPrevProductId(prevProductId);
  }, [productId]);

  useEffect(() => {
    if (quantity && cpPerUnit) {
      let cost = quantity * cpPerUnit;
      if (taxInPercent) {
        let costWithTax = roundToTwoDecimalPlaces(cost + cost * (taxInPercent / 100));
        form.setFieldValue(['items', index, 'costIncludingTax'], costWithTax);
      }
      form.setFieldValue(['items', index, 'cost'], cost);
    }
  }, [quantity, cpPerUnit, taxInPercent]);

  const getCpLabel = () => {
    if (currencyType) {
      if (currencyType == 'RUPEE') return 'CP /Unit';
      else return 'CP /Unit';
    } else return 'CP /Unit';
  };

  return (
    <>
      <Col span={3}>
        <Form.Item
          label="Product Id"
          name={['items', index, 'productId']}
          rules={[{ required: true, message: 'Please select Product' }]}
        >
          <Select>
            {productList.map(product => (
              <Select.Option key={product.id} value={product.id}>
                {product.product.tradeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
      <Col span={2}>
        <Form.Item label="Description" name={['items', index, 'description']}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={2}>
        <Form.Item
          label="Quantity"
          name={['items', index, 'quantity']}
          rules={[
            { required: true, message: 'Please enter quantity' },
            {
              validator: (_, value) => {
                if (value > 0) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('value must be greater than zero'));
              },
            },
          ]}
        >
          <InputNumber />
        </Form.Item>
      </Col>
      {/* <Col span={3}>
        <Form.Item
          name={['items', index, 'uom']}
          label="Unit of measurement"
          rules={[{ required: true, message: 'Please select a Uom' }]}
        >
          <Select>
            {productUOMList.map(uom => (
              <Select.Option key={uom.key} value={uom.value}>
                {uom.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col> */}
      <Col span={2}>
        <Form.Item
          label={getCpLabel()}
          name={['items', index, 'cpPerUnit']}
          rules={[
            { required: true, message: 'Please enter CP' },
            {
              validator: (_, value) => {
                if (value > 0) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('value must be greater than zero'));
              },
            },
          ]}
        >
          <InputNumber />
        </Form.Item>
      </Col>
      <Col span={2}>
        <Form.Item label="Cost" name={['items', index, 'cost']}>
          <InputNumber disabled />
        </Form.Item>
      </Col>
      <Col span={2}>
        <Form.Item
          label="Tax (in %)"
          name={['items', index, 'taxInPercent']}
          rules={[
            { required: true, message: 'Please enter tax' },
            {
              validator: (_, value) => {
                if (value < 0) {
                  return Promise.reject('Tax percent must be greater than or equal zero');
                }
                if ((value * 100) % 1 !== 0) {
                  // Checks if more than 2 digits after decimal
                  return Promise.reject(
                    'Only up to two digits after the decimal point are allowed'
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber />
        </Form.Item>
      </Col>
      <Col span={3}>
        <Form.Item label="Cost(incl tax)" name={['items', index, 'costIncludingTax']}>
          <InputNumber disabled />
        </Form.Item>
      </Col>
    </>
  );
};

DispatchProduct.propTypes = {
  index: PropTypes.number.isRequired,
  productList: PropTypes.array.isRequired,
  form: PropTypes.object.isRequired,
  formView: PropTypes.string,
};

export default DispatchProduct;
