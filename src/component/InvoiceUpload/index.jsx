import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'antd'; // Add Modal to imports
import UploadForm from './UploadForm';
import MarginAnalysisTable from './MarginAnalysisTable';
import { useLocation, useNavigate } from 'react-router-dom';
import { updateTasks } from '../../service/api/taskService';
import { getTasks } from '../../service/api/taskService';
import { message } from 'antd'; // Add this if not already imported
import PageLoader from '../Loaders/PageLoader';
import { calculateMarginData } from '../../service/api/invoiceApi';
import { Badge, Table } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { getInvoice } from '../../service/api/invoiceApi';
import { downloadFile } from '../../service/api/storageService';
import dayjs from 'dayjs';
const mandatoryInvoices = [
  'Supplier Invoice',
  'First mile logistics',
  'Sea Freight',
  'Last mile logistics',
  'Destination Charges',
  'Duty Applicable',
];
import { currencyMap } from '../../constants/formConstant';

const InvoiceDashBoard2 = () => {
  // const [orderData, setOrderData] = useState(null);
  const [showMarginAnalysis, setShowMarginAnalysis] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false); // Add this state
  const [taskData, setTaskData] = useState({ pending: [], completed: [] }); // Add this state
  const [rfqData, setRfqData] = useState();
  const [invoice, setInvoice] = useState();
  const [marginData, setMarginData] = useState();
  const [POData, setPOData] = useState();
  const [loading, setLoading] = useState(false);
  const [selectedInvoiceData, setSelectedInvoiceData] = useState(null);
  const [invoiceTypes, setInvoiceTypes] = useState([]);

  const navigate = useNavigate();
  const location = useLocation();
  const { customerOrder, supplierOrder, data } = location.state || {};

  const handleCancel = () => {
    navigate(-1);
  };
  const handleAllInvoicesUploaded = () => {
    showModal();
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleModalOk = async () => {
    setIsModalVisible(false);

    const payload = {
      size: 100,
      number: 0,
      filters: {
        secondaryId: [POData?.customerPurchcaseOrderId],
        name: ['Invoice Upload'],
      },
      search: {},
    };

    getTasks(payload)
      .then(response => {
        const newData = response?.data?.content[0];
        if (newData) {
          const updatedTask = {
            ...newData,
            status: 'COMPLETED',
          };
          updateTasks(updatedTask).then(response => {
            navigate(-1);
          });
        }
      })
      .catch(error => {
        console.error('Error fetching tasks:', error);
      });
    message.success('Invoice Uploaded Successfully');
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  const calculateTasks = () => {
    if (!marginData?.marginData || !Array.isArray(marginData.marginData)) {
      setTaskData({ pending: [], completed: [] });
      return;
    }

    const completed = [];
    const pending = [];

    // Iterate through marginData and check only mandatory invoices
    marginData.marginData.forEach(item => {
      // Check if this cost type is in the mandatory invoices list
      if (mandatoryInvoices.includes(item.costType)) {
        if (item.invoiceValue && item.invoiceValue > 0) {
          // Invoice value exists and is greater than 0 - completed
          completed.push(item.costType);
        } else {
          // Invoice value is 0, null, undefined, or negative - pending
          pending.push(item.costType);
        }
      }
    });

    // Update the task data state
    setTaskData({
      pending,
      completed,
    });
  };

  // Add this useEffect to automatically calculate tasks when marginData changes
  useEffect(() => {
    if (marginData?.marginData) {
      calculateTasks();
    }
  }, [marginData]);

  // Add this useEffect to handle data prop
  useEffect(() => {
    if (data && data.poId) {
      setLoading(true)
      calculateMarginData(data.poId)
        .then(res => {
          if (res.data) {
            let invoiceData = res.data;
            setMarginData(invoiceData);
            getInvoice(data.poId).then(res => {
              if (res.data) {
                let invoiceData = res.data;
                setSelectedInvoiceData(invoiceData);
                const invoiceTypes = res?.data?.invoices?.map(invoice => ({
                  type: invoice?.invoiceType,
                  invoiceNumber: invoice?.invoiceNumber,
                  totalAmount: invoice?.totalAmount,
                  currency: invoice?.currencyType,
                  invoiceDate: invoice?.invoiceDate,
                  dueDate: invoice?.dueDate,
                  description: invoice?.reasonforDeviation,
                  invoiceFile: invoice?.invoiceFile,

                }));
                setInvoiceTypes(invoiceTypes);
              } else {
                setInvoiceTypes([]);
              }
            });
          }
        })
        .catch(error => {
          console.error('Error fetching margin data:', error);
        })
        .finally(() => {
          setLoading(false); // This will run after all promises complete
        });
        
    }
  }, [data]);

  const onDownload = async file => {
    try {
      setLoading(true);
      const apiRes = await downloadFile(file.fileId);
      window.open(apiRes.data.url, '_self');
    } catch (error) {
      console.error('Error while downloading file:', error);
    }finally {
      setLoading(false);
    }
  };

  const formatDate = dateString => {
    if (!dateString) return 'N/A';

    try {
      // Handle different date formats
      const date = dayjs(dateString);

      // Check if date is valid
      if (!date.isValid()) return 'Invalid Date';

      // Format to readable format (e.g., "15/Dec/2023")
      return date.format('DD/MMM/YYYY');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">LogiStack Invoice Management</h1>
          <p className="text-gray-600">Upload invoices and track margin analysis.</p>
        </div>
        {loading && <PageLoader style={{ backgroundColor: 'rgba(255, 255, 255, 0.6)' }} />}
        {!data && (
          <UploadForm
            setShowMarginAnalysis={setShowMarginAnalysis}
            setRfqData={setRfqData}
            invoice={invoice}
            customerOrder={customerOrder}
            supplierOrder={supplierOrder}
            setInvoice={setInvoice}
            setMarginData={setMarginData}
            masterCurrency={currencyMap[marginData?.masterCurrency] || marginData?.masterCurrency}
            marginData={marginData?.marginData || []}
            POData={POData}
            setPOData={setPOData}
            setLoading={setLoading}
            data={data}
          />
        )}

        {(showMarginAnalysis || data) && (
          <MarginAnalysisTable
            currency="USD"
            marginData={marginData?.marginData || []}
            targetContributionMargin={marginData?.targetQualificationValue}
            overallMarginDeviation={marginData?.overallMarginDeviation}
            masterCurrency={currencyMap[marginData?.masterCurrency] || marginData?.masterCurrency}
            setLoading={setLoading}
          />
        )}

        {data && (
          <>
            {/* Option 2: Header with divider */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Uploaded Invoices</h2>
              <p className="text-sm text-gray-600 mt-1">
                {invoiceTypes?.length || 0} invoice(s) uploaded
              </p>
            </div>
            <Table
              dataSource={invoiceTypes}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'Invoice Type',
                  dataIndex: 'type',
                  key: 'type',
                  render: text => <Badge color="blue" text={text} />,
                },
                {
                  title: 'Invoice Number',
                  dataIndex: 'invoiceNumber',
                  key: 'invoiceNumber',
                  render: invoiceNumber => invoiceNumber || 'N/A',
                },
                {
                  title: 'Amount',
                  dataIndex: 'totalAmount',
                  key: 'totalAmount',
                  render: (totalAmount, record) =>
                    `${record.currency} ${totalAmount?.toLocaleString()}`,
                },
                {
                  title: 'Invoice Date',
                  dataIndex: 'invoiceDate',
                  key: 'invoiceDate',
                  render: text => <span>{formatDate(text)}</span> ,
                },
                {
                  title: 'Due Date',
                  dataIndex: 'dueDate',
                  key: 'dueDate',
                  render: text => <span>{formatDate(text)}</span> ,
                },
                {
                  title: 'Reason For Deviation',
                  dataIndex: 'description',
                  key: 'description',
                  render: text => <span>{text}</span> ,
                },
                {
                  title: 'File',
                  dataIndex: 'invoiceFile',
                  key: 'invoiceFile',
                  render: (invoiceFile) => {
                    if (invoiceFile) {
                      return (
                        <div className="flex items-center gap-2">
                          <span
                            className="text-sm text-gray-600 truncate max-w-32"
                            title={invoiceFile?.name}
                          >
                            {invoiceFile?.name || 'Unknown File'}
                          </span>
                          <Button
                            type="link"
                            size="small"
                            icon={<DownloadOutlined />}
                            onClick={() => onDownload(invoiceFile)}
                            className="p-0"
                            title="Download file"
                          >
                            Download
                          </Button>
                        </div>
                      );
                    }
                    return <span className="text-gray-400">No file</span>;
                  },
                },
              ]}
            />
          </>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center gap-4 pt-6">
          <Button onClick={handleCancel} className="px-8">
            Cancel
          </Button>
          {!data && (
            <Button onClick={handleAllInvoicesUploaded} className="px-8">
              All invoices Uploaded
            </Button>
          )}
          <Modal
            title="Invoice Upload Status"
            open={isModalVisible}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
            okText="Yes"
            cancelText="No"
            cancelButtonProps={{
              type: 'primary', // This highlights the NO button
              danger: true,
            }}
            okButtonProps={{
              type: 'default',
            }}
            width={600}
          >
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-green-600 mb-2">
                  Completed Tasks ({taskData.completed.length})
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  {taskData.completed.map((task, index) => (
                    <li key={index} className="text-green-700">
                      {task}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-red-600 mb-2">
                  Pending Tasks ({taskData.pending.length})
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  {taskData.pending.map((task, index) => (
                    <li key={index} className="text-red-700">
                      {task}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="p-1 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800">Are you sure all invoices has been uploaded?</p>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDashBoard2;
