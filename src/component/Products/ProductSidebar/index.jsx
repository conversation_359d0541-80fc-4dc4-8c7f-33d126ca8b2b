/* eslint-disable react/prop-types */
import { Button, Space, message } from 'antd';
import PropTypes from 'prop-types';
import React from 'react';
import { Link } from 'react-router-dom';
import close from '../../../assets/icons/close.svg';
import edit from '../../../assets/icons/edit_icon.svg';
import view from '../../../assets/icons/view_icon.svg';
import RouteFactory from '../../../service/RouteFactory';
import css from './ProductSideBar.module.css';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';
import { userPermissionsList } from '../../../constants/UserTabsConstants';
import { deleteProduct } from '../../../service/api/productApi';
import { hasPermission } from '../../../util/userUtils';

const ProductSideBar = props => {
  const { product, hide } = props;
  const user = useSelector(state => state.user);

  const dispatch = useDispatch();
  const [messageApi, contextHolder] = message.useMessage();

  const hideSideDrawer = () => {
    hide();
  };
  const openForm = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };

  const handleDelete = id => {
    deleteProduct(id)
      .then(() => {
        console.log('sucessfully deleted');
        window.location.replace(new RouteFactory().dashboard().product().build());
      })
      .catch(err => {
        // handle message using error
        const errMsg = err?.response?.data?.message;
        messageApi.error(errMsg);
      });
  };

  return (
    <div className={css.customerSider}>
      {contextHolder}
      <div className={css.titleBar}>
        <div className={css.title}>{product.tradeName}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().product().setId(product.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => openForm()}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        <Link to={new RouteFactory().dashboard().product().setId(product.id).edit().build()}>
          <Button
            onClick={openForm}
            style={{}}
            type="primary"
            icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            Edit
          </Button>
        </Link>
        {hasPermission(userPermissionsList.deleteProduct, user.permissions)?(<Button onClick={() =>handleDelete(product.id)}>Delete</Button>):null}
      </div>
      <Space direction="vertical" size="large">
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Trade Name
          </div>
          <div className={css.value}>{product.tradeName}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Technical Name
          </div>
          <div className={css.value}>{product.technicalName}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            CAS Number
          </div>
          <div className={css.value}>{product.casNumber}</div>
        </Space.Compact>

        {product.synonyms?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Synonyms
            </div>
            <div className={css.value}>
              {product.synonyms?.map((synonym, i) => (
                <div key={i}>{synonym}</div>
              ))}
            </div>
          </Space.Compact>
        )}

        {product.functions?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Functions
            </div>
            <div className={css.value}>
              {product.functions?.map((func, i) => (
                <div key={i}>{func}</div>
              ))}
            </div>
          </Space.Compact>
        )}

        {product.family?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Family
            </div>
            <div className={css.value}>
              {product.family?.map((fam, i) => (
                <div key={i}>{fam}</div>
              ))}
            </div>
          </Space.Compact>
        )}

        {product.features?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Features
            </div>
            <div className={css.value}>
              {product.features?.map((feature, i) => (
                <div key={i}>{feature}</div>
              ))}
            </div>
          </Space.Compact>
        )}

        {product.specifications?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Specifications
            </div>
            <div className={css.value}>
              {product.specifications?.map((specification, i) => (
                <div key={i}>{specification}</div>
              ))}
            </div>
          </Space.Compact>
        )}
        {product.categories?.length && (
          <Space.Compact block direction="vertical">
            <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
              Categories/Sub Categories
            </div>
            <div className={css.value}>
              {product.categories?.map((category, i) => (
                <div key={i}>
                  <span>{category.category}</span>/<span>{category.subCategory}</span>
                </div>
              ))}
            </div>
          </Space.Compact>
        )}
      </Space>
    </div>
  );
};

export default ProductSideBar;

ProductSideBar.proptypes = {
  customer: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
};
