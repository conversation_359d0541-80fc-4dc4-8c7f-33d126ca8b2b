import { Badge, Button, Input, Table } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import addIcon from '../../assets/icons/add_circle.svg';
import filterIcon from '../../assets/icons/filter_icon.svg';
import {
  supplierDispatchOrderHeader,
  supplierDispatchOrderTablePageSize,
} from '../../constants/TableConstants';
import { formModes } from '../../constants/formConstant';
import { getSupplierDispatchOrderList } from '../../service/api/supplierDispatchOrderApi';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import { formatSupplierDispatchOrderList } from '../../util/formUtil';
import css from '../CustomerPanel/CustomerListView.module.css';
import FilterModal from '../FilterModal/FilterModal';
import HeaderPanel from '../headerPanel';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';
import RouteFactory from '../../service/RouteFactory';

const SupplierDispatchOrderListView = props => {
  const { collapseAsideBarHandler, collapseSideDrawerHandler, setDispatchOrderFormView } = props;

  const [dispatchOrderList, setDispatchOrderList] = useState({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [loading, setLoading] = useState(false);
  const user = useSelector(state => state.user);
  const [filterCount, setFilterCount] = useState(0);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const getDispatchOrderListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getSupplierDispatchOrderList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        // TODO: proper check for dispatchOrderList
        setDispatchOrderList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    collapseSideDrawerHandler(false);
    // collapseAsideBarHandler(true);
  };
  const onClickAddDispatchOrder = () => {
    setDispatchOrderFormView({
      show: true,
      mode: formModes.CREATE,
    });
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };
  // const getDispatchOrderFromSearch = searchText => {
  //   getDispatchOrderListFromConfig(null,null, null, searchText);
  // };

  useEffect(() => {
    //api call to get order list TODO: format api params
    const pageNumber = 0;
    const { filters, searchkey } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getDispatchOrderListFromConfig(
      supplierDispatchOrderTablePageSize,
      pageNumber,
      filters,
      searchkey
    );
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Supplier Dispatch Order" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by PO Number"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={
              value => {
                updateParamsAndNavigate(navigate, location, { searchkey: value });
              }
              // getDispatchOrderFromSearch
            }
            bordered={false}
            enterButton
          />
        </div>
        <Badge color="#23568A" count={filterCount}>
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => setShowFiltersModal(true)}
          >
            <img src={filterIcon} />
            <span>Filter</span>
          </Button>
        </Badge>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              updateParamsAndNavigate(navigate, location, {});
              // getDispatchOrderListFromConfig();
              setShowClear(false);
              setFilterCount(0);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createSupplierOrder, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().supplierDispatchOrder().add().build()}>
            <Button
              onClick={onClickAddDispatchOrder}
              type="primary"
              size="large"
              className={css.btnFlexStyle}
            >
              <img src={addIcon} />
              <span>Add Order</span>
            </Button>
          </Link>
        ) : null}
      </div>
      <Table
        bordered={false}
        columns={supplierDispatchOrderHeader}
        dataSource={formatSupplierDispatchOrderList(dispatchOrderList?.content)}
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          current:(dispatchOrderList?.pageable?.pageNumber)? dispatchOrderList?.pageable?.pageNumber + 1:1,
          pageSize: supplierDispatchOrderTablePageSize,
          total: dispatchOrderList?.totalElements,
          onChange: page => {
            const { filters, searchkey } = getQeryParamsFromURL();
            getDispatchOrderListFromConfig(
              supplierDispatchOrderTablePageSize,
              page - 1,
              filters,
              searchkey
            );
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = dispatchOrderList.content.filter(
                customer => record.key === customer.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
      <FilterModal
        view="supplierOrderBook"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          // getDispatchOrderListFromConfig(null,null, values, null);
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
    </>
  );
};

export default SupplierDispatchOrderListView;

SupplierDispatchOrderListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
  setDispatchOrderFormView: PropTypes.func.isRequired,
};
