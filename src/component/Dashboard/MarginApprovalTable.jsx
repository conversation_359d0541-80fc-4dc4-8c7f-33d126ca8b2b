import React from 'react';
import PropTypes from 'prop-types';
import { Table } from 'antd';
import { Card, Row, Col, Space, Button, Typography, Tabs } from 'antd';
import { UserOutlined, CalendarOutlined, PaperClipOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Text } = Typography;

// Generic method to format complex objects with line breaks
const formatComplexObject = (obj, prefix = '') => {
    if (!obj || typeof obj !== 'object') {
      return obj || 'N/A';
    }
  
    // Properties to exclude from display
    const excludedProperties = ['createdBy', 'createdAt', 'deleted', 'id'];
    
    const parts = [];
    
    // Handle special properties first (if they exist)
    if (obj.type) {
      parts.push(`Type: ${obj.type}`);
    }
    
    if (obj.country) {
      parts.push(`Country: ${obj.country}`);
    }
    
    // Handle nested data object
    if (obj.data && typeof obj.data === 'object') {
      Object.entries(obj.data)
        .filter(([key, value]) => 
          value !== null && 
          value !== undefined && 
          !excludedProperties.includes(key)
        )
        .forEach(([key, value]) => {
          // Format the key from camelCase to Title Case
          const formattedKey = key
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
          parts.push(`${formattedKey}: ${value}`);
        });
    }
    
    // Handle all other properties
    Object.entries(obj)
      .filter(([key, value]) => 
        value !== null && 
        value !== undefined && 
        key !== 'type' && 
        key !== 'country' && 
        key !== 'data' &&
        !excludedProperties.includes(key) // Exclude the specified properties
      )
      .forEach(([key, value]) => {
        // Skip nested objects (except arrays which we'll join)
        if (typeof value === 'object' && !Array.isArray(value)) {
          return;
        }
        
        // Format the key from camelCase to Title Case
        const formattedKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/^./, str => str.toUpperCase())
          .trim();
        
        // Format arrays by joining their values
        const formattedValue = Array.isArray(value) ? value.join(', ') : value;
        
        parts.push(`${formattedKey}: ${formattedValue}`);
      });
    
    // Return a React fragment with line breaks
    return (
      <div>
        {parts.map((part, index) => (
          <div key={index}>{part}</div>
        ))}
      </div>
    );
  };
  
const MarginApprovalTable = ({ formData, onDownload }) => {

  // Define columns for details tables (same structure)
  const detailsColumns = [
    {
      title: 'Details',
      dataIndex: 'attribute',
      key: 'attribute',
      width: '40%',
      className: 'font-medium',
    },
    {
      title: 'Values',
      dataIndex: 'value',
      key: 'value',
      width: '60%',
    },
  ];

  const financialColumns = [
    {
      title: 'Particulars',
      dataIndex: 'particulars',
      key: 'particulars',
      className: 'font-medium',
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      align: 'right',
    },
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
    },
    {
      title: 'Currency Conversion Factor',
      dataIndex: 'conversionFactor',
      key: 'conversionFactor',
      align: 'right',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'right',
    },
    {
      title: 'Unit of Measurement',
      dataIndex: 'uom',
      key: 'uom',
    },
  ];

  const financialTableData = [
    {
      key: 'salesOrderValue',
      particulars: 'Sales Order Value',
      value: formData?.supplierRfq?.salesOrderValue?.value || 'N/A',
      currency: formData?.supplierRfq?.salesOrderValue?.currency || 'N/A',
      conversionFactor: '-',
      quantity: formData?.products[0]?.quantity || 'N/A',
      uom: formData?.products[0]?.uom || "N/A"
    },
    {
      key: 'costPrice',
      particulars: 'Pruchase Order Value',
      value: formData?.supplierRfq?.purchaseOrderValue?.value || 'N/A',
      currency: formData?.supplierRfq?.purchaseOrderValue?.currency || 'N/A',
      conversionFactor: formData?.supplierRfq?.purchaseOrderValue?.conversionRate || "N/A",
      quantity: formData?.supplierRfq?.quotedQuantity || "N/A",
      uom: formData?.products[0]?.uom || "N/A"
    },
    {
      key: 'firstMileLogistics',
      particulars: 'First Mile Logistics (if applicable)',
      value: formData?.supplierRfq?.firstMileLogistics || 'N/A',
      currency: formData?.supplierRfq?.firstMileLogistics ? formData?.supplierRfq?.logisticsCost?.currency : 'N/A',
      conversionFactor: '-',
      quantity: '-',
      uom: '-',
    },
    {
      key: 'seaFreight',
      particulars: 'Sea Freight (If Applicable)',
      value: formData?.supplierRfq?.seaFreight || 'N/A',
      currency: formData?.supplierRfq?.seaFreight ? formData?.supplierRfq?.logisticsCost?.currency : 'N/A',
      conversionFactor: '-',
      quantity: '-',
      uom: '-',
    },
    {
      key: 'lastMileLogistics',
      particulars: 'Last Mile - Inland Logistics (If Applicable)',
      value: formData?.supplierRfq?.lastMileLogistics || 'N/A',
      currency: formData?.supplierRfq?.lastMileLogistics ? formData?.supplierRfq?.logisticsCost?.currency : 'N/A',
      conversionFactor: '-',
      quantity: '-',
      uom: '-',
    },
    {
      key: 'destinationCharges',
      particulars: 'Destination Charges',
      value: formData?.supplierRfq?.destinationCharges || 'N/A',
      currency: formData?.supplierRfq?.destinationCharges ? formData?.supplierRfq?.logisticsCost?.currency : 'N/A',
      conversionFactor: '-',
      quantity: '-',
      uom: '-',
    },
    {
      key: 'totalLogisticsCost',
      particulars: 'Total Logistics Cost',
      value: formData?.supplierRfq?.logisticsCost?.value || 'N/A',
      currency: formData?.supplierRfq?.logisticsCost?.currency || "N/A",
      conversionFactor: formData?.supplierRfq?.logisticsCost?.conversionRate || "N/A",
      quantity: '-',
      uom: '-',
    },
    {
      key: 'dutyApplicable',
      particulars: 'Duty applicable',
      value: formData?.supplierRfq?.dutyAmountValue?.value || 'N/A',
      currency: formData?.supplierRfq?.dutyAmountValue?.currency || 'N/A',
      conversionFactor: formData?.supplierRfq?.dutyAmountValue?.conversionRate || 'N/A',
      quantity: '-',
      uom: '-',
    },
    {
      key: 'contributionMargin',
      particulars: 'Contribution Margin',
      value: formData?.supplierRfq?.targetContributionMargin + '%' || 'N/A',
      currency: '-',
      conversionFactor: '-',
      quantity: '-',
      uom: '-',
    },
  ];

  // Function to generate product data for a specific product
  const generateProductColumnarData = (product, index) => {
    if (!product) return [];
    
    return [
      {
        key: `productName-${index}`,
        attribute: 'Product Name',
        value: product?.product?.tradeName || 'N/A',
      },
      {
        key: `category-${index}`,
        attribute: 'Category',
        value: product?.product?.categories?.[0]?.category || 'N/A',
      },
      {
        key: `packaging-${index}`,
        attribute: 'Packaging',
        value: formatComplexObject(product?.packaging),
      },
      {
        key: `hsnCode-${index}`,
        attribute: 'HSN Code',
        value: product?.hsCode || 'N/A',
      },
    ];
  };

  // Transform customer data to columnar format
  const customerColumnarData = [
    {
      key: 'customerName',
      attribute: 'Customer Name',
      value: formData?.customer?.name || 'N/A',
    },
    {
      key: 'customerCountry',
      attribute: 'Customer Country',
      value: formData.supplierRfq?.customerCountry || 'N/A'
    },
    {
      key: 'cityPlaceOfSupply',
      attribute: 'City (Place of Supply)',
      value: formData.supplierRfq?.cityPlaceOfSupply || 'N/A',
    },
    {
      key: 'customerIncoterms',
      attribute: 'Customer Incoterms',
      value: formatComplexObject(formData?.incoterms || 'N/A',),
    },
    {
      key: 'customerPaymentTerm',
      attribute: 'Customer Payment Term',
      value: formData.paymentTerms?.poPaymentTerms || 'N/A',
    },
    {
      key: 'creditPercentage',
      attribute: 'Credit %',
      value: formData.paymentTerms?.creditAmount || 'N/A',
    },
    {
      key: 'creditDays',
      attribute: 'Credit Days',
      value: formData.paymentTerms?.creditorDays || 'N/A',
    },
    {
      key: 'customerCurrency',
      attribute: 'Customer Currency',
      value: formData?.buyerCurrency || 'N/A',
    },
    {
      key: 'dutyApplicable',
      attribute: 'Duty applicable',
      value: formData.supplierRfq?.dutyPercentage + "%" || 'N/A',
    },
  ];

  // Transform supplier data to columnar format
  const supplierColumnarData = [
    {
      key: 'supplierLocation',
      attribute: 'Supplier location / Country of Origin',
      value: formData.supplierRfq?.countryOfOrigin || 'N/A',
    },
    {
      key: "supplierName",
      attribute: 'Supplier Name',
      value: formData.supplierRfq?.supplierName || 'N/A',

    },
    {
      key: 'supplierLeadTime',
      attribute: 'Supplier Lead Time',
      value: formData.supplierRfq?.leadTime || 'N/A',
    },
    {
      key: 'supplierIncoterms',
      attribute: 'Supplier Incoterms',
      value: formatComplexObject(formData.supplierRfq?.incoterms),
    },
    {
      key: 'supplierPaymentTerm',
      attribute: 'Supplier Payment Term',
      value: formData.supplierRfq?.paymentTerms || 'N/A',
    },
    {
      key: 'supplierCreditPercentage',
      attribute: 'Credit %',
      value: formData?.supplierRfq?.creditPercent || 'N/A',
    },
    {
      key: 'supplierCreditDays',
      attribute: 'Credit Days',
      value: formData.supplierRfq?.creditDays || 'N/A',
    },
    {
      key: 'supplierQuoteValidity',
      attribute: 'Supplier Quote Validity',
      value: dayjs(formData.supplierRfq?.supplierQuotationValidity).format('DD MMM YYYY, HH:mm') || "N/A"
    },
  ];

  const renderTableSection = (title, columns, dataSource, summary = null) => (
    <div className="mb-6">
      <h3 className="text-lg font-medium mb-3">{title}</h3>
      <Table 
        columns={columns} 
        dataSource={dataSource} 
        pagination={false}
        rowKey="key"
        bordered
        size="small"
        className="bg-white"
      />
    </div>
  );

  const dutyScreenshotLink = formData?.supplierRfq?.dutyCheckScreenshot || '';
  const recordedBy = formData?.supplierRFQRecordedBy || 'N/A';
  const recordedOn = formData?.supplierRFQRecordedOn 
    ? dayjs(formData?.supplierRFQRecordedOn ).format('DD MMM YYYY, HH:mm') 
    : 'N/A';

  const hasMultipleProducts = formData?.products?.length > 1;

  return (
    <div className="margin-approval-tables">
    {/* Product Details Section */}
    <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Product Details</h3>
        
        {hasMultipleProducts ? (
          <Tabs defaultActiveKey="0" type="card">
            {formData.products.map((product, index) => (
              <TabPane tab={`Product ${index + 1}`} key={index}>
                <Table 
                  columns={detailsColumns} 
                  dataSource={generateProductColumnarData(product, index)} 
                  pagination={false}
                  rowKey="key"
                  bordered
                  size="small"
                  className="bg-white"
                />
              </TabPane>
            ))}
          </Tabs>
        ) : (
          <Table 
            columns={detailsColumns} 
            dataSource={generateProductColumnarData(formData?.products?.[0], 0)} 
            pagination={false}
            rowKey="key"
            bordered
            size="small"
            className="bg-white"
          />
        )}
      </div>
    {renderTableSection('Customer Details', detailsColumns, customerColumnarData)}
    {renderTableSection('Supplier Details', detailsColumns, supplierColumnarData)}
    {renderTableSection('Financial Details', financialColumns, financialTableData)}
    <Card 
        className="mt-8 shadow-sm" 
        style={{ 
          borderRadius: '8px',
          background: '#f9fafc'
        }}
      >
        
        <Row gutter={[24, 16]} className="py-2">
          <Col xs={24} md={8}>
            <Card 
              size="small" 
              className="h-full"
              style={{ 
                borderRadius: '6px',
                height: '100%',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            >
              <Space direction="vertical" size="small" className="w-full">
                <Text strong className="text-gray-700 flex items-center">
                  <PaperClipOutlined className="mr-2" />  Link of the screenshot for the duty check done by you on the ACE portal:
                </Text>
                <Text className="text-gray-600 ml-6">
                  {dutyScreenshotLink?.name}
                </Text>
                {dutyScreenshotLink ? (
                  <Button 
                    type="button" 
                    icon={<DownloadOutlined />} 
                    onClick={() => onDownload(dutyScreenshotLink)}        
                    className="ml-6"
                  >
                    Download Screenshot
                  </Button>
                ) : (
                  <Text className="text-gray-500 italic ml-6">No screenshot attached</Text>
                )}
              </Space>
            </Card>
          </Col>
          
          <Col xs={24} md={8}>
            <Card 
              size="small" 
              className="h-full"
              style={{ 
                borderRadius: '6px',
                height: '100%',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            >
              <Space direction="vertical" size="small">
                <Text strong className="text-gray-700 flex items-center">
                  <UserOutlined className="mr-2" /> Supplier RFQ Recorded By
                </Text>
                <Text className="text-gray-600 ml-6">{recordedBy}</Text>
              </Space>
            </Card>
          </Col>
          
          <Col xs={24} md={8}>
            <Card 
              size="small" 
              className="h-full"
              style={{ 
                borderRadius: '6px',
                height: '100%',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            >
              <Space direction="vertical" size="small">
                <Text strong className="text-gray-700 flex items-center">
                  <CalendarOutlined className="mr-2" /> Supplier RFQ Recorded On
                </Text>
                <Text className="text-gray-600 ml-6">{recordedOn}</Text>
              </Space>
            </Card>
          </Col>
        </Row>
      </Card>
  </div>
  );
};

MarginApprovalTable.propTypes = {
  formData: PropTypes.object,
  onDownload: PropTypes.func,
};

MarginApprovalTable.defaultProps = {
  productData: {},
  customerData: {},
  supplierData: {},
  financialData: {},
};

export default MarginApprovalTable;
