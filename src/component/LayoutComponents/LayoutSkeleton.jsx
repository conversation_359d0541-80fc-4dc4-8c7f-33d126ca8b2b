import React from 'react';
import { useSelector } from 'react-redux';
// import css from './LayoutSkeleton.module.css';
import AsidePanel from './AsidePanel/AsidePanel';
import MainComponent from './MainComponent/MainComponent';
import SideDrawer from './SideDrawer/SideDrawer';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';

const LayoutSkeleton = () => {
  const collapseAsideBar = useSelector(state => state.collapseAsideBar);
  const collapseSideDrawer = useSelector(state => state.collapseSideDrawer);
  const sideDrawerData = useSelector(state => state.sideDrawerData);

  return (
    <Layout hasSider>
      <AsidePanel />
      <Layout
        style={{
          marginLeft: collapseAsideBar ? '0' : '5%',
          marginRight: collapseSideDrawer ? '0' : sideDrawerData?.isNewDisplay ? '70%' : '25%',
          backgroundColor: '#fff',
          transition: 'margin 0.5s ease',
        }}
      >
        <MainComponent>
          <Outlet />
        </MainComponent>
      </Layout>
      <SideDrawer />
    </Layout>
  );
};

export default LayoutSkeleton;
