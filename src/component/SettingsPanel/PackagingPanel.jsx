
import React, { useEffect, useState } from 'react';
import { Button, Input, Modal, Table } from 'antd';
import { PlusOutlined, DeleteOutlined, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { deletePackaging, getAllPackagingDetails, searchPackagingByName } from '../../service/api/packagingApi';
import { userEntity } from '../../constants/UserTabsConstants';
import { formModes } from '../../constants/formConstant';
import { packagingTableHeader } from '../../constants/TableConstants';


const PackagingPanel = () => {

  const [loading, setLoading] = useState(false);
  const [packagingList, setPackagingList] = useState([]);
  const [confirmationModalVisible, setConfirmationModalVisible] = useState({
    show: false,
    entity: null,
  });
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });

  const { api } = useNotificationContext();

  const packagingTableAction = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    width: 300,
    render: (_, record) => (
      <div className='flex items-center flex-wrap'>          
        <Button
          type='text'
          onClick={() =>{
            setConfirmationModalVisible({
              show: true,
              entity: record,
            });
          }}
          icon={<DeleteOutlined />}
        >
          Delete
        </Button>
      </div>
    )
  };

  const fetchPackagingList = () => {
    setLoading(true);
    getAllPackagingDetails().then((res) => {
      if (res?.data) {
        setPackagingList(res.data);
      }
      setLoading(false);
    }).catch((err) => {
      setLoading(false);
      console.log(err);
    });
  }
  const handlePackagingSearch = (name) => {
    setLoading(true);
    searchPackagingByName(name).then((res) => {
      if (res?.data?.content) {
        setPackagingList(res.data.content);
      }
      setLoading(false);
    }).catch((err) => {
      setLoading(false);
      console.log(err);
    });
  }
  const handleAddPackaging = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.packaging,
        mode: formModes.CREATE,
      },
    });
  }
  const handleDeleteConfirmationModal = () => {
    setLoading(true);
    deletePackaging(confirmationModalVisible?.entity?.id).then(()=>{
      setLoading(false);
      setPackagingList((prevState) => {
        return prevState.filter((item) => item.id !== confirmationModalVisible?.entity?.id)
      })
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      api.success({
        messgae: 'Success',
        description: 'Sucessfully deleted',
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        )
      })
      
    }).catch((err)=>{
      // handle message using error
      setLoading(false);
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      const errMsg = err?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    })
  }
  const handleCancelConfirmationModal = () => {
    setConfirmationModalVisible({
      show: false,
      entity: null,
    });
  };

  useEffect(() => {
    fetchPackagingList();
  }, [])

  return (
    <div
      style={{
        height: '100%',
        borderTop: '1px solid rgba(30, 30, 30, 0.1)'
      }}
    >
      <div className='flex m-3'>
        <div className='w-1/2'>
          <Input.Search
            placeholder="Search Packaging by name"
            loading={false}
            size="large"
            allowClear
            onSearch={value => handlePackagingSearch(value)}
            enterButton
          />
        </div>
        <Button
          type='primary'
          icon={<PlusOutlined style={{ color: '#fff' }}/>}
          onClick={() => handleAddPackaging()}
          className='rounded-[4px] ml-auto'
        >
          New Packaging
        </Button>
      </div>
      <Table
        bordered={false}
        loading={loading}
        columns={[...packagingTableHeader, packagingTableAction]}
        dataSource={packagingList.map((item) => ({...item, key: item.id}))}
        scroll={{
          x: '100%',
          y: 550,
        }}
      />
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={fetchPackagingList}
      />
      <Modal
        title={`Do you confirm to delete the packaging: ${confirmationModalVisible?.entity?.type}  ?`}
        open={confirmationModalVisible?.show}
        onOk={handleDeleteConfirmationModal}
        onCancel={() => handleCancelConfirmationModal()}
        destroyOnClose
      />
    </div>
  )
}

export default PackagingPanel