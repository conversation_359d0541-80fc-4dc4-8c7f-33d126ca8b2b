import React, { useEffect, useState } from 'react';
import { Col, DatePicker, Form, Input, Radio, Row, Select } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import { DateFormat, formModes, orderForList, orderTypeList } from '../../constants/formConstant';
import PropTypes from 'prop-types';
import { getCustomerList } from '../../service/api/customerApi';
import enUS from 'antd/es/calendar/locale/en_US';
import FileUpload from '../FileUpload';
import PageLoader from '../Loaders/PageLoader';
import { validatePONumber } from '../../util/formUtil';
import { getInventoryList } from '../../service/api/inventoryApi';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';

const OrderBasicInfo = props => {
  const { form, initialValue, formView, setInitialValue } = props;
  const user = useSelector(state => state.user);
  const hasLogistics = user.permissionsGroups.includes('Logistack');

  const [customerList, setCustomerList] = useState([]);
  const [inventoryList, setInventoryList] = useState([]);
  const [loading, setLoading] = useState(false);
  const formOrderForWatch = Form.useWatch('orderFor', form);
  const formPONumberWatch = Form.useWatch('purchaseOrderNumber', form);
  const formCustomerIdWatch = Form.useWatch('customer', form);
  const formInventoryIdWatch = Form.useWatch('inventoryId', form);

  const getFormPrefilldValues = () =>
    initialValue && (initialValue.purchaseOrderNumber || initialValue?.inventoryId)
      ? {
        ...(initialValue?.inventoryId ? {
          inventoryId: initialValue?.inventoryId,
          orderFor: 'inventory',
        } : {
          orderFor: 'customer',
          customer: initialValue?.customer,
          purchaseOrderFile: initialValue?.purchaseOrderFile,
        }),
        purchaseOrderNumber: initialValue?.purchaseOrderNumber,
        purchaseOrderDate: initialValue?.purchaseOrderDate,
      }
      : {};

  useEffect(() => {
    if (formOrderForWatch === 'customer' && (!customerList || !customerList.length)) {
      setLoading(true);
      getCustomerList()
        .then(res => {
          if (res.data && res.data.content) {
            setCustomerList(res.data.content);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    } else if (formOrderForWatch === 'inventory' && (!inventoryList || !inventoryList.length)) {
      setLoading(true);
      getInventoryList()
        .then(res => {
          if (res.data && res.data.content) {
            setInventoryList(res.data.content);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [formOrderForWatch]);

  useEffect(() => {
    if (initialValue && initialValue.purchaseOrderNumber) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);

  useEffect(() => {
    if (formCustomerIdWatch && !initialValue?.customerData) {
      const customerObj = customerList.find(customer => customer.id === formCustomerIdWatch);
      if (customerObj && customerObj.name) {
        setInitialValue({
          ...initialValue,
          customerData: customerObj,
        });
      }
    }
  }, [formCustomerIdWatch]);

  useEffect(() => {
    if (formInventoryIdWatch && !initialValue?.inventoryData) {
      const inventoryObj = inventoryList.find(inventory => inventory.id === formInventoryIdWatch);
      if (inventoryObj && inventoryObj.name) {
        setInitialValue({
          ...initialValue,
          inventoryData: inventoryObj,
        });
      }
    }
  }, [formInventoryIdWatch]);

  return (
    <>
      {loading ? (
        <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />
      ) : null}
      <Form
        name="order_basic_info"
        layout="vertical"
        scrollToFirstError
        initialValues={getFormPrefilldValues()}
        preserve={false}
        size="middle"
        form={form}
        style={{marginBottom:"20px"}}
      >
        <Row gutter={16}>
          <Row span={24}>
            <Form.Item
              label="Please Select Order type"
              name="orderFor"
              rules={[{ required: true, message: 'Please select Order type' }]}
            >
              <Radio.Group buttonStyle="solid" disabled={formView === formModes.UPDATE}>
                {orderForList.map(item => (
                  <Radio key={item.key} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Row>
        </Row>
        {formOrderForWatch === 'customer' ? (
          <>
            <div className="border-b border-gray-100 last:border-0 pt-6">
              <div className="flex flex-wrap justify-between gap-2 px-6 py-0 ">
                {customerList.length ? (
                  <Form.Item
                    label="Customer Name"
                    name="customer"
                    rules={[{ required: true, message: 'Please select Customer' }]}
                    style={{width:"20%",marginBottom:"0px"}}
                  >
                    <Select
                      showSearch
                      optionFilterProp="label"
                      disabled={formView === formModes.UPDATE}
                    >
                      {customerList.map(customer => (
                        <Select.Option key={customer.id} value={customer.id} label={customer.name}>
                          {customer.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
                <Form.Item
                  hasFeedback
                  label="PO number"
                  name="purchaseOrderNumber"
                  validateTrigger="onBlur"
                  rules={[
                    {
                      required: true,
                      message: 'Please input PO number!',
                    },
                    {
                      // message: 'PO number must be unique',
                      validator: async (_, value) => {
                        if (formView === formModes.UPDATE) return Promise.resolve('');
                        if (!formCustomerIdWatch)
                          return Promise.reject('please select customer before entering PO number');
                        // to avoid sending empty values to backend
                        if (!value) return Promise.reject('');
                        try {
                          const isPoNumberValid = await validatePONumber(
                            formCustomerIdWatch,
                            value,
                            'CUSTOMER'
                          );
                          return isPoNumberValid
                            ? Promise.resolve('')
                            : Promise.reject('PO number already registered');
                        } catch (err) {
                          console.log('po validation error occured ', err);
                          return Promise.reject('Please provide a valid PO number');
                        }
                      },
                    },
                  ]}
                  style={{width:"20%",marginBottom:"0px"}}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="PO date"
                  name="purchaseOrderDate"
                  rules={[
                    {
                      required: true,
                      message: 'Please input PO date!',
                    }, {
                      validator: async (_, currentDate) => {
                        return currentDate && currentDate <= dayjs().endOf('day') ? Promise.resolve() : Promise.reject('date must be less the or equal to current date')
                      }
                    }
                  ]}
                  style={{width:"20%",marginBottom:"0px"}}
                >
                  <DatePicker
                    format={DateFormat}
                    locale={enUS}
                    showToday
                    style={{ width: '100%' }}
                    suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
                    disabled={formView === formModes.UPDATE}
                  />
                </Form.Item>
                <Row>
                <Col span={24}>
                <Form.Item
                  label="Order Type"
                  name="orderType"
                  rules={[
                    {
                      required: true,
                      message: 'Please input Order Type',
                    },
                  ]}
                >
                  <Radio.Group buttonStyle="solid">
                    {orderTypeList.map(item => (
                      <Radio key={item.key} value={item.value}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item
                  label="PO File"
                  name="purchaseOrderFile"
                  rules={[{ required: true, message: 'Please upload PO File' }]}
                  style={{width:"20%",marginBottom:"0px"}}
                >
                  <FileUpload
                    category="Orders"
                    meta={{ poId: formPONumberWatch, documentType: 'PurchaseOrder' }}
                    maxFileLimit={1}
                    btnStyle={{width:"100%"}}
                  disabled={formView === formModes.UPDATE}
                  />
                </Form.Item>
              </Col>
            </Row>
            </div>
            </div>
          </>
        ) : null}
        {formOrderForWatch === 'inventory' ? (
          <Row gutter={16}>
            <Col span={24}>
              {inventoryList.length ? (
                <Form.Item
                  label="Inventory Name"
                  name="inventoryId"
                  rules={[{ required: true, message: 'Please select inventory' }]}
                >
                  <Select
                    showSearch
                    optionFilterProp="label"
                    disabled={formView === formModes.UPDATE}
                  >
                    {inventoryList.map(inventory => (
                      <Select.Option key={inventory.id} value={inventory.id} label={inventory.name}>
                        {inventory.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null}
            </Col>
          </Row>
        ) : null}
      </Form>
    </>
  );
};

export default OrderBasicInfo;

OrderBasicInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
  setInitialValue: PropTypes.func.isRequired,
};
