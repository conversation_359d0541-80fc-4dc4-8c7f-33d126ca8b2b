import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import css from './CustomerListView.module.css';
import { customerHeader, customerTablePageSize } from '../../constants/TableConstants';
import { Badge, Button, Input, Table } from 'antd';
import filterIcon from '../../assets/icons/filter_icon.svg';
import addIcon from '../../assets/icons/add_circle.svg';
import { useDispatch } from 'react-redux';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import FilterModal from '../FilterModal/FilterModal';
import { formatCustomerData } from '../../util/formUtil';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { useSelector } from 'react-redux';
import { hasPermission } from '../../util/userUtils';
import { getCustomerList } from '../../service/api/customerApi';
import HeaderPanel from '../headerPanel';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';

const CustomerListView = props => {
  const { collapseAsideBarHandler, collapseSideDrawerHandler } = props;

  const [customerList, setCustomerList] = useState({});
  const [loading, setLoading] = useState(false);
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [filterCount, setFilterCount] = useState(0);

  const dispatch = useDispatch();
  const user = useSelector(state => state.user);

  const route = new RouteFactory();
  const navigate = useNavigate();
  const location = useLocation();

  const getCustomerListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getCustomerList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        setCustomerList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    collapseSideDrawerHandler(false);
  };
  const onClickAddCustomer = () => {
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };
  // const getCustomerFromSearch = searchText => {
  //   getCustomerListFromConfig(null, null, null, searchText);
  // };

  useEffect(() => {
    //api call to get customer list
    const pageNumber = 0;
    const { filters, searchkey } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getCustomerListFromConfig(null, pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Customer Directory" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by name"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={value => {
              updateParamsAndNavigate(navigate, location, { searchkey: value });
            }}
            bordered={false}
            enterButton
          />
        </div>
        <Badge color="#23568A" count={filterCount}>
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => setShowFiltersModal(true)}
          >
            <img src={filterIcon} />
            <span>Filter</span>
          </Button>
        </Badge>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              updateParamsAndNavigate(navigate, location, {});
              // getCustomerListFromConfig();
              setShowClear(false);
              setFilterCount(0);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null}
        {/* {hasPermission(userPermissionsList.createCustomer, user.permissions) ? 
        (
          <Link to={route.dashboard().customer().add().build()}>
            <Button
              onClick={onClickAddCustomer}
              type="primary"
              size="large"
              className={css.btnFlexStyle}
            >
              <img src={addIcon} />
              <span>Add Customer</span>
            </Button>
          </Link>
        ) : null} */}
      </div>
      <Table
        bordered={false}
        columns={customerHeader}
        dataSource={formatCustomerData(customerList?.content)}
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          current: customerList?.pageable?.pageNumber + 1,
          pageSize: customerTablePageSize,
          total: customerList?.totalElements,
          onChange: page => {
            const { filters, searchKey } = getQeryParamsFromURL();
            getCustomerListFromConfig(customerTablePageSize, page - 1, filters, searchKey);
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = customerList.content.filter(
                customer => record.id === customer.id
              );
              onRowClick(sourceRecord);
            },
          };
        }}
      />
      <FilterModal
        view="customer"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          //getCustomerListFromConfig(null,null, values, null);
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
    </>
  );
};

export default CustomerListView;

CustomerListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};
