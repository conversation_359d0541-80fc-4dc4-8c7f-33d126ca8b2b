import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig';

export const getSupplierList = (pageSize,pageNumber, filters, searchkey) => {
  const supplierConfig = {
    size: pageSize || 1000,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      name: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.supplierSearch, supplierConfig);
};

export const createSupplier = supplierData => {
  return axiosInstance.post(apiEndpoints.supplierCrud, supplierData);
};

export const updateSupplier = (supplierData, id) => {
  return axiosInstance.put(`${apiEndpoints.supplierCrud}/${id}`, supplierData);
};

export const getSupplierData = id => {
  return axiosInstance.get(`${apiEndpoints.supplierCrud}/${id}`);
};


export const deleteSupplier = id => {
  return axiosInstance.delete(`${apiEndpoints.supplierCrud}/${id}`);
};

export const getAllSupplierByProduct = (id) => {
  return axiosInstance.get(`${apiEndpoints.productSuppliers}/${id}`);
}

export const uploadSupplierProductMstackDocument = (supplierId, productId, documents) => {
  return axiosInstance.post(`${apiEndpoints.uploadMstackDocument}`, {
    supplierId, productId, documents,    
  });
}

export const deleteMstackDocument = (supplierId, productId, fileId, documentType) => {
  return axiosInstance.post(`${apiEndpoints.deleteMstackDocument}`, {
    supplierId, productId, fileId, documentType,
  });
}