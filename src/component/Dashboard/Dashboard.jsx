import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Table, Tabs, Select, Radio, Button, Input } from 'antd';
import css from './Dashboard.module.css';
import HeaderPanel from '../headerPanel';
import TaskCompletionPopup from './TaskCompletionPopup';
import OrderLink from './OrderLink';
import { getTasks } from '../../service/api/taskService';
import { getEmployeeList } from '../../service/api/employee';
import { updateTasks } from '../../service/api/taskService';
import PageLoader from '../Loaders/PageLoader';
import { formatEntityType } from '../../util/formUtil';

const targetGroups = ['logistics', 'sourcing', 'finance', 'CoatingsSourcing' ,
  'OilGasSourcing' ,
  'AgroSourcing' ,
 'FoodAdditivesSourcing' ,
 'PharmaceuticalsSourcing' ,
 'PersonalCareSourcing' ,
 'ConstructionSourcing'];

const Dashboard = () => {
  // Data state
  const [taskData, setTaskData] = useState({ content: [], totalElements: 0 });
  const [groupTaskData, setGroupTaskData] = useState({ content: [], totalElements: 0 });

  // UI state
  const [loading, setLoading] = useState(false);
  const [taskUpdated, setTaskUpdated] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);

  // Pagination and loading state
  const [pageSize] = useState(50); // Increased page size for more items per request

  // Use refs for page tracking to avoid re-renders and access latest values in callbacks
  const myTasksPageRef = useRef(0); // 0-based pagination for API
  const groupTasksPageRef = useRef(0); // 0-based pagination for API
  const [loadingMoreMyTasks, setLoadingMoreMyTasks] = useState(false);
  const [loadingMoreGroupTasks, setLoadingMoreGroupTasks] = useState(false);

  // Flags to track if more data is available
  const [hasMoreMyTasks, setHasMoreMyTasks] = useState(true);
  const [hasMoreGroupTasks, setHasMoreGroupTasks] = useState(true);

  // We're using Table onScroll instead of refs

  const user = useSelector(state => state.user);
  const matchedGroups = user?.permissionsGroups?.filter(group => targetGroups.includes(group));

  // Track active filters for each tab
  const [myTasksFilters, setMyTasksFilters] = useState({});
  const [groupTasksFilters, setGroupTasksFilters] = useState({});

  // Function to fetch tasks with support for infinite scrolling and filtering
  const fetchTasks = async (isGroup = false, loadMore = false, filters = null) => {
    // Set appropriate loading state
    if (loadMore) {
      if (isGroup) {
        setLoadingMoreGroupTasks(true);
      } else {
        setLoadingMoreMyTasks(true);
      }
    } else {
      setLoading(true);
    }

    try {
      // If filters are provided, reset pagination and use the new filters
      // Otherwise, use existing filters and current page
      let currentFilters;
      let page;

      if (filters !== null) {
        // New filters - reset pagination
        page = 0;
        currentFilters = filters;

        // Update stored filters
        if (isGroup) {
          setGroupTasksFilters(filters);
          groupTasksPageRef.current = 0; // Reset page using ref
        } else {
          setMyTasksFilters(filters);
          myTasksPageRef.current = 0; // Reset page using ref
        }
      } else {
        // Use existing filters and pagination
        currentFilters = isGroup ? groupTasksFilters : myTasksFilters;
        page = loadMore ? (isGroup ? groupTasksPageRef.current : myTasksPageRef.current) : 0;
      }

      // Create API payload
      const payload = {
        size: pageSize,
        number: page,
        filters: {
          group: isGroup ? matchedGroups : [],
          assignedTo: !isGroup ? [ user?.entityId ] : [],
        },
        search: {
          // Add filter values to search
          ...currentFilters
        },
      };

      // Call API
      const response = await getTasks(payload);
      const newData = response?.data || { content: [], totalElements: 0 };

      // If we're loading more but got no new data, mark as no more data available
      if (loadMore && (!newData.content || newData.content.length === 0)) {
        if (isGroup) {
          setHasMoreGroupTasks(false);
        } else {
          setHasMoreMyTasks(false);
        }
        return; // Exit early
      }

      if (isGroup) {
        // Update page number using ref
        groupTasksPageRef.current = page + 1;

        // If loading more, append new content to existing content
        if (loadMore) {
          setGroupTaskData(prev => ({
            ...newData,
            content: [...prev.content, ...newData.content]
          }));
        } else {
          // Initial load - replace all data
          setGroupTaskData(newData);
        }

        // Check if there are more items to load
        setHasMoreGroupTasks(newData.content.length === pageSize);
      } else {
        // Update page number using ref
        myTasksPageRef.current = page + 1;

        // If loading more, append new content to existing content
        if (loadMore) {
          setTaskData(prev => ({
            ...newData,
            content: [...prev.content, ...newData.content]
          }));
        } else {
          // Initial load - replace all data
          setTaskData(newData);
        }

        // Check if there are more items to load
        setHasMoreMyTasks(newData.content.length === pageSize);
      }

      // Return a resolved promise for chaining
      return Promise.resolve();
    } catch (error) {
      console.error('Error fetching tasks:', error);
      // Return a rejected promise for error handling
      return Promise.reject(error);
    } finally {
      // Reset loading state
      if (loadMore) {
        if (isGroup) {
          setLoadingMoreGroupTasks(false);
        } else {
          setLoadingMoreMyTasks(false);
        }
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    setLoading(true); // Start loading spinner

    getEmployeeList()
      .then(response => {
        if (response?.data?.content) {
          setEmployeeList(response.data.content); // Set employee list if data is present
        }
      })
      .catch(err => {
        console.error('Failed to fetch employee list:', err);
      })
      .finally(() => {
        setLoading(false); // Stop loading spinner
      });
  }, []);

  const onAssignedChange = async (record, value) => {
    const assignedTo = value === 'NONE' ? null : value;
    await updateTasks({ ...record, assignedTo: assignedTo });
    // Refresh data without loading more
    await fetchTasks(false, false);
    await fetchTasks(true, false);
  };

  // Function to handle filter changes
  const handleFilterChange = (columnKey, value, isGroup) => {
    // Get current filters
    const currentFilters = isGroup ? {...groupTasksFilters} : {...myTasksFilters};

    // Update filters
    if (value === undefined || (Array.isArray(value) && value.length === 0) || value === '') {
      // Remove filter if value is empty
      delete currentFilters[columnKey];
    } else {
      // Add/update filter
      currentFilters[columnKey] = value;
    }

    // Fetch data with new filters
    fetchTasks(isGroup, false, currentFilters);
  };

  // Note: We can add a clear all filters button if needed in the future

  // Get active tab
  const [activeTab, setActiveTab] = useState('1');

  const [orderTypeToggle, setOrderTypeToggle] = useState('Customer Order');

  const taskColumns = [
    {
      title: 'PO/ENQ No',
      dataIndex: 'Id',
      key: 'Id',
      width: 140,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search PO/ENQ No"
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('secondaryId', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('secondaryId', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('secondaryId', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record?.secondaryId?.toLowerCase().includes(value?.toLowerCase()),
      render: (_, record) => <OrderLink orderId={record.orderId} setLoading={setLoading} poNumber={record.secondaryId} />,
    },
    {
      title: "Orders",
      dataIndex: "orderId",
      key: "orderId",
      width: 150,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search Orders"
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('entityType', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('entityType', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('entityType', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data - search in both original and formatted values
      onFilter: (value, record) => {
        const originalValue = record.entityType?.toLowerCase() || '';
        const formattedValue = formatEntityType(record.entityType).toLowerCase();
        const searchValue = value?.toLowerCase() || '';
        return originalValue.includes(searchValue) || formattedValue.includes(searchValue);
      },
      render: (_, record) => formatEntityType(record.entityType),
    },
    {
      title: "Order no.",
      dataIndex: "orderNumber",
      key: "orderNumber",
      width: 150,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search Order no."
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('entityId', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('entityId', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('entityId', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.entityId?.toLowerCase().includes(value?.toLowerCase()),
      render: (_, record) => record.entityId || '-',
    },
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search task name"
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('name', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('name', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('name', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.name.toLowerCase().includes(value.toLowerCase()),
      render: (_, { name }) => name,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 125,
      filters: [
        { text: 'TODO', value: 'TODO' },
        { text: 'In Progress', value: 'IN_PROGRESS' },
        { text: 'Completed', value: 'COMPLETED' }, // Fixed duplicate text for "In Progress"
      ],
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Radio.Group
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <Radio value="TODO">TODO</Radio>
              <Radio value="IN_PROGRESS">IN PROGRESS</Radio>
              <Radio value="COMPLETED">COMPLETED</Radio>
            </div>
          </Radio.Group>
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter - use first value if multiple selected
              const statusValue = selectedKeys.length > 0 ? selectedKeys[0] : '';
              handleFilterChange('status', statusValue, activeTab === '2');
            }}
            size="big"
            style={{ marginTop: 8, width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('status', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters?.(); // Optional chaining in case clearFilters is undefined
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.status === value,
      render: (_, { status }) => status,
    },

    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 150,
      filters: [
        { text: 'Me', value: user?.entityId }, // Filter option for "Me"
      ],
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Radio.Group
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <Radio value={user?.entityId}>Me</Radio>
            </div>
          </Radio.Group>
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              const assignedToValue = selectedKeys.length > 0 ? selectedKeys[0] : '';
              handleFilterChange('assignedTo', assignedToValue, activeTab === '2');
            }}
            size="big"
            style={{ marginTop: 8, width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('assignedTo', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters?.();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.assignedTo === value,
      render: (_, record) => (
        <Select
          showSearch
          value={record.assignedTo ?? 'NONE'} // Show 'None' if null/undefined
          style={{ width: '120px' }}
          onChange={value => onAssignedChange(record, value)} // API call handler
        >
          <Select.Option value="NONE">None</Select.Option> {/* Option for unassigned */}
          <Select.Option value={user.entityId}>Me</Select.Option> {/* Assign to Me option */}
          {employeeList
            .filter(employee => employee.id !== user.entityId) // Exclude current user
            .map(employee => (
              <Select.Option key={employee.id} value={employee.id}>
                {employee.name}
              </Select.Option>
            ))}
        </Select>
      ),
    },
    // {
    //   title: 'Type',
    //   dataIndex: 'orderType',
    //   key: 'orderType',
    //   filters: [
    //     { text: 'Customer Order', value: 'Customer Order' },
    //     { text: 'Sample Order', value: 'Sample Order' },
    //   ],
    //   filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
    //     <div style={{ padding: 8 }}>
    //       <Radio.Group
    //         value={selectedKeys[0]}
    //         onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
    //       >
    //         <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
    //           <Radio value="Customer Order">Customer Order</Radio>
    //           <Radio value="Sample Order">Sample Order</Radio>
    //         </div>
    //       </Radio.Group>
    //       <Button
    //         type="primary"
    //         onClick={() => {
    //           confirm();
    //           // Apply server-side filter - use first value if multiple selected
    //           const orderTypeValue = selectedKeys.length > 0 ? selectedKeys[0] : '';
    //           handleFilterChange('orderId', orderTypeValue === 'Customer Order' ? 'COB' :
    //                                         orderTypeValue === 'Sample Order' ? 'SAMPLEOB' : '',
    //                             activeTab === '2');
    //         }}
    //         size="big"
    //         style={{ marginTop: 8, width: '100%', marginBottom: 4 }}
    //       >
    //         Apply
    //       </Button>
    //       <Button
    //         onClick={() => {
    //           // First apply the empty filter to the server
    //           handleFilterChange('orderId', '', activeTab === '2');
    //           // Then clear the UI filter state
    //           clearFilters?.();
    //           // Close the dropdown
    //           confirm({ closeDropdown: true });
    //         }}
    //         size="small"
    //         style={{ width: '100%' }}
    //       >
    //         Reset
    //       </Button>
    //     </div>
    //   ),
    //   // Keep client-side filtering for already loaded data
    //   onFilter: (value, record) => {
    //     const entityID = record.orderId || '';
    //     const orderType = entityID.includes('COB')
    //       ? 'Customer Order'
    //       : entityID.includes('SAMPLEOB')
    //       ? 'Sample Order'
    //       : 'Unknown';
    //     return orderType === value;
    //   },
    //   width: 110,
    //   render: (_, record) => {
    //     const entityID = record.orderId || '';
    //     const orderType = entityID.includes('COB')
    //       ? 'CUSTOMER'
    //       : entityID.includes('SAMPLEOB')
    //       ? 'SAMPLE'
    //       : 'Unknown';
    //     return orderType;
    //   },
    // },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      sorter: (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime(), // Sort by date
      render: (_, { dueDate }) => {
        if (!dueDate) return;
        const dateObj = new Date(dueDate);
        const date = dateObj.toLocaleDateString(); // Format like '3/17/2025' depending on locale
        const time = dateObj.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        });
        return (
          <div>
            <div>{date}</div>
            <div style={{ color: '#aaa', fontSize: '12px' }}>{time}</div>
          </div>
        );
      },
    },
    // {
    //   title: 'Closed On',
    //   dataIndex: 'completedOn',
    //   key: 'completedOn',
    //   width: 120,
    //   sorter: (a, b) =>
    //     new Date(a.taskCompletedOn).getTime() - new Date(b.taskCompletedOn).getTime(), // Sorting logic
    //   render: (_, { taskCompletedOn }) => {
    //     if (!taskCompletedOn) return null;
    //     const dateObj = new Date(taskCompletedOn);
    //     const date = dateObj.toLocaleDateString();
    //     const time = dateObj.toLocaleTimeString([], {
    //       hour: '2-digit',
    //       minute: '2-digit',
    //       hour12: false,
    //     });

    //     return (
    //       <div>
    //         <div>{date}</div>
    //         <div style={{ color: '#aaa', fontSize: '12px' }}>{time}</div>
    //       </div>
    //     );
    //   },
    // },
    {
      title: 'Customer Name',
      dataIndex: 'dueDate',
      key: 'customerName',
      width: 150,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search Customer name"
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('customerName', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('customerName', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('customerName', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.customerName?.toLowerCase().includes(value?.toLowerCase()),
      render: (_, { customerName }) => customerName
    },
    {
      title: 'Product Name',
      dataIndex: 'productName',
      key: 'productName',
      width: 140,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search product name"
            value={selectedKeys[0] || ''}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('productName', selectedKeys[0], activeTab === '2');
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => {
              confirm();
              // Apply server-side filter
              handleFilterChange('productName', selectedKeys[0], activeTab === '2');
            }}
            size="small"
            style={{ width: '100%', marginBottom: 4 }}
          >
            Apply
          </Button>
          <Button
            onClick={() => {
              // First apply the empty filter to the server
              handleFilterChange('productName', '', activeTab === '2');
              // Then clear the UI filter state
              clearFilters();
              // Close the dropdown
              confirm({ closeDropdown: true });
            }}
            size="small"
            style={{ width: '100%' }}
          >
            Reset
          </Button>
        </div>
      ),
      // Keep client-side filtering for already loaded data
      onFilter: (value, record) => record.productName?.toLowerCase().includes(value?.toLowerCase()),
      render: (_, { productName }) => productName,
    },
    {
      title: 'Action',
      key: 'action',
      width: 90,
      render: (_, record) => (
        <TaskCompletionPopup taskData={record} taskUpdated={() => setTaskUpdated(true)} />
      ),
    },

  ];

  const panelItems = [
    {
      key: '1',
      label: 'My Tasks',
      children: (
        <div>
          <Table
            loading={{
              indicator: <PageLoader style={{ backgroundColor: 'transparent' }} />,
              spinning: loading || loadingMoreMyTasks
            }}
            columns={taskColumns}
            dataSource={taskData?.content}
            rowKey="id"
            tableLayout="fixed"
            pagination={false} // Remove pagination for infinite scrolling
            scroll={{
              x: '100%',
              y: 650, // Fixed height in pixels
              scrollToFirstRowOnChange: false // Prevent scrolling to top on data change
            }}
            bordered
          />
          {/* Status message */}
          <div style={{ textAlign: 'center', padding: '10px 0', color: '#999' }}>
            {!hasMoreMyTasks && taskData?.content?.length > 0 ?
              'All tasks loaded' :
              taskData?.content?.length === 0 ?
                'No tasks found' :
                ' '}
          </div>
        </div>
      ),
    },
    {
      key: '2',
      label: 'Group Tasks',
      children: (
        <div>
          <Table
            loading={{
              indicator: <PageLoader style={{ backgroundColor: 'transparent' }} />,
              spinning: loading || loadingMoreGroupTasks
            }}
            columns={taskColumns}
            dataSource={groupTaskData?.content}
            rowKey="id"
            tableLayout="fixed"
            pagination={false} // Remove pagination for infinite scrolling
            scroll={{
              x: '100%',
              y: 650, // Fixed height in pixels
              scrollToFirstRowOnChange: false // Prevent scrolling to top on data change
            }}
            bordered
          />
          {/* Status message */}
          <div style={{ textAlign: 'center', padding: '10px 0', color: '#999' }}>
            {!hasMoreGroupTasks && groupTaskData?.content?.length > 0 ?
              'All tasks loaded' :
              groupTaskData?.content?.length === 0 ?
                'No tasks found' :
                ' '}
          </div>
        </div>
      ),
    },
  ];

  // Track if we're currently loading more data to prevent duplicate calls
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Handle table scroll for infinite scrolling
  const handleTableScroll = useCallback((e, isGroup) => {
    // Check if e.target exists and has the expected properties
    if (!e || !e.target) {
      return;
    }

    // Get the scroll position
    const { scrollTop, scrollHeight, clientHeight } = e.target;

    // Calculate how close to the bottom we are (in pixels)
    const scrollBottom = scrollHeight - scrollTop - clientHeight;

    // Use a larger threshold (250px) to load more data earlier
    // This ensures we load more data before the user reaches the bottom
    if (scrollBottom < 250 && !isLoadingMore) {
      if (isGroup && hasMoreGroupTasks && !loadingMoreGroupTasks) {
        setIsLoadingMore(true); // Set global loading flag
        fetchTasks(true, true).finally(() => {
          // Reset loading flag after a short delay to prevent immediate re-triggering
          setTimeout(() => setIsLoadingMore(false), 500);
        });
      } else if (!isGroup && hasMoreMyTasks && !loadingMoreMyTasks) {
        setIsLoadingMore(true); // Set global loading flag
        fetchTasks(false, true).finally(() => {
          // Reset loading flag after a short delay to prevent immediate re-triggering
          setTimeout(() => setIsLoadingMore(false), 500);
        });
      }
    }
  }, [hasMoreMyTasks, hasMoreGroupTasks, loadingMoreMyTasks, loadingMoreGroupTasks, isLoadingMore]);

  // Initial data loading
  useEffect(() => {
    // Load initial data
    fetchTasks(false, false); // My Tasks

    fetchTasks(true, false);  // Group Tasks
  }, []);

  // Setup scroll event listeners for tables
  useEffect(() => {
    // Function to handle scroll events with debounce
    let scrollTimeout;
    const handleScroll = (e) => {
      // Clear previous timeout to implement debounce
      clearTimeout(scrollTimeout);

      // Set a new timeout to delay processing the scroll event
      scrollTimeout = setTimeout(() => {
        // Determine if this is the My Tasks table or Group Tasks table
        // The active tab is determined by which table is visible
        const isGroup = activeTab === '2';
        handleTableScroll(e, isGroup);
      }, 50); // 50ms debounce
    };

    // Find the table body elements after a short delay to ensure they're rendered
    setTimeout(() => {
      const tableBodyElements = document.querySelectorAll('.ant-table-body');

      if (tableBodyElements.length > 0) {
        // Add scroll event listeners to each table body
        tableBodyElements.forEach(element => {
          element.addEventListener('scroll', handleScroll);
        });
      }
    }, 500); // Wait 500ms for tables to render

    // Cleanup function to remove event listeners
    return () => {
      clearTimeout(scrollTimeout);
      const tableBodyElements = document.querySelectorAll('.ant-table-body');
      tableBodyElements.forEach(element => {
        element.removeEventListener('scroll', handleScroll);
      });
    };
  }, [handleTableScroll, activeTab]);

  // Handle task updates
  useEffect(() => {
    if (taskUpdated) {
      // Refresh data without loading more
      fetchTasks(false, false);
      fetchTasks(true, false);
      setTaskUpdated(false);
    }
  }, [taskUpdated]);

  return (
    <>
      <HeaderPanel name="Dashboard" />
      <div className={css.dashboardContainer}>
        <div className={`flex justify-between items-center mb-2`} style={{ width: '100%' }}>
          <div>
            <div style={{ fontSize: '24px', fontWeight: '600' }}>Hi, {user.name}</div>
            <div style={{  marginTop:'5px',fontSize:'16px',fontWeight:'500'}}>Welcome to Mstack Dashboard.</div>
          </div>
          <div className={css.toggleRowRight}>
            <span
              className={orderTypeToggle === 'Sample Order' ? css.toggleLabelActive : css.toggleLabel}
              onClick={() => {
                setOrderTypeToggle('Sample Order');
                handleFilterChange('orderId', 'SAMPLEOB', activeTab === '2');
              }}
              style={{ cursor: 'pointer' }}
            >
              Sample
            </span>
            <div
              className={
                css.toggleSwitch +
                ' ' +
                (orderTypeToggle === 'Customer Order' ? css.toggleRight : css.toggleLeft)
              }
              onClick={() => {
                const newType =
                  orderTypeToggle === 'Customer Order' ? 'Sample Order' : 'Customer Order';
                setOrderTypeToggle(newType);
                handleFilterChange(
                  'orderId',
                  newType === 'Customer Order' ? 'COB' : 'SAMPLEOB',
                  activeTab === '2'
                );
              }}
              style={{ cursor: 'pointer' }}
            >
              <div className={css.toggleCircle}></div>
            </div>
            <span
              className={orderTypeToggle === 'Customer Order' ? css.toggleLabelActive : css.toggleLabel}
              onClick={() => {
                setOrderTypeToggle('Customer Order');
                handleFilterChange('orderId', 'COB', activeTab === '2');
              }}
              style={{ cursor: 'pointer' }}
            >
              Customer Orders
            </span>
          </div>
        </div>
        <Tabs
          items={panelItems}
          defaultActiveKey="1"
          activeKey={activeTab}
          type="card"
          onChange={key => {
            setActiveTab(key);
            if (key === '2') {
              // Load group tasks when switching to Group Tasks tab
              fetchTasks(true, false);
            }
          }}
        />
      </div>
    </>
  );
};

export default Dashboard;
