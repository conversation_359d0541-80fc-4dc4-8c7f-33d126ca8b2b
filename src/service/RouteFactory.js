class RouteFactory {
  url = '/';
  dashboard() {
    this.url = this.url + 'dashboard';
    return this;
  }
  product() {
    this.url = this.url + '/products';
    return this;
  }
  login() {
    this.url = this.url + 'login';
    return this;
  }
  add() {
    this.url = this.url + '/add';
    return this;
  }
  edit() {
    this.url = this.url + '/edit';
    return this;
  }
  view() {
    this.url = this.url + '/view';
    return this;
  }
  setId(id) {
    this.url = this.url + `/${id}`;
    return this;
  }
  reviewDocument(type) {
    this.url = this.url +  `/${type}`;
    return this;
  }
  dispatchOrder() {
    this.url = this.url + '/dispatch-order';
    return this;
  }
  invoice() {
    this.url = this.url + '/invoice';
    return this;
  }
  customer() {
    this.url = this.url + '/customers';
    return this;
  }
  enquiry() {
    this.url = this.url + '/enquiry';
    return this;
  }
  orderBook() {
    this.url = this.url + '/order-book';
    return this;
  }
  supplierDispatchOrder() {
    this.url = this.url + '/supplier-dispatch-order';
    return this;
  }
  supplier() {
    this.url = this.url + '/supplier';
    return this;
  }
  supplierOrderBook() {
    this.url = this.url + '/supplier-order-book';
    return this;
  }
  inventory() {
    this.url = this.url + '/inventory';
    return this;
  }
  reports() {
    this.url = this.url + '/reports';
    return this;
  }
  mails() {
    this.url = this.url + '/mails';
    return this;
  }
  settings() {
    this.url = this.url + '/settings';
    return this;
  }
  build() {
    return this.url;
  }
  static buildProduct() {
    return new RouteFactory().dashboard().product().build();
  }
}

export default RouteFactory;
