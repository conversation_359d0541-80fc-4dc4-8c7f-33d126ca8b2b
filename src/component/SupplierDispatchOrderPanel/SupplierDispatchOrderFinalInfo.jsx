import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Col, DatePicker, Form, Input, InputNumber, Row, Select, Button } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import {
  DateFormat,
  formConfigName,
  formModes,
  paymentTermsDate,
} from '../../constants/formConstant';
import IncotermInput from '../IncotermInput/IncotermInput';
import { getFormConfigFromName } from '../../service/api/formConfig';
import PageLoader from '../Loaders/PageLoader';
import DocumentUpload from './DocumentUpload';

const SupplierDispatchOrderFinalInfo = props => {
  const { form, initialValue, formView } = props;

  const [documentList, setDocumentList] = useState([]);
  const [loading, setLoading] = useState(false);

  const getFormPrefilldValues = () =>
    initialValue
      ? {
          paymentTerms: { ...initialValue.paymentTerms },
          labelFile: initialValue.labelFile,
          shipmentDate: initialValue.shipmentDate || '',
          incoterms: initialValue.incoterms,
          remarks: initialValue.remarks,
          bookingMadeOn: initialValue.bookingMadeOn || '',
          qcApprovedOn: initialValue.qcApprovedOn || '',
          loadingCompletedOn: initialValue.shippingCommunicatedOn || '',
          sampleReceivingDate: initialValue.sampleReceivingDate || '',
          dispatchToDestinationDate: initialValue.dispatchToDestinationDate || '',
          destinationRecievedDate: initialValue.destinationRecievedDate || '',
          dispatchFromDestinationOn: initialValue.dispatchFromDestinationOn || '',
          supplierDispatchedOn: initialValue.supplierDispatchedOn || '',
          etaForDestination: initialValue.etaForDestination || '',
        }
      : {};

  useEffect(() => {
    if (!documentList || !documentList.length) {
      setLoading(true);
      getFormConfigFromName(formConfigName.documents)
        .then(res => {
          if (res.data) {
            setDocumentList(res.data.value);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);
  useEffect(() => {
    if (initialValue) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue]);

  useEffect(() => {
    console.log(form.getFieldsValue());
  }, [form.getFieldsValue()]);

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  // // Set initial values in the correct format for Form.List
  // useEffect(() => {
  //   // Convert HashMap to Form.List compatible structure
  //   const formattedDocuments = Object.entries(sampleDoc).reduce((acc, [docType, files]) => {
  //     acc[docType] = files.map(file => ({ ...file })); // Convert to array format
  //     return acc;
  //   }, {});

  //   form.setFieldsValue({ documents: formattedDocuments });
  // }, [form]);

  return (
    <Form
      name="order_payment_info"
      layout="vertical"
      scrollToFirstError
      initialValues={formView === formModes.CREATE ? {} : initialValue}
      size="middle"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Supplier ARN Number" name={'arnNumber'}>
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item label="Shipment date ( Sailing Date )" name={'shipmentDate'}>
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <IncotermInput form={form} />
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditAmount']}
            label="Credit amount(in %)"
            rules={[
              {
                required: true,
                message: 'Credit amount can only be a valid number!',
                type: 'number',
              },
            ]}
          >
            <InputNumber disabled style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'creditorDays']}
            label="Credit days"
            rules={[
              {
                required: true,
                message: 'Credit days can only be a valid number!',
                type: 'number',
              },
            ]}
          >
            <InputNumber disabled style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={['paymentTerms', 'startDate']}
            label="Payment terms should be based on?"
            rules={[
              {
                required: true,
                message: 'Field required!',
              },
            ]}
          >
            <Select disabled>
              {paymentTermsDate.map(date => (
                <Select.Option key={date.key} value={date.value}>
                  {date.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="remarks" label="Remarks">
            <MultipleTextFieldInput
              type="TextArea"
              mode={formView}
              placeholder="Enter a remark"
              showDelete={formView === formModes.CREATE}
              savetimeStamp
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.List label="Add Documents" name={['documents']}>
            {(subFields, { add, remove }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                  marginBottom: '24px',
                }}
              >
                <h2 className="text-xl font-semibold text-gray-800">
                  Documents{' '}
                  <span className="text-sm text-gray-500">
                    (Attach COA, costing document, and others as required)
                  </span>
                </h2>
                <Button type="dashed" onClick={() => add()} block>
                  + Add Documents
                </Button>
                {subFields.map((subField, index) => {
                  return (
                    <DocumentUpload key={index} index={index} remove={remove} subField={subField} />
                  );
                })}
              </div>
            )}
          </Form.List>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="MOA" label="MOA">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="bookingMadeOn" label="booking MadeOn">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="carrierName" label="carrier Name">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="vesselTrackingNumber" label="vessel Tracking Number">
            <Input type="number" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="COReceivedBy" label="CO Received By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="qcApprovedBy" label="QC Approved By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="qcApprovedOn" label="QC Approved On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="loadingCompletedOn" label="loading Completed On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="loadingCompletedBy" label="loading Completed By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="shippingCommunicatedOn" label="shipping Communicated On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="sampleReceivingDate" label="sample Receiving Date">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="rePackagingConfirmedBy" label="rePackaging Confirmed By">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="movementApprovedBy" label="movement Approved By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="dispatchToDestinationDate" label="dispatch To Destination Date">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="destinationRecievedDate" label="destination Recieved Date">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="dispatchFromDestinationOn" label="dispatch From Destination On">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="sampleApprovedBy" label="sample Approved By">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="supplierDispatchedOn" label="supplier Dispatched On">
          <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="dispatchFromOrigin" label="dispatch From Origin">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item name="etaForDestination" label="eta For Destination">
            <DatePicker
              format={DateFormat}
              style={{ width: '100%' }}
              suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SupplierDispatchOrderFinalInfo;

SupplierDispatchOrderFinalInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
};
