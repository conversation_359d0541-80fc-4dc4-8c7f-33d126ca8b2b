#!/bin/sh

# Debug: Print environment variables
echo "Debug: Environment variables:"
echo "VITE_APP_API_BASE_URL: ${VITE_APP_API_BASE_URL}"
echo "VITE_APP_ENV: ${VITE_APP_ENV}"
echo "DEV: ${DEV}"
echo "VITE_APP_GOOGLE_CLIENT_ID: ${VITE_APP_GOOGLE_CLIENT_ID}"
echo "VITE_APP_GOOGLE_ANALYTICS_MEASUREMENT_ID: ${VITE_APP_GOOGLE_ANALYTICS_MEASUREMENT_ID}"
echo "VITE_PUBLIC_POSTHOG_HOST: ${VITE_PUBLIC_POSTHOG_HOST}"
echo "VITE_PUBLIC_POSTHOG_KEY: ${VITE_PUBLIC_POSTHOG_KEY}"
echo "KEYSTONE_BASE_URL: ${KEYSTONE_BASE_URL}"

# Replace placeholders in env-config.js with actual environment variables
echo "window.env = {" > /usr/share/nginx/html/env-config.js
echo "  VITE_APP_API_BASE_URL: \"${VITE_APP_API_BASE_URL}\"," >> /usr/share/nginx/html/env-config.js
echo "  VITE_APP_ENV: \"${VITE_APP_ENV}\"," >> /usr/share/nginx/html/env-config.js
# Remove quotes around boolean value
echo "  DEV: ${DEV}," >> /usr/share/nginx/html/env-config.js
echo "  VITE_APP_GOOGLE_CLIENT_ID: \"${VITE_APP_GOOGLE_CLIENT_ID}\"," >> /usr/share/nginx/html/env-config.js
echo "  VITE_APP_GOOGLE_ANALYTICS_MEASUREMENT_ID: \"${VITE_APP_GOOGLE_ANALYTICS_MEASUREMENT_ID}\"," >> /usr/share/nginx/html/env-config.js
echo "  VITE_PUBLIC_POSTHOG_KEY: \"${VITE_PUBLIC_POSTHOG_KEY}\"," >> /usr/share/nginx/html/env-config.js
echo "  VITE_PUBLIC_POSTHOG_HOST: \"${VITE_PUBLIC_POSTHOG_HOST}\"," >> /usr/share/nginx/html/env-config.js
echo "  KEYSTONE_BASE_URL: \"${KEYSTONE_BASE_URL}\"" >> /usr/share/nginx/html/env-config.js
echo "};" >> /usr/share/nginx/html/env-config.js

# Debug: Print the generated config
echo "Debug: Generated env-config.js content:"
cat /usr/share/nginx/html/env-config.js

# Start Nginx
exec nginx -g 'daemon off;'
