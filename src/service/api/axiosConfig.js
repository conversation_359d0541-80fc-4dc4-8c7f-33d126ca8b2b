import axios from 'axios';
import EnvKeys from '../../constants/EnviornmentKeys';
import { REFRESH_TOKEN_KEY_NAME } from '../../constants/localStorageConstants';
import { refreshToken } from './authService';
import RouteFactory from '../RouteFactory';
import { deleteAuthLocalStorage, setAuthLocalStorage } from '../../util/auth';

//multiple instances can be defined if and as needed

const axiosInstance = axios.create({
  baseURL: EnvKeys.apiHost,
  withCredentials: true,
  // TODO: create a abstraction for headers to import it directly
  headers: {
    'Content-Type': 'application/json',
    // Add other default headers here
  },
});

export const axiosFormDataInstance = axios.create({
  baseURL: EnvKeys.apiHost,
  withCredentials: true,
  headers: {
    'Content-Type': 'multipart/form-data',
    // Add other default headers here
  },
});

export const axiosZipDataInstance = axios.create({
  baseURL: EnvKeys.apiHost,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/zip',
    // Add other default headers here
  },
  responseType: 'blob',
});

// Define request and response configurations or specifc logics

// axiosInstance.interceptors.request.use(
//   (config) => {
//     // Add request-specific logic here
//     return config;
//   },
//   (error) => {
//     return Promise.reject(error);
//   }
// );
let isRefreshing = false;
// let refreshPromise = null;
// Response Interceptors
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  // async (error) => {
  //   const originalRequest = error.config;
  //   const refreshTokenData = localStorage.getItem(REFRESH_TOKEN_KEY_NAME)
  //   if (error.response.status === 401 && !originalRequest._retry && refreshTokenData) {
  //     if (!isRefreshing) {
  //       isRefreshing = true;
  //       const refreshResult = refreshToken(refreshTokenData);
        
  //       // Store the refresh promise to avoid concurrent refresh attempts
  //       refreshPromise = refreshResult.then((newTokens) => {
  //         isRefreshing = false;
  //         return newTokens;
  //       })
  //       .catch((refreshError) => {
  //         isRefreshing = false;
  //         throw refreshError;
  //       });
  //     }
  //     try {
  //       const refreshData = await refreshPromise;
  //       originalRequest._retry = true;
        
  //       // Update the authorization header with the new access token, we have token in http cookie so no need
  //       //TODO:check if user need to be updated here
  //       setAuthLocalStorage(refreshData.data)
  //       // Retry the original request
  //       return axiosInstance(originalRequest);
  //     } catch (refreshError) {
  //       // Handle the case where the refresh token request fails
  //       deleteAuthLocalStorage();
  //       window.location.pathname = new RouteFactory().login().build();
  //       return Promise.reject(refreshError);
  //     }
  //   }
  //   // TODO:test this check
  //   if (error.response.status === 401 ) {
  //     deleteAuthLocalStorage()
  //     window.location.pathname = new RouteFactory().login().build();
  //   }
  //   return Promise.reject(error);
  // }
  async (error) => {
    const originalRequest = error.config;

    // Check if the error is due to token expiration
    if (error.response.status === 401 && !originalRequest._retry && !isRefreshing) {
      originalRequest._retry = true;
      isRefreshing= true;
      try {
        // Refresh the token
        const refreshTokenData = localStorage.getItem(REFRESH_TOKEN_KEY_NAME)
        const refreshData = await refreshToken(refreshTokenData);
        // Update the authorization header with the new access token, we have token in http cookie so no need
        // Set the new access token in localStorage or wherever you store it
        setAuthLocalStorage(refreshData.data)

        // Retry the original request with the new access token
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // Handle refresh token failure (e.g., redirect to login page)
        console.error('Failed to refresh token:', refreshError);
        deleteAuthLocalStorage();
        // Redirect to the login page
        window.location.pathname = new RouteFactory().login().build();
        return Promise.reject(refreshError);
      }
    }
    if (error.response.status === 401 ) {
      deleteAuthLocalStorage()
      window.location.pathname = new RouteFactory().login().build();
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
