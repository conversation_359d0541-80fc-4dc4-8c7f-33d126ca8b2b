import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { Button, Image, Tooltip } from 'antd';
import { 
  // EditOutlined, 
  DeleteOutlined } from '@ant-design/icons';

const CommentBox = props => {
  const { preivewDocument, comment, deleteComment, updateComment } = props;

  const [edit, setEdit] = useState(false);
  const [newComment, setNewComment] = useState(comment?.comment?.text);
  return (
    <div className="mx-5 p-3 w-[-webkit-fill-available] flex flex-col gap-3 rounded-[10px] border border-solid border-space-dust-200 box-border bg-space-dust-100">
      <div className="flex items-start justify-between">
        <div>
          <div className="text-xs text-dark-gray-600 font-medium">You</div>
          <div className="text-[10px] text-dark-gray-500">
            {getDateFromTimeStamp(comment.createdAt)}
          </div>
        </div>
        <div>
          {comment.resolved ? (
            <div className="flex items-center justify-center">
              <span className="text-xs font-medium text-turf-master-600">Resolved</span>
              <Image src="/done_all.svg" alt="done" width={12} height={12} />
            </div>
          ) : null}
          {/* TODO FIX APPROVE STAATUS */}
          {!preivewDocument?.isApproved ? (
            <div style={{ display: 'flex', gap: '8px' }}>
              {/* <Tooltip title="Edit comment">
                <Button shape="circle" icon={<EditOutlined />} onClick={() => setEdit(true)} />
              </Tooltip> */}
              <Tooltip title="Delete comment">
                <Button shape="circle" icon={<DeleteOutlined />} onClick={deleteComment} />
              </Tooltip>
            </div>
          ) : null}
        </div>
      </div>
      {edit ? (
        <>
          <textarea
            placeholder="Your comment"
            autoFocus
            value={newComment ? newComment : ''}
            onChange={event => {
              setNewComment(event.target.value);
            }}
            ref={node => {
              if (node) {
                node.focus();
              }
            }}
          />
          <Button
            className="px-2 py-[1px] text-base-white bg-space-dust-600 rounded-lg"
            onClick={() => {
              console.log('comment ',comment)
              updateComment(comment.id, comment.position, comment.content, {
                ...comment.comment,
                text: newComment,
              });
              setEdit(false);
            }}
          >
            Save
          </Button>
        </>
      ) : (
        <div className="text-dark-gray-600 text-sm">{comment?.comment?.text}</div>
      )}
    </div>
  );
};

export default CommentBox;

CommentBox.propTypes = {
  comment: PropTypes.object.isRequired,
  preivewDocument: PropTypes.object,
  deleteComment: PropTypes.func.isRequired,
  updateComment: PropTypes.func.isRequired,
};
