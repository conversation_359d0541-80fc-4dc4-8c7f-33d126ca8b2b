import React, { useState } from 'react';
import css from './PackagingSelect.module.css';
import PropTypes from 'prop-types';
import { Button, Divider, Form, Input, Select, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { isStringEmpty } from '../../util/stringUtils';
import { useDispatch } from 'react-redux';
import { addPackaging } from '../../store/actions/packagingList';

const PackagingSelect = props => {
  const { packagingList, field, mode, disableCustomPackaging, fieldKey, disabled } = props;

  const dispatch = useDispatch();

  const [customPackaging, setCustomPackaging] = useState({
    type: '',
    packSize: '',
    tareWeight: '',
    dimension: '',
  });

  const handleCustomePackagingInput = e => {
    const { name, value } = e.target;
    setCustomPackaging({
      ...customPackaging,
      [name]: value,
    });
  };

  const addCustomPackaging = e => {
    e.preventDefault();
    if (isStringEmpty(customPackaging.type)) {
      notification.error({
        message: 'Error',
        description: 'Packaging type can not be empty',
        placement: 'topRight',
      });
      return;
    }
    if (isStringEmpty(customPackaging.packSize)) {
      notification.error({
        message: 'Error',
        description: 'Packaging pack size can not be empty',
        placement: 'topRight',
      });
      return;
    }
    if (isStringEmpty(customPackaging.tareWeight)) {
      notification.error({
        message: 'Error',
        description: 'Packaging tare weight can not be empty',
        placement: 'topRight',
      });
      return;
    }
    const [duplicatePackaging] = packagingList.filter(
      item =>
        item.type === customPackaging.type &&
        item.packSize === customPackaging.packSize &&
        item.tareWeight === customPackaging.tareWeight
    );
    if (duplicatePackaging) {
      notification.error({
        message: 'Error',
        description: 'Packaging already exist',
        placement: 'topRight',
      });
      return;
    }
    const customId = `${customPackaging.type}_${customPackaging.packSize}_${customPackaging.tareWeight}`;
    dispatch(addPackaging([{ ...customPackaging, id: customId }]))
    setCustomPackaging({
      type: '',
      packSize: '',
      tareWeight: '',
      dimension: '',
    });
  };

  return packagingList.length ? (
    <Form.Item
      name={field ? [field.name, fieldKey || 'packaging'] : "packaging"}
      label="Packaging details"
      rules={[
        {
          required: true,
          message: 'Please select packaging!',
        },
      ]}
    >
      <Select
        mode={mode}
        className={css.selectBox}
        showSearch
        dropdownRender={menu => (
          <>
            {menu}
            {!disableCustomPackaging ? (<>
              <Divider
                style={{
                  margin: '8px 0',
                }}
              />
              <div className={css.customAddOptn}>
                <Input
                  placeholder="Packaging type"
                  // ref={inputRef}
                  name="type"
                  style={{ width: '49%' }}
                  value={customPackaging.type}
                  onChange={handleCustomePackagingInput}
                />
                <Input
                  placeholder="Tare weight"
                  name="tareWeight"
                  style={{ width: '49%' }}
                  value={customPackaging.tareWeight}
                  onChange={handleCustomePackagingInput}
                />
                <Input
                  placeholder="Pack size"
                  name="packSize"
                  style={{ width: '49%' }}
                  value={customPackaging.packSize}
                  onChange={handleCustomePackagingInput}
                />
                <Input
                  placeholder="Dimension"
                  name="dimension"
                  style={{ width: '49%' }}
                  value={customPackaging.dimension}
                  onChange={handleCustomePackagingInput}
                />
              </div>
              <Button type="text" icon={<PlusOutlined />} onClick={addCustomPackaging}>
                Add item
              </Button>
            </>) : null}
          </>
        )}
        disabled={disabled}
      >
        {packagingList.map((packageType, index) => (
          <Select.Option key={index} value={packageType.id}>
            <div className={css.CustomSelectOptn}>
              <div className={css.optnTitle}>{packageType.type}</div>
              <div className={css.optnDetails}>
                <div className={css.optnLabel}>Pack size:</div>
                <div className={css.optnValue}>{packageType.packSize}</div>
              </div>
              <div className={css.optnDetails}>
                <div className={css.optnLabel}>Tare weight:</div>
                <div className={css.optnValue}>{packageType.tareWeight}</div>
              </div>
              <div className={css.optnDetails}>
                <div className={css.optnLabel}>Dimension:</div>
                <div className={css.optnValue}>{packageType.dimension}</div>
              </div>
            </div>
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  ) : null;
};

export default PackagingSelect;

PackagingSelect.propTypes = {
  packagingList: PropTypes.array.isRequired,
  field: PropTypes.object,
  mode: PropTypes.string,
  disableCustomPackaging: PropTypes.bool,
  fieldKey: PropTypes.string,
  disabled: PropTypes.bool,
};
