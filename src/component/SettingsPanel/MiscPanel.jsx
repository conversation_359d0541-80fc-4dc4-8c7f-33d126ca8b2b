import React, { useEffect, useState } from 'react';
import PageLoader from '../Loaders/PageLoader';
import { Button, Card, Col, Form, Input, message, Row, Select } from 'antd';
import { getOrderBookList, hardAssignPo } from '../../service/api/orderBookApi';

const MiscPanel = () => {
  const [loading, setLoading] = useState(false);
  const [orderBookList, setOrderBookList] = useState([]);

  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  const handlePoUpdate = () => {
    setLoading(true);
    form.validateFields().then(values => {
      const orderBook = orderBookList.find(
        order => order.purchaseOrderNumber == values?.oldPoNumber
      );
      const customerId = orderBook?.customer?.id;
      if (customerId == null)
        throw new Error(
          'feature unavailable for inventory orders or customer data is not present in order book'
        );
      hardAssignPo({ ...values, customerId })
        .then(() => {
          messageApi.success('Po number changed sucessfully', 3);
        })
        .then(() => form.resetFields())
        .then(() => updateOrderBookList())
        .then(() => setLoading(false))
        .catch(err => {
          setLoading(false);
          if (err?.response?.data?.message) messageApi.error(err.response.data?.message, 3);
          else messageApi.error('oops some error occured ', 3);
        });
    });
  };

  const updateOrderBookList = () => {
    setLoading(true);
    const pageNumber = 0;
    const filters = {};
    const searchkey = '';
    getOrderBookList(null, pageNumber, filters, searchkey)
      .then(res => {
        if (res.data && res.data.content) {
          setOrderBookList(res.data.content);
          setLoading(false);
        }
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };

  useEffect(() => {
    if (!orderBookList || !orderBookList.length) {
      updateOrderBookList();
    }
  }, []);

  return (
    <div
      style={{
        borderTop: '1px solid rgba(30, 30, 30, 0.1)',
      }}
      className="h-full flex justify-center bg-neutral-200"
    >
      <div className="m-[5%] p-8 w-3/5 h-fit bg-base-white rounded-lg">
        {contextHolder}
        {loading ? (
          <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} />
        ) : null}
        <Card hoverable title="Update PO Number">
          <Form
            name="po number update"
            layout="vertical"
            scrollToFirstError
            preserve={false}
            form={form}
            size="middle"
            onFinish={handlePoUpdate}
            validateTrigger="onBlur"
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="oldPoNumber"
                  label="Old Po Number"
                  rules={[{ required: true, message: 'please enter old po number' }]}
                >
                  <Select
                    showSearch
                    optionFilterProp="children" // Search on the displayed label
                    filterOption={(input, option) =>
                      option?.children?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {orderBookList?.map(orderBook => (
                      <Select.Option
                        key={orderBook.id}
                        value={orderBook.purchaseOrderNumber}
                        label={orderBook.purchaseOrderNumber}
                      >
                        {orderBook.purchaseOrderNumber}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="updatedPoNumber"
                  label="Enter new po number"
                  rules={[
                    { required: true, message: 'please enter new po number' },
                    {
                      pattern: /^\S+$/,
                      message: 'Po number should not contain any spaces!',
                    },
                  ]}
                >
                  <Input placeholder="Input new po number" />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    Update Po Number
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default MiscPanel;
