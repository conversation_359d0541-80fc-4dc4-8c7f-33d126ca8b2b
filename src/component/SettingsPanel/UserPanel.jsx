import React, { useEffect, useState } from 'react'
import { deleteEmployeeById, employeeResetPasssword, getAllEmployeeList } from '../../service/api/employee';
import { Button, Col, Form, Input, Modal, Row, Table } from 'antd';
import { employeeTableHeader } from '../../constants/TableConstants';
import { EditOutlined, PlusOutlined, DeleteOutlined, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { formModes } from '../../constants/formConstant';
import { userEntity } from '../../constants/UserTabsConstants';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';

const UserPanel = () => {

  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState([]);
  const [confirmationModalVisible, setConfirmationModalVisible] = useState({
    show: false,
    entity: null,
  });
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });
  const [resetPasswordModalData, setResetPasswordModalData] = useState({
    show: false,
    user: null,
  });

  const { api } = useNotificationContext();
  const [form] = Form.useForm();
  const passwordWatch = Form.useWatch("password", form);

  const employeeTableAction = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    width: 300,
    render: (_, record) => (
      <div className='flex items-center flex-wrap'>          
          <Button
            type='text'
            onClick={() => handleEditUser(record)}
            icon={<EditOutlined />}
          >
            Edit
          </Button>
          <Button
            type='text'
            onClick={() => {
              setResetPasswordModalData({
                show: true,
                user: record,
              })
            }}
          >
            Reset Password
          </Button>
          <Button
            type='text'
            onClick={() =>{
              setConfirmationModalVisible({
                show: true,
                entity: record,
              });
            }}
            icon={<DeleteOutlined />}
          >
            Delete
          </Button>
      </div>
    )
  };

  const fetchEmployeeList = () => {
    setLoading(true);
    getAllEmployeeList().then((res) => {
      if (res?.data)
        setUserList(res.data);
      setLoading(false);
    }).catch((err) => {
      setLoading(false);
      console.log(err);
    })
  }
  const handleAddUser = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.employeeUser,
        mode: formModes.CREATE,
      },
    });
  }
  function handleEditUser(record) {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.employeeUser,
        mode: formModes.UPDATE,
        formData: record,
      },
    });
  }
  const handleDeleteConfirmationModal = () => {
    setLoading(true);
    deleteEmployeeById(confirmationModalVisible?.entity?.id).then(()=>{
      setLoading(false);
      setUserList((prevState) => {
        return prevState.filter((item) => item.id !== confirmationModalVisible?.entity?.id)
      })
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      api.success({
        messgae: 'Success',
        description: 'Sucessfully deleted',
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        )
      })
      
    }).catch((err)=>{
      // handle message using error
      setLoading(false);
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      const errMsg=err?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    })
  }
  const handleCancelConfirmationModal = () => {
    setConfirmationModalVisible({
      show: false,
      entity: null,
    });
  };
  const handleCancelResetPasswordModal = () => {
    setResetPasswordModalData({
      show: false,
      user: null,
    })
  }
  const handleUserResetPassword = (userId, password) => {
    setLoading(true);
    const resetPasswordData = {
      id: userId,
      password,
    }
    employeeResetPasssword(resetPasswordData).then(()=>{
      setLoading(false);
      handleCancelResetPasswordModal();
      api.success({
        messgae: 'Success',
        description: 'Sucessfully Reset Password',
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        )
      })
      
    }).catch((err)=>{
      // handle message using error
      setLoading(false);
      const errMsg = err?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    })
  }

  useEffect(() => {
    fetchEmployeeList();
  }, []);

  return (
    <>
      <div className='flex m-3'>
        <Button
          type='primary'
          icon={<PlusOutlined style={{ color: '#fff' }}/>}
          onClick={() => handleAddUser()}
          className='rounded-[4px] ml-auto'
        >
          New User
        </Button>
      </div>
      <Table
        bordered={false}
        loading={loading}
        columns={[...employeeTableHeader, employeeTableAction]}
        dataSource={userList.map((item) => ({...item, key: item.id}))}
        scroll={{
          x: '100%',
          y: 550,
        }}
      />
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={fetchEmployeeList}
      />
      <Modal
        title={`Do you confirm to delete the employee: ${confirmationModalVisible?.entity?.name}  ?`}
        open={confirmationModalVisible?.show}
        onOk={handleDeleteConfirmationModal}
        onCancel={() => handleCancelConfirmationModal()}
        destroyOnClose
      />
      <Modal
        title="Reset Password"
        open={resetPasswordModalData?.show}
        onCancel={() => handleCancelResetPasswordModal()}
        destroyOnClose
        footer={null}
      >
        <Form
          name="user_password_reset"
          layout="vertical"
          scrollToFirstError
          preserve={false}
          form={form}
          size="middle"
          onFinish={(values) => handleUserResetPassword(resetPasswordModalData?.user?.id, values.password)}
          validateTrigger="onBlur"
        >
          <div className='mb-1'>
            User Name: <span className='font-semibold'>{resetPasswordModalData?.user?.name}</span>
          </div>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="password" label="Enter new Password" rules={[{ required: true }]}>
                <Input.Password placeholder="Input password" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="rePassword"
                label="Confirm new Password"
                rules={[
                  { required: true },
                  {
                    validator: (_, value) => {
                      if (value !== passwordWatch ) {
                        return Promise.reject('Password do not match');
                      }                    
                      return Promise.resolve();
                    },
                  },
                ]}              
              >
                <Input.Password placeholder="Confirm password" />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                Reset Password
              </Button>
            </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  )
}

export default UserPanel;