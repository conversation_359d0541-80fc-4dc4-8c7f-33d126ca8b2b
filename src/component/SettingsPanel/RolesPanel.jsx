import React, { useEffect, useState } from 'react'
import { deleteUserRole, getAllUserRoles } from '../../service/api/UserRolesApi';
import { Button, Modal, Table } from 'antd';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { EditOutlined, PlusOutlined, DeleteOutlined, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { userEntity } from '../../constants/UserTabsConstants';
import { formModes } from '../../constants/formConstant';
import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';
import { userRoleTableHeader } from '../../constants/TableConstants';

const RolesPanel = () => {

  const [loading, setLoading] = useState(false);
  const [userRoleList, setUserRoleList] = useState([]);
  const [confirmationModalVisible, setConfirmationModalVisible] = useState({
    show: false,
    entity: null,
  });
  const [overlayData, setOverlayData] = useState({
    show: false,
    drawerData: {},
  });

  const { api } = useNotificationContext();

  const userRoleTableAction = {
    title: 'Actions',
    dataIndex: 'action',
    key: 'actions',
    width: 300,
    render: (_, record) => (
      <div className='flex items-center flex-wrap'>          
          <Button
            type='text'
            onClick={() => handleEditUserRole(record)}
            icon={<EditOutlined />}
          >
            Edit
          </Button>
          <Button
            type='text'
            onClick={() =>{
              setConfirmationModalVisible({
                show: true,
                entity: record,
              });
            }}
            icon={<DeleteOutlined />}
          >
            Delete
          </Button>
      </div>
    )
  };

  const fetchUserRoles = () => {
    setLoading(true);
    getAllUserRoles().then((res) => {
      if (res?.data) {
        setUserRoleList(res.data);
      }
      setLoading(false);
    }).catch((err) => {
      setLoading(false);
      console.log(err);
    });
  }
  const handleAddUserRole = () => {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.userRole,
        mode: formModes.CREATE,
      },
    });
  }
  function handleEditUserRole(record) {
    setOverlayData({
      show: true,
      drawerData: {
        entity: userEntity.userRole,
        mode: formModes.UPDATE,
        formData: record,
      },
    });
  }
  const handleDeleteConfirmationModal = () => {
    setLoading(true);
    deleteUserRole(confirmationModalVisible?.entity?.id).then(()=>{
      setLoading(false);
      setUserRoleList((prevState) => {
        return prevState.filter((item) => item.id !== confirmationModalVisible?.entity?.id)
      })
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      api.success({
        messgae: 'Success',
        description: 'Sucessfully deleted',
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        )
      })
      
    }).catch((err)=>{
      // handle message using error
      setLoading(false);
      setConfirmationModalVisible({
        show: false,
        entity: null,
      });
      const errMsg = err?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    })
  }
  const handleCancelConfirmationModal = () => {
    setConfirmationModalVisible({
      show: false,
      entity: null,
    });
  };

  useEffect(() => {
    fetchUserRoles();
  }, [])

  return (
    <>
      <div className='flex m-3'>
        <Button
          type='primary'
          icon={<PlusOutlined style={{ color: '#fff' }}/>}
          onClick={() => handleAddUserRole()}
          className='rounded-[4px] ml-auto'
        >
          New Role
        </Button>
      </div>
      <Table
        bordered={false}
        loading={loading}
        columns={[...userRoleTableHeader, userRoleTableAction]}
        dataSource={userRoleList.map((item) => ({...item, key: item.id}))}
        scroll={{
          x: '100%',
          y: 550,
        }}
      />
      <OverlayDrawer
        showDrawer={overlayData.show}
        drawerData={overlayData.drawerData}
        hide={() => {
          setOverlayData({
            show: false,
            drawerData: {},
          });
        }}
        onSubmit={fetchUserRoles}
      />
      <Modal
        title={`Do you confirm to delete the employee: ${confirmationModalVisible?.entity?.name}  ?`}
        open={confirmationModalVisible?.show}
        onOk={handleDeleteConfirmationModal}
        onCancel={() => handleCancelConfirmationModal()}
        destroyOnClose
      />
    </>
  )
}

export default RolesPanel