import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Col, Form, Row, Select } from 'antd';
import { getInventoryList } from '../../service/api/inventoryApi';
import PageLoader from '../Loaders/PageLoader';
import { formModes } from '../../constants/formConstant';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';

const InventoryOutBasicInfo = (props) => {
  const { form, initialValue, setInitialValue, mode, customerDispatchList,  } = props;

  const [loading, setLoading] = useState(false);
  const [inventoryList, setInventoryList] = useState([])

  const { api } = useNotificationContext();
  const dispatchIdWatch = Form.useWatch('customerDispatchOrderId', form);
  const inventoryIdWatch = Form.useWatch('inventoryId', form);

  const getFormPrefilldValues = () =>
    initialValue && initialValue.inventoryId
      ? {
          inventoryId: initialValue?.inventoryId,
          customerDispatchOrderId: initialValue?.customerDispatchOrderId,
        }
      : {};

  useEffect(() => {
    if (!inventoryList || !inventoryList.length) {
      setLoading(true);
      //api call to get order list TODO: format api params
      const pageNumber = 0;
      const filters = {};
      const searchkey = '';
      getInventoryList(null, pageNumber, filters, searchkey)
      .then(response => {
        setInventoryList(response.data.content);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
    }
    if (!customerDispatchList?.length) {
      api.error({
        messgae: 'Error',
        description: 'Can not create Inventory link order as no customer dispatch order exist for linking!!. Please add customer dispatch order first.',
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    }
  }, []);

  useEffect(() => {
    if (initialValue && initialValue.inventoryId && inventoryList?.length) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue, inventoryList]);

  useEffect(() => {
    if (dispatchIdWatch && customerDispatchList?.length) {
      const dispatchOrder = customerDispatchList.find((item) => item.id === dispatchIdWatch);
      if (dispatchOrder && dispatchOrder.products) {
        setInitialValue((prevState) => ({
          ...prevState,
          dispatchProducts: dispatchOrder.products,
          dispatchData: {
            dispatchOrderId: dispatchOrder.orderId,
            id: dispatchOrder.id,
            purchaseOrderNumber: dispatchOrder.purchaseOrderNumber,
          }
        }));
      }
    }
  }, [dispatchIdWatch]);
  useEffect(() => {
    if (inventoryIdWatch && inventoryIdWatch?.length) {
      const inventoryObj = inventoryList.find((item) => item.id === inventoryIdWatch);
      if (inventoryObj && inventoryObj.name) {
        setInitialValue((prevState) => ({
          ...prevState,
          inventoryData: inventoryObj,
        }))
      }
    }
  }, [inventoryIdWatch])

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} /> : null}
      <Form
        name="inventoryOut_basic_info"
        layout="vertical"
        scrollToFirstError
        preserve={false}
        size="middle"
        form={form}
      >
        <Row gutter={16}>
          <Col span={24}>
            {/* {customerDispatchList.length ? ( */}
              <Form.Item
                label="Select Customer Dispatch Order"
                name="customerDispatchOrderId"
                rules={[{ required: true, message: 'Please select customet dispatch order' }]}
              >
                <Select showSearch disabled={mode === formModes.UPDATE}>
                  {customerDispatchList.map(order => (
                    <Select.Option key={order.id} value={order.id}>
                      {order.orderId}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            {/* ) : null} */}
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            {inventoryList.length ? (
              <Form.Item
                label="Select Inventory"
                name="inventoryId"
                rules={[{ required: true, message: 'Please select an inventory' }]}
              >
                <Select showSearch disabled={mode === formModes.UPDATE}>
                  {inventoryList.map(inventory => (
                    <Select.Option key={inventory.id} value={inventory.id}>
                      {inventory.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : null}
          </Col>
        </Row>
      </Form>
    </>
  );
}

export default InventoryOutBasicInfo;

InventoryOutBasicInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func.isRequired,
  customerDispatchList: PropTypes.array.isRequired,
  mode: PropTypes.string.isRequired,
};