import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import CommentBox from './CommentBox';
import { getCommentsFromFile } from '../../service/api/documentApi';

const DocumentComments = props => {
  const {
    preivewDocument,
    comments,
    setComments,
    handleRemoveComments,
    handleUpdateComment,
    // resetComments,
  } = props;

  const getComments = async () => {
    if (preivewDocument && preivewDocument.fileId) {
      getCommentsFromFile(preivewDocument.fileId)
        .then(res => {
          setComments(res && res?.data?.comments ? res.data.comments : []);
        })
        .catch(err => {
          console.log(err);
        });
    }
  };

  useEffect(() => {
    getComments();
  }, [preivewDocument?.fileId]);

  return (
    <>
      <div className="h-full relative flex flex-col items-start">
        <div className="px-6 py-5">
          <div className="mb-1 flex items-center justify-between">
            <span className="text-base text-dark-gray-500">Comments</span>
          </div>
        </div>
        <div className="py-6 flex flex-col items-start gap-4 w-full">
          {comments.map((comment, index) => (
            <CommentBox
              key={index}
              comment={comment}
              preivewDocument={preivewDocument}
              index={index}
              deleteComment={() => handleRemoveComments(index)}
              updateComment={handleUpdateComment}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export default DocumentComments;

DocumentComments.propTypes = {
  orderId: PropTypes.string.isRequired,
  comments: PropTypes.array,
  preivewDocument: PropTypes.object,
  setComments: PropTypes.func.isRequired,
  latestVersion: PropTypes.object,
  handleRemoveComments: PropTypes.func.isRequired,
  handleUpdateComment: PropTypes.func.isRequired,
  resetComments: PropTypes.func.isRequired,
};
