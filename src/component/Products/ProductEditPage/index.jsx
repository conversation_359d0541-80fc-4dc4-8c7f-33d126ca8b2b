import { CloseOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { formConfigName, formModes } from '../../../constants/formConstant';
import RouteFactory from '../../../service/RouteFactory';
import { getFormConfigFromName } from '../../../service/api/formConfig';
import { getProductById, updateProduct } from '../../../service/api/productApi';
import MultipleTextFieldInput from '../../MultipleTextFieldInput/MultipleTextFieldInput';
import './ProductEditPage.module.css';
import css from './ProductEditPage.module.css';
import HeaderPanel from '../../headerPanel';
import SubCategoryDropdown from '../SubCategoryDropdown';
import PageLoader from '../../Loaders/PageLoader';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';

const ProductEditPage = () => {

  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { productId } = useParams();
  const dispatch = useDispatch();

  const [loading, isLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);
  const [productData, setProductData] = useState();

  useEffect(() => {
    if (!productData) {
      getProductDetails();
    }
    if (categoryList.length < 1) {
      getConfigs();
    }
  }, []);

  const goBack = () => {
    dispatch(setCollapseAsideBar(false))
    navigate(RouteFactory.buildProduct())
  };
  const getConfigs = async () => {
    try {
      const [apiRes] = await Promise.all([getFormConfigFromName(formConfigName.category)]);
      if (apiRes.data) {
        setCategoryList(apiRes.data.value || []);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const getProductDetails = async (id = productId) => {
    isLoading(false);
    try {
      const apiRes = await getProductById(id);
      isLoading(false);
      if (apiRes.data) {
        setProductData(apiRes.data);
        form.setFieldsValue(productData);
      }
    } catch (error) {
      isLoading(false);
      console.log(error);
    }
  };

  const submitForm = async () => {
    isLoading(true);
    form.validateFields().then(async (values) => {
      const apiRes = await updateProduct(values, productId);
      if (apiRes.status >= 200 && apiRes.status < 300) {
        navigate(RouteFactory.buildProduct());
      }
    }).catch(error => {
      isLoading(false);
      console.log(error)
    });
  };

  if (loading || (!productData && !productData?.id)) {
    return <PageLoader />;
  }

  return (
    <>
      <HeaderPanel
        name="Product Directory"
        sideButtons={
          <Row>
            <Space>
              <Button type="primary" htmlType="submit" onClick={submitForm}>
                Save Product
              </Button>
              <Button type="default" htmlType="cancel" onClick={goBack}>
                Cancel
              </Button>
            </Space>
          </Row>
        }
      />
      <div className={css.formContainer}>
        <Form
          name="productForm"
          layout="vertical"
          scrollToFirstError
          initialValues={productData}
          size="large"
          form={form}
        >
          <Row justify="space-between">
            <Col span={12}>
              <Typography.Title level={2} style={{ color: '#23568A' }}>
                Edit a Product
              </Typography.Title>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Name"
                name="tradeName"
                rules={[
                  {
                    required: true,
                    message: 'Please input product name!',
                  },
                ]}
              >
                <Input disabled/>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Technical Name"
                name="technicalName"
                rules={[
                  {
                    required: true,
                    message: 'Please input product technical name!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="functions" label="Functions">
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[',']} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="family" label="Family">
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[',']} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="CAS Number"
                name="casNumber"
                rules={[
                  {
                    required: true,
                    message: 'Please input CAS Number!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Grade"
                name="grade"
                rules={[
                  {
                    required: true,
                    message: 'Please input grade!',
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <div className={css.categoriesList}>
            <Typography.Title level={4} style={{ color: '#23568A', fontWeight: '400' }}>
              Categories
            </Typography.Title>
            {categoryList.length ? (
              <Form.List
                label="Category"
                name="categories"
                rules={[{ required: true, message: 'Please select categories', type: 'array' }]}
              >
                {(fields, { add, remove }) => (
                  <>
                    {fields.map((field, index) => (
                      <Row gutter={16} key={index}>
                        <Col span={11}>
                          <Form.Item
                            key={field.key}
                            name={[field.name, 'category']}
                            label="Category"
                            rules={[
                              {
                                required: true,
                                message: 'Please select Category!',
                              },
                            ]}
                          >
                            <Select showSearch>
                              {categoryList.map((category, index) => (
                                <Select.Option key={index} value={category.name}>
                                  {category.name}
                                </Select.Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={11}>
                          <SubCategoryDropdown
                            form={form}
                            categoryList={categoryList}
                            field={field}
                          />
                        </Col>
                        <Col span={2}>
                          <Button
                            className={css.categoriesButton}
                            type="primary"
                            shape="circle"
                            icon={<CloseOutlined />}
                            onClick={() => {
                              remove(field.name);
                            }}
                          />
                        </Col>
                      </Row>
                    ))}
                    <Button onClick={() => add()} block>
                      + Add Category
                    </Button>
                  </>
                )}
              </Form.List>
            ) : null}
          </div>

          <Row>
            <Col span={24}>
              <Form.Item name="synonyms" label="Synonyms">
                <MultipleTextFieldInput mode={formModes.CREATE} placeholder="Enter synonyms" />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item name="features" label="Features">
                <MultipleTextFieldInput mode={formModes.CREATE} placeholder="Enter features" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default ProductEditPage;
