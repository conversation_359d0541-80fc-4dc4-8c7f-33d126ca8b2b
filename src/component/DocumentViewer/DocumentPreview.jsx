import React, { useEffect, useState } from 'react'
import DocumentCommentPopup from './DocumentCommentPopup';
import PropTypes from 'prop-types';
import { 
  PdfLoader, 
  PdfHighlighter,  
  Highlight,
  Popup,
  AreaHighlight,
  Tip
} from 'react-pdf-highlighter';
import { downloadFile } from '../../service/api/storageService';
import { LoadingOutlined } from '@ant-design/icons';

const DocumentPreview = (props) => {
  
  const { 
    preivewDocument, comments, handleAddComment, handleUpdateComment, setLoading,
  } = props;

  const [docUrl, setDocUrl] = useState(null);

  let scrollViewerTo;
  const resetHash = () => {
    document.location.hash = "";
  };
  const getHighlightById = (id) => {
    return comments.find((comment) => comment.id === id);
  }
  const parseIdFromHash = () => document.location.hash.slice("#highlight-".length);
  const scrollToHighlightFromHash = () => {
    const highlight = getHighlightById(parseIdFromHash());
    if (highlight) {
      scrollViewerTo(highlight);
    }
  };

  const addHighlight = (highlight) => {
    console.log("Saving comment", highlight);
    handleAddComment(highlight);
  }
  const updateHighlight = (highlightId, position, content, comment) => {
    console.log("Updating comment", highlightId, position, content, comment);
    handleUpdateComment(highlightId, position, content, comment);
  }

  const getDocuemntUrl = async () => {
    try{
      setLoading(true);
      const response = await downloadFile(preivewDocument.fileId)
      if (response && response?.data?.url) setDocUrl(response.data.url);
    } catch (err) {
      console.log('Error while fetching url from storage service');
      console.log(err);
      setDocUrl(null);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    if (preivewDocument && preivewDocument.fileId) {
      getDocuemntUrl();
    }
  }, [preivewDocument?.fileId])

  useEffect(() => {
    window.addEventListener(
      "hashchange",
      scrollToHighlightFromHash,
      false
    );
    return () => {
      window.removeEventListener('hashchange', scrollToHighlightFromHash, false);
    };
  }, []);

  return (
    <>
      <div className='h-full overflow-auto flex'>
        {docUrl ? (
          <PdfLoader
            url={docUrl}
            beforeLoad={
              <LoadingOutlined
                style={{
                  fontSize: 24,
                  margin: 'auto',
                }}
                spin
              />
            }
          >
            {(pdfDocument) => (
                <PdfHighlighter
                  pdfDocument={pdfDocument}
                  enableAreaSelection={(event) => event.altKey}
                  onScrollChange={resetHash}
                  pdfScaleValue="page-width"
                  scrollRef={(scrollTo) => {
                    scrollViewerTo = scrollTo;
                    scrollToHighlightFromHash();
                  }}
                  onSelectionFinished={preivewDocument?.isApproved ? () => {} : (
                    position,
                    content,
                    hideTipAndSelection,
                    transformSelection
                  ) => (
                    <Tip
                      onOpen={transformSelection}
                      onConfirm={(comment) => {
                        addHighlight({ content, position, comment });
                        hideTipAndSelection();
                      }}
                    />
                  )}
                  highlightTransform={(
                    highlight,
                    index,
                    setTip,
                    hideTip,
                    viewportToScaled,
                    screenshot,
                    isScrolledTo
                  ) => {
                    const isTextHighlight = !(highlight.content && highlight.content.image);

                    const component = isTextHighlight ? (
                      <Highlight
                        isScrolledTo={isScrolledTo}
                        position={highlight.position}
                        comment={highlight.comment}
                      />
                    ) : (
                      <AreaHighlight
                        isScrolledTo={isScrolledTo}
                        highlight={highlight}
                        onChange={(boundingRect) => {
                          updateHighlight(
                            highlight.id,
                            { ...highlight.position, boundingRect: viewportToScaled(boundingRect) },
                            { image: screenshot(boundingRect) },
                            highlight.comment,
                          );
                        }}
                      />
                    );

                    return (
                      <Popup
                        popupContent={<DocumentCommentPopup {...highlight} />}
                        onMouseOver={(popupContent) =>
                          setTip(highlight, () => popupContent)
                        }
                        onMouseOut={hideTip}
                        key={index}
                      >
                        {component}
                      </Popup>
                    );
                  }}
                  highlights={comments}
                />
              )}
          </PdfLoader>
        ) : (
          <div className='w-full pt-28 text-center bg-dark-gray-100'>
            {preivewDocument && preivewDocument.fileId ? 'Unable to fetch document.' : 'Please select a document for review'}
          </div>
        )}
      </div>
    </>
  )
}

export default DocumentPreview;

DocumentPreview.propTypes = {
  preivewDocument: PropTypes.object,
  comments: PropTypes.array,
  setLoading: PropTypes.func.isRequired,
  handleAddComment: PropTypes.func.isRequired, 
  handleUpdateComment: PropTypes.func.isRequired
}