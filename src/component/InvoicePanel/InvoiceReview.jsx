import React, { useEffect, useState } from 'react';
import { Button, Space, Tabs } from 'antd';
// import DispatchOrderBasicInfo from './DispatchOrderBasicInfo';
// import { formModes } from '../../constants/formConstant';
// import DispatchOrderProductInfo from './DispatchOrderProductInfo';
// import DispatchOrderFinalInfo from './DispatchOrderFinalInfo';
import TaskTable from '../Task/TaskTable';
import DocumentPanel from '../DocumentPanel/DocumentPanel';
import DispatchOrderReadView from './DispatchOrderReadView';
import HeaderPanel from '../headerPanel';
import { getCriticalPathTasks } from '../../service/api/activityApi';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import CriticalPathFormModal from '../Task/CriticalPathForm';
import { getDispatchOrderFromId } from '../../service/api/dispatchOrderApi';
import PageLoader from '../Loaders/PageLoader';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import RouteFactory from '../../service/RouteFactory';
import { formModes, orderStatusList } from '../../constants/formConstant';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';

const InvoiceReview = () => {

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { dispatchId } = useParams();
  const user = useSelector(state => state.user);
  const [loading, setLoading] = useState(false);
  const [dipatchOrderEntry, setDipatchOrderEntry] = useState({});
  const [taskData, setTaskData] = useState([]);
  const [criticalPahModalVisible, setCriticalPahModalVisible] = useState(false);
  const [updateMode, setUpdateMode] = useState(false);

  
  const showTask = !!taskData.length;
  const panelItems = [
    {
      key: '1',
      label: 'View Details',
      children:(
        <DispatchOrderReadView
          formValues={dipatchOrderEntry}
          productList={dipatchOrderEntry.productList || dipatchOrderEntry.products}
          formView={formModes.READ}
        />
      )
    },
    {
      key: '3',
      label: 'Documents',
      children:(
        <DocumentPanel
          order={dipatchOrderEntry}
        />
      )
    },
  ]
  if (showTask) panelItems.push({
    key: '2',
    label: 'Task Table',
    children: (
      <TaskTable
        tasks={taskData}
        entityId={dipatchOrderEntry?.id}
        setTaskData={setTaskData}
      />
    )
  });

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };

  useEffect(() => {
    if (dispatchId) {
      setLoading(true);
      Promise.all([getCriticalPathTasks(dispatchId), getDispatchOrderFromId(dispatchId)]).then(res => {
        if (res[0]?.data) {
          // console.log(res.data)
          setTaskData(res[0].data);
        }
        if (res[1]?.data) {
          setDipatchOrderEntry(res[1].data);
        }
      })
      .catch(error => {
        console.log(error);
      }).finally(() => {setLoading(false)})
    }
  }, [dispatchId]);

  return (
    <>
      <HeaderPanel
        name="Dispatch Order"
        sideButtons={(
          <Space> 
            {(taskData.length === 0 && dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value&&
            hasPermission(userPermissionsList.generateCriticalPath, user.permissions)) ? (
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  setUpdateMode(false);
                  setCriticalPahModalVisible(true);
                }}
              >
                Generate Critical Path
              </Button>
            ) : null}
            {(taskData.length !== 0 && dipatchOrderEntry.status !== orderStatusList.PAYMENT_COMPLETED.value&&
            hasPermission(userPermissionsList.updateCriticalPath, user.permissions)) ? (
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  setUpdateMode(true);
                  setCriticalPahModalVisible(true);
                }}
              >
                Update Critical Path
              </Button>
            ) : null}
            <Button
              type="primary"
              size="large"
              onClick={() => {
                collapseAsideBarHandler(false);
                navigate(new RouteFactory().dashboard().dispatchOrder().build())
              }}
            >
              Cancel
            </Button>
          </Space>
        )}
      />
      {loading ? <PageLoader /> : (
        <Tabs
          items={panelItems}
          defaultActiveKey={showTask ? '2' : '1'}
          type='card'
          style={{ margin: '5% 2%' }}
        />
      )}
      {dipatchOrderEntry?.id ? (
        <CriticalPathFormModal
          products={dipatchOrderEntry?.products || []}
          visible={criticalPahModalVisible}
          onCancel={()=> setCriticalPahModalVisible(false) }
          entityId={dipatchOrderEntry?.id}
          onSumbit={(criticalPath) => setTaskData(criticalPath)}
          updateMode={updateMode}
        />
      ) : null}
    </>
  );
};

export default InvoiceReview;
