import React from 'react';
import PropTypes from 'prop-types';
import { Button, Space, Tag ,message} from 'antd';
import { useSelector } from 'react-redux';

import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import { useDispatch } from 'react-redux';
import css from './CustomerSideBar.module.css';
import { hasPermission } from '../../util/userUtils';
import { getEnumValueFromList } from '../../util/formUtil';
import { customerSize, customerType } from '../../constants/formConstant';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { Link } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { deleteCustomer } from '../../service/api/customerApi';

const CustomerSideBar = props => {
  const { customer, hide } = props;

  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const [messageApi, contextHolder] = message.useMessage();


  const openForm = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };
  const hideSideDrawer = () => {
    hide();
  };

  const handleDelete=(id)=>{
    deleteCustomer(id).then(()=>{
      console.log('sucessfully deleted')
      window.location.replace(new RouteFactory().dashboard().customer().build())
    }).catch((err)=>{
      // handle message using error
      const errMsg=err?.response?.data?.message;
      messageApi.error(errMsg)
    })
  }

  return customer ? (
    <div className={css.customerSider}>
      {contextHolder}
      <div className={css.titleBar}>
        <div className={css.title}>{customer.name}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().customer().setId(customer.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => openForm()}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        {hasPermission(userPermissionsList.updateCustomer, user.permissions) ? (
          <Link to={new RouteFactory().dashboard().customer().setId(customer.id).edit().build()}>
            <Button
              onClick={() => openForm()}
              style={{}}
              type="primary"
              icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Edit
            </Button>
          </Link>
        ) : null}
        {hasPermission(userPermissionsList.deleteCustomer, user.permissions)?(<Button onClick={() =>handleDelete(customer.id)}>Delete</Button>):null}
      </div>
      <Space direction="vertical" size="large">
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Customer Name
          </div>
          <div className={css.value}>{customer.name}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Address
          </div>
          <div
            className={css.value}
          >{`${customer?.address?.street}, ${customer?.address?.city}, ${customer?.address?.state}`}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Country
          </div>
          <div className={css.value}>{customer?.address?.country}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Email ID
          </div>
          <div className={css.value}>{customer.email}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Mobile No
          </div>
          <div className={css.value}>{`+${customer.countryCode}-${customer.mobile}`}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Customer Size
          </div>
          <div className={css.value}>{getEnumValueFromList(customer.size, customerSize)}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Account Owner
          </div>
          <div className={css.value}>{customer.accountOwner}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Category
          </div>
          <div className={css.value}>
            {customer.categories?.map(cat => (
              <Tag key={cat}>{cat.toUpperCase()}</Tag>
            ))}
          </div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Type
          </div>
          <div className={css.value}>{getEnumValueFromList(customer.type, customerType)}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Remarks
          </div>
          <div className={css.value}>
            {customer.remarks?.map((remark, i) => (
              <div key={i}>{remark.value}</div>
            ))}
          </div>
        </Space.Compact>
      </Space>
    </div>
  ) : null;
};

export default CustomerSideBar;

CustomerSideBar.propTypes = {
  customer: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
};
