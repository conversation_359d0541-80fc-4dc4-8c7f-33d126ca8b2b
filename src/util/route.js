import routes from '../routes';
import RouteFactory from '../service/RouteFactory';

export const isCurrentRouteLoginScreen = () =>
  window.location.pathname.includes(new RouteFactory().login().build());

export const isCurrentRouteProtected = () => {
  if (routes.find(e => e.path.includes(window.location.pathname))) {
    return true;
  }
  return false;
};

export const getFormModeFromPath = (path) => {
  const pathArray = path.split('/');
  const mode = pathArray.length ? pathArray[pathArray.length - 1] : null;
  return mode;
};

export const getFormModeFromPathV2 = (path) => {
  const pathArray = path.split('/');
  if(pathArray.length && pathArray[pathArray.length-1]==""){
    pathArray.pop()
  }
  const mode = pathArray.length ? pathArray[pathArray.length - 1] : null;
  return mode;
};

export const updateParamsAndNavigate = (navigateObj,locationObj,paramsToUpdate) => {
  // clear params if params to update obj is null or empty
  if (!paramsToUpdate || Object.keys(paramsToUpdate).length == 0) {
    navigateObj('');
    return;
  }

  const searchParams = new URLSearchParams(locationObj.search);
  // Update or add each parameter in the object
  Object.entries(paramsToUpdate).forEach(([key, value]) => {
    if(value!==null){
        searchParams.set(key, typeof value=='object'? encodeURIComponent(JSON.stringify(value)) : value);
    }
  });
  // Use navigate to update the URL with the new parameters
  navigateObj(`?${searchParams.toString()}`);
};


export const getQeryParamsFromURL = ()=>{
  const urlParams = new URLSearchParams(location.search);
  const urlFilter = JSON.parse(decodeURIComponent(urlParams.get('filters')));
  return {
    'filters': urlFilter || {},
    'searchkey': urlParams.get('searchkey') || '',
    'page': urlParams.get('page') ? parseInt(urlParams.get('page'), 10) : 1,
    'mode': urlParams.get('mode') || '',
  }
}

export const addQueryParamAndNavigate = (key, value, navigate) => {
  const searchParams = new URLSearchParams(location.search);
  searchParams.set(key, value);
  navigate(`?${searchParams.toString()}`);
};

export const removeQueryParamAndNavigate = (key, navigate) => {
  const searchParams = new URLSearchParams(location.search);
  searchParams.delete(key);
  navigate(`?${searchParams.toString()}`);
}