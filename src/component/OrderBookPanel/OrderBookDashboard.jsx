import React from 'react';
import { useDispatch } from 'react-redux';
// import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import OrderBookListView from './OrderBookListView';
// import OverlayDrawer from '../OverlayDrawer/OverlayDrawer';

const OrderBookDashboard = () => {
  const dispatch = useDispatch();

  // const collapseAsideBarHandler = show => {
  //   dispatch(setCollapseAsideBar(show));
  // };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };

  return (
    <>
      <OrderBookListView
        // collapseAsideBarHandler={collapseAsideBarHandler}
        collapseSideDrawerHandler={collapseSideDrawerHandler}
      />
      {/* <OverlayDrawer /> */}
    </>
  );
};

export default OrderBookDashboard;
