import { Checkbox, Col, Form, Row, Select } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { getEmployeeList } from '../../service/api/employee';
import { getFormConfigFromName } from '../../service/api/formConfig';
import {
  countryList,
  customerSize,
  customerType,
  formConfigName,
} from '../../constants/formConstant';

const CustomerFilter = props => {
  const { form } = props;

  // TODO: move it to store
  const [accountOwnerList, setAccountOwnerList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);

  useEffect(() => {
    if (!accountOwnerList || !accountOwnerList.length) {
      getEmployeeList()
        .then(res => {
          if (res.data && res.data.content) {
            setAccountOwnerList(res.data.content);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    if (!categoryList || !categoryList.length) {
      getFormConfigFromName(formConfigName.category)
        .then(res => {
          if (res.data) {
            setCategoryList(res.data.value);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }, []);

  return (
    <Form
      form={form}
      layout="vertical"
      // className={css.filterForm}
    >
      {accountOwnerList.length ? (
        <Form.Item name="accountOwner" label="Account Owner">
          <Select mode="multiple" showSearch>
            {accountOwnerList.map(emp => (
              <Select.Option key={emp.id} value={emp.name}>
                {emp.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      <Form.Item name="size" label="Size">
        <Checkbox.Group style={{ width: '100%' }}>
          <Row>
            {customerSize.map(size => (
              <Col span={8} key={size.key}>
                <Checkbox value={size.value}>{size.label}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Form.Item>
      {categoryList?.length ? (
        <Form.Item
          label="Category"
          name="categories"
          // rules={[{ required: true, message: 'Please select categories', type: 'array' }]}
        >
          <Select mode="multiple" showSearch>
            {categoryList.map((category, index) => (
              <Select.Option key={index} value={category.name}>
                {category.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      ) : null}
      <Form.Item name="type" label="Type">
        <Checkbox.Group style={{ width: '100%' }}>
          <Row>
            {customerType.map(type => (
              <Col span={8} key={type.key}>
                <Checkbox value={type.value}>{type.label}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Form.Item>
      <Form.Item label="Country" name="address.country">
        <Select mode="multiple" showSearch optionLabelProp="value">
          {countryList.map((country, index) => (
            <Select.Option key={index} value={country.name}>
              {`${country.flag} ${country.name}`}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    </Form>
  );
};

export default CustomerFilter;

CustomerFilter.propTypes = {
  form: PropTypes.object.isRequired,
};
