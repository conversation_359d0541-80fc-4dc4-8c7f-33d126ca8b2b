import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Form, Input, DatePicker, Divider, message, Select } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import dayjs from 'dayjs';
import { getDispatchOrderDataForApi, getPackagingDetailFromId } from '../../util/formUtil';
import { createDispatchOrder, updateDispatchOrder } from '../../service/api/dispatchOrderApi';
import {
  DispatchOrderFormBasicInfo,
  DateFormat,
  paymentTermsDate,
  notifyPartyList,
  currencyTypeList
} from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';
import { CalendarOutlined } from '@ant-design/icons';
import FileUpload from '../FileUpload';
import ProductTable from './ProductTable';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import ObjectUtil from '../../util/objectUtil';
import PageLoader from '../Loaders/PageLoader';

const DispatchOrderForm = props => {
  const { mode, dispatchOrder, orderBook, hide } = props;

  const packagingList = useSelector(state => state.packagingList);
  const dispatch = useDispatch();
  const [dipatchOrderEntry, setDipatchOrderEntry] = useState(
    mode === formModes.UPDATE
      ? {
          ...dispatchOrder,
          productList: [...orderBook.products],
          inventoryId: orderBook?.inventoryId,
        }
      : {
          purchaseOrderNumber: orderBook.purchaseOrderNumber,
          purchaseOrderDate: dayjs(orderBook.purchaseOrderDate),
          customer: orderBook.customer,
          consignee: orderBook.customer?.name,
          taxPercent: orderBook?.taxPercent,
          buyerCurrency: orderBook?.buyerCurrency,
          paymentTerms: { ...orderBook.paymentTerms },
          productList: [...orderBook.products],
          inventoryId: orderBook?.inventoryId,
          deliveryAddress: orderBook?.deliveryAddress,
          products: [
            ...Array(Math.max(1)).fill({}), // Add only the required number of empty objects
          ],
        }
  );
  const [submittingForm, setSubmittingForm] = useState(false);
  const productList =
    dipatchOrderEntry && dipatchOrderEntry?.productList ? dipatchOrderEntry.productList : [];

  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form); // watch all values in form

  const saveOrderDetails = orderData => {
    if (mode === formModes.CREATE) {
      createDispatchOrder(orderData)
        .then(response => {
          console.log('dispatchOrder api response:', response);
          setSubmittingForm(false);
          message.success('Dispatch order created successfully');
          hide();
        })
        .catch(error => {
          console.log(error);
          message.error(error?.response?.data?.message || 'Failed to create dispatch order');
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE) {
      updateDispatchOrder(orderData, dispatchOrder.id)
        .then(response => {
          setSubmittingForm(false);
          message.success('Dispatch order updated successfully');
          hide(response.data);
        })
        .catch(error => {
          console.log(error);
          message.error(error?.response?.data?.message || 'Failed to update dispatch order');
          setSubmittingForm(false);
        });
    }
  };
  const submitFormHandler = dipatchOrderEntry => {
    if (!Array.isArray(dipatchOrderEntry.products) || dipatchOrderEntry.products.length === 0) {
      message.error('At least one product is required!');
      setSubmittingForm(false);
      return; // Stop further execution
    }
    setSubmittingForm(true);
    const formattedData = JSON.parse(JSON.stringify(dipatchOrderEntry));
    getDispatchOrderDataForApi(dipatchOrderEntry, productList)
      .then(results => {
        results.forEach((result, index) => {
          formattedData.products[index].product = result.data;
        });
        formattedData.purchaseOrderDate = dipatchOrderEntry.purchaseOrderDate.toISOString();
        formattedData.deliveryDate = dipatchOrderEntry.deliveryDate
          ? dipatchOrderEntry.deliveryDate.toISOString()
          : '';
        formattedData.shipmentDate = dipatchOrderEntry.shipmentDate
          ? dipatchOrderEntry.shipmentDate.toISOString()
          : '';
        formattedData.expectedDeliveryDate = dipatchOrderEntry.expectedDeliveryDate
          ? dipatchOrderEntry.expectedDeliveryDate.toISOString()
          : '';
        formattedData.shipmentCreatedAtDate = dipatchOrderEntry.shipmentCreatedAtDate
          ? dipatchOrderEntry.shipmentCreatedAtDate.toISOString()
          : '';

        console.log(formattedData); //TODO:remove
        // if (formattedData.productList) delete formattedData.productList;
        console.log(formattedData); //TODO:remove
        saveOrderDetails(formattedData);
      })
      .catch(error => {
        console.log('Error in getting data from server', error);
        setSubmittingForm(false);
      });
  };

  const checkShowIfFilledAndValueIn = (conditions, formValues) => {
    return Object.entries(conditions).every(([key, values]) => {
      const formValue = formValues?.[key];
      if (!formValue) return false;
      if (values.length === 0) return true;
      return values.includes(formValue);
    });
  };

  const addRow = () => {
    form.setFieldsValue({
      products: [...(form.getFieldValue('products') || []), {}],
    });
  };

  const getOptionList = key => {
    switch (key) {
      case 'notifyPartyList':
        return notifyPartyList;
      case 'paymentTermsDate':
        return paymentTermsDate;
      case 'currencyList':
        return currencyTypeList;
      default:
        return [];
    }
  };

  const renderField = field => {
    const options = getOptionList(field.optionListKey);
    const fieldStyle = field.style || {}; // directly use field style or empty object

    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={field?.rows} style={fieldStyle} disabled={field?.disable} />;
      case 'date':
        return (
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={fieldStyle}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }}
            disabled={field?.disable}
             />}
          />
        );
      case 'select':
        return (
          <Select showSearch optionFilterProp="label" style={fieldStyle} disabled={field?.disable}>
            {options.map(option => (
              <Select.Option
                key={option[field.optionMapping.key]}
                value={option[field.optionMapping.value]}
                label={option[field.optionMapping.label]}
              >
                {option[field.optionMapping.label]}
              </Select.Option>
            ))}
          </Select>
        );
      case 'text':
        return <Input className="w-52" style={fieldStyle}  disabled={field?.disable}/>;
      case 'number':
        return <Input className="w-52" style={fieldStyle} type="number" disabled={field?.disable} />;
      case 'file':
        return (
          <FileUpload
            category="Orders"
            meta={{ poId: 123, documentType: 'PurchaseOrder' }}
            maxFileLimit={1}
          />
        );
      default:
        return <Input className="w-52" style={fieldStyle} disabled={field?.disable} />;
    }
  };

  const validateAndSaveFormFields = () => {
    setSubmittingForm(true);
    let formObj = form;
    formObj
      .validateFields()
      .then(values => {
        let valuesCopy = JSON.parse(JSON.stringify(values));
        console.log('values', values);
        valuesCopy.products = (valuesCopy.products || []).filter(product =>
          Object.values(product).some(
            value => value !== '' && value !== undefined && value !== null
          )
        );
        valuesCopy = {
          ...valuesCopy,
          products: [
            ...valuesCopy.products.map(prod => ({
              ...prod,
              labelFile: prod.labelFile && prod.labelFile.length ? prod.labelFile[0] : null,
              packaging: getPackagingDetailFromId(prod.packaging, packagingList),
            })),
          ],
        };
        valuesCopy = {
          ...valuesCopy,
          shipmentDate: valuesCopy.shipmentDate ? dayjs(valuesCopy.shipmentDate) : '',
          expectedDeliveryDate: valuesCopy.expectedDeliveryDate
            ? dayjs(valuesCopy.expectedDeliveryDate)
            : '',
          deliveryDate: valuesCopy.deliveryDate ? dayjs(valuesCopy.deliveryDate) : '',
          invoiceDate: valuesCopy.invoiceDate ? dayjs(valuesCopy.invoiceDate) : '',
          shipmentCreatedAtDate: valuesCopy.shipmentCreatedAtDate
            ? dayjs(valuesCopy.shipmentCreatedAtDate)
            : '',
          invoiceSentOn: valuesCopy.invoiceSentOn ? dayjs(valuesCopy.invoiceSentOn) : '',
          dispatchDate: valuesCopy.dispatchDate ? dayjs(valuesCopy.dispatchDate) : '',
          customsReleasedOn: valuesCopy.customsReleasedOn
            ? dayjs(valuesCopy.customsReleasedOn)
            : '',
        };
        setDipatchOrderEntry({
          ...dipatchOrderEntry,
          ...valuesCopy,
        });
        submitFormHandler({
          ...dipatchOrderEntry,
          ...valuesCopy,
        });
      })
      .catch(error => {
        console.log(error);
        message.error('Please fill all required fields.');
        setSubmittingForm(false);
      });
  };

  useEffect(() => {
    if (!packagingList || !packagingList.length) {
      // setLoading(true);
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
            // setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          // setLoading(false);
        });
    }
  }, []);

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    if (ObjectUtil.isEmptyObject(option)) {
      return option;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getFormPrefilldValues = () =>
    dipatchOrderEntry && dipatchOrderEntry?.products
      ? {
          products: dipatchOrderEntry.products.map(prod => ({
            ...prod,
            product: prod.product,
            uom: prod.uom,
            price: prod.price,
            quantity: prod.quantity,
            units: prod.units,
            hsCode: prod.hsCode,
            
            packaging: getPackagingFormValue(prod.packaging, packagingList),
            otherPackagingDetails: prod.packaging?.otherPackagingDetails,
            // remarks: prod.remarks,
            label: prod.label,
            labelFile: ObjectUtil.isObject(prod.labelFile) ? [prod.labelFile] : prod.labelFile,
          })),
        }
      : {};

  useEffect(() => {
    if (!packagingList || !packagingList.length) {
      // setLoading(true);
      // getAllPackagingDetails()
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
            // setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          // setLoading(false);
        });
    }
  }, []);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      packagingList &&
      packagingList.length &&
      dipatchOrderEntry &&
      dipatchOrderEntry.products &&
      dipatchOrderEntry.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [dipatchOrderEntry.productList, dipatchOrderEntry.products, packagingList]); //TODO: verify this check


  // add a loader 
  if (submittingForm) {
    return <PageLoader />;
  }

  return (
    <>
      <div>
        {/* <div className="flex justify-end">
          <Button
            type="primary"
            size="large"
            loading={submittingForm}
            htmlType='submit'
          >
            {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
          </Button>
        </div> */}
        <Form
          name="supplier_form"
          layout="vertical"
          scrollToFirstError
          size="large"
          form={form}
          initialValues={dipatchOrderEntry}
          onFinish={validateAndSaveFormFields}
          // onValuesChange={() => saveformData()}
        >
          <div className="flex justify-end">
          <Button
            type="primary"
            size="large"
            loading={submittingForm}
            htmlType='submit'
          >
            {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
          </Button>
        </div>
          {DispatchOrderFormBasicInfo.map(section => (
            <div key={section.id} className="border-b border-gray-100 last:border-0 pt-6">
              <div className="flex flex-wrap gap-6 px-6 py-0">
                {section.fields.map(field => {
                  let shouldShow = true;

                  if (field.showIfFilledAndValueIn) {
                    shouldShow = checkShowIfFilledAndValueIn(
                      field.showIfFilledAndValueIn,
                      formValues
                    );
                  }

                  if (!shouldShow) return null;

                  return (
                    <Form.Item
                      key={field.id}
                      label={field.label}
                      name={field.id}
                      rules={field?.rules}
                      style={{ width: '200px' }}
                    >
                      {renderField(field)}
                    </Form.Item>
                  );
                })}
              </div>
            </div>
          ))}
          <Divider className={CSS.divider} />
          <div className="flex justify-between items-center px-6">
            <h2 className="text-xl font-semibold text-gray-800">
              Product Details{' '}
              <span className="text-sm text-gray-500">(at least 1 product is needed)</span>
            </h2>
            <Button onClick={addRow} style={{ marginTop: 10 }}>
              Add Row
            </Button>
          </div>
          <ProductTable
            form={form}
            packagingList={packagingList}
            productList={productList}
            initialValue={dipatchOrderEntry}
            setInitialValue={setDipatchOrderEntry}
          />
        </Form>
      </div>
    </>
  );
};

export default DispatchOrderForm;

DispatchOrderForm.propTypes = {
  mode: PropTypes.string.isRequired,
  dispatchOrder: PropTypes.object,
  orderBook: PropTypes.object,
  hide: PropTypes.func.isRequired,
};
