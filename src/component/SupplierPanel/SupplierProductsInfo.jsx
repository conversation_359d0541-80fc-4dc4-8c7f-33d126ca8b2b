import { But<PERSON>, Col, Form, Row } from 'antd';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { formConfigName } from '../../constants/formConstant';
import css from '../CustomerPanel/CustomerForm.module.css';
import SupplierProduct from './SupplierProduct';
import PageLoader from '../Loaders/PageLoader';
import { getFormConfigFromName } from '../../service/api/formConfig';
import { useSelector, useDispatch } from 'react-redux';
import { addPackaging } from '../../store/actions/packagingList';
import ObjectUtil from '../../util/objectUtil';

const SupplierProductsInfo = props => {
  const { form, initialValue, formView, productList } = props;
  const packagingList = useSelector(state => state.packagingList);

  const [loading, setLoading] = useState(false);
  const [productCertificatesType, setProductCertificatesType] = useState([]);
  const [productDocumentsType, setProductDocumentsType] = useState([]);
  const [hazLevel, setHazLevel] = useState([])
  const dispatch = useDispatch();

  const getPackagingFormValue = (options, list) => {
    if (!options.length) {
      return null;
    }
    const customPackagingList = [];
    const selectedPackagingList = options.map((option) => {
      if (ObjectUtil.isEmptyObject(option)) {
        return option;
      }
      const listOptn = list.find(
        item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
      );
      if (listOptn && listOptn.id) return listOptn.id;
      else {
        const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
        customPackagingList.push({ ...option, id });        
        return id;
      }
    })
    dispatch(addPackaging(customPackagingList));
    return selectedPackagingList;
  };

  const getFormPrefilldValues = () =>
    initialValue && initialValue?.products
      ? {
          products: initialValue.products.map(product => ({
            ...product,
            product: product.product.id ?? product.product,
            packaging: getPackagingFormValue(product.packaging, packagingList),
          })),
        }
      : {};

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      initialValue &&
      initialValue.products &&
      initialValue.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [productList]);

  useEffect(() => {
    setLoading(true);
    const fetchListApi = [
      getFormConfigFromName(formConfigName.productCertificates),
      getFormConfigFromName(formConfigName.productDocuments),
      getFormConfigFromName(formConfigName.hazardousLevel),
    ];
    Promise.all(fetchListApi).then((res) => {
      if (res?.[0]?.data?.value) {
        setProductCertificatesType(res?.[0].data.value)
      }
      if (res?.[1]?.data?.value) {
        setProductDocumentsType(res?.[1].data.value)
      }
      if (res?.[2]?.data?.value) {
        setHazLevel(res?.[2].data.value)
      }
      setLoading(false);
    }).catch((error) => {
      console.log(error);
      setLoading(false);
    })
  }, []);


  if (loading) {
    return <PageLoader style={{ width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      className={css.formContainer}
      name="supplier_product_info"
      layout="vertical"
      scrollToFirstError
      size="large"
      form={form}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.List
            name="products"
            rules={[
              {
                validator: async (_, products) => {
                  console.log('validator --', _);
                  console.log('*** val ', products);
                  if (!products || products.length < 1) {
                    return Promise.reject(new Error('At least 1 product details is required'));
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }) => {
              console.log('validator fields value ', fields);
              return (
                <div
                  style={{
                    display: 'flex',
                    rowGap: 16,
                    flexDirection: 'column',
                  }}
                >
                  {fields.map((field, index) => {
                    return (
                      <SupplierProduct
                        key={`product_${field.name}`}
                        index={index}
                        formView={formView}
                        field={field}
                        remove={remove}
                        form={form}
                        productList={productList}
                        packagingList={packagingList}
                        productCertificatesType={productCertificatesType}
                        productDocumentsType={productDocumentsType}
                        hazLevel={hazLevel}
                      />
                    );
                  })}
                  <Button type="dashed" onClick={() => add()} block>
                    + Add Product
                  </Button>
                </div>
              );
            }}
          </Form.List>
        </Col>
      </Row>
    </Form>
  );
};

export default SupplierProductsInfo;

SupplierProductsInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array,
};
