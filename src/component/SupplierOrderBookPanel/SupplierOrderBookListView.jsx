import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { Badge, Button, Input, Table } from 'antd';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import addIcon from '../../assets/icons/add_circle.svg';
import filterIcon from '../../assets/icons/filter_icon.svg';
import {
  supplierOrderBookHeader,
  supplierOrderTablePageSize,
} from '../../constants/TableConstants';
import { formModes } from '../../constants/formConstant';
import { getSupplierOrderBookList } from '../../service/api/supplierOrderBookApi';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import { formatSupplierOrderBookList } from '../../util/formUtil';
import css from '../CustomerPanel/CustomerListView.module.css';
import FilterModal from '../FilterModal/FilterModal';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import { useLocation, useNavigate } from 'react-router-dom';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';

const SupplierOrderBookListView = props => {
  const {
    collapseAsideBar,
    collapseAsideBarHandler,
    collapseSideDrawerHandler,
    setOrderBookFormView,
  } = props;

  const [orderBookList, setOrderBookList] = useState({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [showClear, setShowClear] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  const user = useSelector(state => state.user);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const getOrderBookListFromConfig = (pageSize, pageNumber, filters, searchkey) => {
    setLoading(true);
    getSupplierOrderBookList(pageSize, pageNumber, filters, searchkey)
      .then(response => {
        // TODO: proper check for orderBookList
        setOrderBookList(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
  };
  const onRowClick = record => {
    dispatch(setSideDrawerData(record));
    collapseSideDrawerHandler(false);
    // collapseAsideBarHandler(true);
  };
  const onClickAddOrder = () => {
    setOrderBookFormView({
      show: true,
      mode: formModes.CREATE,
    });
    collapseAsideBarHandler(true);
    collapseSideDrawerHandler(true);
  };
  // const getOrdersFromSearch = searchText => {
  //   getOrderBookListFromConfig(null,null, null, searchText);
  // };

  useEffect(() => {
    //api call to get order list TODO: format api params
    const pageNumber = 0;
    const { filters, searchkey } = getQeryParamsFromURL();
    if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getOrderBookListFromConfig(supplierOrderTablePageSize, pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <div className={css.header}>
        <Button
          onClick={() => collapseAsideBarHandler(!collapseAsideBar)}
          className={css.foldMenuBtn}
          type="text"
          size="large"
          icon={
            collapseAsideBar ? (
              <MenuUnfoldOutlined style={{ color: '#000' }} />
            ) : (
              <MenuFoldOutlined style={{ color: '#000' }} />
            )
          }
        />
        <div className={css.title}>Supplier Order book</div>
      </div>
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search by PO Number"
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={
              value => {
                updateParamsAndNavigate(navigate, location, { searchkey: value });
              }
              // getOrdersFromSearch
            }
            bordered={false}
            enterButton
          />
        </div>
        <Badge color="#23568A" count={filterCount}>
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => setShowFiltersModal(true)}
          >
            <img src={filterIcon} />
            <span>Filter</span>
          </Button>
        </Badge>
        {showClear ? (
          <Button
            size="large"
            className={css.btnFlexStyle}
            onClick={() => {
              updateParamsAndNavigate(navigate, location, {});
              // getOrderBookListFromConfig();
              setShowClear(false);
              setFilterCount(0);
            }}
          >
            <span>Clear Filter</span>
          </Button>
        ) : null}
        {hasPermission(userPermissionsList.createsupplierOrderBook, user.permissions) ? (
          <Button
            onClick={onClickAddOrder}
            type="primary"
            size="large"
            className={css.btnFlexStyle}
          >
            <img src={addIcon} />
            <span>Add Order</span>
          </Button>
        ) : null}
      </div>
      <Table
        columns={supplierOrderBookHeader}
        dataSource={formatSupplierOrderBookList(orderBookList?.content)}
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          // give current page as 1 if page number is not avialable
          current: (orderBookList?.pageable?.pageNumber)?orderBookList?.pageable?.pageNumber + 1:1,
          pageSize: supplierOrderTablePageSize,
          total: orderBookList?.totalElements,
          onChange: page => {
            const { filters, search } = getQeryParamsFromURL();
            getOrderBookListFromConfig(supplierOrderTablePageSize, page - 1, filters, search);
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
        onRow={record => {
          return {
            onClick: () => {
              const [sourceRecord] = orderBookList.content.filter(
                orderBook => record.orderId === orderBook.id
              );
              onRowClick({
                sourceRecord,
                sourceItemId: record.id,
              });
            },
          };
        }}
      />
      <FilterModal
        view="supplierOrderBook"
        open={showFiltersModal}
        onApplyFilters={values => {
          setFilterCount(
            Object.values(values).reduce(
              (total, currVal) => (Array.isArray(currVal) ? total + currVal.length : total),
              0
            )
          );
          updateParamsAndNavigate(navigate, location, { filters: values });
          // getOrderBookListFromConfig(null,null, values, null);
          setShowFiltersModal(false);
          setShowClear(true);
        }}
        onCancel={() => setShowFiltersModal(false)}
      />
    </>
  );
};

export default SupplierOrderBookListView;

SupplierOrderBookListView.propTypes = {
  collapseAsideBar: PropTypes.bool.isRequired,
  // collaspseSideDrawer: PropTypes.bool.isRequired,
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
  setOrderBookFormView: PropTypes.func.isRequired,
};
