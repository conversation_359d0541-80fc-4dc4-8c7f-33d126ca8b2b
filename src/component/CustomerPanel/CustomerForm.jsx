import React, { useEffect, useState } from 'react';
import { Button, Col, Form, Input, Row, Select, Space, Typography } from 'antd';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import {
  countryList,
  customerSize,
  customerType,
  formConfigName,
  formModes,
} from '../../constants/formConstant';
import { getEmployeeList } from '../../service/api/employee';
import { useDispatch } from 'react-redux';
import { getFormConfigFromName } from '../../service/api/formConfig';
import css from './CustomerForm.module.css';
import PageLoader from '../Loaders/PageLoader';
import HeaderPanel from '../headerPanel';
import { createCustomer, getCustomerData, updateCustomer } from '../../service/api/customerApi';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';
import { getFormModeFromPath } from '../../util/route';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';

const CustomerForm = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { customerId } = useParams();
  const { pathname } = useLocation();

  const [loading, setLoading] = useState(false);
  const [accountOwnerList, setAccountOwnerList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [submittingForm, setSubmittingForm] = useState(false);
  const [customerData, setCustomerData] = useState({});
  const [employeeList, setEmployeeList] = useState([]);
  const { api } = useNotificationContext();


  const currentFormMode = getFormModeFromPath(pathname);
  const route = new RouteFactory();
  const prefixSelector = (
    <Form.Item name="countryCode" noStyle rule={[{ required: true }]}>
      <Select
        style={{
          width: 100,
        }}
        popupMatchSelectWidth={false}
        optionLabelProp="label"
        showSearch
        optionFilterProp="country"
      >
        {countryList.map((country, index) => (
          <Select.Option
            key={index}
            value={country.dial_code}
            label={`+${country.dial_code}`}
            country={country.name}
          >
            <Space>
              <span role="img" aria-label={country.dial_code}>
                {country.flag}
              </span>
              {`+${country.dial_code} ${country.name}`}
            </Space>
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const goToDashBoard = () => {
    collapseSideDrawerHandler(true);
    collapseAsideBarHandler(false);
    navigate(route.dashboard().customer().build());
  };
  const saveCustomerDetails = (updatedCustomerData, mode) => {
    if (mode === formModes.CREATE) {
      createCustomer(customerData)
        .then(response => {
          console.log('Customer api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(err => {
          api.open({
            key: 'customerSaveError',
            message: 'Failed to add new customer!!',
            description: (
              <>
                <div className='text-xs font-medium'>{err?.response?.data?.message || err?.message}</div>
              </>
            ),        
            duration: 5,
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            ),
          });
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE && customerId) {
    let payload = {
      ...customerData,
      emailInfo : {
        salesEmail: updatedCustomerData?.salesEmail,
        accountEmail: updatedCustomerData?.accountEmail,
        optionalEmails: updatedCustomerData?.optionalEmails,
      },
      email : updatedCustomerData?.salesEmail,
      l1Reviewers: updatedCustomerData?.l1Reviewers,
      l2Reviewers: updatedCustomerData.l2Reviewers,
    };
      updateCustomer(payload, customerId)
        .then(response => {
          console.log('Customer api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(error => {
          setSubmittingForm(false);
          console.log(error);
        });
    }
  };
  const submitFormHandler = () => {
    setSubmittingForm(true);
    form
      .validateFields()
      .then(values => {
        saveCustomerDetails(values, currentFormMode);
      })
      .catch(error => {
        console.log(error);
        setSubmittingForm(false);
      });
  };

  useEffect(() => {
    if (currentFormMode === formModes.CREATE) setLoading(true);
    const fetchApiList = [getEmployeeList(), getFormConfigFromName(formConfigName.category)];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setAccountOwnerList(responseList[0].data.content);
        }
        if (responseList[1].data) {
          setCategoryList(responseList[1].data.value);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        if (currentFormMode === formModes.CREATE) setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (customerId && currentFormMode === formModes.UPDATE) {
      setLoading(true);
      getCustomerData(customerId)
        .then(res => {
          if (res?.data?.id) {
            let customerObj = res.data;
            customerObj={
              ...customerObj,
              salesEmail: customerObj?.emailInfo?.salesEmail,
              accountEmail: customerObj?.emailInfo?.accountEmail,
              optionalEmails: customerObj?.emailInfo?.optionalEmails,
            };
            setCustomerData(customerObj);      
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [customerId]);

  useEffect(() => {
    if (currentFormMode === formModes.CREATE) setLoading(true);
    const fetchApiList = [getEmployeeList()];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setEmployeeList(responseList[0].data.content);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        if (currentFormMode === formModes.CREATE) setLoading(false);
      });
  }, []);

  return (
    <>
      <HeaderPanel
        name="Customer Directory"
        sideButtons={
          <>
            <Space>
              <Button
                type="primary"
                // htmlType="submit"
                size="large"
                onClick={() => submitFormHandler()}
                loading={submittingForm}
              >
                {currentFormMode === formModes.CREATE ? 'Add Customer' : 'Save Changes'}
              </Button>
              <Button type="default" onClick={goToDashBoard} size="large">
                Cancel
              </Button>
            </Space>
          </>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <div className={css.formContainer}>
          <Typography.Title level={2} style={{ color: '#23568A' }}>
            Add a customer
          </Typography.Title>
          <Form
            name="customer_form"
            layout="vertical"
            scrollToFirstError
            initialValues={currentFormMode === formModes.CREATE ? {} : customerData}
            preserve={false}
            size="large"
            form={form}
            validateTrigger="onBlur"
          >
            <Typography.Title level={4} style={{ color: '#23568A', fontWeight: '400' }}>
              Basic Information
            </Typography.Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Name"
                  name="name"                 
                  rules={[
                    {
                      required: true,
                      message: 'Please input customer name!',
                    },
                  ]}
                >
                  <Input disabled={currentFormMode === formModes.UPDATE} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="salesEmail"
                  label="Sales Email "
                  defaultValue = {customerData?.emailInfo?.salesEmail}
                  rules={[{ type: 'email' , required : true , message: 'Please input Sales/Business SPOC Email' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="accountEmail"
                  label="Accounts Email"
                  
                  rules={[{ type: 'email', required : true , message: 'Please input Accounts/Controller Email' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="optionalEmails"
                  label="Other Emails"               
                >
                  <Select 
                    mode="tags" 
                    placeholder="Enter email addresses and press Enter"
                    tokenSeparators={[',']}
                    style={{ width: '100%'}}
                    open={false}
                    validateStatus="success"                 
                    rules={[
                      {
                        validator: (_, value) => {
                          if (!value || value.length === 0) {
                            return Promise.resolve();
                          }
                          const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                          const invalidEmails = value.filter(email => !emailPattern.test(email));
                          return invalidEmails.length > 0
                            ? Promise.reject(new Error(`Invalid email(s): ${invalidEmails.join(', ')}`))
                            : Promise.resolve();
                        },
                      },
                    ]}
                  />
                </Form.Item>
              </Col>
              {/* <Col span={12}>
                <Form.Item
                  name="email"
                  label="Email"
                  rules={[{ type: 'email' }, { required: true }]}
                >
                  <Input />
                </Form.Item>
              </Col> */}
        
            </Row>
            {/* <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Postal Code" name={['address', 'postalCode']}>
                  <Input type="number" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="mobile"
                  label="Mobile Number"
                  rules={[
                    {
                      len: 10,
                      message: 'Please input correct phone number!',
                    },
                  ]}
                >
                  <Input addonBefore={prefixSelector} type="tel" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Corporate Address"
                  name={['address', 'street']}
                  rules={[
                    {
                      required: true,
                      message: 'Address required!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="City"
                  name={['address', 'city']}
                  rules={[
                    {
                      required: true,
                      message: 'City required!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="State"
                  name={['address', 'state']}
                  rules={[
                    {
                      required: true,
                      message: 'State required!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Country"
                  name={['address', 'country']}
                  rules={[
                    {
                      required: true,
                      message: 'Country required!',
                    },
                  ]}
                >
                  <Select showSearch>
                    {countryList.map((country, index) => (
                      <Select.Option key={index} value={country.name}>
                        {`${country.flag} ${country.name}`}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Typography.Title level={4} style={{ color: '#23568A', fontWeight: '400' }}>
              Company Information
            </Typography.Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Size of customer" name="size" rules={[{ required: true }]}>
                  <Select optionLabelProp="label">
                    {customerSize.map(size => (
                      <Select.Option key={size.key} value={size.value} label={size.label}>
                        <div style={{ display: 'flex' }}>
                          <span>{size.label}</span>
                          <span style={{ marginLeft: 'auto' }}>{size.label_desc}</span>
                        </div>
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                {accountOwnerList.length ? (
                  <Form.Item name="accountOwner" label="Account Owner" rules={[{ required: true }]}>
                    <Select showSearch>
                      {accountOwnerList.map(emp => (
                        <Select.Option key={emp.id} value={`${emp.name}`}>
                          {`${emp.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                {categoryList?.length ? (
                  <Form.Item
                    label="Category"
                    name="categories"
                    rules={[
                      { required: false, message: 'Please select categories', type: 'array' },
                    ]}
                  >
                    <Select mode="multiple" showSearch>
                      {categoryList.map((category, index) => (
                        <Select.Option key={index} value={category.name}>
                          {category.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
              <Col span={12}>
                <Form.Item name="type" label="Type" rules={[{ required: true }]}>
                  <Select>
                    {customerType.map(type => (
                      <Select.Option key={type.key} value={type.value}>
                        {type.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row> */}
            <Row gutter={16}>
              <Col span={24}>
                {employeeList.length ? (
                  <Form.Item name="l1Reviewers" label="L1 Reviewers" rules={[{ required: true }]}>
                    <Select mode="multiple" showSearch>
                      {employeeList.map(emp => (
                        <Select.Option key={emp.id} value={`${emp.id}`}>
                          {`${emp.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                {employeeList.length ? (
                  <Form.Item name="l2Reviewers" label="L2 Reviewers" rules={[{ required: true }]}>
                    <Select mode="multiple" showSearch>
                      {employeeList.map(emp => (
                        <Select.Option key={emp.id} value={`${emp.id}`}>
                          {`${emp.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
            </Row>
            {/* <Row>
              <Col span={24}>
                <Form.Item name="remarks" label="Remarks(Press enter to add)">
                  <MultipleTextFieldInput
                    type="TextArea"
                    mode={currentFormMode}
                    placeholder="Enter a remark"
                    savetimeStamp
                    showDelete
                  />
                </Form.Item>
              </Col>
            </Row> */}
          </Form>
        </div>
      )}
    </>
  );
};

export default CustomerForm;
