import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Card, Col, Form, InputNumber, Row, Select } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

const InventoryOutBatch = (props) => {
  const {
    form, parentField,  field, index, remove, bacthData,
  } = props;

  const [selectedSupplier, setSelectedSupplier] = useState({});
  const batchSupplierWatch = Form.useWatch(['products', parentField.name, 'productBatches', field.name, "supplierOrderUID"], form);

  useEffect(() => {
    if (batchSupplierWatch) {
      const supplierData = bacthData.find((item) => item.supplierOrderUID === batchSupplierWatch);
      if (supplierData) {
        setSelectedSupplier(supplierData);
        const prodData = form.getFieldValue('products');
        prodData[parentField.name].productBatches[index].supplierName = supplierData?.supplierName;
        prodData[parentField.name].productBatches[index].orderId = supplierData?.orderId;
        form.setFieldsValue({
          products: prodData,
        })
      }
    }
  }, [batchSupplierWatch]);

  return (
    <Card
      size="small"
      title={`Supplier ${index + 1}`}
      key={field.key}
      extra={<CloseOutlined
        onClick={() => {
          remove(field.name);
        }}
      />}
    >
      <Row gutter={16}>
        <Col span={24}>
          {/* {bacthData.length ? ( */}
            <Form.Item
              label="SO number"
              name={[field.name, "supplierOrderUID"]}
              rules={[{ required: true, message: 'Please select SO number' }]}
            >
              <Select showSearch>
                {bacthData.map(order => (
                  <Select.Option key={order?.supplierOrderUID} value={order?.supplierOrderUID}>
                    <div>
                      <div>{order?.orderId}</div>
                      <div>{order?.supplierName}</div>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          {/* ) : null} */}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name={[field.name, 'units']}
            rules={[
              {
                required: true,
                message: 'Please input units',
              },
            ]}
            label="Units"
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <div>Maximum units available: {selectedSupplier.units}</div>
        </Col>
      </Row>
    </Card>
  )
}

export default InventoryOutBatch;

InventoryOutBatch.propTypes = {
  form: PropTypes.object.isRequired,
  parentField: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  remove: PropTypes.func.isRequired,
  bacthData: PropTypes.array.isRequired,
};