.CustomSelectOptn > .optnTitle {
  margin-bottom: 8px;
  color: #1e1e1e;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  letter-spacing: -0.36px;
}
.CustomSelectOptn > .optnDetails {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.CustomSelectOptn > .optnDetails > .optnLabel {
  color: rgba(30, 30, 30, 0.7);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  letter-spacing: -0.28px;
}
.CustomSelectOptn > .optnDetails > .optnValue {
  color: #1e1e1e;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%; /* 14px */
  letter-spacing: -0.28px;
}

.selectBox,
.selectBox > div:first-child {
  height: fit-content !important;
  padding-top: 5px !important;
}
.selectBox > div:first-child > div > div > span {
  height: auto !important;
}

.customAddOptn {
  display: flex;
  flex-wrap: wrap;
  row-gap: 10px;
  align-items: center;
  justify-content: space-between;
}

.productTable {
  width: 100%;
  border-collapse: collapse;
}
.productTable th,
.productTable td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
/* .productTable th {
  background-color: #f4f4f4;
} */
.productTable tbody tr:nth-child(odd) {
  background-color: #f9f9f9;
}
.productTable tbody tr:nth-child(even) {
  background-color: #f0f4f8;
}

.ScrollContainer {
  overflow-x: auto;
  max-width: 100%;
  padding: 16px;
  /* scrollbar-width: thin;
  scrollbar-color: #007bff #f0f0f0; */
}

/* .ScrollContainer::-webkit-scrollbar {
  height: 8px;
}
.ScrollContainer::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}
.ScrollContainer::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 10px;
}
.ScrollContainer::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}  */
