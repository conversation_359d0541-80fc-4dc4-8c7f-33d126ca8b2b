import React , { 
  // useEffect, 
  // useState 
}
 from 'react';
import PropTypes from 'prop-types';
import { Collapse, Descriptions } from 'antd';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { orderStatusList, paymentTermsDate, productIncoTermsList, productUOMList } from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { camelCaseToTitle } from '../../util/stringUtils';
// import {  getEmployeesNameFromId } from "../../util/userUtils";
// import { getEmployeeList } from '../../service/api/employee';


const DispatchOrderFormPreview = (props) => {
  const { dispatchOrderEntry } = props;

  // const [employeeList, setEmployeeList] = useState([]);

  // useEffect(() => {
  //   // if (formView === formModes.CREATE) setLoading(true);
  //   const fetchApiList = [getEmployeeList()];
  //   Promise.all(fetchApiList).then((responseList) => {
  //     if (responseList[0]?.data && responseList[0].data?.content) {
  //       setEmployeeList(responseList[0].data.content);
  //     }
  //     // setLoading(false);
  //   }).catch(error => {
  //     console.log(error);
  //     // if (formView === formModes.CREATE) setLoading(false);
  //   });
  // }, []);

  const orderFieldItems = [
    {
      key: '1',
      label: 'Customer Name',
      children: dispatchOrderEntry?.customer?.name || dispatchOrderEntry?.customerData?.name,
    },
    {
      key: '2',
      label: 'PO number',
      children: dispatchOrderEntry?.purchaseOrderNumber,
    },
    {
      key: '3',
      label: 'PO Date',
      children: getDateFromTimeStamp(dispatchOrderEntry?.purchaseOrderDate),
    },
    {
      key: '4',
      label: 'Status',
      children: (
        <div
          style={{
            color: dispatchOrderEntry?.status ? orderStatusList[dispatchOrderEntry.status].color : '#000',
            fontWeight: '600',
          }}
        >
          {dispatchOrderEntry?.status ? orderStatusList[dispatchOrderEntry.status].label : '-'}
        </div>
      ),
    },
  ];
  const shipmentFieldItems = [
    {
      key: '1',
      label: 'Shipment created at',
      children: dispatchOrderEntry?.shipmentCreatedAtDate
        ? getDateFromTimeStamp(dispatchOrderEntry?.shipmentCreatedAtDate)
        : '',
    },
    {
      key: '2',
      label: 'Consignee Details',
      children: dispatchOrderEntry?.consignee,
    },
    {
      key: '3',
      label: 'Notify Party Details',
      children: dispatchOrderEntry?.notifyParty,
    },
    {
      key: 'palletWt',
      label: 'Pallet Wt ( Chemstack )',
      children: dispatchOrderEntry?.palletWt,
    },
    {
      key: 'mstackPalletWt ( Mstack )',
      label: 'Pallet Wt',
      children: dispatchOrderEntry?.mstackPalletWt,
    },
    {
      key: 'marksAndContainers',
      label: 'Marks & Containers',
      children: dispatchOrderEntry?.marksAndContainers,
    },
    {
      key: '4',
      label: 'Incoterms',
      children: (
        <div className='flex flex-col'>
          <div>
            <span style={{ fontWeight: '500' }}>Type:</span>
            <span>{getEnumValueFromList(dispatchOrderEntry?.incoterms?.type, productIncoTermsList)}</span>
          </div>
          <div>
            <span style={{ fontWeight: '500' }}>Country:</span>
            <span>{dispatchOrderEntry?.incoterms?.country}</span>
          </div>
          {dispatchOrderEntry?.incoterms?.data && Object.keys(dispatchOrderEntry.incoterms.data).length > 0
            ? Object.keys(dispatchOrderEntry.incoterms.data).map(key => (
                <div key={key}>
                  <span style={{ fontWeight: '500' }}>{camelCaseToTitle(key)}:</span>
                  <span>{dispatchOrderEntry.incoterms.data[key]}</span>
                </div>
              ))
            : ''}
        </div>
      ),
    },
  ];
  const paymentFieldItems = [
    {
      key: 'poPaymentTerms',
      label: 'PO Payment Terms',
      children: dispatchOrderEntry?.paymentTerms?.poPaymentTerms,
    },
    {
      key: '10',
      label: 'Credit amount(in %)',
      children: dispatchOrderEntry?.paymentTerms?.creditAmount,
    },
    {
      key: '11',
      label: 'Credit days',
      children: dispatchOrderEntry?.paymentTerms?.creditorDays,
    },
    {
      key: '12',
      label: 'Payment terms based on',
      children: getEnumValueFromList(dispatchOrderEntry?.paymentTerms?.startDate, paymentTermsDate),
    },
  ];

  // const reviewerFieldItems = [
  //   {
  //     key: 'l1Reviewer',
  //     label: 'L1 Reviewers',
  //     children: getEmployeesNameFromId(dispatchOrderEntry?.l1Reviewers,employeeList) ,
  //   },
  //   {
  //     key: 'l2Reviewer',
  //     label: 'L2 Reviewer',
  //     children: getEmployeesNameFromId(dispatchOrderEntry?.l2Reviewers,employeeList),
  //   },
  // ]


  const dispatchProductsList = dispatchOrderEntry?.products?.length
  ? dispatchOrderEntry.products.map((product, index) => ({
      key: index,
      label: product?.product?.tradeName || dispatchOrderEntry?.productList[index]?.product?.tradeName,
      children: (
        <Descriptions
          layout="vertical"
          column={2}
          items={getProductFields(product)}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      )
    }))
  : [];

  function getProductFields (productData) {
    return [
      {
        key: '1',
        label: 'Quantity',
        children: productData?.quantity,
      },
      {
        key: '2',
        label: 'UOM',
        children: getEnumValueFromList(productData?.uom, productUOMList),
      },
      {
        key: '3',
        label: 'Price per unit',
        children: productData?.price,
      },
      {
        key: '4',
        label: 'HS Code',
        children: productData.hsCode,
      },      
      {
        key: '5',
        label: 'Packaging',
        children: (
          <div>
            <div>
              <span style={{ fontWeight: '600' }}>Type:</span>
              <span>{productData?.packaging?.type}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Pack Size:</span>
              <span>{productData?.packaging?.packSize}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Tare Weight:</span>
              <span>{productData?.packaging?.tareWeight}</span>
            </div>
            <div>
              <span style={{ fontWeight: '600' }}>Dimension:</span>
              <span>{productData?.packaging?.dimension}</span>
            </div>
          </div>
        ),
      },
      {
        key: '6',
        label: 'Quantity per packaging',
        children: productData.quantityPerUnit,
      },
      {
        key: '7',
        label: 'District of origin',
        children: productData.districtOfOrigin,
      },  
      {
        key: '8',
        label: 'State of origin',
        children: productData.stateOfOrigin,
      },  
      {
        key: '9',
        label: 'Product name alias',
        children: productData.productNameAlias,
      },  
      {
        key: '10',
        label: 'Chemstack Price',
        children: productData?.chemstackPrice,
      },  
      {
        key: '11',
        label: 'Per Unit Kg Val',
        children: productData?.perUnitKgValue,
      }, 
      {
        key: '12',
        label: 'Batch Date visibility',
        children: productData?.batchDateVisible ? "yes":"no",
      }, 
    ];
  }

  return (
    <div className='flex flex-col gap-5'>
      <Descriptions
        title={<div className='text-lg font-semibold'>Order Details</div>}
        items={orderFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Shipment Details</div>}
        items={shipmentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
      <Descriptions
        title={<div className='text-lg font-semibold'>Payment Details</div>}
        items={paymentFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      />
            {/* <Descriptions
        title={<div className='text-lg font-semibold'>Reviewer Details</div>}
        items={reviewerFieldItems}
        column={2}
        labelStyle={{ fontWeight: '700', color: '#23568A' }}
        layout='vertical'
      /> */}
      <div className='text-lg font-semibold'>Product Detail</div>
      <Collapse
        style={{ marginTop: '20px' }}
        items={dispatchProductsList} defaultActiveKey={['0']}
      />
    </div>
  );
}

export default DispatchOrderFormPreview;

DispatchOrderFormPreview.propTypes = {
  dispatchOrderEntry: PropTypes.object.isRequired,
};