import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

export const getAllUserRoles = () => {
  return axiosInstance.get(apiEndpoints.permissionsGroup);
}

export const getUserRolesbyName = (roleName) => {
  return axiosInstance.get(`${apiEndpoints.permissionsGroup}/${roleName}`);
}

export const createUserRole = (data) => {
  return axiosInstance.post(apiEndpoints.permissionsGroup, data);
}

export const updateUserRole = (data, roleId) => {
  return axiosInstance.put(`${apiEndpoints.permissionsGroup}/${roleId}`, data);
}

export const deleteUserRole = (roleId) => {
  return axiosInstance.delete(`${apiEndpoints.permissionsGroup}/${roleId}`);
}

export const getAllPermissionConfig = () => {
  return axiosInstance.get(`${apiEndpoints.permissionsGroup}${apiEndpoints.permissionConfig}`);
}