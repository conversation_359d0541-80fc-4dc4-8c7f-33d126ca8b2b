import { Drawer } from 'antd';
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { userEntity } from '../../constants/UserTabsConstants';
import OrderBookForm from '../OrderBookPanel/OrderBookForm';
import DispatchOrderForm from '../DispatchOrderPanel/DispatchOrderForm';
import {
  formatDispatchOrderDataForForm,
  formatOrderBookDataForForm,
  formatSupplierOrderBookDataForForm,
  formatSupplierDispatchOrderDataForForm,
} from '../../util/formUtil';
import SupplierOrderBookForm from '../SupplierOrderBookPanel/SupplierOrderBookForm';
import SupplierDispatchOrderForm from '../SupplierDispatchOrderPanel/SupplierDispatchOrderForm';
import InventoryOutOrderForm from '../InventoryPanel/InventoryOutOrderForm';
import UserForm from '../SettingsPanel/UserForm';
import RoleForm from '../SettingsPanel/RoleForm';
import PackagingForm from '../SettingsPanel/PackagingForm';

const OverlayDrawer = (props) => {
  const { showDrawer, drawerData, onSubmit, hide } = props;
  const location = useLocation();

  // Close drawer on route change
  useEffect(() => {
    if (showDrawer) {
      hide();
    }
  }, [location.pathname]);

  const handleFormSubmit = (data) => {
    onSubmit(data);
    drawerData?.formTimeTracker?.endTracking(true,drawerData?.orderType);
    hide();
  };
  const getFormFromEntity = () => {
    if (drawerData?.entity === userEntity.customer)
      return 'customer';
    else if (drawerData?.entity === userEntity.supplierOrderBook)
      return <SupplierOrderBookForm
              mode={drawerData.mode}
              order={drawerData.formData ? formatSupplierOrderBookDataForForm(drawerData.formData) : null}
              orderBookId={drawerData.orderBookId}
              hide={handleFormSubmit}
              COBProducts={drawerData?.orderBookProducts?.map((item) => item.product)}
              orderType={drawerData?.orderType}
              orderBook={drawerData?.orderBook}
              poGenrationRequired={drawerData?.poGenrationRequired}
            />;
    else if (drawerData?.entity === userEntity.orderBook)
      return <OrderBookForm
              mode={drawerData.mode}
              order={drawerData.formData ? formatOrderBookDataForForm(drawerData.formData) : null}
              hide={handleFormSubmit} 
              addOrderFormOpened = {drawerData?.formTimeTracker}             
            />;
    else if (drawerData?.entity === userEntity.supplierDispatchOrder)
      return <SupplierDispatchOrderForm
              mode={drawerData.mode}
              dispatchOrder={drawerData.formData ? formatSupplierDispatchOrderDataForForm(drawerData.formData) : null}
              orderBook={drawerData?.orderBook}
              hide={handleFormSubmit}
              customerorderbookId={drawerData?.customerorderbookId}
              poGenrationRequired={drawerData?.poGenrationRequired}
            />
    else if (drawerData?.entity === userEntity.dispatchOrder)
      return <DispatchOrderForm
              mode={drawerData.mode}
              dispatchOrder={drawerData.formData ? formatDispatchOrderDataForForm(drawerData.formData) : null}
              orderBook={drawerData?.orderBook}
              hide={handleFormSubmit}
            />
    else if (drawerData?.entity === userEntity.products)
      return 'products'
    else if (drawerData?.entity === userEntity.supplier)
      return 'supplier'
    else if (drawerData?.entity === userEntity.enquiry)
      return 'enquiry'
    else if (drawerData?.entity === userEntity.inventoryOut)
      return <InventoryOutOrderForm
              mode={drawerData.mode}
              order={drawerData?.formData}
              customerDispatchList={drawerData?.customerDispatchList}
              hide={handleFormSubmit}
            />
    else if (drawerData?.entity === userEntity.employeeUser)
      return <UserForm
              mode={drawerData.mode}
              user={drawerData?.formData}
              hide={handleFormSubmit}
            />
    else if (drawerData?.entity === userEntity.userRole)
      return <RoleForm
              mode={drawerData.mode}
              userRole={drawerData?.formData}
              hide={handleFormSubmit}
            />
    else if (drawerData?.entity === userEntity.packaging)
      return <PackagingForm
              mode={drawerData.mode}
              packaging={drawerData?.formData}
              hide={handleFormSubmit}
            />
  };

  return (
    <Drawer
      title={<div className='capitalize'>{drawerData.mode} {drawerData.entity}</div>}
      onClose={()=> hide( drawerData?.entity, false )}
      open={showDrawer}
      style={{ position: 'relative' }}
      width='100%'
      destroyOnClose={true}
      maskClosable={true}
      keyboard={true}
      classNames={{
        header: '*:flex-row-reverse',
        body: '!pt-4 !pb-[70px] !px-5'
      }}
    >
      {drawerData?.entity ? getFormFromEntity() : null}
    </Drawer>
  );
}

export default OverlayDrawer;

OverlayDrawer.propTypes = {
  showDrawer: PropTypes.bool.isRequired,
  drawerData: PropTypes.object.isRequired,
  onSubmit: PropTypes.func.isRequired,
  hide: PropTypes.func.isRequired,
}
