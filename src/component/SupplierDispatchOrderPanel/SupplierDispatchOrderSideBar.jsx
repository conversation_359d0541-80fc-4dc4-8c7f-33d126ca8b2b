/* eslint-disable react/prop-types */

import React from 'react';
import PropTypes from 'prop-types';
import { Button, Space } from 'antd';
import close from '../../assets/icons/close.svg';
import edit from '../../assets/icons/edit_icon.svg';
import view from '../../assets/icons/view_icon.svg';
import { useDispatch, useSelector } from 'react-redux';
// import { formModes } from '../../constants/formConstant';
import css from '../CustomerPanel/CustomerSideBar.module.css';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import { hasPermission } from '../../util/userUtils';
import {
  Link,
  // useNavigate
} from 'react-router-dom';
import RouteFactory from '../../service/RouteFactory';

const SupplierDispatchOrderSideBar = props => {
  const { order, hide } = props;

  const dispatch = useDispatch();
  // const navigate = useNavigate();
  const user = useSelector(state => state.user);
  const openFormView = () => {
    dispatch(setCollapseAsideBar(true));
    hide();
  };
  const hideSideDrawer = () => {
    hide();
  };

  return order && order?.products?.length ? (
    <div className={css.customerSider}>
      <div className={css.titleBar}>
        {/* TODO:change the heading to order id */}
        <div className={css.title}>{`${order.purchaseOrderNumber}`}</div>
        <div className={css.closeBtn} onClick={hideSideDrawer}>
          <img src={close} alt="close" />
        </div>
      </div>
      <div className={css.actionBtnBox}>
        <Link
          to={new RouteFactory().dashboard().supplierDispatchOrder().setId(order.id).view().build()}
          target="_blank"
        >
          <Button
            onClick={() => {
              hideSideDrawer();
              // navigate(
              //   new RouteFactory()
              //     .dashboard()
              //     .supplierDispatchOrder()
              //     .setId(order.id)
              //     .view()
              //     .build()
              // );
            }}
            icon={<img src={view} style={{ height: '100%', objectFit: 'contain' }} />}
          >
            View
          </Button>
        </Link>
        {hasPermission(userPermissionsList.updateSupplierOrder, user.permissions) ? (
          <Link
            to={new RouteFactory()
              .dashboard()
              .supplierDispatchOrder()
              .setId(order.id)
              .edit()
              .build()}
            target="_blank"
          >
            <Button
              onClick={() => openFormView()}
              style={{}}
              type="primary"
              icon={<img src={edit} style={{ height: '100%', objectFit: 'contain' }} />}
            >
              Edit
            </Button>
          </Link>
        ) : null}
      </div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Supplier Name
          </div>
          <div className={css.value}>{order?.supplier?.name}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical" size="medium">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            PO Number
          </div>
          <div className={css.value}>{order.purchaseOrderNumber}</div>
        </Space.Compact>
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Created
          </div>
          <div className={css.value}>{getDateFromTimeStamp(order.createdAt)}</div>
        </Space.Compact>
        {order.products.map((productDetail, index) => (
          <div
            key={index}
            style={{
              padding: '15px',
              borderRadius: '12px',
              border: '1px solid rgba(30, 30, 30, 0.20)',
            }}
          >
            <Space.Compact block direction="vertical">
              <div
                style={{ color: 'rgba(35, 86, 138, 0.70)', fontSize: '20px', marginBottom: '10px' }}
              >{`Product ${index + 1}`}</div>
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Product Name
              </div>
              <div className={css.value}>{productDetail?.product?.tradeName}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Quantity
              </div>
              <div className={css.value}>{productDetail.quantity}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Price
              </div>
              <div className={css.value}>{productDetail.price}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Incoterms
              </div>
              <div className={css.value}>{productDetail.incoterms?.type}</div>
            </Space.Compact>
            <Space.Compact block direction="vertical">
              <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
                Product Remarks
              </div>
              <div className={css.value}>
                {productDetail.remarks?.map((remark, i) => (
                  <div key={i}>{remark.value}</div>
                ))}
              </div>
            </Space.Compact>
          </div>
        ))}
        <Space.Compact block direction="vertical">
          <div className={css.label} style={{ color: 'rgba(35, 86, 138, 0.70)' }}>
            Order Remarks
          </div>
          <div className={css.value}>
            {order.remarks?.map((remark, i) => (
              <div key={i}>{remark.value}</div>
            ))}
          </div>
        </Space.Compact>
      </Space>
    </div>
  ) : null;
};

export default SupplierDispatchOrderSideBar;

SupplierDispatchOrderSideBar.proptypes = {
  order: PropTypes.object.isRequired,
  hide: PropTypes.func.isRequired,
  productName: PropTypes.string.isRequired,
};
