import React, { useEffect, useState } from 'react'
import HeaderPanel from '../headerPanel'
import { Button, Descriptions, Tag } from 'antd'
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerData } from '../../store/actions/sideDrawerData';
import RouteFactory from '../../service/RouteFactory';
import { getCustomerData } from '../../service/api/customerApi';
import PageLoader from '../Loaders/PageLoader';
import { customerSize, customerType } from '../../constants/formConstant';
import { getEnumValueFromList } from '../../util/formUtil';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getEmployeesNameFromId } from '../../util/userUtils';
import { getEmployeeList } from '../../service/api/employee';

const CustomerReadView = () => {

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { customerId } = useParams();

  const [loading, setLoading] = useState(false);
  const [customerData, setCustomerData] = useState({});
  const [employeeList, setEmployeeList] = useState([]);

  useEffect(() => {
    // if (formView === formModes.CREATE) setLoading(true);
    const fetchApiList = [getEmployeeList()];
    Promise.all(fetchApiList).then((responseList) => {
      if (responseList[0]?.data && responseList[0].data?.content) {
        setEmployeeList(responseList[0].data.content);
      }
      // setLoading(false);
    }).catch(error => {
      console.log(error);
      // if (formView === formModes.CREATE) setLoading(false);
    });
  }, []);

  const route = new RouteFactory();
  const fieldItems = [
    {
      key: '1',
      label: 'Name',
      children: customerData.name,
    },
    {
      key: '2',
      label: 'Sales Email',
      children: customerData?.emailInfo?.salesEmail,
    },
    {
      key: '15',
      label: 'Accounts Email',
      children: customerData?.emailInfo?.accountEmail,
    },
    {
      key: '16',
      label: 'Other Emails',
      children: customerData?.emailInfo?.optionalEmails?.join(', '),
    },
    {
      key: '3',
      label: 'Mobile',
      children: `${customerData.countryCode || '+ '}-${customerData.mobile}`,
    },
    {
      key: '4',
      label: 'Corporate Address',
      children: customerData?.address?.street,
    },
    {
      key: '5',
      label: 'City',
      children: customerData?.address?.city,
    },
    {
      key: '6',
      label: 'State',
      children: customerData?.address?.state,
    },
    {
      key: '7',
      label: 'Country',
      children: customerData?.address?.country,
    },
    {
      key: '8',
      label: 'Postal Code',
      children: customerData?.address?.postalCode,
    },
    {
      key: '9',
      label: 'Customer Size',
      children: getEnumValueFromList(customerData.size, customerSize),
    },
    {
      key: '10',
      label: 'Account Owner',
      children: customerData.accountOwner,
    },
    {
      key: '11',
      label: 'Categories',
      children: customerData?.categories?.length ? (
        <>{customerData.categories.map((cat) => <Tag key={cat}>{cat.toUpperCase()}</Tag>)}</>
      ) : '-',
    },
    {
      key: '12',
      label: 'Type',
      children: getEnumValueFromList(customerData.type, customerType),
    },
    {
      key: '13',
      label: 'Remarks',
      children: customerData?.remarks ? (
          <div>
            <div>{customerData?.remarks}</div>
          </div>
      ) : null,
    },
    {
      key: "13",
      label: "L1 Reviewers",
      children: getEmployeesNameFromId(customerData?.l1Reviewers,employeeList),
    },
    {
      key: "14",
      label: "L2 Reviewers",
      children: getEmployeesNameFromId(customerData?.l2Reviewers,employeeList)
    }
  ];

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerData(show));
  };

  const goToDashBoard = () => {
    collapseSideDrawerHandler(true);
    collapseAsideBarHandler(false);
    navigate(route.dashboard().customer().build());
  };

  useEffect(() => {
    if (customerId) {
      setLoading(true)
      getCustomerData(customerId).then((res) => {
        if (res?.data?.id) {
          setCustomerData(res.data);
          setLoading(false);
        }
      }).catch(error => {
      console.log(error);
      setLoading(false);
    });
    }
  }, [customerId])

  return (
    <>
      <HeaderPanel
        name="Customer Directory"
        sideButtons={
          <Button
            type="primary"
            size="large"
            onClick={goToDashBoard}
          >
            Cancel
          </Button>
        }
      />
      {loading ? <PageLoader/> : (
        <Descriptions
          style={{ margin: '5%'}}
          title={`Customer Details`}
          layout="vertical"
          bordered
          items={fieldItems}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      )}
    </>
  )
}

export default CustomerReadView