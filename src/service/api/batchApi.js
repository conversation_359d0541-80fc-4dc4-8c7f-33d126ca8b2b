import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig';

export const addProductBatchDetails = (batchData) => {
  return axiosInstance.post(apiEndpoints.batch, batchData);
};
export const updateBatchDetails = (batchData) => {
  return axiosInstance.put(apiEndpoints.batch, batchData);
};

export const getVirtualBatchDetail = (productId, packagingId, inventoryId, customerOrderDispatchId) => {
  return axiosInstance.get(`${apiEndpoints.virtualBatch}?productId=${productId}&packagingId=${packagingId}&inventoryId=${inventoryId}&customerOrderDispatchId=${customerOrderDispatchId}`);
};

export const getBatchDetails = (supplierOrderId)=>{
  return axiosInstance.get(`${apiEndpoints.batch}?supplierOrderId=${supplierOrderId}`); 
}

export const getUnallocatedBatchDetails = (customerDispatchOrderId)=>{
  return axiosInstance.get(`${apiEndpoints.unallocatedBatch}?customerDispatchOrderId=${customerDispatchOrderId}`); 
}

export const addAllocatedBatchDetails = (orderId, batchData) => {
  return axiosInstance.post(`${apiEndpoints.allocateBatch}?orderId=${orderId}`, batchData);
};

export const resetAllocatedBatchDetails = (orderId) => {
  return axiosInstance.post(`${apiEndpoints.resetAllocatedBatch}?orderId=${orderId}`, {});
};

export const fetchAllocatedBatchDetails = (orderId) => {
  return axiosInstance.get(`${apiEndpoints.allocatedBatch}?customerDispatchOrderId=${orderId}`);
}