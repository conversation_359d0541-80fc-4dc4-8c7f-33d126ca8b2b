import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import logo from '../../assets/logo/Mstack-logo-1.svg';
import { googleLogin, login } from '../../service/api/authService';
import { saveUser } from '../../store/actions/user';
import { loginToDispatchConversion, setAuthLocalStorage } from '../../util/auth';
import css from './LoginForm.module.css';
import { GoogleLogin } from '@react-oauth/google';
import { getUserFromEntity } from '../../util/userUtils';
import PageLoader from '../Loaders/PageLoader';
import { toast } from 'react-toastify';
import { usePostHog } from "posthog-js/react";

const LoginForm = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const posthog = usePostHog();

  const loginHandler = async ({ username, password }) => {
    setLoading(true);
    try {
      setLoading(true);
      const lcUsername = username.toLowerCase();
      const response = await login({ username: lcUsername, password });
      const apiData = response.data;
      handleLoginResponse(apiData);
    } catch (error) {
      console.log('Error while login', error);
    } finally {
      setLoading(false);
    }
  };

  const googleLoginHandler = async credentialResponse => {
    setLoading(true);
    try {
      const response = await googleLogin({ googleToken: credentialResponse.credential });
      const apiData = response.data;
      await handleLoginResponse(apiData);
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || 'Something went wrong. Please try again later.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleLoginResponse = async apiData => {
    if (apiData?.user.entityId && apiData?.user.entityType) {
      const entityRes = await getUserFromEntity(apiData?.user.entityId, apiData?.user.entityType);
      const dispatchData = loginToDispatchConversion(apiData, entityRes);
      dispatch(saveUser(dispatchData));
      if (posthog && apiData?.user) {
        posthog.capture('user_login', {
          timestamp: new Date().toISOString(),
          email: apiData.user.username,
        });
      }
    }
    setAuthLocalStorage(apiData);
  };

  return (
    <>
      {loading && <PageLoader />}
      <>
        <div className={css.logo}>
          <img src={logo} alt="Mstack" loading="lazy" />
        </div>
        <Form
          name="normal_login"
          className={css.loginForm}
          layout="vertical"
          initialValues={{
            remember: true,
          }}
          requiredMark={false}
          onFinish={loginHandler}
        >
          <Form.Item
            name="username"
            label="Username"
            rules={[
              {
                required: true,
                type: 'text',
                message: 'Please input your username!',
              },
            ]}
          >
            <Input prefix={<UserOutlined className="site-form-item-icon" />} type="text" />
          </Form.Item>
          <Form.Item
            name="password"
            label="Password"
            rules={[
              {
                required: true,
                message: 'Please input your Password!',
              },
            ]}
          >
            <Input
              prefix={<LockOutlined className="site-form-item-icon" />}
              type="password"
              placeholder="Password"
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" className={css.loginFormButton}>
              Log in
            </Button>
          </Form.Item>
          {/* OR separator */}
          <div className={css.orSeparator}>
            <span>or</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
            <GoogleLogin
              text="signin_with"
              theme="outline"
              shape="circle"
              onSuccess={credentialResponse => googleLoginHandler(credentialResponse)}
              onError={() => toast.error('Google login failed!')}
            />
          </div>
        </Form>
      </>
    </>
  );
};
export default LoginForm;
