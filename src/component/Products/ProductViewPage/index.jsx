import { Button, Descriptions, Tag } from 'antd';
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { setCollapseAsideBar } from '../../../store/actions/collapseAsideBar';
import { setSideDrawerData } from '../../../store/actions/sideDrawerData';
import RouteFactory from '../../../service/RouteFactory';
import HeaderPanel from '../../headerPanel';
import PageLoader from '../../Loaders/PageLoader';
import { getProductById } from '../../../service/api/productApi';
import { getAllSupplierByProduct } from '../../../service/api/supplierService';
import ProductSupplierCard from './ProductSupplierCard';
import css from './ProductViewPage.module.css';

const ProductView = () => {

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { productId } = useParams();

  const [loading, setLoading] = useState(false);
  const [productData, setProductData] = useState({});
  const [supplierData, setSupplierData] = useState([]);

  const fieldItems = [
    {
      key: '1',
      label: 'Name',
      children: productData?.tradeName,
    },
    {
      key: '2',
      label: 'Technical Name',
      children: productData?.technicalName,
    },
    {
      key: '3',
      label: 'CAS Number',
      children: productData?.casNumber,
    },
    {
      key: '4',
      label: 'Grade',
      children: productData?.grade,
    },
    {
      key: '5',
      label: 'Functions',
      children: (
        <>{productData?.functions
          ? productData.functions.map((data) => <Tag key={data}>{data}</Tag>)
          : '-'
        }</>
      ),
    },
    {
      key: '6',
      label: 'Family',
      children: (
        <>{productData?.family
          ? productData.family.map((data) => <Tag key={data}>{data}</Tag>)
          : '-'
        }</>
      ),
    },
    {
      key: '7',
      label: 'Synonyms',
      children: (
        <>{productData?.synonyms
          ? productData.synonyms.map((data) => <div key={data}>{data}</div>)
          : '-'
        }</>
      ),
    },
    {
      key: '8',
      label: 'features',
      children: (
        <>{productData?.features
          ? productData.features.map((data) => <div key={data}>{data}</div>)
          : '-'
        }</>
      ),
    },
  ];

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerData(show));
  };
  const goToDashBoard = () => {
    collapseSideDrawerHandler(true);
    collapseAsideBarHandler(false);
    navigate(RouteFactory.buildProduct());
  };
  const setMstackDocuments = (documents, supplierIndex) => {
    const newData = JSON.parse(JSON.stringify(supplierData));
    newData[supplierIndex].product.mstackDocuments = documents;
    setSupplierData(newData);
  }
  const handleDeleteMstackDocument = (supplierIndex, docIndex, fileindex) => {
    const newData = JSON.parse(JSON.stringify(supplierData));
    newData[supplierIndex].product.mstackDocuments[docIndex].files.splice(fileindex, 1)
    if (!newData[supplierIndex].product.mstackDocuments[docIndex].files.length) {
      newData[supplierIndex].product.mstackDocuments.splice(docIndex, 1);
    }
    setSupplierData(newData);
  }

  useEffect(() => {
    if (productId) {
      setLoading(true)
      const fetchApiList = [getProductById(productId), getAllSupplierByProduct(productId)];
      Promise.all(fetchApiList).then((res) => {
        if (res?.[0]?.data?.id) {
          setProductData(res[0].data);                  
        }
        if (res?.[1].data) {
          setSupplierData(res[1].data)
        }
        setLoading(false);
      }).catch(error => {
        console.log(error);
        setLoading(false);
      });
    }
  }, [productId])

  return (
    <>
      <HeaderPanel
        name="Product Directory"
        sideButtons={
          <Button
            type="primary"
            size="large"
            onClick={goToDashBoard}
          >
            Cancel
          </Button>
        }
      />
      {loading ? <PageLoader/> : (
        <div style={{ margin: '5%'}}>
          <Descriptions
            title={`Product Details`}
            layout="vertical"
            items={fieldItems}
            bordered
            labelStyle={{ fontWeight: '700', color: '#23568A' }}
          />
          {supplierData ? (
            <div style={{ marginTop: '24px' }}>
              <div className={css.title}>{`Available supplier(${supplierData.length})`}</div>
              {supplierData.map((supplier, index) => (
                <ProductSupplierCard
                  key={supplier.id}
                  productId={productId}
                  supplier={supplier}
                  supplierIndex={index}
                  setMstackDocuments={(documents) => setMstackDocuments(documents, index)}
                  handleDeleteMstackDocument={(docIndex, fileindex) => handleDeleteMstackDocument(index, docIndex, fileindex)}
                />
              ))}
            </div>
          ) : null}
        </div>
      )}
    </>
  )
}

export default ProductView