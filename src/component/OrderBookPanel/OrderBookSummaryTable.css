.orderbook-summary-table-container {
  margin: 24px 0 16px 0;
  overflow-x: auto;
}
.orderbook-summary-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(35, 86, 138, 0.06);
  border-radius: 8px;
  overflow: hidden;
}
.orderbook-summary-table th, .orderbook-summary-table td {
  border: 1px solid #23568A;
  padding: 8px 12px;
  text-align: center;
}
.orderbook-summary-table th {
  background: #eaf1fa;
  color: #23568A;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.5px;
}
.orderbook-summary-table td {
  color: #222;
  background: #f8faff;
}
.orderbook-summary-table tbody tr:hover td {
  background: #eaf1fa;
}
.orderbook-summary-table td button, .orderbook-summary-table td .ant-btn {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background: #23568A;
  color: #fff;
  border: none;
  transition: background 0.2s;
}
.orderbook-summary-table td button:hover, .orderbook-summary-table td .ant-btn:hover {
  background: #17406a;
  color: #fff;
}
.orderbook-summary-table thead tr:first-child th:first-child {
  border-top-left-radius: 8px;
}
.orderbook-summary-table thead tr:first-child th:last-child {
  border-top-right-radius: 8px;
}
.orderbook-summary-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: 8px;
}
.orderbook-summary-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 8px;
} 