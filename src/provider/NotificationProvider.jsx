import React, { createContext, useContext, } from 'react';
import PropTypes from 'prop-types';
import { notification } from 'antd';

const NotificationContext = createContext();

export const useNotificationContext = () => {
  return useContext(NotificationContext);
}

export const NotificationContextProvider = ({ children }) => {

  const [api, contextHolder] = notification.useNotification();
  return (
    <NotificationContext.Provider value={{ api }}>
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
}

NotificationContextProvider.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
]).isRequired,
}

