import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Checkbox, Col, Form, Input, Row } from 'antd';
import { formModes } from '../../constants/formConstant';
import { createUserRole, getAllPermissionConfig, updateUserRole } from '../../service/api/UserRolesApi';
import PageLoader from '../Loaders/PageLoader';
import { enumKeyToTitle } from '../../util/stringUtils';

const RoleForm = (props) => {

  const { mode, userRole, hide } = props;

  const [loading, setLoading] = useState(false);
  const [permisionConfig, setPersmissionConfig] = useState([]);

  const [form] = Form.useForm();

  const saveUserRoleDetails = (userRoleData) => {
    setLoading(true);
    if (mode === formModes.CREATE) {
      createUserRole(userRoleData).then(() => {
        setLoading(false);
        hide();
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
    } else if (mode === formModes.UPDATE) {
      updateUserRole(userRoleData, userRole.id).then(() => {
        setLoading(false);
        hide();
      })
      .catch(error => {
        console.log(error);
        setLoading(false);
      });
    }
  }
  const validateFormandSubmit = () => {
    form.validateFields().then((values) => {
      saveUserRoleDetails(values);
    })
    .catch(error => console.log(error));
  }
  const getPermissionCheckbox = () => {
    const permissionGroup = Object.keys(permisionConfig);
    const permissionList = permissionGroup?.map((group) => (
      <div key={group} className='w-full'>
        <div className='mb-2 text-lg font-medium capitalize'>{enumKeyToTitle(group)}</div>
        <Row gutter={8}>
          {permisionConfig?.[group]?.map((permission) => (
            <Col className="mb-1.5 break-all" span={12} key={permission}>
              <Checkbox value={permission}>
                {permission}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </div>
    ))
    return permissionList;
  }

  useEffect(() => {
    if (!permisionConfig?.length) {
      setLoading(true);
      getAllPermissionConfig().then((res) => {
        if (res?.data?.value?.[0]) {
          setPersmissionConfig(res?.data?.value?.[0]);
          setLoading(false);
        }
      }).catch((err) => {
        console.log(err);
        setLoading(false);
      })
    }
  }, [])

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100%' }} /> : null}
      <Form
        name="customer_form"
        layout="vertical"
        scrollToFirstError
        initialValues={mode === formModes.CREATE ? {} : userRole}
        preserve={false}
        size="middle"
        form={form}
        validateTrigger="onBlur"
      >
        <div className='mb-1 font-semibold'>Role Details</div>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Role Name" name="name" rules={[{ required: true }]}>
              <Input style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item name="description" label="Description">
              <Input.TextArea />
            </Form.Item>
          </Col>
        </Row>
        <div className='mb-1 font-semibold'>Permission Details</div>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Select Permission(s)" name="permissions" rules={[{ required: true }]}>
              <Checkbox.Group>
                {getPermissionCheckbox()}
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div className='absolute left-0 bottom-0 w-full p-2 flex justify-between bg-base-white z-10 box-border'>
        <Button
          type="primary"
          size="large"
          onClick={() => validateFormandSubmit()}
          loading={loading}
        >
          {mode === formModes.CREATE ? 'Add Role' : 'Save Changes'}
        </Button>
      </div>
    </>
  )
}

export default RoleForm;

RoleForm.propTypes = {
  mode: PropTypes.string.isRequired,
  userRole: PropTypes.object,
  hide: PropTypes.func.isRequired,
}