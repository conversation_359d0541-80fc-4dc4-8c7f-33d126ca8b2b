import { usePostHog } from 'posthog-js/react';
import { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import EnvKeys from '../../constants/EnviornmentKeys';
import { handlePageName } from './utils';

const INACTIVITY_LIMIT = 5 * 60 * 1000; // 5 minutes in ms

const getValidActiveDuration = ({ isTabVisible, lastActivityTime, lastVisibilityChange }) => {
  const now = Date.now();
  const timeSinceLastActivity = now - lastActivityTime.current;

  if (isTabVisible.current && timeSinceLastActivity < INACTIVITY_LIMIT) {
    const activeDuration = now - lastVisibilityChange.current;
    lastVisibilityChange.current = now;
    lastActivityTime.current = now;
    return activeDuration;
  }

  console.log('[PostHog] Skipping duration due to inactivity or hidden tab');
  return 0;
};

const PostHogTrackerOnPageView = () => {
  const location = useLocation();
  const posthog = usePostHog();
  const user = useSelector(state => state.user);

  const pageStartTime = useRef(Date.now());
  const lastPath = useRef(window.location.href);
  const dailyActiveTime = useRef(0); // Total active time across app
  const pageActiveTime = useRef(0); // Total active time per page
  const lastVisibilityChange = useRef(Date.now());
  const lastActivityTime = useRef(Date.now());
  const isTabVisible = useRef(true);
  const hourlyTimer = useRef(null);

  // Register PostHog super properties
  useEffect(() => {
    if (posthog) {
      posthog.register({
        CLIENT: 'LOGISTACK',
        ENV: EnvKeys.env,
      });
    }
  }, [posthog]);

  // Track time on page navigation with full accumulation
  useEffect(() => {
    // Capture latest visible time before navigating away
    const latestActive = getValidActiveDuration({
      isTabVisible,
      lastActivityTime,
      lastVisibilityChange,
    });

    pageActiveTime.current += latestActive;

    const durationInMin = Math.round((pageActiveTime.current / 60000) * 100) / 100;

    console.log(pageActiveTime.current, 'pageActiveTime.current', durationInMin, 'durationInMin');

    if (posthog && lastPath.current !== window.location.href) {
      if (durationInMin > 0) {
        posthog.capture('page_time_spent', {
          path: lastPath.current,
          minutes: durationInMin,
          screen_name: handlePageName(lastPath.current).toUpperCase(),
        });
      }

      posthog.capture('page_visited', {
        path: window.location.href,
        screen_name: handlePageName(window.location.href).toUpperCase(),
      });

      lastPath.current = window.location.href;
      pageStartTime.current = Date.now();
      pageActiveTime.current = 0; // Reset for new page
    }
  }, [location, posthog]);

  // Identify user
  useEffect(() => {
    if (user?.id && posthog) {
      posthog.identify(user.id, {
        email: user.username,
      });
    }
  }, [user, posthog]);

  // Track user activity
  useEffect(() => {
    const updateActivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityTime.current;

      // If user was inactive for a while, reset visibility window
      if (timeSinceLastActivity > INACTIVITY_LIMIT) {
        lastVisibilityChange.current = now;
      }

      lastActivityTime.current = now;
    };

    const events = ['mousemove', 'keydown', 'scroll', 'click'];
    events.forEach(event => window.addEventListener(event, updateActivity));

    return () => {
      events.forEach(event => window.removeEventListener(event, updateActivity));
    };
  }, []);

  // Track visibility changes (accumulate time for both daily and page)
  useEffect(() => {
    const handleVisibilityChange = () => {
      const now = Date.now();

      if (document.visibilityState === 'hidden') {
        if (isTabVisible.current) {
          const timeSinceLastActivity = now - lastActivityTime.current;
          if (timeSinceLastActivity < INACTIVITY_LIMIT) {
            const delta = now - lastVisibilityChange.current;
            dailyActiveTime.current += delta;
            pageActiveTime.current += delta;
          }
        }
        isTabVisible.current = false;
      } else {
        lastVisibilityChange.current = now;
        lastActivityTime.current = now;
        isTabVisible.current = true;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Daily engagement reporting every 15 minutes
  useEffect(() => {
    const reportActiveTime = () => {
      const latestActive = getValidActiveDuration({
        isTabVisible,
        lastActivityTime,
        lastVisibilityChange,
      });

      dailyActiveTime.current += latestActive;
      pageActiveTime.current += latestActive;

      const minutesSpent = Math.round((dailyActiveTime.current / 60000) * 100) / 100;
      const pageMinutes = Math.round((pageActiveTime.current / 60000) * 100) / 100;

      if (posthog && user?.id && minutesSpent > 0) {
        posthog.capture('daily_time_spent', {
          user_id: user.id,
          email: user.username,
          date: new Date().toISOString().split('T')[0],
          minutes: minutesSpent,
        });
      }

      if (posthog && pageMinutes > 0) {
        posthog.capture('page_time_spent', {
          path: window.location.href,
          minutes: pageMinutes,
          screen_name: handlePageName(window.location.href).toUpperCase(),
        });
      }

      // Reset
      dailyActiveTime.current = 0;
      pageActiveTime.current = 0;
    };

    hourlyTimer.current = setInterval(reportActiveTime, 15 * 60 * 1000); // every 15 minutes

    window.addEventListener('beforeunload', reportActiveTime);

    return () => {
      clearInterval(hourlyTimer.current);
      window.removeEventListener('beforeunload', reportActiveTime);
    };
  }, [posthog, user]);


  return null;
};

export default PostHogTrackerOnPageView;
