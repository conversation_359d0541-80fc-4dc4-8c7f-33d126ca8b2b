import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Card, Col, Form, InputNumber, Row, Select } from 'antd';
import { formModes, productUOMList } from '../../constants/formConstant';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import MultipleTextFieldInput from '../MultipleTextFieldInput/MultipleTextFieldInput';
import dayjs from 'dayjs';
import PackagingSelect from '../PackagingSelectComponent/PackagingSelect';
import { getDispatchOrderForOrderbook } from '../../service/api/dispatchOrderApi';
import ObjectUtil from '../../util/objectUtil';
import { validateSupplierProductOrderQuantity } from '../../service/api/supplierDispatchOrderApi';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { CloseCircleFilled } from '@ant-design/icons';

const SupplierDispatchOrderProduct = props => {
  const {
    form,
    field,
    index,
    formView,
    remove,
    productList,
    initialValue,
    setInitialValue,
    packagingList,
    customerorderbookId,
    setLoading,
  } = props;

  const [customerDispatchOrders, setCustomerDispatchOrders] = useState([]);
  const { api } = useNotificationContext();

  const formIdWatch = Form.useWatch(['products', field.name, 'product'], form);
  const quantityWatch = Form.useWatch(['products', field.name, 'quantity'], form);


  useEffect(() => {
    if (
      formView !== formModes.READ &&
      formIdWatch &&
      (!initialValue?.products?.length ||
        !initialValue?.products[index] ||
        initialValue?.products[index]?.id !== formIdWatch)
    ) {
      let newInitialValue = JSON.parse(JSON.stringify(initialValue));
      if (!initialValue?.products?.length) initialValue.products = [];
      let newValue = {};
      const [productInfo] = productList.filter(prod => prod.id === formIdWatch);
      if (productInfo && productInfo.uom) {
        newValue = {
          ...productInfo,
          uom: productInfo.uom,
          quantity: productInfo.quantity,
          units: productInfo.units,
          product: productInfo.id,
          packaging: productInfo.packaging,
          price: productInfo.price,
          hsCode: productInfo.hsCode,
          linkedOrderIds: productInfo.linkedOrderIds,
          label: productInfo.label,
        };
        if (!newInitialValue?.products?.length) newInitialValue.products = [];
        newInitialValue.products[index] = newValue;
        newInitialValue = {
          ...newInitialValue,
          incoterms: productInfo.incoterms,
          purchaseOrderDate: newInitialValue.purchaseOrderDate
            ? dayjs(newInitialValue.purchaseOrderDate)
            : '',
          shipmentDate: productInfo.shipmentDate ? dayjs(productInfo.shipmentDate) : '',
          deliveryDate: productInfo.deliveryDate ? dayjs(productInfo.deliveryDate) : '',
          products: [
            ...newInitialValue.products.map((item, idx) => {
              const newProductFormField = form.getFieldValue(['products', idx]);
              return ObjectUtil.mergeWithoutNull(item, newProductFormField);
            }),
          ],
        };
      }
      setInitialValue(newInitialValue);
    }
  }, [formIdWatch]);

  useEffect(() => {
    setLoading(true);
    getDispatchOrderForOrderbook(customerorderbookId).then(response => {
      // TODO: proper check for dispatchOrderList
      if(!response?.data?.length) {
        api.error({
          messgae: 'Error',
          description: 'Can not create supplier dispatch order as no customer dispatch order exist for linking!!. Please add customer dispatch order first.',
          icon: (
            <CloseCircleFilled
              style={{
                color: '#dc2626',
              }}
            />
          )
        })
      }
      setCustomerDispatchOrders(response.data);
      setInitialValue(prevState => { return {
        ...prevState,
        cdoList: response.data,
      }})
      setLoading(false);
    })
    .catch(error => {
      setLoading(false);
      const errMsg = error?.response?.data?.message;
      api.error({
        messgae: 'Error',
        description: errMsg,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        )
      })
    });
  }, []);

  const validateProductQuantity = async (poNumber, productId, productQuantity) => {
    const response = await validateSupplierProductOrderQuantity(
      poNumber,
      productId,
      productQuantity,
      initialValue?.id,
      initialValue?.customer?.id,
    );
    const isValid = response.data.isValid;
    const availableQuantity = response.data.availableQuantity;
    if (availableQuantity == 0) return Promise.reject('No more quantity left for usage');
    return isValid
      ? Promise.resolve()
      : Promise.reject(`Quantity cannot exceed more then ${availableQuantity}`);
  };

  const getProductRelatedCustomerDispatchOrders = (customerDispatchOrdersList, productId) => {
    const productIdToCheck = productList.find(product => product.id === productId)?.product?.id;
    return customerDispatchOrdersList.filter(order => {
      const matchingProduct = order.products.find(
        product => product.product.id === productIdToCheck
      );
      if (matchingProduct) {
        order.productQty = matchingProduct.quantity;
        return true;
      }
      return false;
    });
  };

  const calculateTotalQuantity = linkedOrders => {
    return linkedOrders.reduce((totalQuantity, linkedOrder) => {
      if (linkedOrder && linkedOrder.quantity != null) {
        return totalQuantity + linkedOrder.quantity;
      }
      return totalQuantity;
    }, 0);
  };

  const hasDuplicateLinkedOrders = array => {
    const orderIdSet = new Set();
    let hasDuplicates = false;

    array.forEach(obj => {
      const orderId = obj && obj.orderId;

      if (orderId !== undefined) {
        if (orderIdSet.has(orderId)) {
          // Duplicate found
          hasDuplicates = true;
        } else {
          orderIdSet.add(orderId);
        }
      }
    });

    return hasDuplicates;
  };

  return (
    <Card
      size="small"
      title={`Product ${index + 1}`}
      key={field.key}
      extra={<CloseOutlined
        onClick={() => {
          remove(field.name);
        }}
      />}
    >
      <Row gutter={16}>
        <Col span={24}>
          {productList.length ? (
            <Form.Item name={[field.name, 'product']} label="Product Name">
              <Select showSearch optionFilterProp="label" disabled={formView === formModes.UPDATE}>
                {productList.map(product => (
                  <Select.Option
                    key={product.id}
                    value={product.id}
                    label={product.product.tradeName}
                  >
                    {product.product.tradeName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          ) : null}
        </Col>
      </Row>
      {formIdWatch ? (
        <>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name={[field.name, 'uom']}
                rules={[
                  {
                    required: true,
                    message: 'Please input unit of measurement!',
                  },
                ]}
                label="Unit of measurement(UOM)"
              >
                <Select disabled={formView === formModes.UPDATE}>
                  {productUOMList.map(uom => (
                    <Select.Option key={uom.key} value={uom.value}>
                      {uom.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Price per unit"
                rules={[
                  {
                    required: true,
                    message: 'Please input price!',
                    validator(_, value) {
                      if (value === undefined || value === null) return Promise.resolve();
                      if (value < 0) {
                        return Promise.reject(new Error('Price cannot be less then zero'));
                      }
                      return Promise.resolve();
                    }
                  },
                ]}
                name={[field.name, 'price']}
              >
                <InputNumber />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Quantity"
                name={[field.name, 'quantity']}
                validateTrigger="onBlur"
                // rules={[
                //   {
                //     required: true,
                //     message: 'Please input quantity!',
                //   },
                //   () => ({
                //     async validator(_, value) {
                //       const [prod] = initialValue.products.filter(
                //         item => item.product === formIdWatch
                //       );
                //       const poNumber = initialValue.purchaseOrderNumber;

                //       if (prod && prod.quantity) {
                //         if (value) {
                //           if (value <= 0)
                //             return Promise.reject(
                //               new Error('quantity can never be less then equal to zero')
                //             );
                //           // return Promise.resolve('');
                //           return await validateProductQuantity(poNumber, formIdWatch, value);
                //         }
                //       }
                //       return Promise.reject(new Error('Unable to validate!'));
                //     },
                //   }),
                // ]}
              >
                <InputNumber style={{ width: '100%' }} disabled={formView === formModes.UPDATE}/>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Units"
                name={[field.name, 'units']}
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: 'Please input units!',
                  },
                  () => ({
                    async validator(_, value) {
                      if (value) {
                        if (value <= 0)
                          return Promise.reject(
                            new Error('quantity can never be less then equal to zero')
                          );
                        return Promise.resolve('');
                      }
                      return Promise.reject(new Error('Unable to validate!'));
                    },
                  }),
                ]}
              >
                <InputNumber style={{ width: '100%' }} disabled={formView === formModes.UPDATE}/>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Quantity per packaging type"
                name={[field.name, 'quantityPerUnit']}
                validateTrigger="onBlur"
                rules={[
                  {
                    required: true,
                    message: 'Please input quantity per unit!',
                    type: 'number',
                  },
                ]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              {/* {customerDispatchOrders.length ? ( */}
                <Form.List
                  name={[field.name, 'linkedOrders']}
                  rules={[
                    {
                      required: true,
                      message: 'Please add linked dispatch orders!',
                    },
                    () => ({
                      validator(_, value) {
                        if (!value) return Promise.resolve();
                        if (hasDuplicateLinkedOrders(value))
                          return Promise.reject(
                            'linked dispatch orders cannot have duplicate order id '
                          );
                        const totalLinkedOrderQty = calculateTotalQuantity(value);
                        if (totalLinkedOrderQty !== quantityWatch) {
                          return Promise.reject(
                            'Total qty of linked dispatch orders qty must always be equal to supplier dispatch order qty!'
                          );
                        } else return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  {(fields, { add, remove }, { errors }) => {
                    return (
                      <>
                        {fields.map(({ key, name }) => (
                          <Card
                            size="small"
                            style={{ margin: '20px' }}
                            key={key}
                            title={`Linked Order ${name + 1}`}
                            extra={<CloseOutlined onClick={() => remove(name)} />}
                          >
                            <Row gutter={16}>
                              <Col span={24}>
                                <Form.Item
                                  name={[name, 'orderId']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please select linked Orders!',
                                    },
                                  ]}
                                  label="Linked Order"
                                >
                                  <Select showSearch optionFilterProp="label">
                                    {getProductRelatedCustomerDispatchOrders(
                                      customerDispatchOrders,
                                      formIdWatch
                                    ).map(order => (
                                      <Select.Option
                                        key={order.id}
                                        value={order.id}
                                        label={order.orderId}
                                      >
                                        {order.orderId +" - PO: "+order.purchaseOrderNumber}
                                      </Select.Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                              </Col>
                              <Col span={24}>
                                <Form.Item
                                  name={[name, 'quantity']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please enter quantity!',
                                    },
                                  ]}
                                  label="Quantity"
                                >
                                  <InputNumber />
                                </Form.Item>
                              </Col>
                            </Row>
                          </Card>
                        ))}
                        <Form.Item>
                          <Button
                            block
                            style={{ marginTop: '20px' }}
                            type="dashed"
                            onClick={() => add()}
                            icon={<PlusOutlined />}
                          >
                            Link Disptach Orders
                          </Button>
                        </Form.Item>
                        <div style={{ color: 'red' }}>
                          <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                        </div>
                      </>
                    );
                  }}
                </Form.List>
              {/* ) : null} */}
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <PackagingSelect
                packagingList={packagingList}
                field={field}
                disableCustomPackaging
                disabled={formView === formModes.UPDATE}
              />
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name={[field.name, 'remarks']} label="Remarks">
                <MultipleTextFieldInput
                  type="TextArea"
                  mode={formView}
                  placeholder="Enter a remark"
                  savetimeStamp
                  showDelete={formView === formModes.CREATE}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ) : null}
    </Card>
  );
};

export default SupplierDispatchOrderProduct;

SupplierDispatchOrderProduct.propTypes = {
  form: PropTypes.object.isRequired,
  field: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  formView: PropTypes.string.isRequired,
  remove: PropTypes.func.isRequired,
  productList: PropTypes.array.isRequired,
  initialValue: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func,
  packagingList: PropTypes.array.isRequired,
  customerorderbookId: PropTypes.string.isRequired,
  setLoading: PropTypes.func.isRequired,
};
