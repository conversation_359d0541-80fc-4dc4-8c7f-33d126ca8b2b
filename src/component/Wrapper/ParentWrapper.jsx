import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { REFRESH_TOKEN_KEY_NAME } from '../../constants/localStorageConstants';
import RouteFactory from '../../service/RouteFactory';
import { refreshToken } from '../../service/api/authService';
import { saveUser } from '../../store/actions/user';
import { isUserLoggedIn, loginToDispatchConversion, setAuthLocalStorage } from '../../util/auth';
import { isCurrentRouteLoginScreen, isCurrentRouteProtected } from '../../util/route';
import { getUserFromEntity } from '../../util/userUtils';
import PageLoader from '../Loaders/PageLoader';

const ParentWrapper = ({ children }) => {
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);

  const [loading, isLoading] = useState(true);

  useEffect(() => {
    let refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY_NAME);
    if (!refreshToken) {
      if (!isCurrentRouteLoginScreen()) {
        window.location.pathname = new RouteFactory().login().build();
      }
      return;
    }
    if (!isUserLoggedIn(user)) {
      refreshAccessToken(refreshToken);
      return;
    }
    isLoading(false);
  }, []);

  const refreshAccessToken = async token => {
    try {
      const response = await refreshToken(token);
      const apiData = response.data;
      if (apiData?.user.entityId && apiData?.user.entityType) {
        const entityRes = await getUserFromEntity(apiData?.user.entityId, apiData?.user.entityType)
        const dispatchData = loginToDispatchConversion(apiData, entityRes.data)
        dispatch(saveUser(dispatchData))
      }
      setAuthLocalStorage(apiData);
      isLoading(false);
    } catch (error) {
      isLoading(false);
      console.log('Error while refreshing token', error);
    }
  };

  if (loading && isCurrentRouteProtected()) {
    return <PageLoader />;
  }

  return <>{children}</>;
};

export default ParentWrapper;

ParentWrapper.propTypes = {
  children: PropTypes.node.isRequired,
};

//TODO: add comments for functions and workflow