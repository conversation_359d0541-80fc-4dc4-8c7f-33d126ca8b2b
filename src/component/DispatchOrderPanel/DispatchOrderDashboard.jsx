import React from 'react';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import DispatchOrderListView from './DispatchOrderListView';

const DispatchOrderDashboard = () => {
  const dispatch = useDispatch();

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };

  return (
    <DispatchOrderListView
      collapseAsideBarHandler={collapseAsideBarHandler}
      collapseSideDrawerHandler={collapseSideDrawerHandler}
    />
  );
};

export default DispatchOrderDashboard;
