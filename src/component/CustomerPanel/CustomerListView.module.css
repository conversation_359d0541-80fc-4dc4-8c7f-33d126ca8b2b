.functionalBar {
  padding: 32px 32px 24px;
  display: flex;
  width: 100%;
}

.seacrhBar {
  width: -webkit-fill-available;
}

.searchInput {
  background: rgba(30, 30, 30, 0.05);
  border-radius: 12px;
}
.searchInput span,
.searchInput span input {
  background: transparent;
}

.btnFlexStyle {
  margin-left: 12px;
  display: flex;
  align-items: center;
  padding: 12px;
}

.tableContainer {
  margin: 0 32px 32px;
}

.tableContainer table {
  border-radius: 12px;
  border: 1px solid rgba(35, 86, 138, 0.20);
}

:root .tableContainer table > thead > tr > th {
  background: #f4f6f9;
}

/* TODO:added for ordebook header need to format css properly */

.header {
  padding: 4px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(35, 86, 138, 0.2);
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #fff;
  z-index: 99;
}

.foldMenuBtn {
  margin-right: 24px;
  padding: 0px;
  background-color: rgba(30, 30, 30, 0.05);
  border-radius: 8px;
}

.title {
  font-size: 20px;
  font-weight: 500;
  line-height: 100%;
}

.formBtnContainer {
  margin: 5px 0 5px auto;
}