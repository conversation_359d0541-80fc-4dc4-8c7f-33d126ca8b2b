import apiEndpoints from '../../constants/apiConstant';
import axiosInstance from './axiosConfig'; // Import the Axios instance

//TODO: implement a fucntionality to setup response properly.
export const getSupplierDispatchOrderList = (pageSize,pageNumber, filters, searchkey) => {
  const customerConfig = {
    size: pageSize || 100,
    number: pageNumber || 0,
    filters: filters || {},
    search: {
      purchaseOrderNumber: searchkey || '',
    },
  };
  return axiosInstance.post(apiEndpoints.supplierDispatchOrderSearch, customerConfig);
};

export const createSupplierDispatchOrder = orderData => {
  return axiosInstance.post(apiEndpoints.supplierDispatchOrderCrud, orderData);
};

export const updateSupplierDispatchOrder = (orderData, id) => {
  return axiosInstance.put(`${apiEndpoints.supplierDispatchOrderCrud}/${id}`, orderData);
};

export const getSupplierDispatchOrderFromId = (dispatchId) => {
  return axiosInstance.get(`${apiEndpoints.supplierDispatchOrderCrud}/${dispatchId}`);
}
export const deleteSupplierDispatchOrderFromId = (dispatchId) => {
  return axiosInstance.delete(`${apiEndpoints.supplierDispatchOrderCrud}/${dispatchId}`);
}

export const validateSupplierProductOrderQuantity = (poNumber,productId,productQuantity,dispatchId,customerId) => {
  const body = {
    poNumber, productId, productQuantity, dispatchId, customerId
  };
  return axiosInstance.post(`${apiEndpoints.supplierDispatchOrderValidateProductQuantity}`, body);
};