import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import css from '../CustomerPanel/CustomerListView.module.css';
import {  Button, Input, Table } from 'antd';
import {
  invoiceTablePageSize,
  uploadInvoiceHeaders,
} from '../../constants/TableConstants';
import HeaderPanel from '../headerPanel';
import {  useLocation, useNavigate } from 'react-router-dom';
import { getQeryParamsFromURL, updateParamsAndNavigate } from '../../util/route';
import { getTasks } from '../../service/api/taskService';
import { EyeOutlined, UploadOutlined,  } from '@ant-design/icons';
import { hasPermission } from '../../util/userUtils';
import { userPermissionsList } from '../../constants/UserTabsConstants';
import useClickTracker from '../PostHog/useClickTracker';

const UploadedInvoiceListView = props => {

  const [invoiceListReponse, setInvoiceListResponse] = useState({});
  const [loading, setLoading] = useState(false);
  const user = useSelector(state => state.user);
  const navigate = useNavigate();
  const location = useLocation();
  const { trackClick } = useClickTracker();

  const getInvoiceListFromConfig = (pageSize, pageNumber, filters = {}, searchkey = '') => {
    setLoading(true);

    const payload = {
      size: pageSize,
      number: pageNumber,
      filters,
      search: {
        name: 'Invoice Upload',
      },
    };

    getTasks(payload)
      .then(response => {
        setInvoiceListResponse(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching tasks:', error);
      });
  };


  const handleViewClick2= (record)=>{
    navigate('/invoice', {
      state: {
        data:{ poId :record?.secondaryId , orderId: record?.orderId},
        from: 'uploaded-invoice-list',
      },
    });
  }

  // Update the handleUploadClick function
  const handleUploadClick = record => {
    // Navigate to invoice upload page with the record data
    navigate('/invoice', {
      state: {
        taskData: record,
        from: 'uploaded-invoice-list',
      },
    });
     trackClick('button_clicked', { uploaded_via : "Direct Upload", name : "Invoice Upload" });
  };

  const getUploadInvoiceColumns = (onViewClick) => [
    ...uploadInvoiceHeaders, // existing columns
    {
      title: 'Actions',
      key: 'actions',
      align: 'center',
      width: 120,
      render: (text, record) => {
        console.log('this is record', record);

        return (
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            disabled={!hasPermission(userPermissionsList.viewInvoiceTab,user.permissions)}
            onClick={e => {
              e.stopPropagation(); // Prevent row click
              onViewClick(record);
            }}
          >
            View
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    //api call to get order list TODO: format api params
    const pageNumber = 0;
    const { searchkey } = getQeryParamsFromURL();
    console.log(searchkey);
    let filters = searchkey
      ? searchkey.startsWith('COB')
        ? {
            orderId: [searchkey],
          }
        : {
            secondaryId: [searchkey],
          }
      : {};
    // if ((filters && Object.keys(filters).length != 0) || searchkey) setShowClear(true);
    getInvoiceListFromConfig(10, pageNumber, filters, searchkey);
  }, [location.pathname, location.search]);

  return (
    <>
      <HeaderPanel name="Invoice" />
      <div className={css.functionalBar}>
        <div className={css.seacrhBar}>
          <Input.Search
            placeholder="Search ..."
            className={css.searchInput}
            loading={false}
            size="large"
            allowClear
            onSearch={
              value => {
                updateParamsAndNavigate(navigate, location, { searchkey: value });
              }
              // getDispatchOrderFromSearch
            }
            bordered={false}
            enterButton
          />
        </div>
         {hasPermission(userPermissionsList.uploadInvoice, user.permissions) && <Button
          size="large"
          className={css.btnFlexStyle}
          onClick={() => handleUploadClick()}
          icon={<UploadOutlined />}
        >
          <span>Upload Invoice</span>
        </Button>}
      </div>
      <Table
        columns={getUploadInvoiceColumns(handleViewClick2)} // Use the function with handlers
        dataSource={invoiceListReponse?.content?.map((invoice, index) => {
          return {
            ...invoice,
            key: index,
          };
        })}
        className={css.tableContainer}
        pagination={{
          showSizeChanger: false,
          current: invoiceListReponse?.pageable?.pageNumber + 1,
          pageSize: invoiceTablePageSize,
          total: invoiceListReponse?.totalElements,
          onChange: page => {
            const { filters, searchkey } = getQeryParamsFromURL();
            getInvoiceListFromConfig(invoiceTablePageSize, page - 1, filters, searchkey);
          },
        }}
        loading={loading}
        scroll={{
          x: '100%',
          y: 600,
        }}
       
      />
    </>
  );
};

export default UploadedInvoiceListView;

UploadedInvoiceListView.propTypes = {
  collapseAsideBarHandler: PropTypes.func.isRequired,
  collapseSideDrawerHandler: PropTypes.func.isRequired,
};
