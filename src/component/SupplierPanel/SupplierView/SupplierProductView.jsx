import { Descriptions } from 'antd';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import FileUpload from '../../FileUpload';
import SupplierPackagingListView from './SupplierPackagingListView';
import {
  formConfigName,
} from '../../../constants/formConstant';
import { getFormConfigFromName } from '../../../service/api/formConfig';
import { getEnumValueFromList } from '../../../util/formUtil';

const SupplierProductView = props => {
  const { productData } = props;

  const [productCertificatesType, setProductCertificatesType] = useState([]);
  const [productDocumentsType, setProductDocumentsType] = useState([]);

  const getAddrFromSupplierData = address => {
    const street = address.street;
    const country = address.country;
    const state = address.state;
    const city = address.city;
    let addrStr = street ? street : '';
    addrStr += city ? ' , ' + city : '';
    addrStr += state ? ' , ' + state : '';
    addrStr += country ? ' , ' + country : '';
    return addrStr;
  };
  const getDocumentItems = (documents, labelList) => {
    return documents.map((document, index) => {
      console.log('current document ', document);
      return {
        key: index,
        label: getEnumValueFromList(document.documentType, labelList),
        children: <FileUpload deleteDisabled value={document.files} disabled />,
      };
    });
  };

  const items = [
    {
      key: '1',
      label: 'Plant Address',
      children: getAddrFromSupplierData(productData.address),
    },
    {
      key: '2',
      label: 'Capacity availability (in metric tonnes)',
      children: productData.capacityAvailable,
    },
    {
      key: '3',
      label: 'Total capacity (in metric tonnes)',
      children: productData.totalCapacity,
    },
    {
      key: '4',
      label: 'Lead Time',
      children: productData.leadTime,
    },
    {
      key: '5',
      label: 'Typical Order Size',
      children: productData.typicalOrderSize,
    },
    {
      key: '6',
      label: 'HS Code',
      children: productData.hsCode,
    },
    {
      key: '7',
      label: 'Start Date ',
      children: productData.paymentTerms?.startDate,
    },
    {
      key: '8',
      label: 'Hazardous ',
      children: productData.hazardous ? 'Yes' : 'No',
    },
    {
      key: '9',
      label: 'Hazardous Level',
      children: productData.hazardousLevel?.length
        ? productData.hazardousLevel.join('/')
        : 'NA',
    },
    {
      key: '10',
      label: 'Packaging',
      span: 12,
      children: <SupplierPackagingListView packageList={productData.packaging} />,
    },
    {
      key: '11',
      label: 'Certificate Documents',
      span: 12,
      children: productData.certificateDocuments ? (
        <Descriptions
          colon={false}
          layout="vertical"
          items={getDocumentItems(productData.certificateDocuments, productCertificatesType)}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      ) : null,
    },
    {
      key: '12',
      label: 'Other Documents',
      span: 12,
      children: productData.documents ? (
        <Descriptions
          colon={false}
          layout="vertical"
          items={getDocumentItems(productData.documents, productDocumentsType)}
          labelStyle={{ fontWeight: '700', color: '#23568A' }}
        />
      ) : null,
    },
    {
      key: '13',
      label: 'Export Approved Remarks',
      children: productData.exportApprovedRemarks
        ? productData.exportApprovedRemarks.join(', ')
        : '',
    },
    {
      key: '14',
      label: 'Duty Remarks',
      children: productData.dutyRemarks ? productData.dutyRemarks.join(', ') : '',
    },
  ];

  useEffect(() => {
    const fetchListApi = [
      getFormConfigFromName(formConfigName.productCertificates),
      getFormConfigFromName(formConfigName.productDocuments),
    ];
    Promise.all(fetchListApi).then((res) => {
      if (res?.[0]?.data?.value) {
        setProductCertificatesType(res?.[0].data.value)
      }
      if (res?.[1]?.data?.value) {
        setProductDocumentsType(res?.[1].data.value)
      }
    }).catch((error) => {
      console.log(error);
    })
  }, []);

  return <Descriptions colon={false} layout="vertical" items={items} labelStyle={{ fontWeight: '700', color: '#23568A' }}/>;
};

export default SupplierProductView;

SupplierProductView.propTypes = {
  productData: PropTypes.object,
};
