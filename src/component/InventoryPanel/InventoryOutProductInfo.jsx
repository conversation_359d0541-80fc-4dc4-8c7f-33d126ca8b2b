import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Form, Row } from 'antd';
import InventoryOutProduct from './InventoryOutProduct';
import PageLoader from '../Loaders/PageLoader';

const InventoryOutProductInfo = (props) => {
  const { form, initialValue } = props;

  const [loading, setLoading] = useState(false);

  return (
    <>
      {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} /> : null}
      <Form
        name="inventoryOut_product_info"
        layout="vertical"
        scrollToFirstError
        size="middle"
        form={form}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.List
              name="products"
              rules={[
                {
                  required: true,
                  message: 'At least 1 product details is required!',
                },
              ]}
            >
              {(fields, { add, remove }, { errors }) => (
                <div
                  style={{
                    display: 'flex',
                    rowGap: 16,
                    flexDirection: 'column',
                  }}
                >
                  {fields.map((field, index) => {
                    return (
                      <InventoryOutProduct
                        key={`product_${field.key}`}
                        form={form}
                        field={field}
                        index={index}
                        remove={remove}
                        initialValue={initialValue}
                        setLoading={setLoading}
                      />
                    );
                  })}
                  {fields.length < initialValue?.dispatchProducts?.length ? (
                    <Button type="dashed" onClick={() => add()} block>
                      + Add Product
                    </Button>
                  ) : null}
                  <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
                </div>
              )}
            </Form.List>
          </Col>
        </Row>
      </Form>
    </>
  )
}

export default InventoryOutProductInfo;

InventoryOutProductInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
};