import React from 'react';
import CustomerListView from './CustomerListView';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';

const CustomerDashboard = () => {

  const dispatch = useDispatch();

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };

  return (
    <CustomerListView
      collapseAsideBarHandler={collapseAsideBarHandler}
      collapseSideDrawerHandler={collapseSideDrawerHandler}
    />
  );
};

export default CustomerDashboard;
