import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Form, Row } from 'antd';
// import { getFormConfigFromName } from '../../service/api/formConfig';
import { formModes } from '../../constants/formConstant';
import DispatchOrderProduct from './DispatchOrderProduct';
import { useDispatch, useSelector } from 'react-redux';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';
import ObjectUtil from '../../util/objectUtil';
import PageLoader from '../Loaders/PageLoader';
import { getAllPackagingDetails } from '../../service/api/packagingApi';

const DispatchOrderProductInfo = props => {
  const { form, initialValue, formView, setInitialValue } = props;

  const [loading, setLoading] = useState(false);

  const packagingList = useSelector(state => state.packagingList);
  const dispatch = useDispatch();
  const productList = initialValue && initialValue?.productList ? initialValue.productList : [];

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    if (ObjectUtil.isEmptyObject(option)) {
      return option;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getFormPrefilldValues = () =>
    initialValue && initialValue?.products
      ? {
          products: initialValue.products.map(prod => ({
            ...prod,
            product: prod.product,
            uom: prod.uom,
            price: prod.price,
            quantity: prod.quantity,
            units: prod.units,
            hsCode: prod.hsCode,
            packaging: getPackagingFormValue(prod.packaging, packagingList),
            remarks: prod.remarks || [],
            label: prod.label,
            labelFile: ObjectUtil.isObject(prod.labelFile) ? [prod.labelFile] : prod.labelFile,
          })),
        }
      : {};

  useEffect(() => {
    if (!packagingList || !packagingList.length) {
      setLoading(true);
      // getAllPackagingDetails()
      getAllPackagingDetails()
        .then(res => {
          if (res.data) {
            const pkgList = res.data.map(pkg => ({
              ...pkg,
              id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
            }));
            dispatch(setPackagingList(pkgList));
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, []);

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      packagingList &&
      packagingList.length &&
      initialValue &&
      initialValue.products &&
      initialValue.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [initialValue.productList, initialValue.products, packagingList]); //TODO: verify this check

  if (loading) {
    return <PageLoader style={{ position: 'absolute', width: '100%', height: '100vh' }} />;
  }

  return (
    <Form
      name="dispatch_order_product_info"
      layout="vertical"
      scrollToFirstError
      disabled={formView === formModes.READ}
      size="middle"
      form={form}
      // preserve={false}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.List
            name="products"
            rules={[
              {
                required: true,
                message: 'At least 1 product details is required!',
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <div
                style={{
                  display: 'flex',
                  rowGap: 16,
                  flexDirection: 'column',
                }}
              >
                {fields.map((field, index) => {
                  return (
                    <DispatchOrderProduct
                      key={`product_${field.name}`}
                      form={form}
                      field={field}
                      index={index}
                      formView={formView}
                      remove={remove}
                      productList={productList}
                      initialValue={initialValue}
                      setInitialValue={setInitialValue}
                      packagingList={packagingList}
                    />
                  );
                })}
                {formView !== formModes.READ && fields.length < productList.length ? (
                  <Button type="dashed" onClick={() => add()} block>
                    + Add Product
                  </Button>
                ) : null}
                <Form.ErrorList errors={errors} style={{ color: '#ff4d4f' }} />
              </div>
            )}
          </Form.List>
        </Col>
      </Row>
    </Form>
  );
};

export default DispatchOrderProductInfo;

DispatchOrderProductInfo.propTypes = {
  form: PropTypes.object.isRequired,
  initialValue: PropTypes.object,
  formView: PropTypes.string.isRequired,
  setInitialValue: PropTypes.func,
};
