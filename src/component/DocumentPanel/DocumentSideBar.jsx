import React, { useEffect, useState } from 'react';
import { Drawer, Timeline } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
// import dayjs from 'dayjs';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import { getEmployeeNameFromId } from '../../util/userUtils';
import { getEmployeeList } from '../../service/api/employee';

const DocumentSideBar = ({ visible, onClose, events }) => {
  const [employeeList, setEmployeeList] = useState([]);

  useEffect(() => {
    // if (formView === formModes.CREATE) setLoading(true);
    const fetchApiList = [getEmployeeList()];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setEmployeeList(responseList[0].data.content);
        }
        // setLoading(false);
      })
      .catch(error => {
        console.log(error);
        // if (formView === formModes.CREATE) setLoading(false);
      });
  }, []);

  return (
    <Drawer title="Event Timeline" placement="right" onClose={onClose} visible={visible}>
      <Timeline mode="left">
        {events.map((event, index) => (
          <Timeline.Item key={index} dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}>
            {/* <h3>{event.name}</h3> */}
            <p>
              <strong>Event:</strong> {event?.context?.description}
            </p>
            <p>
              <strong>Date:</strong> {event?.createdAt ? getDateFromTimeStamp(event.createdAt) : ''}
            </p>
            <p>
              <strong>Intitated By:</strong> {getEmployeeNameFromId(event?.createdBy, employeeList)}
            </p>
          </Timeline.Item>
        ))}
      </Timeline>
    </Drawer>
  );
};

DocumentSideBar.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  events: PropTypes.array.isRequired,
};

export default DocumentSideBar;
