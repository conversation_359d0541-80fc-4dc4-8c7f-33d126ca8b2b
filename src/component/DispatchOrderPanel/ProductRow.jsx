import React, { useState, useEffect } from 'react';
import { Form, Input, Select, InputNumber, Button, Modal } from 'antd';
import { formModes } from '../../constants/formConstant';
import PropTypes from 'prop-types';
import css from './ProductTable.module.css';
import { productUOMList, productLabelTypes } from '../../constants/formConstant';
import dayjs from 'dayjs';
import ObjectUtil from '../../util/objectUtil';
import { validateProductOrderQuantity } from '../../service/api/dispatchOrderApi';
import { DeleteOutlined } from '@ant-design/icons';

const ProductRow = ({
  form,
  formView,
  packagingList,
  productList,
  index,
  name,
  initialValue,
  setInitialValue,
  onDeleteRow,
}) => {
  const [focusedField, setFocusedField] = useState(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const formIdWatch = Form.useWatch(['products', name, 'product'], form);
  const packagingId = Form.useWatch(['products', name, 'packaging'], form);
  const packagingObj = packagingList.find(pkg => pkg.id === packagingId);
  const isOtherPackaging = packagingObj && packagingObj.type === 'Others';

  useEffect(() => {
    if (
      formView !== formModes.READ &&
      formIdWatch &&
      (!initialValue?.products?.length ||
        !initialValue?.products[index] ||
        initialValue?.products[index]?.id !== formIdWatch)
    ) {
      let newInitialValue = JSON.parse(JSON.stringify(initialValue));
      if (!initialValue?.products?.length) initialValue.products = [];
      let newValue = {};
      const [productInfo] = productList.filter(prod => prod.id === formIdWatch);
      if (productInfo && productInfo.uom) {
        newValue = {
          ...productInfo,
          uom: productInfo.uom,
          quantity: productInfo.quantity,
          units: productInfo.units,
          product: productInfo.id,
          packaging: productInfo.packaging,
          price: productInfo.price,
        };
        if (!newInitialValue?.products?.length) newInitialValue.products = [];
        newInitialValue.products[index] = newValue;
        newInitialValue = {
          ...newInitialValue,
          products: [
            ...newInitialValue.products.map((item, idx) => {
              const newProductFormField = form.getFieldValue(['products', idx]);
              return ObjectUtil.mergeWithoutNull(item, newProductFormField);
            }),
          ],
          incoterms: productInfo.incoterms,
          purchaseOrderDate: dayjs(newInitialValue.purchaseOrderDate),
          shipmentDate: productInfo.shipmentDate ? dayjs(productInfo.shipmentDate) : '',
          deliveryDate: productInfo.deliveryDate ? dayjs(productInfo.deliveryDate) : '',
          invoiceDate: productInfo.invoiceDate ? dayjs(productInfo.invoiceDate) : '',
        };
      }
      console.log('newInitialValue', newInitialValue);
      setInitialValue(newInitialValue);
    }
  }, [formIdWatch]);

  const isRowFilled = row => {
    return Object.values(row).some(value => value && value.toString().trim() !== '');
  };

  const validateProductQuantity = async (poNumber, productId, productQuantity) => {
    console.log(initialValue);
    const response = await validateProductOrderQuantity(
      poNumber,
      productId,
      productQuantity,
      initialValue?.id,
      initialValue?.customer?.id,
      initialValue?.inventoryId
    );
    console.log('response', response?.data?.isValid);
    const isValid = response.data.isValid;
    const availableQuantity = response.data.availableQuantity;
    if (availableQuantity == 0) return Promise.reject('No more quantity left for usage');
    return isValid
      ? Promise.resolve()
      : Promise.reject(
          `quantity must not exceed more than 5% of the available quantity, which is ${availableQuantity}`
        );
  };

  return (
    <tr>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        {productList && productList.length ? (
          <Form.Item
            name={[name, 'product']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              style={{
                width: focusedField === `${index}-product` ? '350px' : '150px',
              }}
              showSearch
              allowClear
              optionFilterProp="label"
              onFocus={() => setFocusedField(`${index}-product`)}
              onBlur={() => setFocusedField(null)}
              disabled={formView === formModes.UPDATE}
            >
              {productList.map(product => (
                <Select.Option
                  key={product.id}
                  value={product.id}
                  label={product.product.tradeName}
                >
                  {product.product.tradeName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'uom']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Select
            disabled={formView === formModes.UPDATE}
            style={{
              width: focusedField === `${index}-street` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-street`)}
            onBlur={() => setFocusedField(null)}
          >
            {productUOMList.map(uom => (
              <Select.Option key={uom.key} value={uom.value}>
                {uom.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </td>
      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantity']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              async validator(_, value) {
                const rowValues = getFieldValue(['products', name]);

                // ✅ If row is not filled, skip all validations
                if (!isRowFilled(rowValues)) {
                  return Promise.resolve();
                }

                // ✅ If value is empty when row is filled
                if (!value) {
                  return Promise.reject(
                    new Error('This field is required when other fields are filled.')
                  );
                }

                // ✅ If value is less than or equal to zero
                if (value <= 0) {
                  return Promise.reject(
                    new Error('Value can never be less than or equal to zero.')
                  );
                }

                // ✅ Further async validation based on initial data
                const [prod] = initialValue.productList.filter(item => item.id === formIdWatch);
                const poNumber = initialValue.purchaseOrderNumber;

                // If product and quantity are present in initial data
                // if (prod && prod.quantity) {
                //   await validateProductQuantity(poNumber, formIdWatch, value);
                // }

                // ✅ If everything is fine, pass validation
                return Promise.resolve();
              },
            }),
          ]}
        >
          <InputNumber
            style={{
              width: focusedField === `${index}-quantity` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantity`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>


      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'price']}
          style={{ marginBottom: 0, display: 'block' }}

          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const rowValues = getFieldValue(['products', name]);
                if (isRowFilled(rowValues) && !value) {
                  return Promise.reject();
                }
                if (value && value <= 0) {
                  return Promise.reject(new Error('Price can never be less than or equal to zero'));
                }
              
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-price` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-price`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      {packagingList.length > 0 && (
        <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
          <Form.Item
            name={[name, 'packaging']}
            style={{ marginBottom: 0, display: 'block' }}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Select
              // mode="multiple"
              className={css.selectBox}
              style={{
                width: focusedField === `${index}-packaging` ? '350px' : '150px',
              }}
              optionFilterProp="label"
              onFocus={() => setFocusedField(`${index}-packaging`)}
              onBlur={() => setFocusedField(null)}
              allowClear
            >
              {packagingList.map((packageType, index) => (
                <Select.Option key={index} value={packageType.id} label={packageType.type}>
                  <div className={css.CustomSelectOptn}>
                    <div className={css.optnTitle}>{packageType.type}</div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Pack size:</div>
                      <div className={css.optnValue}>{packageType.packSize}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Tare weight:</div>
                      <div className={css.optnValue}>{packageType.tareWeight}</div>
                    </div>
                    <div className={css.optnDetails}>
                      <div className={css.optnLabel}>Dimension:</div>
                      <div className={css.optnValue}>{packageType.dimension}</div>
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </td>
      )}
      <td style={{ padding: '4px', verticalAlign: 'top', height: '50px' }}>
        {isOtherPackaging && (
          <span style={{ color: 'red', marginRight: 2 }}>*</span>
        )}
        <Form.Item
          name={[name, 'otherPackagingDetails']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (isOtherPackaging && !value) {
                  return Promise.reject(new Error('Other Packaging Details is required!'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-otherPackagingDetails` ? '350px' : '150px',
              borderColor: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? 'red' : undefined,
              background: isOtherPackaging && !form.getFieldValue(['products', name, 'otherPackagingDetails']) ? '#fff0f0' : undefined,
            }}
            onFocus={() => setFocusedField(`${index}-otherPackagingDetails`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

     <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'units']}
          style={{ marginBottom: 0, display: 'block' }}
          rules={[
            ({ getFieldValue }) => ({
              async validator(_, value) {
                const rowValues = getFieldValue(['products', name]);

                // If row is not filled, skip all validations (pass silently)
                if (!isRowFilled(rowValues)) {
                  return Promise.resolve();
                }

                // If value is empty when row is filled
                if (!value) {
                  return Promise.reject(
                    new Error('This field is required when other fields are filled.')
                  );
                }

                // Check if value is <= 0
                if (value <= 0) {
                  return Promise.reject(
                    new Error('Value can never be less than or equal to zero.')
                  );
                }

                // Custom async validation (if needed, based on initialValue check)
                const [prod] = initialValue.productList.filter(item => item.id === formIdWatch);

                if (prod && prod.units) {
                  // You can place additional async logic here if necessary
                  // Example placeholder for async check:
                  // const isValid = await validateUnits(value);
                  // if (!isValid) return Promise.reject(new Error('Invalid units value.'));
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <InputNumber
            style={{
              width: focusedField === `${index}-units` ? '150px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-units`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>
      

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item
          name={[name, 'quantityPerUnit']}
          // validateTrigger="onBlur"
        >
          <InputNumber
            style={{
              width: focusedField === `${index}-quantityPerUnit` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-quantityPerUnit`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'chemstackPrice']}>
          <InputNumber
            style={{
              width: focusedField === `${index}-chemstackPrice` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-chemstackPrice`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'perUnitKgValue']}>
          <InputNumber
            style={{
              width: focusedField === `${index}-perUnitKgValue` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-perUnitKgValue`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'hsCode']}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const rowValues = getFieldValue(['products', name]);
                  if (isRowFilled(rowValues) && !value) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
        >
          <InputNumber
            style={{
              width: focusedField === `${index}-hsCode` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-hsCode`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'productNameAlias']}>
          <Input
            style={{
              width: focusedField === `${index}-productNameAlias` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-productNameAlias`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'countryOfOrigin']} 
        rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              const rowValues = getFieldValue(['products', name]);
              if (isRowFilled(rowValues) && !value) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          }),
        ]}
        >
          <Input
            style={{
              width: focusedField === `${index}-countryOfOrigin` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-countryOfOrigin`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'label']} style={{ marginBottom: 0, display: 'block' }}>
          <Select
            style={{
              width: focusedField === `${index}-label` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-label`)}
            onBlur={() => setFocusedField(null)}
          >
            {productLabelTypes.map(({ value, label }) => (
              <Select.Option key={value} value={value}>
                {label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'batchDateVisibility']}>
          <Input
            style={{
              width: focusedField === `${index}-batchDateVisibility` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-batchDateVisibility`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', verticalAlign: 'top', height: '100%' }}>
        <Form.Item name={[name, 'remarks']} style={{ marginBottom: 0, display: 'block' }}>
          <Input
            style={{
              width: focusedField === `${index}-remarks` ? '350px' : '150px',
            }}
            onFocus={() => setFocusedField(`${index}-remarks`)}
            onBlur={() => setFocusedField(null)}
          />
        </Form.Item>
      </td>

      <td style={{ padding: '4px', textAlign: 'center', verticalAlign: 'middle', height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', margin: '4px 0' }}>
          <Button
            type="text"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => setDeleteConfirmVisible(true)}
            style={{ 
              color: '#ff4d4f',
              border: '1px solid #ff4d4f',
              borderRadius: '4px',
              width: '32px',
              height: '28px',
              padding: '0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title="Delete this product row"
          />
        </div>

        {/* Confirmation Modal */}
        <Modal
          title="Confirm Delete"
          open={deleteConfirmVisible}
          onOk={() => {
            onDeleteRow();
            setDeleteConfirmVisible(false);
          }}
          onCancel={() => setDeleteConfirmVisible(false)}
          okText="Delete"
          cancelText="Cancel"
          okButtonProps={{ danger: true }}
        >
          <p>Are you sure you want to delete this product row?</p>
          <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
            This action cannot be undone.
          </p>
        </Modal>
      </td>
    </tr>
  );
};

ProductRow.propTypes = {
  form: PropTypes.object.isRequired,
  formView: PropTypes.string.isRequired,
  productList: PropTypes.array.isRequired,
  packagingList: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
  initialValue: PropTypes.object.isRequired,
  setInitialValue: PropTypes.func.isRequired,
  onDeleteRow: PropTypes.func.isRequired,
};

export default ProductRow;
