import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { Badge, Button, Dropdown, Input, Modal, Progress, Select } from 'antd';
import { getDateFromTimeStamp } from '../../util/dateUtils';
import DocumentGeneration from '../DocumentGeneration/DocumentGeneration';
import PageLoader from '../Loaders/PageLoader';
import {  downloadAllFilesForOrder, fetchUploadDocuments, getAllFilesForOrder, getValidFileTypes, saveDocumentAfterUpload, sendToCustomer, uploadFileToStorage } from '../../service/api/documentApi';
import download from '../../assets/icons/arrow_download.svg';
import upload from '../../assets/icons/arrow_upload.svg';
import generateDoc from '../../assets/icons/generation_logo.svg';
import preview from '../../assets/icons/preview.svg';
// import updateDoc from '../../assets/icons/doc_update.svg';
import review from '../../assets/icons/doc_review.svg';
import { downloadFile } from '../../service/api/storageService';
import { 
  LoadingOutlined, CheckCircleFilled, CloseCircleFilled, SearchOutlined, CloseOutlined, DownloadOutlined,
  SendOutlined,
  BookOutlined,
} from '@ant-design/icons';
import DocumentViewer from '../DocumentViewer/DocumentViewer';
import { 
  // canBeGenerated, 
  canBeGeneratedByUser } from '../../util/userUtils';
import { saveByteArray } from '../../util/formatUtil';
import { useNotificationContext } from '../../provider/NotificationProvider';
import { docStatusLabelMap } from '../../constants/formConstant';
import DocumentSideBar from './DocumentSideBar';
import { generateInvoiceNumberForOrder } from '../../service/api/dispatchOrderApi';


const DocumentPanel = (props) => {

  const { order ,customerData, updateDispatchOrder} = props;
  const user = useSelector(state => state.user);
  const [loading, setLoading] = useState(false);
  const [downloadLoader, setDownloadLoader] = useState(false);
  const [showDocGeneration, setShowDocGeneration] = useState(false);
  const [docForGeneration, setDocForGeneration] = useState(null);
  const [showDocPreview, setShowDocPreview] = useState(false);
  const [docForPreview, setDocForPreview] = useState({});
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showSearch, setShowSearch] = useState(false);
  const [searchKey, setSearchKey] = useState('');
  const [otherDocs, setOtherDocs] = useState([]);
  const [uploadToBeDoc, setUploadToBeDoc] = useState({});
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [eventHistory,setEventHistory]=useState([]);

  const [orderDocuments, setOrderDocuments] = useState({
    draftDocuments: [],
    finalDocuments: [],
  });
  const [addDocuments, setAddDocuments] = useState({
    toBeAdded: [],
    added: [],
  });


  const showDrawer = (events) => {
    if(events) setEventHistory(events)
    else setEventHistory([])
    setDrawerVisible(true);
  };

  const closeDrawer = () => {
    setEventHistory([]);
    setDrawerVisible(false);
  };


  const { api } = useNotificationContext();

  const { confirm } = Modal;

  const getReviewConfirmationModal = (title, content, okFun, cancelFunc) => {
    return confirm({
      title,
      content,
      onOk() {
        okFun();
      },
      onCancel() {
        cancelFunc();
      },
    });
  };
  
  const dropDownItems = [
    {
      label: 'Preview',
      key: 0,
      icon: <img src={preview} style={{ height: '100%', objectFit: 'contain' }} />
    },
    {
      label: 'Download',
      key: 1,
      icon: <img src={download} style={{ height: '100%', objectFit: 'contain' }} />,
    },
    {
      label: 'Send to customer',
      key: 1,
      icon: <img src={download} style={{ height: '100%', objectFit: 'contain' }} />,
    },
  ];

  // const getBgColorFromStatus = (status) => {
  //   if (!status) return '#000';
  //   switch(status.toLowerCase()) {
  //     case 'new': return '#EBF3FC';
  //     case 'approved': return '#F1FAF1';
  //     case 'in progress': return '#FFF9F5';
  //     default: return '#000';
  //   }
  // }

  const getBgColorFromStatus = (status) => {
    if (!status) return '#000';
    switch(status.toUpperCase()) {
      case 'PENDING_FOR_REVIEW': return '#EBF3FC';
      case 'PENDING_FOR_APPROVAL': return '#FFF9F5';
      case 'APPROVED': return '#F1FAF1';
      case 'SENT_TO_CUSTOMER': return '#E6F7E6';
      default: return '#000';
    }
  }
  
  // const getBorderColorFromStatus = (status) => {
  //   if (!status) return '#000';
  //   switch(status.toLowerCase()) {
  //     case 'new': return '#B4D6FA';
  //     case 'approved': return '#9FD89F';
  //     case 'in progress': return '#FDCFB4';
  //     default: return '#000';
  //   }
  // }
  const getBorderColorFromStatus = (status) => {
    if (!status) return '#000';
    switch(status.toUpperCase()) {
      case 'PENDING_FOR_REVIEW': return '#B4D6FA';
      case 'PENDING_FOR_APPROVAL': return '#FDCFB4';
      case 'APPROVED': return '#9FD89F';
      case 'SENT_TO_CUSTOMER': return '#B4E2B4';
      default: return '#000';
    }
  }
  
  // const getColorFromStatus = (status) => {
  //   if (!status) return '#000';
  //   switch(status.toLowerCase()) {
  //     case 'new': return '#115EA3';
  //     case 'approved': return '#0E700E';
  //     case 'in progress': return '#8A3707';
  //     default: return '#000';
  //   }
  // }

  const getColorFromStatus = (status) => {
    if (!status) return '#000';
    switch(status.toUpperCase()) {
      case 'PENDING_FOR_REVIEW': return '#115EA3';
      case 'PENDING_FOR_APPROVAL': return '#8A3707';
      case 'APPROVED': return '#0E700E';
      case 'SENT_TO_CUSTOMER': return '#0C590C';
      default: return '#000';
    }
  }
  
  const handleDocGeneration = (docData) => {
    setDocForGeneration(docData);
    setShowDocGeneration(true);
  }
  const hideDocumentGeneration = () => {
    setShowDocGeneration(false);
    setDocForGeneration(null);
  }
  const handleDocPreview = (docData, previewOnly) => {
    setDocForPreview({
      document: docData,
      previewOnly,
    });
    setShowDocPreview(true);
  }
  const hideDocPreview = () => {
    setShowDocPreview(false);
    setDocForPreview({});
  }
  const filterDocumentsFromState = (docList) => {
    const newList = {
      draftDocuments: [],
      finalDocuments: [],
    };
    docList.forEach((item) => {
      if (item.state === 'Draft') {
        newList.draftDocuments.push(item);
      } else if (item.state === 'Final') {
        newList.finalDocuments.push(item);
      }
    });
    return newList;
  };
  const filterAddDocuments = (docList) => {
    const newList = {
      toBeAdded: [],
      added: [],
    };
    docList.forEach((item) => {
      if (item.fileId && item.fileName) {
        newList.added.push(item);
      } else {
        newList.toBeAdded.push(item);
      }
    });
    return newList;
  };
  const handleDropDownClick = (e, file) => {
    const actionObj = dropDownItems?.[e.key];
    if (actionObj?.label === 'Download') {
      onDownload(file);
    }
    if (actionObj?.label === 'Preview') {
      handleDocPreview(file, true);
    }
    // console.log(actionObj)
    if (actionObj?.label == 'Send to customer') {
      getReviewConfirmationModal(
        'Send to customer',
        'Are you sure you want to send document to customer ? ',
        () => {
          sendDocToCustomer(file?.id)
        },
        () => {
          console.log('cancelled');
        }
      );
    }
  }
  const onDownload = async (file) => {
    try {
      api.open({
        key: 'fileDownload',
        message: 'Downloading File...',
        description:(
          <div>
            <div className='text-xs font-medium'>{file.label}</div>
            <div className='text-xs text-sub-text-black'>{file.fileName}</div>
          </div>
        ),        
        duration: 0,
        icon: (
          <LoadingOutlined
            style={{
              color: '#0F6CBD',
            }}
          />
        ),
      });
      const fileId = file?.fileId;
      const apiRes = await downloadFile(fileId);
      window.open(apiRes.data.url, '_self');
      api.open({
        key: 'fileDownload',
        message: 'File Downloaded',
        description:(
          <div>
            <div className='text-xs font-medium'>{file.label}</div>
            <div className='text-xs text-sub-text-black'>{file.fileName}</div>
          </div>
        ),        
        duration: 3,
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ),
      });
    } catch (error) {
      console.log('Error while fetching url from storage service');
      api.open({
        key: 'fileDownload',
        message: 'Download Failed!!',
        description:(
          <div>
            <div className='text-xs font-medium'>{file.label}</div>
            <div className='text-xs text-sub-text-black'>{file.fileName}</div>
          </div>
        ),        
        duration: 3,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
      console.log(error);
    }
  };
  const onFileUpload = (e, document, isFinal) => {
    setUploadProgress(10);
    api.open({
      key: 'uploadNotif',
      message: 'Uploading File',
      description:(
        <Progress
          percent={10}
          status="active"
          strokeColor="#23568A"
          trailColor="#E6E6E6"
        />
      ),        
      duration: 0,
      icon: (
        <LoadingOutlined
          style={{
            color: '#0F6CBD',
          }}
        />
      ),
    });
    const fileData = new FormData();
    fileData.append('fileDto', JSON.stringify({
      category: "DispatchOrders",
      meta: {entityId: order.id, documentType: document.docType},
      name: e.target.files[0].name
    }));
    fileData.append('file', e.target.files[0]);
    uploadFileToStorage(fileData).then((res1) => {
      if (res1.data, res1.data.id) {
        setUploadProgress(40);
        const docData = {
          docType: document.docType,
          fileName: res1.data.name,
          fileId: res1.data.id,
          entityType: document.entityType,
          entityId: document.entityId,
          label: document.label,
          ...(isFinal ? { state: 'Final' } : {})
        }
        saveDocumentAfterUpload(docData).then((res2) => {
          if (res2.data) {
            setUploadProgress(100);
            api.open({
              key: 'uploadNotif',
              message: 'File Uploaded',
              description:(
                <div>
                  <div>{document.label}</div>
                  <div>{res1.data.name}</div>
                </div>
              ),        
              duration: 5,
              icon: (
                <CheckCircleFilled
                  style={{
                    color: '#107C10',
                  }}
                />
              ),
            });
            fetchData();
          }
        }).catch((err) => {
          console.log(err);
          api.open({
            key: 'uploadNotif',
            message: 'Upload Failed!',
            description:(
              <Progress
                percent={uploadProgress}
                status="exception"
                strokeColor="#dc2626"
                trailColor="#E6E6E6"
              />
            ),        
            duration: 5,
            icon: (
              <CloseCircleFilled
                style={{
                  color: '#dc2626',
                }}
              />
            ),
          });
        })
      }
    }).catch((err) => {
      console.log(err);
      api.open({
        key: 'uploadNotif',
        message: 'Upload Failed!',
        description:(
          <Progress
            percent={uploadProgress}
            status="exception"
            strokeColor="#dc2626"
            trailColor="#E6E6E6"
          />
        ),        
        duration: 5,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
    })
  };
  const onDocumentGeneration = (file, err) => {
    if (file) fetchData();
    setTimeout(() => {
      api.open({
        key: 'fileGenerate',
        message: file ? 'File Generated' : 'Generation Failed',
        description: (
          <div>
            <div className='text-xs font-medium'>{file ? file.label : 'Unable to generated file'}</div>
            <div className='text-xs text-sub-text-black'>{file ? file.fileName : err?.response?.data?.message || err?.message}</div>
          </div>
        ),        
        duration: 5,
        icon: file ? (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ) : (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
    }, 500);
  }
  const onDocumentApprove = (file, err) => {
    if (file) fetchData();
    setTimeout(() => {
      api.open({
        key: 'fileapprove',
        message: file ? 'File Approved' : 'Approval Failed',
        description: (
          <div>
            <div className='text-xs font-medium'>{file ? file.label : 'Unable to approve file'}</div>
            <div className='text-xs text-sub-text-black'>{file ? file.fileName : err?.response?.data?.message ||  err?.message}</div>
          </div>
        ),        
        duration: 5,
        icon: file ? (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ) : (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
    }, 500);
  }
  const fetchData = async () => {
    setLoading(true);
    Promise.all([getAllFilesForOrder(order.id), getValidFileTypes(order.id)]).then((res) => {
      if (res?.[0]?.data) {
        setOrderDocuments(filterDocumentsFromState(res[0].data));
      }
      if (res?.[1]?.data) {
        setAddDocuments(filterAddDocuments(res[1].data));
      }
      setUploadToBeDoc({});
      setLoading(false)
    }).catch((err) => {
      console.log(err);
      setLoading(false)
    })     
  }
  const downLoadAllFiles = () => {
    setDownloadLoader(true);
    
    downloadAllFilesForOrder(order.id).then((res) => {
      // const byteArray = hexStringToBytes(res.data);
      saveByteArray(`Final Documents.zip`, res.data)
      setDownloadLoader(false);

    }).catch((err) => {
      setDownloadLoader(false);
      api.open({
        key: 'downloadFiles',
        message: 'Download Failed!!',
        description: (
          <>
            <div className='text-xs font-medium'>Error occured while downloading files</div>
            <div className='text-xs text-sub-text-black'>{err?.response?.data?.message || err?.message}</div>
          </>
        ),        
        duration: 5,
        icon: (
          <CloseCircleFilled
            style={{
              color: '#dc2626',
            }}
          />
        ),
      });
    })
  }
  const fetchOtherDoc = () => {
    fetchUploadDocuments(order.id).then((res) => {
      if (res.data) {
        setOtherDocs(res.data);
      }
    }).catch((err) => console.log(err));
  }
  useEffect(() => {
    if (order.id) {
      fetchData();
      fetchOtherDoc();
    }

  }, []);

  // const canBeUploaded=(file)=>{
  //   return false;
  // }

  const getReadableStatus = (status) => {
    return docStatusLabelMap[status] || status; // Fallback to the original status if not found
  };

  const sendDocToCustomer=(docId)=>{
    sendToCustomer(docId).then(()=>{
      console.log("doc sent to customer")
    }).then(()=>{
      api.open({
        key: 'Sent to customer',
        message: 'Document sent to customer',      
        duration: 1,
        icon: (
          <CheckCircleFilled
            style={{
              color: '#107C10',
            }}
          />
        ),
      });
    })
    .catch(err=>console.log(err))
  }

  // canApprove 
  const canApprove=()=>{
    if(!(user!=null && order!=null)) return false;
    // check for admin
    if(user?.permissionsGroups.includes('ADMIN')) return true;
    // check for l2 
    return customerData?.l2Reviewers && user.entityId && customerData?.l2Reviewers.includes(user.entityId);
  }
  // canSendForReview
  const canSendForReview=()=>{
    if(!(user && order)) return false;
    // check for admin
    if(user?.permissionsGroups.includes('ADMIN')) return true;
    // check for l1
    return customerData?.l1Reviewers && user.entityId && customerData?.l1Reviewers.includes(user.entityId);
  }
  // canSendToCustomer
  const canSendToCustomer=()=>{
    
    if(!(user && order)) return false;
    // temporary disable 
    return false;
    // TODO enable when send feature done 
    // check for admin
    // if(user?.permissionsGroups.includes('ADMIN')) return true;
    // check for l2
    // return customerData?.l2Reviewers && user.entityId && customerData?.l2Reviewers.includes(user.entityId);
  }
  // canRevert
  const canRevert=()=>{
    if(!(user && order)) return false;
    // check for admin
    if(user?.permissionsGroups.includes('ADMIN')) return true;
    // check for l2
    return customerData?.l2Reviewers && user.entityId && customerData?.l2Reviewers.includes(user.entityId);
  }
  // canComment
  // finalDocumentOptions
  const getFinalDocumentOptions=()=>{
    let items= [
      {
        label: 'Preview',
        key: 0,
        icon: <img src={preview} style={{ height: '100%', objectFit: 'contain' }} />
      },
      {
        label: 'Download',
        key: 1,
        icon: <img src={download} style={{ height: '100%', objectFit: 'contain' }} />,
      },
    ]
    if(canSendToCustomer()) items.push( {
      label: 'Send to customer',
      key: 2,
      icon: < SendOutlined style={{ height: '100%', objectFit: 'contain' }} />
    })
    return items;
  }

  const canGenerateInvoiceNumber =(order , docType)=>{
    // console.log(order, docType)
    if(order && docType){
      // console.log('docType ',docType, ' invoice number ',order?.mstackInvoiceNumber)
      if(docType=='MSTACK_INVOICE' && (order?.mstackInvoiceNumber==null || order?.mstackInvoiceNumber=="")) return true
      if(docType=='CHEMSTACK_TAX_INVOICE' && (order?.chemstackInvoiceNumber==null || order?.chemstackInvoiceNumber=="")) return true
      else return false;
    }
    return false;
  }

  const generateInvoiceNumber=(order,docType)=>{
    return generateInvoiceNumberForOrder({
      entityId:order?.id,
      entityType:'CUSTOMER_ORDER',
      invoiceType:docType
    }).then(()=>{
      updateDispatchOrder()
    })
  }
  

  return (showDocGeneration ? (
    <DocumentGeneration
      hide={() => hideDocumentGeneration()}
      docData={docForGeneration}
      onDocumentGeneration={onDocumentGeneration}
    />
  ) : showDocPreview && docForPreview?.document ? (
      <DocumentViewer
        document={docForPreview?.document}
        orderId={order.id}
        previewOnly={docForPreview?.previewOnly}
        hide={hideDocPreview}
        onFileUpload={onFileUpload}
        handleDocGeneration={handleDocGeneration}
        onDocumentApprove={onDocumentApprove}
        onDownload={onDownload}
        sendDocToCustomer={sendDocToCustomer}
        canBeGeneratedByUser={(docType)=>canBeGeneratedByUser(order ,docForPreview?.document,customerData,docType,user)}
        canApprove={canApprove}
        canSendForReview={canSendForReview}
        canSendToCustomer={canSendToCustomer}
        canRevert={canRevert}
        fetchData={fetchData}
        // can generate invoice number 
        canGenerateInvoiceNumber={()=>canGenerateInvoiceNumber(order,docForPreview?.document?.docType)}
        // generate invoice number 
        generateInvoiceNumber={()=>generateInvoiceNumber(order,docForPreview?.document?.docType)}
      />
    ) : (
      <div className='flex gap-5'>
        {loading ? <PageLoader style={{ position: 'absolute', width: '100%', height: '85vh'}}/> : null}
        <>
          <div
            className='w-1/4 flex px-4 py-3 flex-col items-start gap-3 rounded-lg shadow-shadow-box'
          >
            <div className='flex w-full gap-3 justify-between'>
              {showSearch ? (
              <>
                <Input
                  placeholder="Search"
                  size="small"
                  allowClear
                  onChange={e => {
                    setSearchKey(e.target.value)
                  }}
                />
                <CloseOutlined onClick={() => { setSearchKey(''); setShowSearch(false)}}/>
              </>
              ) : (
                <>
                  <div className='text-sm font-bold'>
                    Final Documents
                  </div>
                  <SearchOutlined onClick={() => setShowSearch(true)} />
                </>
              )}
            </div>
            <div className='w-full h-full flex flex-col gap-3'>
              {orderDocuments?.finalDocuments?.length ? (
                <Button
                  type="default"
                  className='rounded-md'
                  icon={<DownloadOutlined />}
                  loading={downloadLoader}
                  onClick={() => downLoadAllFiles()}
                >
                  Download All Files
                </Button>
              ) :
                <div className='h-full flex items-center justify-center bg-neutral-100 text-gray-500'>
                  No Final document available
                </div>
              }
              {orderDocuments.finalDocuments.filter((file) => file.label.includes(searchKey) || file.fileName.includes(searchKey)).map((file) => (
                <div
                  key={file.fileId}
                  className='p-2 flex flex-wrap items-center gap-3 justify-between hover:bg-neutral-100'
                >
                  <div>
                    <div className='text-sm font-medium'>{file.label}</div>
                    <div className='text-xs text-sub-text-black'>{file.fileName}</div>
                  </div>
                  <Dropdown
                    trigger={['click']}
                    menu={{
                     items:getFinalDocumentOptions(),
                      onClick: (e) => handleDropDownClick(e, file),
                    }}
                  >
                    <div
                    className=' w-6 h-6 text-center text-color-text-heading rounded leading-[15px] hover:bg-hover-gray hover:cursor-pointer'
                    >
                      ...
                    </div>
                  </Dropdown>
                </div>
              ))}
            </div>
          </div>
          <div
            className='w-3/4 flex flex-col gap-5'
          >
            <div className='flex gap-4'>
              <div className='flex flex-col gap-4 w-[100%]'>
                <div className='text-sm font-bold'>Add Documents</div>
                <div className='grid grid-cols-2 gap-3'>
                  {addDocuments.toBeAdded.map((file) => (
                    <div
                      key={file.label}
                      className='flex flex-col px-4 py-3 justify-center items-center gap-3 rounded-lg border-dashed border-theme-border bg-theme-bg-light'
                    >
                      <div
                      className='text-color-text-heading text-sm font-medium'
                      >
                        {file.label}
                      </div>
                      <div className='flex gap-2.5 items-center'>
                        {canBeGeneratedByUser(order,file ,customerData,file.docType,user) ? (
                          <Button
                            type='text'
                            className='px-2 py-0.5 flex items-center rounded bg-base-white text-xs text-text-black border-solid border-border-black-100'
                            onClick={() => handleDocGeneration(file)}
                            icon={<img src={generateDoc} style={{ height: '100%', objectFit: 'contain' }} />}
                          >
                            Generate
                          </Button>
                        ) : null}
                        {/* {canBeUploaded(file)?( <Button
                          type='text'
                          className='px-2 py-0.5 relative flex items-center rounded bg-base-white text-xs text-text-black border-solid border-border-black-100'
                          icon={<img src={upload} style={{ height: '100%', objectFit: 'contain' }} />}
                        >
                          Upload
                          <input
                            type="file"
                            accept="application/pdf, image/*"
                            className='absolute z-[2] opacity-0 w-full'
                            onChange={(e) => onFileUpload(e, file)}
                          />
                        </Button>):''} */}
                      </div>
                    </div>
                  ))}
                  {otherDocs?.length ? (
                    <div
                      key="other_upload"
                      className='flex flex-col px-4 py-3 justify-center items-center gap-3 rounded-lg border-dashed border-theme-border bg-theme-bg-light'
                    >
                      <Select
                        className='w-full'
                        value={uploadToBeDoc.docType}
                        placeholder="Select Document type"
                        onChange={(value) => {
                          const docData = otherDocs.find((doc) => doc.docType === value);
                          setUploadToBeDoc(docData);
                        }}
                      >
                        {otherDocs.map((doc, index) => (
                          <Select.Option
                            key={index}
                            value={doc.docType}
                            label={doc.label}
                          >
                            {doc.label}
                          </Select.Option>
                        ))}
                      </Select>
                      <div className='flex gap-2.5 items-center'>
                        <Button
                          type='text'
                          className='px-2 py-0.5 relative flex items-center rounded bg-base-white text-xs text-text-black border-solid border-border-black-100'
                          icon={<img src={upload} style={{ height: '100%', objectFit: 'contain' }} />}
                        >
                          Upload
                          <input
                            type="file"
                            accept="application/pdf, image/*"
                            className='absolute z-[2] opacity-0 w-full'
                            onChange={(e) => onFileUpload(e, uploadToBeDoc)}
                          />
                        </Button>
                      </div>
                    </div>
                  ) : null}                   
                </div>
                {addDocuments?.toBeAdded?.length ? null : 
                  <div className='h-full flex items-center justify-center bg-neutral-100 text-gray-500'>
                    No new document to add
                  </div>
                }
              </div>
              {/* <div className='w-[30%] flex flex-col gap-4'>
                <div className='text-sm font-bold'>Recently Added</div>
                <div className='flex flex-col gap-2 h-full'>
                  {addDocuments.added.map((file) => (
                    <div
                      key={file.fileId}
                      className='flex flex-wrap p-3 rounded shadow-shadow-box gap-2'
                    >
                      <div>
                        <div className='text-xs font-medium'>{file.label}</div>
                        <div className='text-xs text-sub-text-black'>{file.fileName}</div>
                      </div>
                      <Button
                        className='ml-auto'
                        type="text"
                        icon={<img src={preview} style={{ height: '100%', objectFit: 'contain' }} />}
                        onClick={() => handleDocPreview(file, true)}
                      />
                      <Button
                        type="text"
                        icon={<img src={download} style={{ height: '100%', objectFit: 'contain' }} />}
                        onClick={() => onDownload(file)}
                      />
                    </div>
                  ))}
                  {addDocuments?.added?.length ? null :
                    <div className='h-full flex items-center justify-center bg-neutral-100 text-gray-500'>
                      No document added
                    </div>
                  }
                </div>
              </div> */}
            </div>
            <div className='p-4 flex flex-col border gap-4 rounded-lg shadow-shadow-box'>
              <div className='text-sm font-bold'>Review Documents</div>
              <div className='flex flex-col'>
                {orderDocuments.draftDocuments.map((file) => (
                  <div
                    key={file.fileId}
                    className='grid items-start justify-between'
                    style={{ gridTemplateColumns: '2fr 6fr 3fr 4fr' }}
                  >
                    <div className='px-2 py-1.5'>
                      {file.status ? (<div
                        className='w-fit flex items-center pr-1.5 pl-2 gap-0.5 rounded-full'
                        style={{ backgroundColor: getBgColorFromStatus(file.status), border:`1px solid ${getBorderColorFromStatus(file.status)}`}}
                      >
                        {/* <span
                          className='w-2 h-2 rounded-full'
                          style={{ border: `1px solid ${getColorFromStatus(file.status)}`}}
                        /> */}
                        <span
                          className='text-[13px] leading-[14px]'
                          style={{ color: getColorFromStatus(file.status)}}
                        >
                          {getReadableStatus(file.status)}
                        </span>
                      </div>) : null}
                    </div>
                    <div className='px-2 py-1.5'>
                      <div className='text-sm'>{file.label}</div>
                      <div className='text-xs text-sub-text-black'>{file.fileName}</div>
                    </div>
                    <div className='px-2 py-1.5'>
                      <div className='text-xs text-sub-text-black'>Created at</div>
                      <div className='text-sm'>{getDateFromTimeStamp(file.createdAt)}</div>
                    </div>
                    <div className='px-2 py-1.5 flex justify-end gap-2'>
                      {file?.mode?.toLowerCase() === 'uploaded' && file?.approved ? (
                      <Button
                        type='text'
                        className='px-2 py-0.5 relative flex items-center text-xs text-text-black'
                        icon={<img src={upload} style={{ height: '100%', objectFit: 'contain' }} />}
                      >
                        Finalise
                        <input
                          type="file"
                          accept="application/pdf, image/*"
                          className='absolute z-[2] opacity-0 w-full'
                          onChange={(e) => onFileUpload(e, uploadToBeDoc, true)}
                        />
                      </Button>
                      ) : null}
                      {(canSendToCustomer())?<Button
                        disabled
                        type='text'
                        className='px-2 py-0.5 flex items-center text-xs text-text-black'
                        icon={
                          <SendOutlined />
                        }
                       
                        onClick={() =>{
                          getReviewConfirmationModal(
                            'Send to customer',
                            'Are you sure you want to send document to customer ? ',
                            () => {
                              sendDocToCustomer(file?.id)
                            },
                            () => {
                              console.log('cancelled');
                            }
                          );
                        }}
                      >
                        Send To Customer
                      </Button>:null}
                      <Button
                        type='text'
                        className='px-2 py-0.5 flex items-center text-xs text-text-black'
                        icon={
                          <Badge count={file.unresolvedComments} size="small">
                            <img src={review} style={{ height: '100%', objectFit: 'contain' }} />
                          </Badge>                      
                        }
                        onClick={() => handleDocPreview(file, false)}
                      >
                        Review
                      </Button>
                      <Button
                        type='text'
                        className='px-2 py-0.5 flex items-center text-xs text-text-black'
                        icon={
                          <BookOutlined />                      
                        }
                        onClick={()=>showDrawer(file?.eventsHistory)}
                      >
                        Event Timeline
                      </Button>
                      <Button
                        type='text'
                        className='px-2 py-0.5 flex items-center text-xs text-text-black'
                        icon={<img src={download} style={{ height: '100%', objectFit: 'contain' }} />}
                        onClick={() => onDownload(file)}
                      >
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
                {addDocuments?.added?.length ? null :
                  <div className='h-full p-8 flex items-center justify-center bg-neutral-100 text-gray-500'>
                    No document for review
                  </div>
                }
              </div>
            </div>
          </div>
        </>
        <DocumentSideBar visible={drawerVisible} onClose={closeDrawer} events={eventHistory} />
      </div>
    )
  );
}

export default DocumentPanel

DocumentPanel.propTypes = {
  order: PropTypes.object.isRequired,
  customerData: PropTypes.object.isRequired,
  updateDispatchOrder : PropTypes.func
}