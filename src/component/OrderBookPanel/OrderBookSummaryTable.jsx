import React, { useEffect, useState } from 'react';
import './OrderBookSummaryTable.css';
import PropTypes from 'prop-types';
import { getOrderTreeDataFromId } from '../../service/api/orderBookApi';
import { Spin, Card } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const OrderBookSummaryTable = ({orderbook}) => {

  const [orderBookData, setOrderBookData] = useState(null);
  const [loading, setLoading] = useState(false);

  // API call to get order book tree data
  useEffect(() => {
    const fetchOrderBookData = async () => {
      if (!orderbook?.id) {
        console.log("No orderbook ID available");
        return;
      }

      setLoading(true);
      try {
        const response = await getOrderTreeDataFromId(orderbook?.id);
        setOrderBookData(response?.data);
      } catch (error) {
        console.error("Error fetching order book data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderBookData();
  }, [orderbook?.id]);
  
  // Use API response data instead of props
  const customerData = {
    name: orderBookData?.customer || 'N/A',
    purchaseOrderNumber: orderBookData?.purchaseOrderNumber || 'N/A'
  };
  // Helper function to get total number of rows needed for CDO section
  const getTotalCDORows = () => {
    return (orderBookData?.customerOrders || []).reduce((total, cdo) => {
      const productCount = cdo.orderedProducts?.length || 0;
      return total + (productCount > 0 ? productCount : 1); // At least 1 row per CDO
    }, 0);
  };


  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '200px',
        padding: '40px'
      }}>
          <Spin 
            indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} 
            size="large"
          />
      </div>
    );
  }

  // --- Customer & CDO Table ---
  const cdoRows = (orderBookData?.customerOrders || []).flatMap((cdo, cdoIdx) => {
    const products = cdo.orderedProducts || [];
    
    if (products.length === 0) {
      // No products - show single row with "No Products"
      return (
        <tr key={cdo.orderId}>
          {cdoIdx === 0 && (
            <>
              <td rowSpan={getTotalCDORows()}>{customerData.name}</td>
              <td rowSpan={getTotalCDORows()}>{customerData.purchaseOrderNumber}</td>
            </>
          )}
          <td>{cdo.orderId}</td>
          <td colSpan={2} style={{ textAlign: 'center', color: '#aaa' }}>No Products</td>
        </tr>
      );
    }
    
    // Show each product in separate rows
    return products.map((product, productIdx) => (
      <tr key={`${cdo.orderId}-${productIdx}`}>
        {cdoIdx === 0 && productIdx === 0 && (
          <>
            <td rowSpan={getTotalCDORows()}>{customerData.name}</td>
            <td rowSpan={getTotalCDORows()}>{customerData.purchaseOrderNumber}</td>
          </>
        )}
        {productIdx === 0 && (
          <td rowSpan={products.length}>{cdo.orderId}</td>
        )}
        <td>{product.productName || 'N/A'}</td>
        <td>{product.quantity || 0}</td>
      </tr>
    ));
  });

  // --- Supplier & SDO Table ---
  const supplierRows = (orderBookData?.SupplierOrders || []).flatMap((so, soIdx) => {
    const supplierProducts = so.orderedProducts || [];
    const hasSDOs = so.dispatchOrders && so.dispatchOrders.length > 0;
    
    // Calculate rows needed for this specific supplier order
    const getSupplierOrderRows = (supplierOrder) => {
      if (supplierOrder.dispatchOrders && supplierOrder.dispatchOrders.length > 0) {
        return supplierOrder.dispatchOrders.reduce((total, sdo) => {
          const productCount = sdo.orderedProducts?.length || 0;
          return total + (productCount > 0 ? productCount : 1);
        }, 0);
      }
      return 1; // Single row for "No SDO"
    };
    
    const rowsForThisSupplier = getSupplierOrderRows(so);
    
    if (hasSDOs) {
      return so.dispatchOrders.flatMap((sdo, sdoIdx) => {
        const sdoProducts = sdo.orderedProducts || [];
        
        if (sdoProducts.length === 0) {
          // No products in SDO - show single row with "No Products"
          return (
            <tr key={so.id + '-' + sdoIdx}>
              {sdoIdx === 0 && (
                <>
                  <td rowSpan={rowsForThisSupplier}>{so.supplierName}</td>
                  <td rowSpan={rowsForThisSupplier}>{so.purchaseOrderNumber}</td>
                </>
              )}
              <td>{sdo.orderId}</td>
              <td colSpan={2} style={{ textAlign: 'center', color: '#aaa' }}>No Products</td>
            </tr>
          );
        }
        
        // Show each product in SDO in separate rows
        return sdoProducts.map((product, productIdx) => (
          <tr key={`${so.id}-${sdoIdx}-${productIdx}`}>
            {sdoIdx === 0 && productIdx === 0 && (
              <>
                <td rowSpan={rowsForThisSupplier}>{so.supplierName}</td>
                <td rowSpan={rowsForThisSupplier}>{so.purchaseOrderNumber}</td>
              </>
            )}
            {productIdx === 0 && (
              <td rowSpan={sdoProducts.length}>{sdo.orderId}</td>
            )}
            <td>{product.productName || 'N/A'}</td>
            <td>{product.quantity || 0}</td>
          </tr>
        ));
      });
    } else {
      // No SDOs - show single row with "No SDO"
      return (
        <tr key={so.id}>
          <td>{so.supplierName}</td>
          <td>{so.purchaseOrderNumber}</td>
          <td colSpan={3} style={{ textAlign: 'center', color: '#aaa' }}>No SDO</td>
        </tr>
      );
    }
  });

  return (
    <div className="orderbook-summary-table-container">
      {/* Customer & CDO Table */}
      <table className="orderbook-summary-table">
        <thead>
          <tr>
            <th colSpan="2">Customer Details</th>
            <th colSpan="3">CDO Details</th>
          </tr>
          <tr>
            <th>Customer Name</th>
            <th>PO #</th>
            <th>CDO No.</th>
            <th>Chemical Name</th>
            <th>Qty</th>
          </tr>
        </thead>
        <tbody>
          {(orderBookData?.customerOrders?.length > 0
            ? cdoRows
            : (
                <tr>
                  <td>{customerData.name}</td>
                  <td>{customerData.purchaseOrderNumber}</td>
                  <td colSpan={3} style={{ textAlign: 'center', color: '#aaa' }}>No CDOs</td>
                </tr>
              )
          )}
        </tbody>
      </table>

      {/* Supplier & SDO Table */}
      {orderBookData?.SupplierOrders?.length > 0 && (
        <table className="orderbook-summary-table" style={{ marginTop: '24px' }}>
          <thead>
            <tr>
              <th colSpan="2">Supplier Details</th>
              <th colSpan="3">SDO Details</th>
            </tr>
            <tr>
              <th>Supplier Name</th>
              <th>Supplier Order #</th>
              <th>SDO #</th>
              <th>Chemical Name</th>
              <th>Qty</th>
            </tr>
          </thead>
          <tbody>{supplierRows}</tbody>
        </table>
      )}
    </div>
  );
};

OrderBookSummaryTable.propTypes = {
  orderbook: PropTypes.shape({
    id: PropTypes.string,
    customer: PropTypes.string,
    purchaseOrderNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    customerOrders: PropTypes.arrayOf(
      PropTypes.shape({
        orderId: PropTypes.string,
        purchaseOrderNumber: PropTypes.string,
        id: PropTypes.string,
        customerName: PropTypes.string,
        status: PropTypes.string,
        orderedProducts: PropTypes.arrayOf(
          PropTypes.shape({
            quantity: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            productName: PropTypes.string,
          })
        ),
      })
    ),
    SupplierOrders: PropTypes.arrayOf(
      PropTypes.shape({
        supplierName: PropTypes.string,
        orderBookId: PropTypes.string,
        purchaseOrderNumber: PropTypes.string,
        id: PropTypes.string,
        dispatchOrders: PropTypes.arrayOf(
          PropTypes.shape({
            orderId: PropTypes.string,
            purchaseOrderNumber: PropTypes.string,
            id: PropTypes.string,
            orderedProducts: PropTypes.arrayOf(
              PropTypes.shape({
                quantity: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
                productName: PropTypes.string,
              })
            ),
          })
        ),
        orderedProducts: PropTypes.arrayOf(
          PropTypes.shape({
            quantity: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            productName: PropTypes.string,
          })
        ),
      })
    ),
    products: PropTypes.arrayOf(
      PropTypes.shape({
        quantity: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        productName: PropTypes.string,
      })
    ),
  })
};

export default OrderBookSummaryTable; 