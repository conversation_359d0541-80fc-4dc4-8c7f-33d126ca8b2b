import React from 'react';
import { useDispatch } from 'react-redux';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import SupplierListView from './SupplierListView';

const SupplierPanel = () => {
  const dispatch = useDispatch();

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };

  return (
    <SupplierListView
      collapseAsideBarHandler={collapseAsideBarHandler}
      collapseSideDrawerHandler={collapseSideDrawerHandler}
    />
  );
};

export default SupplierPanel;
