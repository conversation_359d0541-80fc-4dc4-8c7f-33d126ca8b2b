import React from 'react';
import { isMstackOrder } from '../../util/userUtils';

export const templateObj = {
  MSTACK_TO_CHEMSTACK_PO: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm 5mm 2mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            src="https://mstack-assets.s3.amazonaws.com/logos/mstack-logo-1.svg"
            alt="header logo"
          ></img>
        </div>
        <div
          style={{
            marginTop: '1mm',
            textAlign: 'center',
            fontWeight: '600',
          }}
        >
          {formData?.MSTACK_ADDRESS}
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        PURCHASE ORDER
      </div>
      <div
        style={{
          margin: '0 auto',
          display: 'table',
          width: '209mm',
        }}
      >
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              padding: '2mm',
              boxSizing: 'border-box',
              width: '145mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <p
              style={{
                margin: 0,
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              To,
            </p>
            <p>Chemstack Private Limited</p>
            <p
              style={{
                margin: '2.5mm 0',
              }}
            >
              {formData?.CHEMSTACK_ADDRESS}
            </p>
          </div>
          <div
            style={{
              display: 'table-cell',
              padding: '2mm',
              boxSizing: 'border-box',
              width: '64mm',
              borderBottom: '1px solid',
              background: '#f3f3f3',
            }}
          >
            <div
              style={{
                marginBottom: '3mm',
              }}
            >
              <span
                style={{
                  marginBottom: '3mm',
                  marginRight: '1mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                PO No.:
              </span>
              <span>{formData?.PO_NUMBER}</span>
            </div>
            <div>
              <span
                style={{
                  color: '#28388e',
                  fontWeight: '600',
                  marginRight: '1mm',
                }}
              >
                Date:
              </span>
              <span>{formData?.PO_DATE}</span>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              padding: '0',
              boxSizing: 'border-box',
              width: '145mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                margin: '3mm auto',
                width: '99.5%',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              Attn. Mr. Prateek Singh
            </div>
            <div
              style={{
                margin: '3mm auto',
                width: '99.5%',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              Below is our order confirmation:
            </div>
          </div>
          <div
            style={{
              display: 'table-cell',
              padding: '0',
              boxSizing: 'border-box',
              width: '63mm',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                margin: '3mm auto',
                width: '99.5%',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              <span>Currency:</span>
              <span>{formData?.BUYER_CURRENCY}</span>
            </div>
          </div>
        </div>
      </div>
      <table
        style={{
          width: '210mm',
          minHeight: '100mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead
          style={{
            background: '#28388e',
            color: '#fff',
          }}
        >
          <tr>
            <th
              style={{
                width: '5mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              No.
            </th>
            <th
              style={{
                width: '61mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Item
            </th>
            <th
              style={{
                width: '45mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Packing
            </th>
            <th
              style={{
                width: '32mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Quantity
            </th>
            <th
              style={{
                height: '30px',
                fontSize: '10px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Price/Unit
            </th>
            <th
              style={{
                height: '30px',
                fontSize: '10px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.PRODUCTS_DATA?.map((row, index) => (
            <tr key={row?.ITEM}>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {index + 1}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.ITEM}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.PACKAGING}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.QUANTITY}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.UNIT_PRICE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {row?.TOTAL_PRICE}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div
        style={{
          display: 'table',
        }}
      >
        <div
          style={{
            display: 'table-cell',
            width: '146mm',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div
            style={{
              margin: '10mm 0 5mm',
              padding: '2mm',
              background: '#f3f3f3',
            }}
          >
            <div
              style={{
                display: 'inline-block',
                color: '#28388e',
                fontWeight: '600',
                marginRight: '1mm',
              }}
            >
              Says:
            </div>
            <span>{formData?.TOTAL_PRICE_WORDS}</span>
          </div>
          <div
            style={{
              display: 'table',
              width: '146mm',
            }}
          >
            <div
              style={{
                display: 'table-row',
                background: '#f3f3f3',
              }}
            >
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Term of Payment :
              </div>
              <span>{formData?.PAYMENT_TERMS}</span>
            </div>
            <div
              style={{
                display: 'table-row',
              }}
            >
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Shipment Term :
              </div>
              <span>{formData?.SHIPPMENT_TERMS}</span>
            </div>
            <div
              style={{
                display: 'table-row',
                background: '#f3f3f3',
              }}
            >
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Shipping Method :
              </div>
              <span>{formData?.SHIPPMENT_METHOD}</span>
            </div>
            <div
              style={{
                display: 'table-row',
              }}
            >
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Expected ETD :
              </div>
              <span>{formData?.ETD}</span>
            </div>
            <div
              style={{
                display: 'table-row',
                background: '#f3f3f3',
              }}
            >
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Expected ETA :
              </div>
              <span>{formData?.ETA}</span>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-cell',
            verticalAlign: 'top',
            width: '63mm',
            borderBottom: '1px solid',
          }}
        >
          <div
            style={{
              padding: '1.5mm',
              backgroundColor: '#28388e',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <span
              style={{
                color: '#fff',
                fontWeight: '700',
              }}
            >
              Subtotal:
            </span>
            <span
              style={{
                color: '#fff',
                fontWeight: '700',
                marginRight: '8mm',
              }}
            >
              {formData?.TOTAL_PRICE}
            </span>
          </div>
        </div>
      </div>
      <div
        style={{
          padding: '4mm',
          display: 'table',
        }}
      >
        <div
          style={{
            width: '103mm',
            display: 'table-cell',
          }}
        >
          <div>
            <div
              style={{
                fontWeight: '700',
                marginBottom: '2mm',
              }}
            >
              Mstack Inc.
            </div>
            <div
              style={{
                fontWeight: '700',
                marginBottom: '2mm',
              }}
            >
              Confirmed & signed
            </div>
          </div>
          <div>
            <img
              src="https://mstack-assets.s3.amazonaws.com/logos/mstack_stamp.png"
              alt="header logo"
              style={{ width: '40mm' }}
            ></img>
          </div>
          <div
            style={{
              marginTop: '2mm',
            }}
          >
            (Shreyans Chopra)
          </div>
        </div>
        <div
          style={{
            width: '103mm',
            display: 'table-cell',
            textAlign: 'right',
          }}
        >
          <div>
            <div
              style={{
                fontWeight: '700',
                marginBottom: '2mm',
              }}
            >
              Chemstack Private Limited
            </div>
            <div
              style={{
                fontWeight: '700',
                marginBottom: '2mm',
              }}
            >
              Confirmed & Accepted,
            </div>
          </div>
          <div>
            <img
              src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_stamp.png"
              alt="header logo"
              style={{ width: '40mm' }}
            ></img>
          </div>
          <div
            style={{
              marginTop: '2mm',
            }}
          >
            (Prateek Singh)
          </div>
        </div>
      </div>
    </div>
  ),
  CHEMSTACK_COMMERCIAL_INVOICE: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm 5mm 2mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            style={{ width: '100mm' }}
            src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_logo.png"
            alt="header logo"
          />
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        INVOICE
      </div>
      <div>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <div
            style={{
              padding: '2mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div>
              <p
                style={{
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                EXPORTER -
              </p>
              <p>Chemstack Private Limited</p>
              <p>{formData.CHEMSTACK_ADDRESS}</p>
            </div>
          </div>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              padding: '2mm',
              borderBottom: '1px solid',
            }}
          >
            <div>
              <div
                style={{
                  marginBottom: '1mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Invoice Number
              </div>
              <div>{formData.INVOICE_NUMBER}</div>
            </div>
            <div>
              <div
                style={{
                  marginBottom: '1mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Invoice Date
              </div>
              <div>{formData.INVOICE_DATE}</div>
            </div>
            <div>
              <div
                style={{
                  marginBottom: '1mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Buyer&apos;s Order No.
              </div>
              <div>{formData.BUYER_ORDER_NO}</div>
            </div>
            <div>
              <div
                style={{
                  marginBottom: '1mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Date
              </div>
              <div>{formData.BUYER_ORDER_DATE}</div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <div
            style={{
              padding: '2mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Consignee
            </div>
            <div style={{ marginTop: '3mm' }}>{formData.CONSIGNEE_ADDRESS}</div>
          </div>
          <div
            style={{
              padding: '2mm',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Buyer
            </div>
            <div style={{ marginTop: '2mm' }}>Mstack Inc.</div>
            <div>{formData.MSTACK_ADDR}</div>
          </div>
        </div>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <div
            style={{
              borderRight: '1px solid',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Method of Dispatch
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.METHOD_OF_DISPATCH}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Type of Shipment
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.TYPE_OF_SHIPMENT}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Vessel / Aircraft No.
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.VESSEL_NO}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Voyage No.
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.VOYAGE_NO}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Port of Loading
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.PORT_OF_LOADING}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Port of Discharge
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.PORT_OF_DISCHARGE}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Final Destination
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.FINAL_DESTINATION}</div>
            </div>
            <div
              style={{
                margin: '0 auto',
                width: '99.5%',
              }}
            >
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Date of Departure
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.DEPARTURE_DATE}</div>
            </div>
          </div>
          <div
            style={{
              margin: '0 auto',
              width: '99.5%',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Country Of Origin of Goods
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.COUNTRY_OF_ORIGIN_OF_GOODS}</div>
            </div>
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Country of Final Destination
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.COUNTRY_OF_FINAL_DESTINATION}</div>
            </div>
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Terms / Method of Payment
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.PAYMENT_TERM}</div>
            </div>
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Term of Shipment
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.TERM_OF_SHIPMENT}</div>
            </div>
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Marine Cover Policy No.
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.MARINE_COVER_POLICY_NO}</div>
            </div>
            <div>
              <div
                style={{
                  margin: '2mm 0 1mm 0',
                  padding: '1mm',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Invoice due date
              </div>
              <div style={{ marginLeft: '2mm' }}>{formData.INVOICE_DUE_DATE}</div>
            </div>
          </div>
        </div>
      </div>
      <table
        style={{
          width: '210mm',
          minHeight: '100mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead style={{ backgroundColor: '#28388e', color: '#fff' }}>
          <tr>
            <th
              style={{
                padding: '1.5mm',
                width: '10mm',
                height: '30px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              S. NO.
            </th>
            <th
              style={{
                padding: '1.5mm',
                width: '50mm',
                height: '30px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Description of Goods
            </th>
            <th
              style={{
                padding: '1.5mm',
                width: '40mm',
                height: '30px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              HS Code
            </th>
            <th
              style={{
                padding: '1.5mm',
                width: '32mm',
                height: '30px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Quantity
            </th>
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                height: '30px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Unit
            </th>
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                height: '30px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Price (in USD)
            </th>
            <th
              style={{
                padding: '1.5mm',
                borderRight: 'none',
                fontSize: '10px',
                height: '30px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Amount (in USD)
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.TABLE_DATA?.map((row, index) => (
            <tr key={index}>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {index + 1}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row.DESCRIPTION}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row.HS_CODE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row.QUANTITY}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row.UNIT}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row.PRICE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  fontSize: '10px',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {row.AMOUNT}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
        }}
      >
        <div
          style={{
            padding: '2mm',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div style={{ color: '#28388e', fontWeight: '600' }}>Bank Details</div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>Account Name :</span>
            <span>{formData.ACCOUNT_NAME}</span>
          </div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>Bank Account Number :</span>
            <span>{formData.ACCOUNT_NUMBER}</span>
          </div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>Bank Name :</span>
            <span>{formData.BANK_NAME}</span>
          </div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>IFS Code :</span>
            <span>{formData.IFS_CODE}</span>
          </div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>MICR Code :</span>
            <span>{formData.MICR_CODE}</span>
          </div>
          <div style={{ marginTop: '1mm' }}>
            <span style={{ color: '#28388e', fontWeight: '600' }}>Bank Address :</span>
            <span>{formData.BANK_ADDRESS}</span>
          </div>
        </div>
        <div style={{ borderBottom: '1px solid' }}>
          <div
            style={{
              padding: '2mm',
              width: '100%',
              color: '#fff',
              fontWeight: '600',
              background: '#28388e',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <span>Total</span>
            <span style={{ marginRight: '12mm' }}>{formData.TOTAL}</span>
          </div>
          <div
            style={{
              margin: '0 auto',
              width: '99.5%',
              padding: '2mm',
              display: 'flex',
              justifyContent: 'flex-start',
              gap: '2mm',
              color: ' #28388e',
              fontWeight: '600',
              background: '#f3f3f3',
            }}
          >
            <div>Amount chargeable(USD):</div>
            <div>{formData.TOTAL_IN_WORDS}</div>
          </div>
          <div style={{ marginTop: '2mm', textAlign: 'right' }}>
            <div style={{ fontWeight: 600, color: '#28388e' }}>
              <div>FOR CHEMSTACK PRIVATE LIMITED</div>
            </div>
            <div>
              <img
                src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_stamp.png"
                alt="header logo"
              />
            </div>
            <div style={{ fontWeight: 600, color: '#28388e' }}>AUTHORISED SIGNATORY</div>
          </div>
        </div>
      </div>
      <div style={{ padding: '2mm' }}>
        <div>Declaration :</div>
        <div style={{ marginTop: '2mm' }}>
          {`We declare that this Invoice shows the actual price of the goods
          .described and that all particulars are true and correct. Export made
          under Letter of Undertaking for export of goods or services without
          payment of integrated tax [See rule 96A] issued by The President of India
          acting through the proper officer. LUT number: ${formData.LUT_NUMBER},
          dated: ${formData.LUT_DATE}, Vaild Till ${formData.LUT_VALID_DATE}`}
        </div>
      </div>
    </div>
  ),
  CHEMSTACK_PACKAGING_LIST: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_logo.png"
            alt="header logo"
            style={{ width: '100mm' }}
          />
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        PACKING LIST
      </div>
      <div style={{ margin: 'auto', display: 'table', width: '208mm' }}>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              width: '104mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                padding: '2mm',
              }}
            >
              <p>EXPORTER -</p>
              <p>Chemstack Private Limited</p>
              <p>{formData.CHEMSTACK_ADDRESS}</p>
            </div>
            <div
              style={{
                margin: '2mm auto auto',
                width: '99.5%',
                padding: '1mm 2mm',
                borderTop: '1px solid',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              <span>Mob.:</span>
              <span>{formData.MOBILE_NUMBER}</span>
            </div>
          </div>
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              width: '104mm',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
              }}
            >
              <div
                style={{
                  padding: '2mm',
                  width: '50mm',
                }}
              >
                <div
                  style={{
                    marginBottom: '1mm',
                    color: '#28388e',
                    fontWeight: '600',
                  }}
                >
                  Invoice No.
                </div>
                <div>{formData.INVOICE_NUMBER}</div>
              </div>
              <div
                style={{
                  padding: '2mm',
                  width: '50mm',
                }}
              >
                <div
                  style={{
                    marginBottom: '1mm',
                    color: '#28388e',
                    fontWeight: '600',
                  }}
                >
                  Invoice Date
                </div>
                <div>{formData.INVOICE_DATE}</div>
              </div>
              <div
                style={{
                  padding: '2mm',
                  width: '50mm',
                }}
              >
                <div
                  style={{
                    marginBottom: '1mm',
                    color: '#28388e',
                    fontWeight: '600',
                  }}
                >
                  Buyer&apos;s Order No.:
                </div>
                <div>{formData.BUYER_ORDER_NO}</div>
              </div>
              <div
                style={{
                  padding: '2mm',
                  width: '50mm',
                }}
              >
                <div
                  style={{
                    marginBottom: '1mm',
                    color: '#28388e',
                    fontWeight: '600',
                  }}
                >
                  Buyer&apos;s Order Date:
                </div>
                <div>{formData.BUYER_ORDER_DATE}</div>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              borderRight: '1px solid',
              borderBottom: '1px solid',
              display: 'table-cell',
              boxSizing: 'border-box',
            }}
          >
            <div
              style={{
                margin: '5mm auto 0',
                padding: '1mm 2mm',
                width: '99.5%',
                borderTop: '1px solid',
                borderBottom: '1px solid',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Consignee
            </div>
            <div style={{ marginTop: '8mm', padding: '2mm' }}>{formData.CONSIGNEE_ADDRESS}</div>
          </div>
          <div style={{ display: 'table-cell', boxSizing: 'border-box' }}>
            <div
              style={{
                margin: '5mm auto 0',
                padding: '1mm 2mm',
                width: '99.5%',
                borderTop: '1px solid',
                borderBottom: '1px solid',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Other Reference(s)
            </div>
            <div style={{ marginTop: '1mm', padding: '1mm 2mm' }}>
              <span>I.E.C. NO.</span>
              <span>{formData.IEC_NO}</span>
            </div>
            <div
              style={{ margin: 'auto', width: '99.5%', padding: '1mm 2mm', background: '#f3f3f3' }}
            >
              <span>PAN NO.</span>
              <span>{formData.PAN_NO}</span>
            </div>
            <div style={{ padding: '1mm 2mm' }}>
              <span>GSTIN NO.:</span>
              <span>{formData.GSTIN_NO}</span>
            </div>
            <div
              style={{ margin: 'auto', width: '99.5%', padding: '1mm 2mm', background: '#f3f3f3' }}
            >
              <span>CIN:</span>
              <span>{formData.CIN_NO}</span>
            </div>
            <div style={{ padding: '1mm 2mm' }}>
              <span>Trade Agreement Code:</span>
              <span>{formData.TRADE_CODE}</span>
            </div>
            <div style={{ margin: 'auto', width: '99.5%' }}>
              <div
                style={{
                  marginTop: '2mm',
                  padding: '1mm 2mm',
                  borderTop: '1px solid',
                  borderBottom: '1px solid',
                  background: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Buyer
              </div>
              <div style={{ marginTop: '2mm', paddingLeft: '2mm' }}>Mstack Inc.</div>
              <div style={{ padding: '1mm 2mm' }}>{formData.MSTACK_ADDR}</div>
              <div
                style={{
                  padding: '2mm',
                  borderTop: '1px solid',
                  borderBottom: '1px solid',
                  background: '#f3f3f3',
                }}
              >
                AMENDED FROM TIME TO TIME, INCLUDING REALIZATION/REPATRIATION OF FOREIGN EXCHANGE TO
                /FROM INDIA.
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              borderRight: '1px solid',
            }}
          >
            <div
              style={{
                margin: 'auto',
                width: '99.5%',
                padding: '1mm',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Terms of delivery:
            </div>
            <div style={{ padding: '1mm' }}>{formData.DELIVERY_TERMS}</div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '99.5%',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Notify Party:
            </div>
            <div style={{ padding: '1mm' }}>{formData.NOTIFY_PARTY}</div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '99.5%',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Country of Origin of Goods:
            </div>
            <div style={{ padding: '1mm' }}>{formData.COUNTRY_OF_ORIGIN}</div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '99.5%',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Country of Final Destination:
            </div>
            <div style={{ padding: '1mm' }}>{formData.COUNTRY_OF_DESTINATION}</div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '99.5%',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Place of Delivery :
            </div>
            <div style={{ padding: '1mm' }}>{formData.PLACE_OF_DELIVERY}</div>
          </div>
          <div style={{ display: 'table-cell', boxSizing: 'border-box' }}>
            <div
              style={{
                margin: 'auto',
                display: 'table',
                width: '103mm',
              }}
            >
              <div
                style={{
                  display: 'table-row',
                }}
              >
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      background: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Pre-carriage by:
                  </div>
                  <div
                    style={{
                      padding: '2mm',
                    }}
                  >
                    {formData.PRECARRIAGE}
                  </div>
                </div>
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      background: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Port of Loading:
                  </div>
                  <div
                    style={{
                      padding: '2mm',
                    }}
                  >
                    {formData.PORT_OF_LOADING}
                  </div>
                </div>
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      background: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Port of Discharge:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.PORT_OF_DISCHARGE}</div>
                </div>
              </div>
              <div
                style={{
                  display: 'table-row',
                }}
              >
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      background: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Vessel/ Flight No.:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.VESSEL_FLIGHT_NO}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <table
        style={{
          width: '210mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead style={{ backgroundColor: '#28388e', color: '#fff' }}>
          <tr>
            <th
              style={{
                width: '20mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Marks &amp; Nos. / Container Number(s)
            </th>
            <th
              style={{
                width: '61mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              No. &amp; Kinds of Packages
            </th>
            <th
              style={{
                width: '45mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Description
            </th>
            <th
              style={{
                width: '32mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Export HS Code
            </th>
            {/* <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Qty. (Kgs)
            </th> */}
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Net weight (Kgs)
            </th>
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Gross weight (kgs)
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.TABLE_DATA?.map((item, index) => (
            <tr key={index}>
              {index === 0 ? (
                <td
                  style={{
                    padding: '1mm',
                    textAlign: 'center',
                    verticalAlign: 'top',
                    fontSize: '10px',
                    border: '1px solid black',
                    borderCollapse: 'collapse',
                    borderLeft: 'none',
                  }}
                  rowSpan={formData?.TABLE_DATA?.length}
                >
                  {formData.CONTAINER_NO}
                </td>
              ) : null}
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.PACKAGES
                  ? item.PACKAGES.map((packageData, index) => (
                      <div
                        key={index}
                        style={{
                          margin: '0 2mm',
                          padding: '2mm 0',
                          borderBottom: index === item.PACKAGES.length - 1 ? '' : '1px dashed',
                        }}
                      >
                        {packageData.map((data, index) => (
                          <div key={index}>{data}</div>
                        ))}
                      </div>
                    ))
                  : null}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.DESCRIPTION}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.HS_CODE}
              </td>
              {/* <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >{item.QUANTITY}</td> */}
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {item.NET_WEIGHT}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.GROSS_WEIGHT}
              </td>
            </tr>
          ))}
          <tr>
            <td
              style={{
                padding: '1mm',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            />
            <td
              style={{
                padding: '1mm',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              {formData.PALLET_DATA}
            </td>
            <td />
            <td />
            <td />
            {/* <td /> */}
            <td
              style={{
                padding: '1mm',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              {formData.PALLET_TOTAL}
            </td>
          </tr>
        </tbody>
      </table>
      <div
        style={{
          marginLeft: 'auto',
          width: '60mm',
          display: 'flex',
          flexDirection: 'column',
          background: '#28388e',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '2mm',
            color: '#fff',
            fontWeight: '600',
          }}
        >
          <span>Gross weight(kgs)</span>
          <span style={{ marginRight: '8mm' }}>{formData.TOTAL_GROSS_WEIGHT}</span>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '2mm',
            color: '#fff',
            fontWeight: '600',
          }}
        >
          <span>Net Weight(kgs)</span>
          <span style={{ marginRight: '8mm' }}>{formData.TOTAL_NET_WEIGHT}</span>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '2mm',
            color: '#fff',
            fontWeight: '600',
          }}
        >
          <span>Tare Weight(kgs)</span>
          <span style={{ marginRight: '8mm' }}>{formData.TOTAL_TARE_WEIGHT}</span>
        </div>
      </div>
      <div
        style={{
          borderTop: '1px solid',
          display: 'flex',
          pageBreakInside: 'avoid',
        }}
      >
        <div
          style={{
            width: '140mm',
            borderRight: '1px solid',
            fontWeight: 600,
            color: '#28388e',
            padding: '2mm',
          }}
        >
          <div>Declaration :</div>
          <div style={{ marginTop: '2mm' }}>
            We declare that this invoice shows the actual price of the goods described above and
            that all particulars stated herein are true and correct.
          </div>
        </div>
        <div style={{ padding: '2mm', textAlign: 'right' }}>
          <div style={{ fontWeight: 600, color: '#28388e' }}>
            <div>FOR CHEMSTACK PRIVATE LIMITED</div>
          </div>
          <div>
            <img
              style={{ width: '40mm' }}
              src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_stamp.png"
              alt="header logo"
            />
          </div>
          <div style={{ fontWeight: 600, color: '#28388e' }}>AUTHORISED SIGNATORY</div>
        </div>
      </div>
    </div>
  ),
  CHEMSTACK_TAX_INVOICE: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_logo.png"
            alt="header logo"
            style={{ width: '100mm' }}
          />
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        INVOICE
      </div>
      <div style={{ display: 'table', width: '209mm' }}>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              width: '104mm',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                padding: '2mm',
              }}
            >
              <p>EXPORTER -</p>
              <p>Chemstack Private Limited</p>
              <p>{formData.CHEMSTACK_ADDRESS}</p>
            </div>
            <div
              style={{
                marginTop: '2mm',
                padding: '1mm 2mm',
                width: '104mm',
                borderTop: '1px solid',
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              <span>Mob.:</span>
              <span>{formData.MOBILE_NUMBER}</span>
            </div>
          </div>
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              width: '104mm',
              borderBottom: '1px solid',
            }}
          >
            <div style={{ display: 'table' }}>
              <div
                style={{
                  padding: '2mm',
                  display: 'table-cell',
                  width: '50mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                <div>GST/Tax Invoice Number</div>
                <div>{formData.INVOICE_NUMBER}</div>
              </div>
              <div
                style={{
                  padding: '2mm',
                  display: 'table-cell',
                  width: '50mm',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                <div>Invoice Date</div>
                <div>{formData.INVOICE_DATE}</div>
              </div>
            </div>
            <div
              style={{
                marginTop: '9mm',
                padding: '3mm 2mm',
                color: '#28388e',
                fontWeight: '600',
                borderTop: '1px solid',
              }}
            >
              <span>Buyer&apos;s Order No.: </span>
              <span>{formData.BUYER_ORDER_NO}</span>
              <span style={{ margin: '0 2mm' }}>|</span>
              <span>Date: </span>
              <span>{formData.BUYER_ORDER_DATE}</span>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              borderRight: '1px solid',
              borderBottom: '1px solid',
            }}
          >
            <div
              style={{
                marginTop: '5mm',
                padding: '1mm 2mm',
                width: '104mm',
                borderTop: '1px solid',
                borderBottom: '1px solid',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Consignee
            </div>
            <div
              style={{
                marginTop: '8mm',
                padding: '2mm',
              }}
            >
              {formData.CONSIGNEE_ADDRESS}
            </div>
          </div>
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
            }}
          >
            <div
              style={{
                margin: '5mm auto auto',
                padding: '1mm 2mm',
                width: '104mm',
                borderTop: '1px solid',
                borderBottom: '1px solid',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Other Reference(s)
            </div>
            <div
              style={{
                marginTop: '1mm',
                padding: '1mm 2mm',
              }}
            >
              <span>I.E.C. NO.</span>
              <span>{formData.IEC_NO}</span>
            </div>
            <div
              style={{
                margin: 'auto',
                padding: '1mm 2mm',
                width: '104mm',
                background: '#f3f3f3',
              }}
            >
              <span>PAN NO.</span>
              <span>{formData.PAN_NO}</span>
            </div>
            <div
              style={{
                padding: '1mm 2mm',
              }}
            >
              <span>GSTIN NO.:</span>
              <span>{formData.GSTIN_NO}</span>
            </div>
            <div
              style={{
                padding: '1mm 2mm',
                width: '104mm',
                background: '#f3f3f3',
              }}
            >
              <span>CIN:</span>
              <span>{formData.CIN_NO}</span>
            </div>
            <div
              style={{
                padding: '1mm 2mm',
              }}
            >
              <span>Trade Agreement Code:</span>
              <span>{formData.TRADE_CODE}</span>
            </div>
            <div
              style={{
                margin: 'auto',
                width: '104mm',
              }}
            >
              <div
                style={{
                  marginTop: '2mm',
                  padding: '1mm 2mm',
                  borderTop: '1px solid',
                  borderBottom: '1px solid',
                  backgroundColor: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Buyer
              </div>
              <div
                style={{
                  marginTop: '1mm',
                  padding: '2mm',
                }}
              >
                <div>Mstack Inc.</div>
                {formData.MSTACK_ADDR}
              </div>
              <div
                style={{
                  padding: '2mm',
                  borderTop: '1px solid',
                  borderBottom: '1px solid',
                  background: ' #f3f3f3',
                }}
              >
                AMENDED FROM TIME TO TIME, INCLUDING REALIZATION/REPATRIATION OF FOREIGN EXCHANGE TO
                /FROM INDIA.
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'table-row',
          }}
        >
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
              borderRight: '1px solid',
            }}
          >
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '104mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Notify Party:
            </div>
            <div
              style={{
                padding: '1mm',
              }}
            >
              {formData.NOTIFY_PARTY}
            </div>
            {/* <div
              style={{
                padding: '1mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600'
              }}
            >
              Place of reciept:
            </div> */}
            <div
              style={{
                padding: '1mm',
              }}
            >
              {formData.PLACE_OF_RECIEPT}
            </div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '104mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Country of Origin of Goods:
            </div>
            <div
              style={{
                padding: '1mm',
              }}
            >
              {formData.COUNTRY_OF_ORIGIN}
            </div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '104mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Country of Final Destination:
            </div>
            <div
              style={{
                padding: '1mm',
              }}
            >
              {formData.COUNTRY_OF_DESTINATION}
            </div>
            <div
              style={{
                padding: '1mm',
                margin: 'auto',
                width: '104mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Place of Delivery :
            </div>
            <div
              style={{
                padding: '1mm',
              }}
            >
              {formData.PLACE_OF_DELIVERY}
            </div>
          </div>
          <div
            style={{
              display: 'table-cell',
              boxSizing: 'border-box',
            }}
          >
            <div>
              <div
                style={{
                  padding: '1mm',
                  margin: 'auto',
                  width: '104mm',
                  backgroundColor: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Terms of delivery:
              </div>
              <div style={{ padding: '1mm' }}>{formData.DELIVERY_TERMS}</div>
            </div>
            <div
              style={{
                margin: '5mm auto auto',
                display: 'table',
                width: '104mm',
              }}
            >
              <div
                style={{
                  display: 'table-row',
                }}
              >
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      backgroundColor: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Pre-carriage by:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.PRECARRIAGE}</div>
                </div>
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      backgroundColor: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Port of Loading:
                  </div>
                  <div
                    style={{
                      padding: '2mm',
                    }}
                  >
                    {formData.PORT_OF_LOADING}
                  </div>
                </div>
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      backgroundColor: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Payment term:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.PAYMENT_TERM}</div>
                </div>
              </div>
              <div
                style={{
                  display: 'table-row',
                }}
              >
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      backgroundColor: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Vessel/ Flight No.:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.VESSEL_FLIGHT_NO}</div>
                </div>
                <div
                  style={{
                    display: 'table-cell',
                  }}
                >
                  <div
                    style={{
                      padding: '2mm',
                      backgroundColor: '#f3f3f3',
                      color: '#28388e',
                      fontWeight: '600',
                    }}
                  >
                    Port of Discharge:
                  </div>
                  <div style={{ padding: '2mm' }}>{formData.PORT_OF_DISCHARGE}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <table
        style={{
          width: '210mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead style={{ backgroundColor: ' #28388e', color: '#fff' }}>
          <tr>
            <th
              style={{
                width: '5mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              S.No.
            </th>
            <th
              style={{
                width: '61mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Product
            </th>
            <th
              style={{
                width: '45mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              District of Origin
            </th>
            <th
              style={{
                width: '32mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              State of Origin
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.PRODUCT_ORIGIN_DATA?.map((item, index) => (
            <tr key={index}>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {index + 1}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.PRODUCT}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.DISTRICT_OF_ORIGIN}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.STATE_OF_ORIGIN}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <table
        style={{
          width: '210mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead style={{ backgroundColor: ' #28388e', color: '#fff' }}>
          <tr>
            <th
              style={{
                width: '5mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Marks & Nos. / Container Number(s)
            </th>
            <th
              style={{
                width: '61mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              No. & Kinds of Packages
            </th>
            <th
              style={{
                width: '45mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Description
            </th>
            <th
              style={{
                width: '32mm',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Export HS Code
            </th>
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Qty. (Kgs)
            </th>
            <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Rate (In USD)
            </th>
            <th
              style={{
                borderRight: 'none',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Total (In USD)
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.TABLE_DATA?.map((item, index) => (
            <tr key={index}>
              {index === 0 ? (
                <td
                  style={{
                    padding: '1mm',
                    textAlign: 'center',
                    fontSize: '10px',
                    border: '1px solid black',
                    borderCollapse: 'collapse',
                    borderLeft: 'none',
                  }}
                  rowSpan={formData?.TABLE_DATA?.length}
                >
                  {formData.CONTAINER_NUMBER}
                </td>
              ) : null}
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.PACKAGES
                  ? item.PACKAGES.map((packageData, index) => (
                      <div
                        key={index}
                        style={{
                          margin: '0 2mm',
                          padding: '2mm 0',
                          borderBottom: index === item.PACKAGES.length - 1 ? '' : '1px dashed',
                        }}
                      >
                        {packageData.map((data, index) => (
                          <div key={index}>{data}</div>
                        ))}
                      </div>
                    ))
                  : null}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.DESCRIPTION}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.HS_CODE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.QUANTITY}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.RATE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {item.TOTAL}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div style={{ padding: '1mm' }}>GOODS ARE OF INDIAN ORIGIN</div>
        <div
          style={{
            padding: '1mm 2mm',
            width: '20%',
            background: '#28388e',
            color: '#fff',
            fontWeight: '600',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <span>Total</span>
          <span style={{ marginRight: '5mm' }}>{formData.TOTAL}</span>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderTop: '1px solid',
          borderBottom: '1px solid',
        }}
      >
        <div style={{ padding: '1mm' }}>Amount chargeable(USD):</div>
        <div
          style={{
            padding: '1mm 26mm',
            background: '#28388e',
            color: '#fff',
            fontWeight: '600',
          }}
        >
          {formData.TOTAL_IN_WORDS}
        </div>
      </div>
      <div style={{ display: 'flex' }}>
        <div
          style={{
            width: ' 85mm',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div style={{ display: 'table', width: ' 84mm' }}>
            <div style={{ display: 'table-row' }}>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  backgroundColor: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Invoice Value in USD :
              </div>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                }}
              >
                {formData.TOTAL}
              </div>
            </div>
            <div style={{ display: 'table-row' }}>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  backgroundColor: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Custom Exchange Rate
              </div>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  background: '#f3f3f3',
                }}
              >
                {formData.EXCHANGE_RATE}
              </div>
            </div>
            <div style={{ display: 'table-row' }}>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                  backgroundColor: '#f3f3f3',
                  color: '#28388e',
                  fontWeight: '600',
                }}
              >
                Invoice Value in INR :
              </div>
              <div
                style={{
                  display: 'table-cell',
                  padding: '1.5mm 2mm',
                }}
              >
                {formData.TOTAL_IN_INR}
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            padding: '2mm',
            width: '40mm',
            borderBottom: '1px solid',
          }}
        >
          Supply meant for export{' '}
          {formData?.IGST && formData?.IGST >= 0
            ? ' with payment of IGST '
            : ' under LUT without payment of IGST LUT (ARN) Number :'}{' '}
          {formData?.IGST && formData?.IGST >= 0 ? '' : <span>{formData.ARN_NUMBER}</span>}
        </div>
        <div
          style={{
            display: 'table',
            verticalAlign: 'top',
            width: ' 85mm',
            borderLeft: ' 1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div style={{ display: 'table-row' }}>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              Total Amount Before GST in INR:
            </div>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              {formData.TOTAL_BEFORE_GST}
            </div>
          </div>
          <div style={{ display: 'table-row' }}>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              ADD: IGST18% in INR:
            </div>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              {formData.IGST}
            </div>
          </div>
          <div style={{ display: 'table-row' }}>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                backgroundColor: '#f3f3f3',
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              TOTAL AMT After GST in INR
            </div>
            <div
              style={{
                display: 'table-cell',
                padding: '1.5mm 2mm',
                background: '#f3f3f3',
              }}
            >
              {formData.TOTAL_AFTER_GST}
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          padding: '4mm',
          display: 'table',
          pageBreakInside: 'avoid',
        }}
      >
        <div style={{ fontWeight: '600', color: '#28388e' }}>
          <div>Declaration :</div>
          <div style={{ marginTop: '2mm' }}>
            We declare that this invoice shows the actual price of the goods described above and
            that all particulars stated herein are true and correct.
          </div>
        </div>
        <div
          style={{
            width: '80mm',
            display: 'table-cell',
            textAlign: 'right',
          }}
        >
          <div style={{ fontWeight: '600', color: '#28388e' }}>
            <div>FOR CHEMSTACK PRIVATE LIMITED</div>
          </div>
          <div>
            <img
              style={{ width: '40mm' }}
              src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_stamp.png"
              alt="header logo"
            ></img>
          </div>
          <div style={{ fontWeight: '600', color: '#28388e' }}>AUTHORISED SIGNATORY</div>
        </div>
      </div>
    </div>
  ),
  MSTACK_INVOICE: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm 5mm 2mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            src="https://mstack-assets.s3.amazonaws.com/logos/mstack-logo-1.svg"
            alt="header logo"
          ></img>
        </div>
        <div
          style={{
            marginTop: '1mm',
            textAlign: 'center',
            fontWeight: '600',
          }}
        >
          {formData?.MSTACK_ADDRESS}
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        INVOICE
      </div>
      <div
        style={{
          display: 'flex',
          // alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <div
          style={{
            width: '100%',
          }}
        >
          <div
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              padding: '1.2mm',
              fontWeight: 600,
            }}
          >
            Bill To,
          </div>
          <div style={{ padding: '1.2mm', whiteSpace: 'pre-line' }}>{formData.BILLING_ADDRESS}</div>
        </div>
        {formData.SHIPING_ADDRESS ? (
          <div
            style={{
              width: '100%',
              borderLeft: '1px solid',
            }}
          >
            <div
              style={{
                background: '#f3f3f3',
                color: '#28388e',
                padding: '1.2mm',
                fontWeight: 600,
              }}
            >
              Ship To,
            </div>
            <div style={{ padding: '1.2mm', whiteSpace: 'pre-line' }}>
              {formData.SHIPING_ADDRESS}
            </div>
          </div>
        ) : null}
      </div>
      <table
        style={{
          width: '100%',
          minHeight: '50mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
          borderBottom: 'none',
        }}
      >
        <tr>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
            }}
          >
            Invoice no. :
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
            }}
          >
            {formData.INVOICE_NUMBER}
          </td>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
            }}
          >
            Date:
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
            }}
          >
            {formData.INVOICE_DATE}
          </td>
        </tr>
        <tr>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
            }}
          >
            Customer PO no. :
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
            }}
          >
            {formData.PO_NUMBER}
          </td>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
            }}
          >
            Currency:
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
            }}
          >
            {formData.CURRENCY}
          </td>
        </tr>
        <tr>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
              borderBottom: 'none',
            }}
          >
            Terms:
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
              borderBottom: 'none',
            }}
          >
            {formData.TERMS}
          </td>
          <td
            style={{
              background: '#f3f3f3',
              color: '#28388e',
              fontWeight: '700',
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
              borderBottom: 'none',
            }}
          >
            Due Date:
          </td>
          <td
            style={{
              textAlign: 'center',
              fontSize: '10px',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderRight: 'none',
              borderBottom: 'none',
            }}
          >
            {formData.DUE_DATE}
          </td>
        </tr>
      </table>
      <table
        style={{
          width: '210mm',
          fontSize: '10px',
          minHeight: '100mm',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead
          style={{
            background: '#28388e',
            color: '#fff',
          }}
        >
          <tr>
            <th
              style={{
                width: '5mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              No.
            </th>
            <th
              style={{
                width: '61mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Item
            </th>
            <th
              style={{
                width: '45mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Packing
            </th>
            <th
              style={{
                width: '32mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Quantity
            </th>
            <th
              style={{
                width: '32mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Product Uom
            </th>
            <th
              style={{
                fontSize: '10px',
                height: '30px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Price/Unit
            </th>
            <th
              style={{
                fontSize: '10px',
                height: '30px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.PRODUCTS_DATA?.map((row, index) => (
            <tr key={row?.ITEM}>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {index + 1}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.ITEM_NAME_ALIAS ? row.ITEM_NAME_ALIAS : row?.ITEM}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.PACKAGING}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.QUANTITY_VALUE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.QUANTITY_UOM_ABB}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.UNIT_PRICE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {row?.TOTAL_PRICE}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
        }}
      >
        <div style={{ margin: 'auto', width: '99.5%', borderRight: '1px solid' }}>
          <div
            style={{
              padding: '1mm',
              fontWeight: '700',
              borderBottom: '1px solid',
            }}
          >
            Total Value: {formData.TOTAL_IN_WORDS}
          </div>
          <div style={{ borderBottom: '1px solid' }}>
            <div
              style={{
                padding: '1mm',
                fontWeight: '700',
                background: '#f3f3f3',
              }}
            >
              Description of Goods
            </div>
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
              }}
            >
              <div>Incoterm:</div>
              <div>{formData.INCOTERM}</div>
            </div>
            {formData.PORT_OF_LOADING ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  background: '#f3f3f3',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Port of loading:</div>
                <div>{formData.PORT_OF_LOADING}</div>
              </div>
            ) : null}
            {formData.PORT_OF_DISCHARGE ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Port of discharge:</div>
                <div>{formData.PORT_OF_DISCHARGE}</div>
              </div>
            ) : null}
            {formData.COUNTRY_OF_ORIGIN ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  background: '#f3f3f3',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Country of origin:</div>
                <div>{formData.COUNTRY_OF_ORIGIN}</div>
              </div>
            ) : null}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '1mm',
                gap: '1mm',
              }}
            >
              <div>{formData.FREIGHT_TERM}</div>
            </div>
            {/* <div
              style={{
                display: 'flex',
                alignItems: 'center',
                background: '#f3f3f3',
                padding: '1mm',
                gap: '1mm',
              }}
            >
              <div>Export HS Code:</div>
              <div>{formData.HS_CODE}</div>
            </div> */}
          </div>
          <div style={{ borderBottom: '1px solid' }}>
            <div
              style={{
                padding: '1mm',
                fontWeight: '700',
                background: '#f3f3f3',
              }}
            >
              Remarks
            </div>
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
              }}
            >
              {formData?.DELIVERY_DATE ? (
                <div>Delivery Date : {formData?.DELIVERY_DATE}</div>
              ) : null}
            </div>
          </div>
          <div>
            <div
              style={{
                padding: '1mm',
                fontWeight: '700',
                background: '#f3f3f3',
              }}
            >
              To Remit Electronically:
            </div>
            <div
              style={{
                padding: '2mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1mm',
                }}
              >
                <div>Account Name:</div>
                <div>Mstack Inc.</div>
              </div>
              <div>|</div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1mm',
                }}
              >
                <div>Account Number:</div>
                <div>{formData.ACCOUNT_NUMBER}</div>
              </div>
              <div>|</div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1mm',
                }}
              >
                <div>Bank:</div>
                <div>{formData.BANK}</div>
              </div>
            </div>
            <div
              style={{
                padding: '2mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
                background: '#f3f3f3',
              }}
            >
              <div>Bank Address:</div>
              <div>{formData.BANK_ADDRESS}</div>
            </div>
            <div
              style={{
                padding: '2mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1mm',
                }}
              >
                <div>SWIFT code (for wire transfer):</div>
                <div>{formData.SWIFT_CODE}</div>
              </div>
              <div>|</div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1mm',
                }}
              >
                <div>ACH Routing Number:</div>
                <div>{formData.ACH_ROUTING_NUMBER}</div>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {formData.FOB_COST ? (
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                justifyContent: 'space-between',
                background: '#28388e',
                color: '#fff',
                fontWeight: '600',
                borderBottom: '1px solid',
              }}
            >
              <div>FOB Cost</div>
              <div style={{ marginRight: '7mm' }}>{formData.FOB_COST}</div>
            </div>
          ) : null}
          {formData.FREIGHT_COST ? (
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                justifyContent: 'space-between',
                background: '#28388e',
                color: '#fff',
                fontWeight: '600',
                borderBottom: '1px solid',
              }}
            >
              <div>Freight Cost</div>
              <div style={{ marginRight: '7mm' }}>{formData.FREIGHT_COST}</div>
            </div>
          ) : null}
          {formData.INSURANCE_COST ? (
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                justifyContent: 'space-between',
                background: '#28388e',
                color: '#fff',
                fontWeight: '600',
                borderBottom: '1px solid',
              }}
            >
              <div>Insurance Cost</div>
              <div style={{ marginRight: '7mm' }}>{formData.INSURANCE_COST}</div>
            </div>
          ) : null}
          <div
            style={{
              padding: '1mm',
              display: 'flex',
              justifyContent: 'space-between',
              background: '#28388e',
              color: '#fff',
              fontWeight: '600',
              borderBottom: '1px solid',
            }}
          >
            <div>Grand Total</div>
            <div style={{ marginRight: '7mm' }}>{formData.TOTAL}</div>
          </div>
          <div
            style={{
              padding: '5mm 0',
              margin: 'auto auto 0',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div style={{ fontWeight: '700' }}>Authorized Signatory</div>
            <img
              src="https://mstack-assets.s3.amazonaws.com/logos/mstack_stamp.png"
              alt="header logo"
              style={{ width: '40mm' }}
            />
            <div style={{ fontWeight: '700' }}>For Mstack Inc.</div>
          </div>
        </div>
      </div>
    </div>
  ),
  MSTACK_PACKING_LIST: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm 5mm 2mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          <img
            src="https://mstack-assets.s3.amazonaws.com/logos/mstack-logo-1.svg"
            alt="header logo"
          ></img>
        </div>
        <div
          style={{
            marginTop: '1mm',
            textAlign: 'center',
            fontWeight: '600',
          }}
        >
          {formData?.MSTACK_ADDRESS}
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        PACKING LIST
      </div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <div
          style={{
            width: '100%',
          }}
        >
          <div
            style={{
              background: '#f3f3f3',
              padding: '1.2mm',
              fontWeight: 600,
            }}
          >
            Consignee:
          </div>
          <div style={{ padding: '1.2mm' }}>{formData.CONSIGNEE}</div>
        </div>
      </div>
      <table
        style={{
          width: '100%',
          minHeight: '20mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
          borderBottom: 'none',
        }}
      >
        <thead>
          <tr>
            <td
              style={{
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '700',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Packing List no. :
            </td>
            <td
              style={{
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              {formData.PACKAGING_LIST_NUMBER}
            </td>
            <td
              style={{
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '700',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Date:
            </td>
            <td
              style={{
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              {formData.INVOICE_DATE}
            </td>
          </tr>
          <tr>
            <td
              style={{
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '700',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Customer PO no. :
            </td>
            <td
              style={{
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              {formData.PO_NUMBER}
            </td>
            <td
              style={{
                background: '#f3f3f3',
                color: '#28388e',
                fontWeight: '700',
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Currency:
            </td>
            <td
              style={{
                textAlign: 'center',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              {formData.CURRENCY}
            </td>
          </tr>
        </thead>
      </table>
      <table
        style={{
          width: '210mm',
          minHeight: '100mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead style={{ backgroundColor: '#28388e', color: '#fff' }}>
          <tr>
            <th
              style={{
                width: '20mm',
                height: '60px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Marks &amp; Nos. / Container Number(s)
            </th>
            <th
              style={{
                width: '61mm',
                padding: '1.5mm',
                height: '60px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              No. &amp; Kinds of Packages
            </th>
            <th
              style={{
                width: '45mm',
                height: '60px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Description
            </th>
            <th
              style={{
                width: '32mm',
                height: '60px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              UOM
            </th>
            {/* <th
              style={{
                width: "32mm",
                height: '60px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Export HS Code
            </th> */}
            {/* <th
              style={{
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Qty. (Kgs)
            </th> */}
            <th
              style={{
                padding: '1.5mm',
                height: '60px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Net weight
            </th>
            <th
              style={{
                padding: '1.5mm',
                height: '60px',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              Gross weight
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.TABLE_DATA?.map((item, index) => (
            <tr key={index}>
              {index === 0 ? (
                <td
                  style={{
                    padding: '1mm',
                    textAlign: 'center',
                    verticalAlign: 'top',
                    fontSize: '10px',
                    border: '1px solid black',
                    borderCollapse: 'collapse',
                    borderLeft: 'none',
                  }}
                  rowSpan={formData?.TABLE_DATA?.length}
                >
                  {formData.CONTAINER_NUMBER}
                </td>
              ) : null}
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.PACKAGES
                  ? item.PACKAGES.map((packageData, index) => (
                      <div
                        key={index}
                        style={{
                          margin: '0 2mm',
                          padding: '2mm 0',
                          borderBottom: index === item.PACKAGES.length - 1 ? '' : '1px dashed',
                        }}
                      >
                        {packageData.map((data, index) => (
                          <div key={index}>{data}</div>
                        ))}
                      </div>
                    ))
                  : null}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.DESCRIPTION}
              </td>
              <td
                style={{
                  padding: '0.2mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.UOM}
              </td>
              {/* <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >{item.HS_CODE}</td> */}
              {/* <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >{item.QUANTITY}</td> */}
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {item.NET_WEIGHT}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {item.GROSS_WEIGHT}
              </td>
            </tr>
          ))}
          {formData.PALLET_DATA ? (
            <tr>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              />
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {formData.PALLET_DATA}
              </td>
              <td />
              <td />
              <td />
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderRight: 'none',
                }}
              >
                {formData.PALLET_TOTAL}
              </td>
            </tr>
          ) : null}
        </tbody>
      </table>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
        }}
      >
        <div style={{ borderRight: '1px solid' }}>
          <div>
            <div
              style={{
                padding: '1mm',
                fontWeight: '700',
                background: '#f3f3f3',
              }}
            >
              Description of Goods
            </div>
            <div
              style={{
                padding: '1mm',
                display: 'flex',
                alignItems: 'center',
                gap: '1mm',
              }}
            >
              <div>Incoterm:</div>
              <div>{formData.INCOTERM}</div>
            </div>
            {formData.PORT_OF_LOADING ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  background: '#f3f3f3',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Port of loading:</div>
                <div>{formData.PORT_OF_LOADING}</div>
              </div>
            ) : null}
            {formData.PORT_OF_DISCHARGE ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Port of discharge:</div>
                <div>{formData.PORT_OF_DISCHARGE}</div>
              </div>
            ) : null}
            {formData.COUNTRY_OF_ORIGIN ? (
              <div
                style={{
                  margin: '0 auto',
                  width: '99.5%',
                  display: 'flex',
                  alignItems: 'center',
                  background: '#f3f3f3',
                  padding: '1mm',
                  gap: '1mm',
                }}
              >
                <div>Country of origin:</div>
                <div>{formData.COUNTRY_OF_ORIGIN}</div>
              </div>
            ) : null}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '1mm',
                gap: '1mm',
              }}
            >
              <div>{formData.FREIGHT_TERM}</div>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              background: '#28388e',
            }}
          >
            <div
              style={{
                padding: '2mm',
                color: '#fff',
                fontWeight: '600',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <span>Gross weight</span>
              <span style={{ marginRight: '8mm' }}>{formData.TOTAL_GROSS_WEIGHT}</span>
            </div>
            <div
              style={{
                padding: '2mm',
                color: '#fff',
                fontWeight: '600',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <span>Net Weight</span>
              <span style={{ marginRight: '8mm' }}>{formData.TOTAL_NET_WEIGHT}</span>
            </div>
            <div
              style={{
                padding: '2mm',
                color: '#fff',
                fontWeight: '600',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <span>Tare Weight</span>
              <span style={{ marginRight: '8mm' }}>{formData.TOTAL_TARE_WEIGHT}</span>
            </div>
          </div>
          <div
            style={{
              padding: '5mm 0',
              margin: 'auto auto 0',
              width: '100%',
              minHeight: '50mm',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div style={{ fontWeight: '700' }}>Authorized Signatory</div>
            <img
              src="https://mstack-assets.s3.amazonaws.com/logos/mstack_stamp.png"
              alt="header logo"
              style={{ width: '40mm' }}
            />
            <div style={{ fontWeight: '700' }}>For Mstack Inc.</div>
          </div>
        </div>
      </div>
    </div>
  ),
  BILL_OF_LADING: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '2mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        BILL OF LADING (B/L)
      </div>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          minHeight: '50mm',
        }}
      >
        <div
          style={{
            display: 'grid',
            gridTemplateRows: '1fr 1fr 1fr',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Shipper:</div>
            <div>{formData.SHIPPER}</div>
            <div>{formData.SHIPPER_ADDRESS}</div>
          </div>
          <div
            style={{
              margin: '0 auto',
              width: '99.5%',
              padding: '2mm',
              background: '#f3f3f3',
              boxSizing: 'border-box',
            }}
          >
            <div style={{ fontWeight: '700', color: '#28388e' }}>Consignee:</div>
            <div>{formData.CONSIGNEE}</div>
          </div>
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Notify Address:</div>
            <div>{formData.NOTIFY_ADDRESS}</div>
          </div>
        </div>
        <div
          style={{
            display: 'grid',
            gridTemplateRows: '1fr 1fr',
            borderBottom: '1px solid',
          }}
        >
          <div
            style={{
              margin: '0 auto',
              width: '99.5%',
              padding: '2mm',
              background: '#f3f3f3',
              boxSizing: 'border-box',
            }}
          >
            <div style={{ fontWeight: '700', color: '#28388e' }}>BILL OF LADING NO.:</div>
            <div>{formData.BL_NUMBER}</div>
          </div>
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Shipping Line name/logo:</div>
            <div>
              {formData.SHIPPING_LINE_LOGO ? (
                <img src={formData.SHIPPING_LINE_LOGO} alt="shipping line logo" />
              ) : (
                formData.SHIPPING_LINE_NAME
              )}
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          minHeight: '50mm',
        }}
      >
        <div
          style={{
            margin: '0 auto',
            width: '99.5%',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            borderRight: '1px solid',
            borderBottom: '1px solid',
            boxSizing: 'border-box',
          }}
        >
          <div style={{ padding: '2mm', background: '#f3f3f3' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Voyage no.:</div>
            <div>{formData.VOYAGE_NUMBER}</div>
          </div>
          {/* <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Place of receipt (if pre-carriage):</div>
            <div>{formData.PLACE_OF_RECEIPT}</div>
          </div> */}
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Vessel (ocean):</div>
            <div>{formData.VESSEL}</div>
          </div>
          <div style={{ padding: '2mm', background: '#f3f3f3' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Port of Loading:</div>
            <div>{formData.PORT_OF_LOADING}</div>
          </div>
          <div style={{ padding: '2mm', background: '#f3f3f3' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Port of discharge:</div>
            <div>{formData.PORT_OF_DISCHARGE}</div>
          </div>
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Place of delivery:</div>
            <div>{formData.PLACE_OF_DELIVERY}</div>
          </div>
        </div>
        <div
          style={{
            margin: '0 auto',
            width: '99.5%',
            display: 'grid',
            gridTemplateRows: '1fr1 fr 1fr',
            borderBottom: '1px solid',
          }}
        >
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>No of original BL&apos;s:</div>
            <div>{formData.NP_OF_BL}</div>
          </div>
          <div style={{ padding: '2mm', background: '#f3f3f3' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Ocean freight payable at:</div>
            <div>{formData.OCEAN_FREIGHT_PAY_AT}</div>
          </div>
          <div style={{ padding: '2mm' }}>
            <div style={{ fontWeight: '700', color: '#28388e' }}>Measuremnt:</div>
            <div>{formData.MESUREMENT}</div>
          </div>
        </div>
      </div>
      <div>
        <div
          style={{
            display: 'flex',
          }}
        >
          <div
            style={{
              width: '30%',
              padding: '1.5mm',
              fontSize: '10px',
              color: '#fff',
              background: '#28388e',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
              boxSizing: 'border-box',
            }}
          >
            Marks nos/ Container no.
          </div>
          <div
            style={{
              width: '20%',
              padding: '1.5mm',
              fontSize: '10px',
              color: '#fff',
              background: '#28388e',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
              boxSizing: 'border-box',
            }}
          >
            Quantity and Pkgs
          </div>
          <div
            style={{
              width: '30%',
              padding: '1.5mm',
              fontSize: '10px',
              color: '#fff',
              background: '#28388e',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
              boxSizing: 'border-box',
            }}
          >
            Description of goods
          </div>
          <div
            style={{
              width: '10%',
              padding: '1.5mm',
              fontSize: '10px',
              color: '#fff',
              background: '#28388e',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
              boxSizing: 'border-box',
            }}
          >
            Gross Weight
          </div>
          <div
            style={{
              width: '10%',
              padding: '1.5mm',
              fontSize: '10px',
              color: '#fff',
              background: '#28388e',
              border: '1px solid black',
              borderCollapse: 'collapse',
              borderLeft: 'none',
              boxSizing: 'border-box',
            }}
          >
            Measurement
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            minHeight: '100mm',
          }}
        >
          <div
            style={{
              width: '25%',
              padding: '1mm',
              fontSize: '10px',
              borderRight: '1px solid black',
              boxSizing: 'border-box',
            }}
          >
            <div>{formData.MARKS_NO}</div>
            {/* {formData?.MARKS_CONTAINER_DATA?.length ? formData.MARKS_CONTAINER_DATA.map((data, index) => (
              <div key={index}>
                <div>{data.PRODUCT_NAME}</div>
                
                <div                
                  style={{
                    margin: '0 2mm',
                    padding: '2mm 0',
                    borderBottom: index === (data.PACKAGES.length -1) ? '1px dashed' : '',
                  }}
                >
                  {data?.PACKAGES?.map((data, index) => (
                    <div className="mt-1" key={index}>
                      {data.map((item) => <div key={item}>{item}</div>)}
                    </div>
                  ))}
                </div>
              </div>
            )) : null} */}
          </div>
          <div
            style={{
              width: '20%',
              padding: '1mm',
              fontSize: '10px',
              borderRight: '1px solid black',
              boxSizing: 'border-box',
            }}
          >
            {formData?.QUANTITY_PKGS?.map((item, index) => (
              <div key={index}>{item}</div>
            ))}
          </div>
          <div
            style={{
              width: '30%',
              padding: '1mm',
              fontSize: '10px',
              borderRight: '1px solid black',
              boxSizing: 'border-box',
            }}
          >
            <div>{`${formData.SHIPMENT_TYPE} container`}</div>
            <div>
              {formData?.PRODUCT_DATA?.map((item, index) => (
                <div
                  key={index}
                  style={{
                    paddingBottom: '1mm',
                    borderBottom: index === item.length - 1 ? '' : '1px dashed',
                  }}
                >
                  <div>{item?.nameAlias ? item.nameAlias : item.name} </div>
                  <div>{item.hsCode}</div>
                  <div>{item.hazInfo}</div>
                  {item.pallets ? <div>{item.pallets}</div> : null}
                </div>
              ))}
            </div>
            <div>{`Invoice no: ${formData.INVOICE_NO}   Date: ${formData.INVOICE_DATE}`}</div>
            <div>{`SB no: ${formData.SHIPPING_BILL_NO}   Date: ${formData.SHIPPING_BILL_DATE}`}</div>
            <div>{`PO no: ${formData.PO_NUMBER}   Date: ${formData.PO_DATE}`}</div>
            <div>{`Net Weight: ${formData.NET_WEIGHT}`}</div>
            {formData.PREPAID_DATA ? <div>{`Prepaid: ${formData.PREPAID_DATA}`}</div> : null}
            <div>On behalf of:</div>
            <div>Mstack Inc.</div>
            <div>{formData.BEHALF_OF}</div>
            {formData.FOB_COST ? <div>{`FOB Cost: ${formData.FOB_COST}`}</div> : null}
            {formData.FREIGHT_COST ? <div>{`Freight Cost: ${formData.FREIGHT_COST}`}</div> : null}
            {formData.INSURANCE_COST ? (
              <div>{`Insurance Cost: ${formData.INSURANCE_COST}`}</div>
            ) : null}
          </div>
          <div
            style={{
              width: '15%',
              padding: '1mm',
              fontSize: '10px',
              borderRight: '1px solid black',
              whiteSpace: 'normal',
              boxSizing: 'border-box',
            }}
          >
            {formData.GROSS_WEIGHT}
          </div>
          <div
            style={{
              width: '10%',
              padding: '1mm',
              fontSize: '10px',
            }}
          >
            {formData.MEASUREMENT}
          </div>
        </div>
      </div>
    </div>
  ),
  SUPPLIER_PO: formData => (
    <div
      style={{
        width: '210mm',
        margin: '5mm',
        border: '2px solid',
        fontFamily: 'sans-serif',
        fontSize: '10px',
        backgroundColor: '#fff',
      }}
    >
      <div
        style={{
          padding: '5mm 5mm 2mm',
        }}
      >
        <div
          style={{
            margin: 'auto',
            width: '100mm',
          }}
        >
          {isMstackOrder(formData?.BILLING_ADDRESS) ? (
            <img
              src="https://mstack-assets.s3.amazonaws.com/logos/mstack-logo-1.svg"
              alt="header logo"
            ></img>
          ) : (
            <img
              style={{ width: '100mm' }}
              src="https://mstack-assets.s3.amazonaws.com/logos/chemstack_logo.png"
              alt="header logo"
            />
          )}
        </div>
        <div
          style={{
            marginTop: '1mm',
            textAlign: 'center',
            fontWeight: '600',
          }}
        >
          {formData?.COMPANY_ADDRESS}
        </div>
      </div>
      <div
        style={{
          padding: '1mm',
          color: '#fff',
          background: '#28388e',
          fontWeight: '700',
          fontSize: '12px',
          textAlign: 'center',
        }}
      >
        PURCHASE ORDER
      </div>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr',
        }}
      >
        <div
          style={{
            padding: '5px',
            display: 'flex',
            flexDirection: 'column',
            gap: '3px',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              PO No. :
            </div>
            <div>{formData?.PO_NUMBER}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              PO Date :
            </div>
            <div>{formData?.PO_DATE}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Supplier Ref. No. :
            </div>
            <div>{formData?.SUPPLIER_REF_NO}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Quotation No. :
            </div>
            <div>{formData?.QUOTATION_NUMBER}</div>
          </div>
        </div>
        <div
          style={{
            padding: '5px',
            display: 'flex',
            flexDirection: 'column',
            gap: '3px',
            borderRight: '1px solid',
            borderBottom: '1px solid',
          }}
        >
          {formData?.PORT_OF_LOADING ? (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  color: '#28388e',
                  fontWeight: '600',
                  width: '50%',
                }}
              >
                Port of Loading :
              </div>
              <div>{formData?.PORT_OF_LOADING}</div>
            </div>
          ) : null}
          {formData?.PORT_OF_DISCHARGE ? (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  color: '#28388e',
                  fontWeight: '600',
                  width: '50%',
                }}
              >
                Port of Discharge :
              </div>
              <div>{formData?.PORT_OF_DISCHARGE}</div>
            </div>
          ) : null}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '45%',
              }}
            >
              Payment Terms :
            </div>
            <div>{formData?.PAYMENT_TERMS}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Country of origin :
            </div>
            <div>{formData?.COUNTRY_OF_ORIGIN}</div>
          </div>
        </div>
        <div
          style={{
            padding: '5px',
            display: 'flex',
            flexDirection: 'column',
            gap: '3px',
            borderBottom: '1px solid',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Trans Shipment :
            </div>
            <div>{formData?.TRANS_SHIPMENT ? 'Allowed' : 'Not Allowed'}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Partial Shipment :
            </div>
            <div>{formData?.PARTIAL_SHIPMENT ? 'Allowed' : 'Not Allowed'}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Mode of delivery :
            </div>
            <div>{formData?.MODE_OF_DELIVERY}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Delivery Terms :
            </div>
            <div>{formData?.DELIVERY_TERMS}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Delivery Location :
            </div>
            <div>{formData?.DELIVERY_LOCATION}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
                width: '50%',
              }}
            >
              Pickup Location :
            </div>
            <div>{formData?.PICKUP_LOCATION}</div>
          </div>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1px solid',
        }}
      >
        <div
          style={{
            padding: '5px',
            width: '50%',
            borderRight: '1px solid',
          }}
        >
          <div
            style={{
              color: '#28388e',
              fontWeight: '600',
              marginBottom: '3px',
            }}
          >
            Supplier Address
          </div>
          <div>{formData?.SUPPLIER_ADDRESS}</div>
        </div>
        <div
          style={{
            padding: '5px',
            width: '50%',
          }}
        >
          <div
            style={{
              color: '#28388e',
              fontWeight: '600',
              marginBottom: '3px',
            }}
          >
            Delivery Address
          </div>
          <div>{formData?.DELIVERY_ADDRESS}</div>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          borderBottom: '1px solid',
        }}
      >
        <div
          style={{
            padding: '5px',
            width: '50%',
            borderRight: '1px solid',
          }}
        >
          <div
            style={{
              color: '#28388e',
              fontWeight: '600',
              marginBottom: '3px',
            }}
          >
            Factory Address
          </div>
          <div>{formData?.FACTORY_ADDRESS}</div>
        </div>
        <div
          style={{
            padding: '5px',
            width: '50%',
          }}
        >
          <div
            style={{
              marginBottom: '3px',
              display: 'flex',
              gap: '5px',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              GSTIN
            </div>
            <div>{formData?.GSTIN}</div>
          </div>
          <div
            style={{
              marginBottom: '3px',
              display: 'flex',
              gap: '5px',
            }}
          >
            <div
              style={{
                color: '#28388e',
                fontWeight: '600',
              }}
            >
              PAN
            </div>
            <div>{formData?.PAN}</div>
          </div>
        </div>
      </div>
      <table
        style={{
          width: '210mm',
          minHeight: '30mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead
          style={{
            background: '#28388e',
            color: '#fff',
          }}
        >
          <tr>
            <th
              style={{
                width: '61mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Product
            </th>
            <th
              style={{
                width: '45mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              No. & kind of packaging
            </th>
            <th
              style={{
                width: '32mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              HS Code
            </th>
            <th
              style={{
                width: '32mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Qty ({formData?.PRODUCT_UOM})
            </th>
            <th
              style={{
                height: '30px',
                fontSize: '10px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              <div>Rate/Unit</div>
              <div>
                (
                {formData?.CURRENCY_TYPE && formData?.CURRENCY_TYPE == '₹'
                  ? ' Rs.'
                  : formData.CURRENCY_TYPE}
                ) / {formData?.PRODUCT_UOM}{' '}
              </div>
            </th>
            <th
              style={{
                height: '30px',
                fontSize: '10px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderRight: 'none',
              }}
            >
              <div>Amount </div>
              <div>
                (
                {formData?.CURRENCY_TYPE && formData?.CURRENCY_TYPE == '₹'
                  ? ' Rs.'
                  : formData.CURRENCY_TYPE}
                )
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.PRODUCTS_DATA?.map(row => (
            <tr key={row?.ITEM}>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {row?.ITEM}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                <div>{row?.PACKAGING}</div>
                {row?.PACK_SIZE && <div>Pack Size: {row.PACK_SIZE}</div>}
                {row?.TARE_WEIGHT && <div>Tare Weight: {row.TARE_WEIGHT}</div>}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.HS_CODE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.QUANTITY}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.UNIT_PRICE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.TOTAL_PRICE_AS_STR}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div
        style={{
          marginLeft: 'auto',
          width: '60mm',
          display: 'flex',
          flexDirection: 'column',
          background: '#28388e',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '2mm',
            color: '#fff',
            fontWeight: '600',
          }}
        >
          <span>Sub Total</span>
          <span style={{ marginRight: '8mm' }}>{formData?.SUB_TOTAL}</span>
        </div>
        {formData?.TAX_LIST?.map(taxItem => (
          <div
            key={taxItem?.TAX_TYPE}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              padding: '2mm',
              color: '#fff',
              fontWeight: '600',
            }}
          >
            <div>
              <span style={{ textTransform: 'uppercase' }}>{`${taxItem?.TAX_TYPE}`}</span>{' '}
              {`(${taxItem?.TAX_PERCENT}%)`}
            </div>
            <span style={{ marginRight: '8mm' }}>{taxItem?.TAX_VALUE_AS_STR}</span>
          </div>
        ))}
      </div>
      <div
        style={{
          display: 'flex',
          borderTop: '1px solid',
          borderBottom: '1px solid',
        }}
      >
        <div
          style={{
            padding: '4px',
            width: '50%',
            borderRight: '1px solid',
          }}
        >
          <div style={{ fontWeight: '600' }}>Amount in words :</div>
          <div>{formData?.TOTAL_PRICE_WORDS}</div>
        </div>
        <div
          style={{
            padding: '4px',
            width: '50%',
          }}
        >
          <div style={{ fontWeight: '600' }}>
            Order Value (
            {formData?.CURRENCY_TYPE && formData?.CURRENCY_TYPE == '₹'
              ? ' Rs. '
              : formData.CURRENCY_TYPE}
            ) :
          </div>
          <div>{formData?.ORDER_VALUE}</div>
        </div>
      </div>
      <div
        style={{
          padding: '4px',
          fontWeight: '600',
        }}
      >
        Delivery Schedule
      </div>
      <table
        style={{
          width: '210mm',
          minHeight: '25mm',
          fontSize: '10px',
          border: '1px solid black',
          borderCollapse: 'collapse',
          borderRight: 'none',
        }}
      >
        <thead
          style={{
            background: '#28388e',
            color: '#fff',
          }}
        >
          <tr>
            <th
              style={{
                width: '61mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
                borderLeft: 'none',
              }}
            >
              Item Name
            </th>
            <th
              style={{
                width: '30mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Qty ({formData?.PRODUCT_UOM})
            </th>
            <th
              style={{
                width: '30mm',
                height: '30px',
                padding: '1.5mm',
                fontSize: '10px',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Delivery Date
            </th>
            <th
              style={{
                height: '30px',
                fontSize: '10px',
                padding: '1.5mm',
                border: '1px solid black',
                borderCollapse: 'collapse',
              }}
            >
              Item Remarks
            </th>
          </tr>
        </thead>
        <tbody>
          {formData?.SCHEDULE_DATA?.map(row => (
            <tr key={row?.ITEM}>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                  borderLeft: 'none',
                }}
              >
                {row?.ITEM}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.QUANTITY}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.DELIVERY_DATE}
              </td>
              <td
                style={{
                  padding: '1mm',
                  textAlign: 'center',
                  verticalAlign: 'top',
                  fontSize: '10px',
                  border: '1px solid black',
                  borderCollapse: 'collapse',
                }}
              >
                {row?.REMARK}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div style={{ padding: '5px', borderBottom: '1px solid' }}>
        <div style={{ fontWeight: '600' }}>Additional Condtions:</div>
        <span style={{ whiteSpace: 'pre-wrap' }}>{formData?.ADDITIONAL_CONDITIONS}</span>
      </div>
      <div style={{ padding: '5px', borderBottom: '1px solid' }}>
        <div style={{ fontWeight: '600' }}>Terms and Condtions:</div>
        <span style={{ whiteSpace: 'pre-wrap' }}>{formData?.TERMS_AND_CONDITIONS}</span>
      </div>
      <div style={{ padding: '2mm', fontWeight: '600' }}>
        This is digital signed document, no physical signature is required
      </div>
    </div>
  ),
};
