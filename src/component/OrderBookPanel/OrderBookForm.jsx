import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Form, Input, Select, DatePicker, Radio, Divider, message, Switch } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { formModes } from '../../constants/formConstant';
import { getCustomerList } from '../../service/api/customerApi';
import { createOrder, updateOrder } from '../../service/api/orderBookApi';
import { getOrderBookDataForApi, getPackagingDetailFromId } from '../../util/formUtil';
import dayjs from 'dayjs';
import {
  OrderBookFormBasicInfo,
  DateFormat,
  orderForList,
  orderTypeList,
  paymentTermsDate,
  categoryOptions,
  currencyTypeList,
  sampleRequestedByList,
} from '../../constants/formConstant';
import enUS from 'antd/es/calendar/locale/en_US';
import { CalendarOutlined } from '@ant-design/icons';
import FileUpload from '../FileUpload';
import ProductTable from './ProductTable';
import { getFormModeFromPathV2 } from '../../util/route';
import { useLocation } from 'react-router-dom';
import { getProductList } from '../../service/api/productApi';
import { getAllPackagingDetails } from '../../service/api/packagingApi';
import { addPackaging, setPackagingList } from '../../store/actions/packagingList';
import { validatePONumber } from '../../util/formUtil';
import { createDispatchOrder } from '../../service/api/dispatchOrderApi';
import { getDispatchOrderFromPoNumber } from '../../service/api/invoiceApi';
import PageLoader from '../Loaders/PageLoader';

const OrderBookForm = props => {
  const { mode, order, hide, addOrderFormOpened } = props;
  const packagingList = useSelector(state => state.packagingList);
  const [orderBookEntry, setOrderBookEntry] = useState(mode === formModes.UPDATE ? order : {});
  const [submittingForm, setSubmittingForm] = useState(false);
  const [form] = Form.useForm();
  const [customerList, setCustomerList] = useState([]);
  const [productList, setProductList] = useState([]);
  const [dispatchOrders, setDispatchOrders] = useState([]);
  const dispatch = useDispatch();
  const formValues = Form.useWatch([], form); // watch all values in form
  const { pathname } = useLocation();
  const currentFormMode = getFormModeFromPathV2(pathname);
  const formOrderForWatch = Form.useWatch('orderType', form);
  const formPONumberWatch = Form.useWatch('purchaseOrderNumber', form);
  const formCustomerIdWatch = Form.useWatch('customer', form);
  const orderType = Form.useWatch('orderType', form);

  const OrderBookDocumentGroups = [
    { name: 'COA', required: false },
    { name: 'TDS', required: orderType === 'CUSTOMER_ORDER' ? true : false },
    { name: 'SDS', required: false },
    { name: 'Other', required: false },
  ];

  useEffect(() => {
    if (mode === formModes.CREATE) {
      form.setFieldsValue({
        products: [
          ...Array(Math.max(1)).fill({}), // Add only the required number of empty objects
        ],
      });
    }
  }, []);

  useEffect(() => {
    if (mode === formModes.UPDATE && formPONumberWatch) {
      getDispatchOrderFromPoNumber(formPONumberWatch)
        .then(res => {
          if (res.data && res.data.length > 0) {
            setDispatchOrders(res.data);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }, [mode, formPONumberWatch]);

  const saveOrderDetails = orderData => {
    if (mode === formModes.CREATE) {
      createOrder(orderData)
        .then(response => {
          message.success('Order created successfully');
          addOrderFormOpened.endTracking(true, response?.data?.orderType);
          if (response?.data?.orderType === 'SAMPLE') {
            orderData.purchaseOrderNumber = response?.data?.purchaseOrderNumber;
            createDispatchOrder(orderData)
              .then(() => {
                setSubmittingForm(false);
                hide();
              })
              .catch(error => {
                message.error(error?.response?.data?.message || 'Failed to create dispatch order');
                setSubmittingForm(false);
              });
          }
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.error('Order creation error:', error);
          message.error(error?.response?.data?.message || 'Failed to create order');
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE) {
      updateOrder(orderData, order.id)
        .then(() => {
          message.success('Order updated successfully');
          setSubmittingForm(false);
          hide();
        })
        .catch(error => {
          console.error('Order update error:', error);
          message.error(error?.response?.data?.message || 'Failed to update order');
          setSubmittingForm(false);
        });
    }
  };

  const submitFormHandler = orderBookEntry => {

    if (!Array.isArray(orderBookEntry.products) || orderBookEntry.products.length === 0) {
      message.error('At least one product is required!');
      setSubmittingForm(false);
      return;
    }
    const missingIncoterms = orderBookEntry.products.some(product => !product.incoterms);
    const missingPackaging = orderBookEntry.products.some(product => !product.packaging);

    // handle for empty incoterms
    if(orderBookEntry?.orderType === "CUSTOMER_ORDER"){

      orderBookEntry?.products?.forEach(product => {
        if(product?.incoterms?.type === "FOR"){
          product.incoterms.data = {};
        }
      });
    }
    
    const missingDocuments = orderBookEntry.products.some(product => {

      return OrderBookDocumentGroups.filter(doc => doc.required)
        .some(doc => {
          // If the document type property doesn't exist at all, it's missing
          if (!(doc.name in product.documents)) {
            console.log(`${doc.name} property doesn't exist in documents:`, product.documents);
            return true;
          }
          
          const docArray = product.documents[doc.name];
          const isMissing = !Array.isArray(docArray) || docArray.length === 0;
          console.log(`Checking ${doc.name}:`, { docArray, isMissing });
          return isMissing;
        });
    });

    const missingTestingFlag = orderBookEntry.products.some(
      product => product.testingRequired === undefined || product.testingRequired === null
    );

    if (missingIncoterms && orderBookEntry.orderType !== 'SAMPLE') {
      setSubmittingForm(false);
      message.error('Incoterms is Required!');
      return;
    }

    if (missingDocuments && orderBookEntry.orderType !== 'SAMPLE') {
      setSubmittingForm(false);
      message.error('Required documents are missing!');
      return;
    }

    if (missingTestingFlag ) {
      setSubmittingForm(false);
      message.error('Testing Flag is Required!');
      return;
    }

    if (missingPackaging) {
      setSubmittingForm(false);
      message.error('Packaging Details is Required!');
      return;
    }

    setSubmittingForm(true);

    const formattedData = JSON.parse(JSON.stringify(orderBookEntry));

    getOrderBookDataForApi(orderBookEntry)
      .then(results => {
        results.forEach((result, index) => {
          if (index === 0 && !formattedData.inventoryId) {
            formattedData.customer = result.data;
          } else {
            formattedData.products[formattedData.inventoryId ? index : index - 1].product =
              result.data;
            formattedData.products[
              formattedData.inventoryId ? index : index - 1
            ].expectedDeliveryDate = !orderBookEntry.products[
              formattedData.inventoryId ? index : index - 1
            ].expectedDeliveryDate
              ? null
              : orderBookEntry.products[
                  formattedData.inventoryId ? index : index - 1
                ].expectedDeliveryDate.toISOString();
          }
        });

        saveOrderDetails(formattedData);
      })
      .catch(error => {
        console.error('Data fetching error:', error);
        setSubmittingForm(false);
      });
  };

  const validateAndSaveFormFields = () => {
    setSubmittingForm(true);
    form
      .validateFields()
      .then(values => {
        let valuesCopy = JSON.parse(JSON.stringify(values));
        valuesCopy.products = (valuesCopy.products || []).filter(product =>
          Object.values(product).some(
            value => value !== '' && value !== undefined && value !== null
          )
        );

        valuesCopy = {
          ...valuesCopy,
          purchaseOrderDate: valuesCopy.purchaseOrderDate
            ? dayjs(valuesCopy.purchaseOrderDate)
            : null,
          products: [
            ...valuesCopy.products.map(prod => {
              let packaging = getPackagingDetailFromId(prod.packaging, packagingList);
              // Move otherPackagingDetails into packaging if present
              if (prod.otherPackagingDetails) {
                packaging = {
                  ...packaging,
                  otherPackagingDetails: prod.otherPackagingDetails,
                };
              }
              const { otherPackagingDetails, ...restProd } = prod;
              return {
                ...restProd,
                packaging,
                expectedDeliveryDate: prod.expectedDeliveryDate
                  ? dayjs(prod.expectedDeliveryDate)
                  : '',
                marginApprovedOn: prod.marginApprovedOn ? dayjs(prod.marginApprovedOn) : '',
              };
            }),
          ],
        };

        submitFormHandler(valuesCopy);
      })
      .catch(error => {
        console.error('Form validation error:', error);
        message.error('Please fill all required fields.');
        setSubmittingForm(false);
      });
  };

  const getOptionList = key => {
    switch (key) {
      case 'orderTypeList':
        return orderTypeList;
      case 'orderForList':
        return orderForList;
      case 'customerList':
        return customerList;
      case 'paymentTermsDate':
        return paymentTermsDate;
      case 'categoryOptions':
        return categoryOptions;
      case 'currencyList':
        return currencyTypeList;
      case 'sampleRequestedByList':
        return sampleRequestedByList;
      default:
        return [];
    }
  };

  const renderField = field => {
    const options = getOptionList(field.optionListKey);
    const fieldStyle = field.style || {}; // directly use field style or empty object

    // Calculate disabled state
    const isDisabled = field.disabled ? field.disabled({ mode }) : false;
    const isSampleOrderInEditMode = mode === formModes.UPDATE && orderType === 'SAMPLE' && field.id === 'purchaseOrderNumber';
    const hasDispatchOrders = mode === formModes.UPDATE && dispatchOrders.length > 0 && field.id === 'purchaseOrderNumber';

    switch (field.type) {
      case 'textarea':
        return <Input.TextArea rows={field?.rows} style={fieldStyle} disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}/>;
      case 'select':
        return (
          <Select showSearch optionFilterProp="label" style={fieldStyle} disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}>
            {options.map(option => (
              <Select.Option
                key={option[field.optionMapping.key]}
                value={option[field.optionMapping.value]}
                label={option[field.optionMapping.label]}
              >
                {option[field.optionMapping.label]}
              </Select.Option>
            ))}
          </Select>
        );
      case 'date':
        return (
          <DatePicker
            format={DateFormat}
            locale={enUS}
            showToday
            style={fieldStyle}
            disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}
            suffixIcon={<CalendarOutlined style={{ color: '#000' }} />}
          />
        );
      case 'radio':
        return (
          <Radio.Group buttonStyle="solid" disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}>
            {options.map(item => (
              <Radio
                key={item.key}
                value={item.value}
                style={{
                  display: 'block',
                  height: '30px',
                  lineHeight: '30px',
                  marginBottom: '0px',
                }}
              >
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      case 'text':
        return <Input className="w-52" style={fieldStyle} disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}/>;
      case 'number':
        return <Input className="w-52" style={fieldStyle} type="number" disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}/>;
      case 'file':
        return (
          <FileUpload
            category="Orders"
            meta={{ poId: formPONumberWatch, documentType: 'PurchaseOrder' }}
            maxFileLimit={1}
          />
        );
      case 'switch':
        return <Switch className="w-12" style={fieldStyle} disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}/>;
      default:
        return <Input className="w-52" style={fieldStyle} disabled={isDisabled || isSampleOrderInEditMode || hasDispatchOrders}/>;
    }
  };

  const checkShowIfFilledAndValueIn = (conditions, formValues) => {
    return Object.entries(conditions).every(([key, values]) => {
      const formValue = formValues?.[key];
      if (!formValue) return false;
      if (values.length === 0) return true;
      return values.includes(formValue);
    });
  };

  const addRow = () => {
    form.setFieldsValue({
      products: [...(form.getFieldValue('products') || []), {}],
    });
  };

  useEffect(() => {
    if (!customerList || !customerList.length) {
      getCustomerList()
        .then(res => {
          if (res.data && res.data.content) {
            setCustomerList(res.data.content);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }, [formOrderForWatch]);

  useEffect(() => {
    Promise.all([getProductList(), getAllPackagingDetails()])
      .then(responseList => {
        if (responseList[1].data) {
          const pkgList = responseList[1].data.map(pkg => ({
            ...pkg,
            id: pkg.id || `${pkg.type}_${pkg.packSize}_${pkg.tareWeight}`,
          }));
          dispatch(setPackagingList(pkgList));
        }
        if (responseList[0]?.data && responseList[0].data?.content) {
          setProductList(responseList[0].data.content);
        }
      })
      .catch(error => {
        console.error(error);
      });
  }, []);

  const validateForm = () => {
    const formValues = form.getFieldsValue();
    const validProducts = (formValues.products || []).filter(product =>
      Object.values(product).some(value => value !== '' && value !== undefined && value !== null)
    );
  };

  const saveformData = () => {
    if (currentFormMode === formModes.CREATE) {
      const formValues = form.getFieldsValue();
      const cleanedData = JSON.parse(JSON.stringify(formValues));
    } else {
      validateForm();
    }
  };

  const getPackagingFormValue = (option, list) => {
    if (!option) {
      return null;
    }
    const listOptn = list.find(
      item => item.id === (option.id || `${option.type}_${option.packSize}_${option.tareWeight}`)
    );
    if (listOptn && listOptn.id) return listOptn.id;
    else {
      const id = `${option.type}_${option.packSize}_${option.tareWeight}`;
      dispatch(addPackaging([{ ...option, id }]));
      return id;
    }
  };

  const getFormPrefilldValues = () => {
    if (!orderBookEntry || !orderBookEntry?.products) return {};
    return {
      ...orderBookEntry,
      products: orderBookEntry.products.map(prod => ({
        ...prod,
        product: prod.product,
        uom: prod.uom,
        price: prod.price,
        quantity: prod.quantity,
        units: prod.units,
        margin: prod.margin,
        packaging: getPackagingFormValue(prod.packaging, packagingList),
        otherPackagingDetails: prod.packaging?.otherPackagingDetails,
        incoterms: prod.incoterms
          ? {
              ...prod.incoterms,
              data: {
                ...prod.incoterms.data,
              },
            }
          : {},
        expectedDeliveryDate: prod.expectedDeliveryDate ? dayjs(prod.expectedDeliveryDate) : '',
        marginApprovedOn: prod.marginApprovedOn ? dayjs(prod.marginApprovedOn) : '',
        remarks: prod.remarks ? [...prod.remarks] : null,
        documents: prod.documents ? prod.documents : null,
      })),
    };
  };

  useEffect(() => {
    if (
      productList &&
      productList.length &&
      packagingList &&
      packagingList.length &&
      orderBookEntry &&
      orderBookEntry.products &&
      orderBookEntry.products.length
    ) {
      form.setFieldsValue(getFormPrefilldValues());
    }
  }, [productList]);

  // add a loader
  if (submittingForm) {
    return <PageLoader />;
  }

  return (
    <>
      <div>
        <Form
          name="supplier_form"
          layout="vertical"
          scrollToFirstError
          size="large"
          form={form}
          onValuesChange={() => saveformData()}
          onFinish={validateAndSaveFormFields}
        >
          <div className="flex justify-end gap-4 items-center">
            {formValues?.orderType === 'CUSTOMER_ORDER' && mode === formModes.UPDATE && (
              <Form.Item 
                name="onHold"
                valuePropName="checked"
                style={{ marginBottom: 0 }}
              >
                <div className="flex flex-col items-center gap-2">
                  <span style={{ 
                    fontSize: '16px', 
                    fontWeight: 600, 
                    color: 'rgba(35, 86, 138, 0.7)' 
                  }}>
                    On Hold
                  </span>
                  <Switch 
                    defaultChecked={false}
                    checked={form.getFieldValue('onHold')}
                    onChange={(checked) => {
                      form.setFieldsValue({ onHold: checked });
                    }}
                  />
                </div>
              </Form.Item>
            )}
            <Button
              type="primary"
              size="large"
              loading={submittingForm}
              htmlType='submit'
            >
              {mode === formModes.UPDATE ? 'Edit' : 'Submit'}
            </Button>
          </div>

          {OrderBookFormBasicInfo.map(section => (
            <div key={section.id} className="border-b border-gray-100 last:border-0 pt-6">
              <div className="flex flex-wrap gap-6 px-6 py-0">
                {section.fields.map(field => {
                  let shouldShow = true;

                  if (field.showIfFilledAndValueIn) {
                    shouldShow = checkShowIfFilledAndValueIn(
                      field.showIfFilledAndValueIn,
                      formValues
                    );
                  }

                  if (!shouldShow) return null;

                  return (
                    <Form.Item
                      key={field.id}
                      label={
                        field.label === 'PO Number'
                          ? orderType === 'CUSTOMER_ORDER'
                            ? 'PO Number'
                            : 'Sample Number'
                          : field.label === 'PO Date'
                          ? orderType === 'CUSTOMER_ORDER'
                            ? 'PO Date'
                            : 'Sample Request Date'
                          : field.label
                      }
                      name={field.id}
                      rules={
                        field.getRules
                          ? field.getRules({ mode, formCustomerIdWatch, validatePONumber })
                          : field.rules
                      }
                      style={{ width: '200px' }}
                      valuePropName={field.type === 'switch' ? 'checked' : 'value'} // Add this
                    >
                      {renderField(field)}
                    </Form.Item>
                  );
                })}
              </div>
            </div>
          ))}
          <Divider className={CSS.divider} />
          <div className="flex justify-between items-center px-6">
            <h2 className="text-xl font-semibold text-gray-800">
              Product Details{' '}
              <span className="text-sm text-gray-500">(at least 1 product is needed)</span>
            </h2>
            <Button onClick={addRow} style={{ marginTop: 10 }}>
              Add Row
            </Button>
          </div>
          <ProductTable
            form={form}
            packagingList={packagingList}
            productList={productList}
            mode={mode}
          />
        </Form>
      </div>
    </>
  );
};

export default OrderBookForm;

OrderBookForm.propTypes = {
  mode: PropTypes.string.isRequired,
  order: PropTypes.object,
  hide: PropTypes.func.isRequired,
  addOrderFormOpened: PropTypes.func,
};
