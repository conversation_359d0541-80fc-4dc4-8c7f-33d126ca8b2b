import React, { useEffect, useState } from 'react';
import { getFormModeFromPath } from '../../util/route';
import { Button, Col, Form, Input, InputNumber, Row, Select, Space, Typography } from 'antd';
import { useDispatch } from 'react-redux';
import css from '../CustomerPanel/CustomerForm.module.css';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { setCollapseAsideBar } from '../../store/actions/collapseAsideBar';
import { setSideDrawerStatus } from '../../store/actions/collapseSideDrawer';
import RouteFactory from '../../service/RouteFactory';
import { enquiryPriorityList, formConfigName, formModes, productUOMList } from '../../constants/formConstant';
import { createEnquiry, getEnquiryData, updateEnquiry } from '../../service/api/enquiryApi';
import HeaderPanel from '../headerPanel';
import PageLoader from '../Loaders/PageLoader';
import { getCustomerList } from '../../service/api/customerApi';
import IncotermInput from '../IncotermInput/IncotermInput';
import { getFormConfigFromName } from '../../service/api/formConfig';

const EnquiryForm = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { enquiryId } = useParams();
  const { pathname } = useLocation();

  const [loading, setLoading] = useState(false);
  const [submittingForm, setSubmittingForm] = useState(false);
  const [enquiryData, setEnquiryData] = useState({});
  const [customerList, setCustomerList] = useState([]);
  const [categoryList,setCategoryList]=useState([]);

  const currentFormMode = getFormModeFromPath(pathname);

  const collapseAsideBarHandler = show => {
    dispatch(setCollapseAsideBar(show));
  };
  const collapseSideDrawerHandler = show => {
    dispatch(setSideDrawerStatus(show));
  };
  const goToDashBoard = () => {
    collapseSideDrawerHandler(true);
    collapseAsideBarHandler(false);
    navigate(new RouteFactory().dashboard().enquiry().build());
  };
  const saveEnquiryDetails = (enquiryFormData, mode) => {
    if (mode === formModes.CREATE) {
      createEnquiry(enquiryFormData)
        .then(response => {
          console.log('Enquiry api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(error => {
          console.log(error);
          setSubmittingForm(false);
        });
    }
    if (mode === formModes.UPDATE && enquiryId) {
      updateEnquiry(enquiryFormData, enquiryId)
        .then(response => {
          console.log('Enquiry api response:', response);
          setSubmittingForm(false);
          goToDashBoard();
        })
        .catch(error => {
          setSubmittingForm(false);
          console.log(error);
        });
    }
  };
  const submitFormHandler = () => {
    setSubmittingForm(true);
    form
      .validateFields()
      .then(values => {
        saveEnquiryDetails(values, currentFormMode);
      })
      .catch(error => {
        console.log(error);
        setSubmittingForm(false);
      });
  };

  useEffect(() => {
    if (currentFormMode === formModes.CREATE) setLoading(true);
    const fetchApiList = [getCustomerList(),getFormConfigFromName(formConfigName.category)];
    Promise.all(fetchApiList)
      .then(responseList => {
        if (responseList[0]?.data && responseList[0].data?.content) {
          setCustomerList(responseList[0].data.content);
        }
        if (responseList[1].data) {
          setCategoryList(responseList[1].data.value);
        }
        setLoading(false);
      })
      .catch(error => {
        console.log(error);
        if (currentFormMode === formModes.CREATE) setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (enquiryId && currentFormMode === formModes.UPDATE) {
      setLoading(true);
      getEnquiryData(enquiryId)
        .then(res => {
          if (res?.data?.id) {
            setEnquiryData(res.data);
            setLoading(false);
          }
        })
        .catch(error => {
          console.log(error);
          setLoading(false);
        });
    }
  }, [enquiryId]);

  useEffect(() => {
    if (currentFormMode === formModes.UPDATE && enquiryData.id &&  customerList?.length) {
      form.setFieldsValue({...enquiryData, customerId: enquiryData?.customerId || enquiryData?.customer?.id});
    }

  }, [enquiryData, customerList])

  return (
    <>
      <HeaderPanel
        name="Enquiry Directory"
        sideButtons={
          <>
            <Space>
              <Button
                type="primary"
                size="large"
                onClick={() => submitFormHandler()}
                loading={submittingForm}
              >
                {currentFormMode === formModes.CREATE ? 'Add Enquiry' : 'Save Changes'}
              </Button>
              <Button type="default" onClick={goToDashBoard} size="large">
                Cancel
              </Button>
            </Space>
          </>
        }
      />
      {loading ? (
        <PageLoader />
      ) : (
        <div className={css.formContainer}>
          <Typography.Title level={2} style={{ color: '#23568A' }}>
            Add a Enquiry
          </Typography.Title>
          <Form
            name="enquiry_form"
            layout="vertical"
            scrollToFirstError
            // initialValues={currentFormMode === formModes.CREATE ? {} : {...enquiryData, customerId: enquiryData?.customerId || enquiryData?.customer?.id} }
            preserve={false}
            size="large"
            form={form}
            validateTrigger="onBlur"
          >
            <Row gutter={16}>
              <Col span={12}>
                {customerList.length ? (
                  <Form.Item
                    label="Customer Name"
                    name="customerId"
                    rules={[{ required: true, message: 'Please select Customer' }]}
                  >
                    <Select
                      showSearch
                      optionFilterProp="label"
                      disabled={currentFormMode === formModes.UPDATE}
                    >
                      {customerList.map(customer => (
                        <Select.Option key={customer.id} value={customer.id} label={customer.name}>
                          {customer.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
              </Col>
              <Col span={12}>
                <Form.Item
                  name="productName"
                  label="Product Name"
                  rules={[
                    {
                      required: true,
                      message: 'Please select product!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="enquiryPriority"
                  label="Priority"
                  rules={[
                    {
                      required: true,
                      message: 'Please select enquiry prioirty!',
                    },
                  ]}
                >
                  <Select>
                    {Object.keys(enquiryPriorityList).map(key => {
                      let priority = enquiryPriorityList[key];
                      return (
                        <Select.Option key={priority.key} value={priority.value}>
                          {priority.label}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="categories"
                  label="Category"
                  rules={[
                    {
                      required: true,
                      message: 'Please select enquiry category!',
                    },
                  ]}
                >
                  <Select mode='multiple'>
                    {Object.keys(categoryList).map((key,index) => {
                      let category = categoryList[key];
                      return (
                        <Select.Option key={index} value={category.name}>
                          {category.name}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="casNumber"
                  label="CAS number"
                  // rules={[ // making cas number not mandatory for now
                  //   {
                  //     required: true,
                  //     message: 'Please enter grade!',
                  //   }
                  // ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="grade"
                  label="Grade"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter grade!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="quantity"
                  label="Quantity"
                  rules={[
                    {
                      required: true,
                      message: 'Please enter quantity!',
                    },
                  ]}
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="unitOfMeasure"
                  label="Unit of measurement(UOM)"
                  rules={[
                    {
                      required: true,
                      message: 'Please select UOM!',
                    },
                  ]}
                >
                  <Select>
                    {productUOMList.map(uom => (
                      <Select.Option key={uom.key} value={uom.value}>
                        {uom.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="application"
                  label="Application"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: 'Please enter application!',
                  //   }
                  // ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <IncotermInput form={form} />
              </Col>
            </Row>
          </Form>
        </div>
      )}
    </>
  );
};

export default EnquiryForm;
